#ifdef BLUAPI_EXPORTS
#define BLUAPI_API __declspec(dllexport)
#else
#define BLUAPI_API __declspec(dllimport)
#endif

typedef struct
{
	int h_Handle;
	int port;
	double AvgCurrent;
	int voltage;
	char errMsg[256];
}PowerSupplyInfo;

typedef enum {
	Connect = 0,
	PowerOn,
	PowerOff,
	GetCurrent,
	PowerOnToReboot,
	FunctionNoExist = 255
} E_BLU_FUNCTION_ID;

BLUAPI_API int __stdcall fnBLUAPI(E_BLU_FUNCTION_ID FunctionId, PowerSupplyInfo* Info);
