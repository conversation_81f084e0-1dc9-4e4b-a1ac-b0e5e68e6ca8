#include "mainwindow.h"
#include "qdebug.h"
#include <shlwapi.h>
#include <windows.h>
#include "ui_mainwindow.h"

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_frameVisible(false)
    , ui(new Ui::MainWindow)
    , m_showlogdialog(new ShowLogDialog(this))
    , m_mainwindowcallback(new MainWindowCallback(this))
    , m_messettingdialog(new MesSettingDialog(this))
    , m_aboutDialog(new CAboutDialog(this))
{

    //init UI
    ui->setupUi(this);
    InitUIControlVariable();
    InitToolBar();
    InitDlgUIProp();

    //init signal
    for(int i=0;i<MAX_SUPPORT_COMPORT_NUM;i++)
    {
        settingflag[i] = true;
        m_time_label_timer[i] = new QTimer(this);
        timer[i] = new QTimer(this);
    }

    connect(m_time_label_timer[0], SIGNAL(timeout()), this, SLOT(slot_UpdateTestTime1()));
    connect(m_time_label_timer[1], SIGNAL(timeout()), this, SLOT(slot_UpdateTestTime2()));
    connect(m_time_label_timer[2], SIGNAL(timeout()), this, SLOT(slot_UpdateTestTime3()));
    connect(m_time_label_timer[3], SIGNAL(timeout()), this, SLOT(slot_UpdateTestTime4()));
    connect(m_pAboutAction, SIGNAL(triggered(bool)), this, SLOT(Slot_actionAboutTriggered()));
    connect(m_pClearRecordAction, SIGNAL(triggered(bool)), this, SLOT(Slot_actionClearRecordTriggered()));
    connect(timer[0], SIGNAL(timeout()), this, SLOT(Dut1CheckFixtureStatus()));
    connect(timer[1], SIGNAL(timeout()), this, SLOT(Dut2CheckFixtureStatus()));
    connect(timer[2], SIGNAL(timeout()), this, SLOT(Dut3CheckFixtureStatus()));
    connect(timer[3], SIGNAL(timeout()), this, SLOT(Dut4CheckFixtureStatus()));

    InitEditReturnFun();

    //init ui effect
    ui->stackedWidget->setCurrentIndex(0);
    ui->frame->hide();
    ui->line->hide();
    QTimer *mouseCheckTimer = new QTimer(this);
    connect(mouseCheckTimer, &QTimer::timeout, this, &MainWindow::checkMousePosition);
    mouseCheckTimer->start(100);

    //Init Setting
    m_settingclass = new SettingClass(this, ui);
    m_settingclass->InitSettings();
    m_xlsxInstance = new QxlsxInstance();

    RegTestItem();

    InitTestItemCaseUI();
    InitTestItemParams();

    //init Main UI display
	Common::getInstance()->ClearTestCount();
    UpdateUIDisplay();
    InitTestRate();
    ui->Dut1_SN_Edit->setFocus();

    //Auto Function
    for(UINT i = 0 ; i < MAX_SUPPORT_COMPORT_NUM; i++)
        CheckCtrlBoxStatus(i);
}

MainWindow::~MainWindow()
{
    if (m_hasUnsavedChanges) {
        QMessageBox::StandardButton reply = QMessageBox::question(this,
                                                                "UnSaved Changes",
                                                                "The parameters have been changed but have not been saved yet. Do you want to save them now ?",
                                                                QMessageBox::Yes | QMessageBox::No);
        if (reply == QMessageBox::Yes) {
            on_Save_btn_clicked();
        }
    }
    
    Common::getInstance()->saveCfgFile(Common::getInstance()->getCfgFilePath());
    Common::getInstance()->saveOptionFile();
    Sleep(500);
    delete ui;
}

void MainWindow::InitToolBar()
{
    // init ToolBar
    m_pAboutAction = new QAction("About", this);
    m_pAboutAction->setIcon(QIcon(":/images/info.png"));
    m_pAboutAction->setFont(QFont("Arial", 9, QFont::Normal, false));
    m_pClearRecordAction = new QAction("Clear Record", this);
    m_pClearRecordAction->setIcon(QIcon(":/images/clear.png"));
    m_pClearRecordAction->setFont(QFont("Arial", 9, QFont::Normal, false));

    QMenu *menuMore = new QMenu();
    menuMore->addAction(m_pAboutAction);
    menuMore->addAction(m_pClearRecordAction);

    ui->More_btn->setMenu(menuMore);
}

void MainWindow::InitUIControlVariable()
{
    PreLoaderPortlable << ui->Dut1_Preloader_Port_label << ui->Dut2_Preloader_Port_label << ui->Dut3_Preloader_Port_label << ui->Dut4_Preloader_Port_label;
    KernelPortlable << ui->Dut1_Kernel_Port_label << ui->Dut2_Kernel_Port_label << ui->Dut3_Kernel_Port_label << ui->Dut4_Kernel_Port_label;

    TestBotton << ui->Dut1_Start_Button << ui->Dut2_Start_Button << ui->Dut3_Start_Button << ui->Dut4_Start_Button;

    CodeGroups << ui->Dut1_CodeInfo_GroupBox << ui->Dut2_CodeInfo_GroupBox << ui->Dut3_CodeInfo_GroupBox << ui->Dut4_CodeInfo_GroupBox;
    barcodeEdits << ui->Dut1_SN_Edit << ui->Dut2_SN_Edit << ui->Dut3_SN_Edit << ui->Dut4_SN_Edit;
    imei1Edits << ui->Dut1_IMEI1_Edit << ui->Dut2_IMEI1_Edit << ui->Dut3_IMEI1_Edit << ui->Dut4_IMEI1_Edit;
    imei2Edits << ui->Dut1_IMEI2_Edit << ui->Dut2_IMEI2_Edit << ui->Dut3_IMEI2_Edit << ui->Dut4_IMEI2_Edit;
    wifiEdits << ui->Dut1_WIFI_Edit << ui->Dut2_WIFI_Edit << ui->Dut3_WIFI_Edit << ui->Dut4_WIFI_Edit;
    btEdits << ui->Dut1_BT_Edit << ui->Dut2_BT_Edit << ui->Dut3_BT_Edit << ui->Dut4_BT_Edit;
    meidEdits << ui->Dut1_MEID_Edit << ui->Dut2_MEID_Edit << ui->Dut3_MEID_Edit << ui->Dut4_MEID_Edit;
    EtherMacEdits << ui->Dut1_EthernetMac_Edit << ui->Dut2_EthernetMac_Edit << ui->Dut3_EthernetMac_Edit << ui->Dut4_EthernetMac_Edit;
    SerialNoEdits << ui->Dut1_SerialNo_Edit << ui->Dut2_SerialNo_Edit << ui->Dut3_SerialNo_Edit << ui->Dut4_SerialNo_Edit;
    NetCodeEdits << ui->Dut1_NetCode_Edit << ui->Dut2_NetCode_Edit << ui->Dut3_NetCode_Edit << ui->Dut4_NetCode_Edit;
    OBSNEdits << ui->Dut1_OBSN_Edit << ui->Dut2_OBSN_Edit << ui->Dut3_OBSN_Edit << ui->Dut4_OBSN_Edit;
    IWSNEdits << ui->Dut1_IWSN_Edit << ui->Dut2_IWSN_Edit << ui->Dut3_IWSN_Edit << ui->Dut4_IWSN_Edit;
    BatLabelEdits << ui->Dut1_BatLabel_Edit << ui->Dut2_BatLabel_Edit << ui->Dut3_BatLabel_Edit << ui->Dut4_BatLabel_Edit;
    ScrilSfEdits << ui->Dut1_ScrilSf_Edit << ui->Dut2_ScrilSf_Edit << ui->Dut3_ScrilSf_Edit << ui->Dut4_ScrilSf_Edit;

    barcodelabel << ui->Dut1_SN_label << ui->Dut2_SN_label << ui->Dut3_SN_label << ui->Dut4_SN_label;
    imei1label << ui->Dut1_IMEI1_label << ui->Dut2_IMEI1_label << ui->Dut3_IMEI1_label << ui->Dut4_IMEI1_label;
    imei2label << ui->Dut1_IMEI2_label << ui->Dut2_IMEI2_label << ui->Dut3_IMEI2_label << ui->Dut4_IMEI2_label;
    wifilabel << ui->Dut1_WIFI_label << ui->Dut2_WIFI_label << ui->Dut3_WIFI_label << ui->Dut4_WIFI_label;
    btlabel << ui->Dut1_BT_label << ui->Dut2_BT_label << ui->Dut3_BT_label << ui->Dut4_BT_label;
    meidlabel << ui->Dut1_MEID_label << ui->Dut2_MEID_label << ui->Dut3_MEID_label << ui->Dut4_MEID_label;
    EtherMaclabel << ui->Dut1_EthernetMac_label << ui->Dut2_EthernetMac_label << ui->Dut3_EthernetMac_label << ui->Dut4_EthernetMac_label;
    SerialNolabel << ui->Dut1_SerialNo_label << ui->Dut2_SerialNo_label << ui->Dut3_SerialNo_label << ui->Dut4_SerialNo_label;
    NetCodelabel << ui->Dut1_NetCode_label << ui->Dut2_NetCode_label << ui->Dut3_NetCode_label << ui->Dut4_NetCode_label;
    OBSNlabel << ui->Dut1_OBSN_label << ui->Dut2_OBSN_label << ui->Dut3_OBSN_label << ui->Dut4_OBSN_label;
    IWSNlabel << ui->Dut1_IWSN_label << ui->Dut2_IWSN_label << ui->Dut3_IWSN_label << ui->Dut4_IWSN_label;
    BatLabellabel << ui->Dut1_BatLabel_label << ui->Dut2_BatLabel_label << ui->Dut3_BatLabel_label << ui->Dut4_BatLabel_label;
    ScrilSflabel << ui->Dut1_ScrilSf_label << ui->Dut2_ScrilSf_label << ui->Dut3_ScrilSf_label << ui->Dut4_ScrilSf_label;

    ItemTableWidget << ui->Dut1_tableWidget << ui->Dut2_tableWidget << ui->Dut3_tableWidget << ui->Dut4_tableWidget;

    test_message << ui->Log_textEdit_1 << ui->Log_textEdit_2 << ui->Log_textEdit_3 << ui->Log_textEdit_4;
}

void MainWindow::InitDlgUIProp()
{
    QRegExpValidator* barcodeValidator = new QRegExpValidator(QRegExp("[A-Za-z0-9]{0," + QString::number(BARCODE_MAX_LENGTH) + "}"), this);
    QRegExpValidator* imeiValidator = new QRegExpValidator(QRegExp("[0-9]{0," + QString::number(IMEI_MAX_LENGTH) + "}"), this);
    QRegExpValidator* btValidator = new QRegExpValidator(QRegExp("[A-Fa-f0-9]{0," + QString::number(BT_ADDRESS_MAX_LENGTH) + "}"), this);
    QRegExpValidator* wifiValidator = new QRegExpValidator(QRegExp("[A-Fa-f0-9]{0," + QString::number(WIFI_MAC_MAX_LENGTH) + "}"), this);
    QRegExpValidator* meidValidator = new QRegExpValidator(QRegExp("[A-Za-z0-9]{0," + QString::number(MEID_LENGTH) + "}"), this);
    QRegExpValidator* serialNoValidator = new QRegExpValidator(QRegExp("[A-Za-z0-9]{0," + QString::number(SERIAL_NO_LEN) + "}"), this);
    QRegExpValidator* netCodeValidator = new QRegExpValidator(QRegExp("[A-Za-z0-9]{0,21}"), this);
    QRegExpValidator* ethernetMacValidator = new QRegExpValidator(QRegExp("[A-Fa-f0-9]{0," + QString::number(ETHERNET_MAC_MAX_LENGTH) + "}"), this);
    QRegExpValidator* ScrilSfValidator = new QRegExpValidator(QRegExp("[A-Za-z0-9]{0," + QString::number(SCRILSF_LENGTH) + "}"), this);

    for(int i=0; i< MAX_SUPPORT_COMPORT_NUM; i++)
    {
        barcodeEdits[i]->setValidator(barcodeValidator);
        imei1Edits[i]->setValidator(imeiValidator);
        imei2Edits[i]->setValidator(imeiValidator);
        wifiEdits[i]->setValidator(wifiValidator);
        btEdits[i]->setValidator(btValidator);
        meidEdits[i]->setValidator(meidValidator);
        SerialNoEdits[i]->setValidator(serialNoValidator);
        NetCodeEdits[i]->setValidator(netCodeValidator);
        EtherMacEdits[i]->setValidator(ethernetMacValidator);
        OBSNEdits[i]->setValidator(barcodeValidator);
        IWSNEdits[i]->setValidator(barcodeValidator);
        BatLabelEdits[i]->setValidator(barcodeValidator);
        ScrilSfEdits[i]->setValidator(ScrilSfValidator);
    }
}

void MainWindow::InitEditReturnFun()
{
    connect(ui->Dut1_SN_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut1EditReturnPressed);
    connect(ui->Dut2_SN_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut2EditReturnPressed);
    connect(ui->Dut3_SN_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut3EditReturnPressed);
    connect(ui->Dut4_SN_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut4EditReturnPressed);
    connect(ui->Dut1_IMEI1_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut1EditReturnPressed);
    connect(ui->Dut2_IMEI1_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut2EditReturnPressed);
    connect(ui->Dut3_IMEI1_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut3EditReturnPressed);
    connect(ui->Dut4_IMEI1_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut4EditReturnPressed);
    connect(ui->Dut1_IMEI2_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut1EditReturnPressed);
    connect(ui->Dut2_IMEI2_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut2EditReturnPressed);
    connect(ui->Dut3_IMEI2_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut3EditReturnPressed);
    connect(ui->Dut4_IMEI2_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut4EditReturnPressed);
    connect(ui->Dut1_MEID_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut1EditReturnPressed);
    connect(ui->Dut2_MEID_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut2EditReturnPressed);
    connect(ui->Dut3_MEID_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut3EditReturnPressed);
    connect(ui->Dut4_MEID_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut4EditReturnPressed);
    connect(ui->Dut1_BT_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut1EditReturnPressed);
    connect(ui->Dut2_BT_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut2EditReturnPressed);
    connect(ui->Dut3_BT_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut3EditReturnPressed);
    connect(ui->Dut4_BT_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut4EditReturnPressed);
    connect(ui->Dut1_WIFI_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut1EditReturnPressed);
    connect(ui->Dut2_WIFI_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut2EditReturnPressed);
    connect(ui->Dut3_WIFI_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut3EditReturnPressed);
    connect(ui->Dut4_WIFI_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut4EditReturnPressed);
    connect(ui->Dut1_EthernetMac_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut1EditReturnPressed);
    connect(ui->Dut2_EthernetMac_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut2EditReturnPressed);
    connect(ui->Dut3_EthernetMac_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut3EditReturnPressed);
    connect(ui->Dut4_EthernetMac_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut4EditReturnPressed);
    connect(ui->Dut1_SerialNo_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut1EditReturnPressed);
    connect(ui->Dut2_SerialNo_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut2EditReturnPressed);
    connect(ui->Dut3_SerialNo_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut3EditReturnPressed);
    connect(ui->Dut4_SerialNo_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut4EditReturnPressed);
    connect(ui->Dut1_NetCode_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut1EditReturnPressed);
    connect(ui->Dut2_NetCode_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut2EditReturnPressed);
    connect(ui->Dut3_NetCode_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut3EditReturnPressed);
    connect(ui->Dut4_NetCode_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut4EditReturnPressed);
    connect(ui->Dut1_OBSN_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut1EditReturnPressed);
    connect(ui->Dut2_OBSN_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut2EditReturnPressed);
    connect(ui->Dut3_OBSN_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut3EditReturnPressed);
    connect(ui->Dut4_OBSN_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut4EditReturnPressed);
    connect(ui->Dut1_IWSN_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut1EditReturnPressed);
    connect(ui->Dut2_IWSN_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut2EditReturnPressed);
    connect(ui->Dut3_IWSN_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut3EditReturnPressed);
    connect(ui->Dut4_IWSN_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut4EditReturnPressed);
    connect(ui->Dut1_BatLabel_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut1EditReturnPressed);
    connect(ui->Dut2_BatLabel_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut2EditReturnPressed);
    connect(ui->Dut3_BatLabel_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut3EditReturnPressed);
    connect(ui->Dut4_BatLabel_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut4EditReturnPressed);
    connect(ui->Dut1_ScrilSf_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut1EditReturnPressed);
    connect(ui->Dut2_ScrilSf_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut2EditReturnPressed);
    connect(ui->Dut3_ScrilSf_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut3EditReturnPressed);
    connect(ui->Dut4_ScrilSf_Edit, &QLineEdit::returnPressed, this, &MainWindow::onDut4EditReturnPressed);
}

QListWidget* MainWindow::getListWidget() const
{
    return ui->listWidget;
}

void MainWindow::Slot_actionAboutTriggered()
{
    qDebug() << "[user][event] Open About page";
    m_aboutDialog->setModal(true);
    m_aboutDialog->show();
}

void MainWindow::Slot_actionClearRecordTriggered()
{
    qDebug() << "[user][event] Clear test record";
    Common::getInstance()->ClearTestCount();
    UpdateUIDisplay();
}

void MainWindow::UpdateUIDisplay()
{
    if(Common::getInstance()->getDutPort(0, PRELOADER_COM) > 0)
        ui->Dut1_Preloader_Port_label->setText(QString("P[ %1 ]").arg(Common::getInstance()->getDutPort(0, PRELOADER_COM)));
    else
        ui->Dut1_Preloader_Port_label->setText("P[...]");

    if(Common::getInstance()->getDutPort(1, PRELOADER_COM) > 0)
        ui->Dut2_Preloader_Port_label->setText(QString("P[ %1 ]").arg(Common::getInstance()->getDutPort(1, PRELOADER_COM)));
    else
        ui->Dut2_Preloader_Port_label->setText("P[...]");

    if(Common::getInstance()->getDutPort(2, PRELOADER_COM) > 0)
        ui->Dut3_Preloader_Port_label->setText(QString("P[ %1 ]").arg(Common::getInstance()->getDutPort(2, PRELOADER_COM)));
    else
        ui->Dut3_Preloader_Port_label->setText("P[...]");

    if(Common::getInstance()->getDutPort(3, PRELOADER_COM) > 0)
        ui->Dut4_Preloader_Port_label->setText(QString("P[ %1 ]").arg(Common::getInstance()->getDutPort(3, PRELOADER_COM)));
    else
        ui->Dut4_Preloader_Port_label->setText("P[...]");


    if(Common::getInstance()->getDutPort(0, KERNEL_COM) > 0)
        ui->Dut1_Kernel_Port_label->setText(QString("K[ %1 ]").arg(Common::getInstance()->getDutPort(0, KERNEL_COM)));
    else
        ui->Dut1_Kernel_Port_label->setText("K[...]");

    if(Common::getInstance()->getDutPort(1, KERNEL_COM) > 0)
        ui->Dut2_Kernel_Port_label->setText(QString("K[ %1 ]").arg(Common::getInstance()->getDutPort(1, KERNEL_COM)));
    else
        ui->Dut2_Kernel_Port_label->setText("K[...]");

    if(Common::getInstance()->getDutPort(2, KERNEL_COM) > 0)
        ui->Dut3_Kernel_Port_label->setText(QString("K[ %1 ]").arg(Common::getInstance()->getDutPort(2, KERNEL_COM)));
    else
        ui->Dut3_Kernel_Port_label->setText("K[...]");

    if(Common::getInstance()->getDutPort(3, KERNEL_COM) > 0)
        ui->Dut4_Kernel_Port_label->setText(QString("K[ %1 ]").arg(Common::getInstance()->getDutPort(3, KERNEL_COM)));
    else
        ui->Dut4_Kernel_Port_label->setText("K[...]");


    if(!Common::getInstance()->getActiveEnable(0))
        ui->Thread1_GroupBox->hide();
    else
        ui->Thread1_GroupBox->show();

    if(!Common::getInstance()->getActiveEnable(1))
        ui->Thread2_GroupBox->hide();
    else
        ui->Thread2_GroupBox->show();

    if(!Common::getInstance()->getActiveEnable(2))
        ui->Thread3_GroupBox->hide();
    else
        ui->Thread3_GroupBox->show();

    if(!Common::getInstance()->getActiveEnable(3))
        ui->Thread4_GroupBox->hide();
    else
        ui->Thread4_GroupBox->show();

    ResetTestTime();

    for(int nTreadID=0; nTreadID < MAX_SUPPORT_COMPORT_NUM; nTreadID++)
    {
        UpdateOperateResult(nTreadID, IDLE);
        InitItemListView(nTreadID);
        JudgeMesStatus(nTreadID);
        InitRelayPort(nTreadID);
        InitScanGunPort(nTreadID);
        UpdateTestRate(nTreadID, IDLE);
        ClearLogs(nTreadID);
    }

    if(Common::getInstance()->getMesOfflineEnable())
        ui->Mes_btn->setIcon(QIcon(":/images/disconnected.png"));
    else
        ui->Mes_btn->setIcon(QIcon(":/images/connected.png"));

    CheckMainUIInputDisplay();
}

void MainWindow::UpdateStartButton(UINT nTreadID)
{
    QString strText = TestBotton[nTreadID]->text();

    if (strText == "Start")
    {
        if(!WriteUIChange(nTreadID))
            return;

        if(!Common::getInstance()->getMesOfflineEnable())
        {
            if(!Common::getInstance()->getMesNotCheckStationEnable())
            {
                if(!CheckSNFromMes(nTreadID, barcodeEdits[nTreadID]->text()))
                    return;
            }

            if(LoadMesFlag)
            {
                if(!GetCodesFromMes(nTreadID, barcodeEdits[nTreadID]->text()))
                    return;
            }
        }

        if(LoadXlsxFlag)
            InitScanDataFromXlsx(nTreadID);

        if(LoadDefaultFlag)
            InitScanDataFromDafault(nTreadID);

        if(LoadSection)
        {
            if(!InitScanDataFromSection(nTreadID))
                return;
        }

        if(LoadYnSystem)
        {
            if(!InitScanDataFromYnSystem(nTreadID))
                return;
        }

        DisableUIItem(nTreadID);
        m_mainwindowcallback->Thread_Start(nTreadID);
    }
    else if (strText == "Stop")
    {
        TestBotton[nTreadID]->setEnabled(false);
        m_mainwindowcallback->Thread_Stop(nTreadID);
        m_time_label_timer[nTreadID]->stop();
        CheckCtrlBoxStatus(nTreadID);
    }

    CheckMouseFocus(nTreadID);
}

void MainWindow::UpdateOperateResult(UINT nTreadID, TestResult_Status Status)
{
    QLineEdit *ResultStatus;
    switch(nTreadID)
    {
    case 0:
        ResultStatus = ui->Dut1_Result;
        break;
    case 1:
        ResultStatus = ui->Dut2_Result;
        break;
    case 2:
        ResultStatus = ui->Dut3_Result;
        break;
    case 3:
        ResultStatus = ui->Dut4_Result;
        break;
    default:
        return;
    }

    switch (Status)
    {
    case FAIL:
        ResultStatus->show();
        ResultStatus->setText("FAIL");
        ResultStatus->setStyleSheet("background: #FF0000;");
        UpdateTestRate(nTreadID, FAIL);
        break;

    case PASS:
        ResultStatus->show();
        ResultStatus->setText("PASS");
        ResultStatus->setStyleSheet("background: #00FF00;");
        UpdateTestRate(nTreadID, PASS);
        break;

    case RUNNING:
        ResultStatus->show();
        ResultStatus->setText("RUNNING");
        ResultStatus->setStyleSheet("background: #FFFF00;");
        break;

    case IDLE:
        ResultStatus->show();
        ResultStatus->setText("IDLE");
        ResultStatus->setStyleSheet("background: #C0C0C0;");
        break;

    default:
        //Do nothing
        break;
    }

    ResultStatus->setAlignment(Qt::AlignHCenter);

    double FontSize = 30;
    auto font = ResultStatus->font();
    font.setPointSize(FontSize);
    ResultStatus->setFont(font);
}

void MainWindow::InitItemListView(UINT nTreadID)
{
    ItemTableWidget[nTreadID]->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);//延伸显示(水平铺满)
    ItemTableWidget[nTreadID]->setSelectionMode(QAbstractItemView::NoSelection);                //被选中后不高亮

    QMargins margins(1, 1, 1, 1); // 上、右、下、左的边距
    ItemTableWidget[nTreadID]->setContentsMargins(margins);// 设置内边距，增加边框显示

    ItemTableWidget[nTreadID]->clearContents();
    ItemTableWidget[nTreadID]->setRowCount(0);
    //启用鼠标跟踪
    ItemTableWidget[nTreadID]->setMouseTracking(true);
    //信号与槽
    connect(ItemTableWidget[nTreadID], &QTableWidget::entered, this, &MainWindow::ShowTooltip);
}

void MainWindow::UpdateTestListItem(UINT nTreadID, QString m_ItemName, TestResult_Status m_Status, QString value)
{
    QColor backgroundcolor;
    QString ItemStatus;

    switch(m_Status)
    {
    case PASS:
        backgroundcolor = QColor(194,255,194);
        ItemStatus = "PASS";
        break;
    case FAIL:
        backgroundcolor = QColor(255,72,0);
        ItemStatus = "FAIL";
        break;
    case RUNNING:
        backgroundcolor = QColor(254,209,0);
        ItemStatus = "Running...";
        break;
    default:
        backgroundcolor = QColor(255,255,255);
        ItemStatus = "";
        break;
    }

    if(ItemTableWidget[nTreadID]->rowCount() != 0)
    {
        for (int row = 0; row < ItemTableWidget[nTreadID]->rowCount(); ++row)
        {
            if (ItemTableWidget[nTreadID]->model()->index(row, 0).data().toString() == m_ItemName)
            {
                QTableWidgetItem *item = new QTableWidgetItem(m_ItemName);
                item->setBackgroundColor(backgroundcolor);
                ItemTableWidget[nTreadID]->setItem(row,0,item);
                item = new QTableWidgetItem(ItemStatus);
                item->setBackgroundColor(backgroundcolor);
                item->setTextAlignment(Qt::AlignCenter);
                ItemTableWidget[nTreadID]->setItem(row,1,item);
                item = new QTableWidgetItem(value);
                item->setBackgroundColor(backgroundcolor);
                item->setTextAlignment(Qt::AlignCenter);
                ItemTableWidget[nTreadID]->setItem(row,2,item);
                return;
            }
        }
    }

    int count = ItemTableWidget[nTreadID]->rowCount();
    ItemTableWidget[nTreadID]->insertRow(count);

    QTableWidgetItem *item = new QTableWidgetItem(m_ItemName);
    item->setBackgroundColor(backgroundcolor);
    ItemTableWidget[nTreadID]->setItem(count,0,item);
    item = new QTableWidgetItem(ItemStatus);
    item->setBackgroundColor(backgroundcolor);
    item->setTextAlignment(Qt::AlignCenter);
    ItemTableWidget[nTreadID]->setItem(count,1,item);
    item = new QTableWidgetItem(value);
    item->setBackgroundColor(backgroundcolor);
    item->setTextAlignment(Qt::AlignCenter);
    ItemTableWidget[nTreadID]->setItem(count,2,item);
}

void MainWindow::EnableUIItem(UINT nTreadID)
{
    TestBotton[nTreadID]->setText("Start");
    CodeGroups[nTreadID]->setEnabled(true);

    barcodeEdits[nTreadID]->clear();
    imei1Edits[nTreadID]->clear();
    imei2Edits[nTreadID]->clear();
    wifiEdits[nTreadID]->clear();
    btEdits[nTreadID]->clear();
    meidEdits[nTreadID]->clear();
    SerialNoEdits[nTreadID]->clear();
    EtherMacEdits[nTreadID]->clear();
    NetCodeEdits[nTreadID]->clear();
    OBSNEdits[nTreadID]->clear();
    IWSNEdits[nTreadID]->clear();
    BatLabelEdits[nTreadID]->clear();
    ScrilSfEdits[nTreadID]->clear();

    settingflag[nTreadID] = true;
    if(settingflag[0] && settingflag[1] && settingflag[2] && settingflag[3])
        ui->frame->setEnabled(true);

    CheckMouseFocus(nTreadID);

    TestBotton[nTreadID]->setEnabled(true);
}

void MainWindow::DisableUIItem(UINT nTreadID)
{
    TestBotton[nTreadID]->setText("Stop");
    CodeGroups[nTreadID]->setEnabled(false);
    ui->frame->setEnabled(false);
    settingflag[nTreadID] = false;
}

void MainWindow::ClearLogs(UINT nTreadID)
{
    test_message[nTreadID]->clear();
}

void MainWindow::AppendLogs(UINT nTreadID, const QString log, LOGCOLOR_TYPE textcolor)
{
    WriteLog(nTreadID, log);

    QString addlog;
    if(textcolor == RED)
    {
        addlog = "<font color=\"red\">" + log + "</font>";
    }
    else if(textcolor == GREEN)
    {
        addlog = "<font color=\"green\">" + log + "</font>";
    }
    else
    {
        addlog = log;
    }

    test_message[nTreadID]->append(addlog);
    double FontSize = 11;
    auto font = test_message[nTreadID]->font();
    font.setPointSize(FontSize);
    test_message[nTreadID]->setFont(font);

    QTextBlockFormat blockFormat;
    blockFormat.setLineHeight(8, QTextBlockFormat::LineDistanceHeight);//设置行间距
    auto textCursor = test_message[nTreadID]->textCursor();
    textCursor.setBlockFormat(blockFormat);
    test_message[nTreadID]->setTextCursor(textCursor);
}

void MainWindow::UpdateBarcodeToUI(UINT nTreadID, QString m_Barcode)
{
    barcodeEdits[nTreadID]->setText(m_Barcode);

    QWidget *focusedWidget = QApplication::focusWidget();
    if((focusedWidget != ui->Dut1_SN_Edit && focusedWidget != ui->Dut2_SN_Edit && focusedWidget != ui->Dut3_SN_Edit && focusedWidget != ui->Dut4_SN_Edit) ||
        (focusedWidget == ui->Dut1_SN_Edit && !ui->Dut1_SN_Edit->text().isEmpty()) ||
        (focusedWidget == ui->Dut2_SN_Edit && !ui->Dut2_SN_Edit->text().isEmpty()) ||
        (focusedWidget == ui->Dut3_SN_Edit && !ui->Dut3_SN_Edit->text().isEmpty()) ||
        (focusedWidget == ui->Dut4_SN_Edit && !ui->Dut4_SN_Edit->text().isEmpty()))
    {
        barcodeEdits[nTreadID]->setFocus();
    }

}

void MainWindow::UpdateUIPortInfo(UINT nTreadID)
{
    PreLoaderPortlable[nTreadID]->setText(QString("P[ %1 ]").arg(Common::getInstance()->getDutPort(nTreadID, PRELOADER_COM)));
    KernelPortlable[nTreadID]->setText(QString("K[ %1 ]").arg(Common::getInstance()->getDutPort(nTreadID, KERNEL_COM)));
    m_settingclass->InitPortListView();
}

void MainWindow::InitTestRate()
{
    float rate = 0.00;

    if(Common::getInstance()->getTestCount(0)==0)
    {
        ui->TestCountMsg_1->setText("0");
        ui->PassRate_1->setText("0.00 %");
    }
    else
    {
        rate = (static_cast<double>(Common::getInstance()->getTestCount(0, PASS))/Common::getInstance()->getTestCount(0)) * 100;
        ui->TestCountMsg_1->setText(QString::number(Common::getInstance()->getTestCount(0)));
        ui->PassRate_1->setText(QString("%1 %").arg(QString::number(rate, 'f', 2)));
    }

    if(Common::getInstance()->getTestCount(1)==0)
    {
        ui->TestCountMsg_2->setText("0");
        ui->PassRate_2->setText("0.00 %");
    }
    else
    {
        rate = (static_cast<double>(Common::getInstance()->getTestCount(1, PASS))/Common::getInstance()->getTestCount(1)) * 100;
        ui->TestCountMsg_2->setText(QString::number(Common::getInstance()->getTestCount(1)));
        ui->PassRate_2->setText(QString("%1 %").arg(QString::number(rate, 'f', 2)));
    }

    if(Common::getInstance()->getTestCount(2)==0)
    {
        ui->TestCountMsg_3->setText("0");
        ui->PassRate_3->setText("0.00 %");
    }
    else
    {
        rate = (static_cast<double>(Common::getInstance()->getTestCount(2, PASS))/Common::getInstance()->getTestCount(2)) * 100;
        ui->TestCountMsg_3->setText(QString::number(Common::getInstance()->getTestCount(2)));
        ui->PassRate_3->setText(QString("%1 %").arg(QString::number(rate, 'f', 2)));
    }

    if(Common::getInstance()->getTestCount(3)==0)
    {
        ui->TestCountMsg_4->setText("0");
        ui->PassRate_4->setText("0.00 %");
    }
    else
    {
        rate = (static_cast<double>(Common::getInstance()->getTestCount(3, PASS))/Common::getInstance()->getTestCount(3)) * 100;
        ui->TestCountMsg_4->setText(QString::number(Common::getInstance()->getTestCount(3)));
        ui->PassRate_4->setText(QString("%1 %").arg(QString::number(rate, 'f', 2)));
    }
}

void MainWindow::UpdateTestRate(UINT nTreadID, TestResult_Status Status)
{
    QLineEdit *TestRate, *TestCount;
    switch(nTreadID)
    {
    case 0:
        TestCount = ui->TestCountMsg_1;
        TestRate = ui->PassRate_1;
        break;
    case 1:
        TestCount = ui->TestCountMsg_2;
        TestRate = ui->PassRate_2;
        break;
    case 2:
        TestCount = ui->TestCountMsg_3;
        TestRate = ui->PassRate_3;
        break;
    case 3:
        TestCount = ui->TestCountMsg_4;
        TestRate = ui->PassRate_4;
        break;
    default:
        return;
    }

    switch (Status)
    {
    case FAIL:
        Common::getInstance()->setTestCount(nTreadID, FAIL);
        break;

    case PASS:
        Common::getInstance()->setTestCount(nTreadID, PASS);
        break;

    default:
        //Do nothing
        break;
    }

    if(Common::getInstance()->getTestCount(nTreadID) == 0)
    {
        TestCount->setText("0");
        TestRate->setText("0.00 %");
    }
    else
    {
        float rate = (static_cast<double>(Common::getInstance()->getTestCount(nTreadID, PASS))/Common::getInstance()->getTestCount(nTreadID)) * 100;
        TestCount->setText(QString::number(Common::getInstance()->getTestCount(nTreadID)));
        TestRate->setText(QString("%1 %").arg(QString::number(rate, 'f', 2)));
    }

    m_time_label_timer[nTreadID]->stop();
    CheckCtrlBoxStatus(nTreadID);
    SetTimeToLog(nTreadID);
}

void MainWindow::ResetTestTime()
{
    ui->TestTimeMsg_1->setText("0:00");
    ui->TestTimeMsg_2->setText("0:00");
    ui->TestTimeMsg_3->setText("0:00");
    ui->TestTimeMsg_4->setText("0:00");
}

void MainWindow::slot_UpdateTestTime1()
{
    QString zero = "";
    int seconds = 0;

    seconds = m_start_clock[0].elapsed() / 1000;
    zero = (10 > (seconds % 60)) ? "0" : "";
    ui->TestTimeMsg_1->setText(QString::number(seconds / 60) + ":" + zero + QString::number(seconds % 60));
}

void MainWindow::slot_UpdateTestTime2()
{
    QString zero = "";
    int seconds = 0;

    seconds = m_start_clock[1].elapsed() / 1000;
    zero = (10 > (seconds % 60)) ? "0" : "";
    ui->TestTimeMsg_2->setText(QString::number(seconds / 60) + ":" + zero + QString::number(seconds % 60));
}

void MainWindow::slot_UpdateTestTime3()
{
    QString zero = "";
    int seconds = 0;

    seconds = m_start_clock[2].elapsed() / 1000;
    zero = (10 > (seconds % 60)) ? "0" : "";
    ui->TestTimeMsg_3->setText(QString::number(seconds / 60) + ":" + zero + QString::number(seconds % 60));
}

void MainWindow::slot_UpdateTestTime4()
{
    QString zero = "";
    int seconds = 0;

    seconds = m_start_clock[3].elapsed() / 1000;
    zero = (10 > (seconds % 60)) ? "0" : "";
    ui->TestTimeMsg_4->setText(QString::number(seconds / 60) + ":" + zero + QString::number(seconds % 60));
}

void MainWindow::slot_start_timer(UINT nTreadID)
{
    m_start_clock[nTreadID].restart();
    m_time_label_timer[nTreadID]->start(1000);
}

void MainWindow::SetTimeToLog(UINT nTreadID)
{
    QLineEdit *TestTime;
    switch(nTreadID)
    {
    case 0:
        TestTime = ui->TestTimeMsg_1;
        break;
    case 1:
        TestTime = ui->TestTimeMsg_2;
        break;
    case 2:
        TestTime = ui->TestTimeMsg_3;
        break;
    case 3:
        TestTime = ui->TestTimeMsg_4;
        break;
    default:
        return;
    }

    AppendLogs(nTreadID, QString("[Main]Test time is %1").arg(TestTime->text()));
}

void MainWindow::on_Dut1_Start_Button_clicked()
{
    UpdateStartButton(0);
}

void MainWindow::on_Dut2_Start_Button_clicked()
{
    UpdateStartButton(1);
}


void MainWindow::on_Dut3_Start_Button_clicked()
{
    UpdateStartButton(2);
}


void MainWindow::on_Dut4_Start_Button_clicked()
{
    UpdateStartButton(3);
}

void MainWindow::onDut1EditReturnPressed()
{
    UpdateStartButton(0);
}

void MainWindow::onDut2EditReturnPressed()
{
    UpdateStartButton(1);
}

void MainWindow::onDut3EditReturnPressed()
{
    UpdateStartButton(2);
}

void MainWindow::onDut4EditReturnPressed()
{
    UpdateStartButton(3);
}

void MainWindow::on_Log_Button_1_clicked()
{
    qDebug() << "[user][event] ShowLog1 button clicked";
    m_showlogdialog->setModal(true);
    m_showlogdialog->InitLogText(ui->Log_textEdit_1->toHtml());
    m_showlogdialog->exec();
}


void MainWindow::on_Log_Button_2_clicked()
{
    qDebug() << "[user][event] ShowLog2 button clicked";
    m_showlogdialog->setModal(true);
    m_showlogdialog->InitLogText(ui->Log_textEdit_2->toHtml());
    m_showlogdialog->exec();
}


void MainWindow::on_Log_Button_3_clicked()
{
    qDebug() << "[user][event] ShowLog3 button clicked";
    m_showlogdialog->setModal(true);
    m_showlogdialog->InitLogText(ui->Log_textEdit_3->toHtml());
    m_showlogdialog->exec();
}


void MainWindow::on_Log_Button_4_clicked()
{
    qDebug() << "[user][event] ShowLog4 button clicked";
    m_showlogdialog->setModal(true);
    m_showlogdialog->InitLogText(ui->Log_textEdit_4->toHtml());
    m_showlogdialog->exec();
}

void MainWindow::SetLogFilePath(UINT nTreadID)
{
    SYSTEMTIME time;
    char FolderPath[1024] = {0};
    char m_LogPath[1024] = {0};
    LogFilePath[nTreadID] = "";

    ::GetCurrentDirectory(1024, FolderPath);

    sprintf_s(m_LogPath, "%s\\log\\%d", FolderPath, nTreadID+1);

    FileUtils::CheckDirectory(Common::getInstance()->ConvertStdString(m_LogPath).toStdString(), true);

    ::GetLocalTime(&time);
    sprintf_s(m_LogPath, "%s\\%04u-%02u-%02u-%02u-%02u-%02u.txt",
              m_LogPath, time.wYear, time.wMonth, time.wDay, time.wHour, time.wMinute, time.wSecond);

    LogFilePath[nTreadID] = QString(m_LogPath);

}

void MainWindow::WriteLog(UINT nTreadID, QString log)
{
    QFile file(LogFilePath[nTreadID]);
    if (file.open(QIODevice::Append))
    {
        SYSTEMTIME st;
        GetLocalTime(&st);
        char buf[512] = { 0 };
        sprintf_s(buf, 512, "%04d-%02d-%02d %02d:%02d:%02d:%03d ", st.wYear, st.wMonth, st.wDay,
                  st.wHour, st.wMinute, st.wSecond, st.wMilliseconds);
        file.write(Common::getInstance()->ConvertStdString(buf).toUtf8());
        file.write(log.toUtf8());
        file.write("\r\n");
        file.close();
    }
}

void MainWindow::JudgeMesStatus(UINT nTreadID)
{
    RefreshMesType();
    QString Type = Common::getInstance()->getMesType();
    bool Offline = Common::getInstance()->getMesOfflineEnable();
    bool ret = true;
    MesDataUnit_s param;
    MesErrChar errInfo;
    memset(&errInfo, 0, sizeof(errInfo));
    memset(&param, 0, sizeof(param));

    if(Offline)
    {
        this->setWindowTitle(QString(g_strToolVersion).append("("+Type+")(Offline)"));
    }
    else
    {
        this->setWindowTitle(QString(g_strToolVersion).append("("+Type+")(Online)"));

        if(Type == "AGN_MES")
        {
            sprintf_s(param.OrderNo, "%s", Common::getInstance()->getAGNMesOrder().toStdString().c_str());
            sprintf_s(param.StationCode, "%s", Common::getInstance()->getAGNMesStation().toStdString().c_str());
        }

        ret = m_mainwindowcallback->g_pMesProxy[nTreadID]->PreWork(&param, &errInfo);
        if (!ret)
        {
            AppendLogs(nTreadID, "Init Mes Fail", RED);
        }
    }

    TestBotton[nTreadID]->setEnabled(TestBotton[nTreadID]->isEnabled()?ret:TestBotton[nTreadID]->isEnabled());
}

void MainWindow::ShowTooltip(const QModelIndex &index)
{
    QToolTip::showText(QCursor::pos(),index.data().toString());
}

void MainWindow::checkMousePosition()
{
    QPoint globalPos = QCursor::pos();
    QPoint localPos = this->mapFromGlobal(globalPos);
    int width = ui->frame->width()+ui->line->width() + 20;

    if (localPos.x () <= ui->line->width()) {
        if (m_frameVisible)
            return;
        ui->frame->show();
        ui->line->show();
        m_frameVisible = true;
    } else if (m_frameVisible && localPos.x() > width) {
        ui->frame->hide();
        ui->line->hide();
        m_frameVisible = false;
    } else if (m_frameVisible && localPos.x() <= width) {
        ui->frame->show();
        ui->line->show();
    }
}

void MainWindow::on_Mes_btn_clicked()
{
    qDebug() << "[user][event] MesSettings button clicked";
    m_messettingdialog->setModal(true);
    m_messettingdialog->InitSettings();
    m_messettingdialog->exec();

    for(int nTreadID=0; nTreadID < MAX_SUPPORT_COMPORT_NUM; nTreadID++)
        JudgeMesStatus(nTreadID);

    if(Common::getInstance()->getMesOfflineEnable())
        ui->Mes_btn->setIcon(QIcon(":/images/disconnected.png"));
    else
        ui->Mes_btn->setIcon(QIcon(":/images/connected.png"));
}


void MainWindow::on_Home_btn_clicked()
{
    if (ui->stackedWidget->currentIndex() == 1) {
        if (!checkUnsavedChanges()) {
            return;
        }
    }
    ui->stackedWidget->setCurrentIndex(0);

    for(UINT i = 0 ; i < MAX_SUPPORT_COMPORT_NUM; i++)
        CheckCtrlBoxStatus(i);
}


void MainWindow::on_Setting_btn_clicked()
{
    qDebug() << "[user][event] Settings button clicked";

    ui->stackedWidget->setCurrentIndex(1);
    ui->Common_tabWidget->setCurrentIndex(0);
    loadTestCases();

    for(UINT i = 0 ; i < MAX_SUPPORT_COMPORT_NUM; i++)
        CheckCtrlBoxStatus(i, false);
}

/*
 * Setting Dialog
 */

void MainWindow::on_CfgFile_btn_clicked()
{
    QString OptionPath = ABS_PATH_C("history.ini");
    IniItem item(OptionPath, "Common", "CfgFile");

    QString CfgFile_dir = item.GetStringValue();
    QString CfgFile_name = QFileDialog::getOpenFileName(
        this,
        tr("Open Cfg File..."),
        CfgFile_dir,
        tr("Cfg files(*.cfg)|*.cfg"));

    if (CfgFile_name.isEmpty()) {
        return ;
    }

    //LOG("file_name: %s", CfgFile_name.toLocal8Bit().constData());

    //check Legality of file path
    QString error_msg;
    if (CfgFile_name.isEmpty()) {
        ui->CfgFile_lineEdit->setText("");
    } else {
        QString file_name = QDir::toNativeSeparators(CfgFile_name);
        if(FileUtils::validFile(file_name, error_msg))
        {
            ui->CfgFile_lineEdit->setText(file_name);
            Common::getInstance()->setCfgFilePath(file_name);
            m_settingclass->InitSettings();
        }
        else
        {
            QMessageBox::critical(this, "Open Cfg File...", error_msg, QMessageBox::Yes, QMessageBox::Yes);
            ui->CfgFile_lineEdit->setText("");
        }
    }
}


void MainWindow::on_JsonFile_btn_clicked()
{
    QString OptionPath = ABS_PATH_C("history.ini");
    IniItem item(OptionPath, "Common", "JsonFile");

    QString JsonFile_dir = item.GetStringValue();
    QString JsonFile_name = QFileDialog::getOpenFileName(
        this,
        tr("Open json File..."),
        JsonFile_dir,
        tr("json files(*.json)|*.json"));

    if (JsonFile_name.isEmpty()) {
        return ;
    }

    //LOG("file_name: %s", JsonFile_name.toLocal8Bit().constData());

    //check Legality of file path
    QString error_msg;
    if (JsonFile_name.isEmpty()) {
        ui->JsonFile_lineEdit->setText("");
    } else {
        QString file_name = QDir::toNativeSeparators(JsonFile_name);
        if(FileUtils::validFile(file_name, error_msg))
        {
            ui->JsonFile_lineEdit->setText(file_name);
            Common::getInstance()->setJsonFilePath(file_name);
            InitTestItemParams();
        }
        else
        {
            QMessageBox::critical(this, "Open Json File...", error_msg, QMessageBox::Yes, QMessageBox::Yes);
            ui->JsonFile_lineEdit->setText("");
        }
    }
}


void MainWindow::on_LogPath_btn_clicked()
{
    QString folderPath = QFileDialog::getExistingDirectory(nullptr, QObject::tr("Open Directory"),
                                                           QDir::homePath());

    if (folderPath.isEmpty()) {
        ui->LogPath_lineEdit->setText("");
    }
    else
    {
        ui->LogPath_lineEdit->setText(folderPath.replace("/", "\\"));
    }
    Common::getInstance()->setLogPath(ui->LogPath_lineEdit->text());
}


void MainWindow::on_Save_btn_clicked()
{
    m_settingclass->SaveSettings();
    saveTestCases();
    Common::getInstance()->saveCfgFile(Common::getInstance()->getCfgFilePath());
    Common::getInstance()->saveOptionFile();
    m_hasUnsavedChanges = false;
    showstatusbarInfo("Save Success!", GREEN);
    QMessageBox::information(this, "Success ", "Save Success! ", QMessageBox::Yes, QMessageBox::Yes);
    UpdateUIDisplay();
}

void MainWindow::on_Cancel_btn_clicked()
{
    m_settingclass->InitSettings();
    InitTestItemParams();
    ui->stackedWidget->setCurrentIndex(0);
}


void MainWindow::on_m_btnApDbBrowse_clicked()
{
    QString OptionPath;
    IniItem item(OptionPath, "Common", "ApDBPath");

    QString APDB_dir = item.GetStringValue();
    QString APDB_file_name = QFileDialog::getOpenFileName(
        this,
        tr("Open APDB File..."),
        APDB_dir,
        tr("Database files(*.*)|*.*"));

    if (APDB_file_name.isEmpty()) {
        return ;
    }

    //LOG("file_name: %s", APDB_file_name.toLocal8Bit().constData());

    //check Legality of file path
    QString error_msg;
    if (APDB_file_name.isEmpty()) {
        ui->m_edtApDbFile->setText("");
    } else {
        QString file_name = QDir::toNativeSeparators(APDB_file_name);
        if(FileUtils::validFile(file_name, error_msg))
        {
            ui->m_edtApDbFile->setText(file_name);
        }
        else
        {
            QMessageBox::critical(this, "Open ApDB File...", error_msg, QMessageBox::Yes, QMessageBox::Yes);
        }
    }
}


void MainWindow::on_m_btnMdDbBrowse_clicked()
{
    QString OptionPath;
    IniItem item(OptionPath, "Common", "MdDBPath");

    QString MDDB_dir = item.GetStringValue();
    QString MDDB_file_name = QFileDialog::getOpenFileName(
        this,
        tr("Open MDDB File..."),
        MDDB_dir,
        tr("Database files(*.*)|*.*"));

    if (MDDB_file_name.isEmpty()) {
        return ;
    }

    //LOG("file_name: %s", MDDB_file_name.toLocal8Bit().constData());

    //check Legality of file path
    QString error_msg;
    if (MDDB_file_name.isEmpty()) {
        ui->m_edtMdDbFile->setText("");
    } else {
        QString file_name = QDir::toNativeSeparators(MDDB_file_name);
        if(FileUtils::validFile(file_name, error_msg))
        {
            ui->m_edtMdDbFile->setText(file_name);
        }
        else
        {
            QMessageBox::critical(this, "Open MdDB File...", error_msg, QMessageBox::Yes, QMessageBox::Yes);
            ui->m_edtMdDbFile->setText("");
        }
    }
}


void MainWindow::RegTestItem()
{
    TestCaseFactory::registerTestCase<RebootItem>();
    TestCaseFactory::registerTestCase<PowerOffItem>();
    TestCaseFactory::registerTestCase<FactoryResetItem>();
    TestCaseFactory::registerTestCase<CheckCardItem>();
    TestCaseFactory::registerTestCase<CheckCodesItem>();
    TestCaseFactory::registerTestCase<WriteCodesItem>();
    TestCaseFactory::registerTestCase<LoadSnItem>();
    TestCaseFactory::registerTestCase<EnterModeItem>();
    TestCaseFactory::registerTestCase<InputCodeItem>();
    TestCaseFactory::registerTestCase<ReadSwVerItem>();
    TestCaseFactory::registerTestCase<CheckBatLevelItem>();
    TestCaseFactory::registerTestCase<CheckBarcodeFlagItem>();
    TestCaseFactory::registerTestCase<TeeSupportItem>();
    TestCaseFactory::registerTestCase<TeeCheckItem>();
    TestCaseFactory::registerTestCase<InputYDCodesItem>();
    TestCaseFactory::registerTestCase<PowerOffCurrentItem>();
    TestCaseFactory::registerTestCase<PowerOnCurrentItem>();
    TestCaseFactory::registerTestCase<DeepSleepCurrentItem>();
    TestCaseFactory::registerTestCase<ChargeCurrentItem>();
    TestCaseFactory::registerTestCase<BottomCurrentItem>();
}

void MainWindow::InitTestItemCaseUI()
{
    ui->listWidget->setSelectionMode(QAbstractItemView::SingleSelection);
    // 移除拖拽功能
    // ui->listWidget->setDragDropMode(QAbstractItemView::InternalMove);
    ui->listWidget->setContextMenuPolicy(Qt::CustomContextMenu);

    // 保持原有参数面板初始化代码不变
    m_paramPanel = new QWidget;
    ui->scrollArea->setWidget(m_paramPanel);
    ui->scrollArea->setWidgetResizable(true);

    // 连接新信号
    connect(ui->listWidget, &QListWidget::itemSelectionChanged,
        this, &MainWindow::updateParameterPanel);
    connect(ui->listWidget, SIGNAL(customContextMenuRequested(const QPoint&)),
        this, SLOT(showContextMenu(const QPoint&)));
    connect(ui->listWidget, &QListWidget::itemChanged,
        this, &MainWindow::onListItemChanged);
}

void MainWindow::InitTestItemParams()
{
    Common::getInstance()->loadJsonFile(Common::getInstance()->getJsonFilePath());
    loadTestCases();
    setItemInfoParams();
}

void MainWindow::loadTestCases()
{
    qDeleteAll(m_testCases);
    m_testCases.clear();
    ui->listWidget->clear();

    QJsonDocument doc = Common::getInstance()->getItemJsonDoc();
    if (!doc.isEmpty()) {
        QJsonArray testCasesArray = doc.array();
        QSet<QString> addedTypes; // 用于跟踪已添加的测试项类型

        for (int i = 0; i < testCasesArray.size(); ++i) {
            QJsonObject testCaseObj = testCasesArray[i].toObject();
            QString type = testCaseObj["type"].toString();
            
            // 如果该类型已经添加过,则跳过
            if (addedTypes.contains(type)) {
                continue;
            }
            
            QVariantMap params;
            QJsonObject paramsObj = testCaseObj["params"].toObject();
            for (auto it = paramsObj.begin(); it != paramsObj.end(); ++it) {
                params[it.key()] = it.value().toVariant();
            }

            createTestCase(type, params);
            addedTypes.insert(type); // 标记该类型已添加
        }
    }
    else {
        createTestCase("EnterMode");
    }

    if (ui->listWidget->count() > 0) {
        ui->listWidget->setCurrentRow(0);
    }
}

void MainWindow::saveTestCases()
{
    QJsonArray testCasesArray;

    for (int i = 0; i < ui->listWidget->count(); ++i) {
        QListWidgetItem* item = ui->listWidget->item(i);
        QString type = item->data(Qt::UserRole).toString();
        TestCaseBase* testCase = m_testCases.value(item->text());

        if (testCase) {
            QJsonObject testCaseObj;
            testCaseObj["type"] = type;

            // 获取复选框状态
            bool enabled = (item->checkState() == Qt::Checked);
            testCaseObj["enable"] = enabled;

            QVariantMap params = testCase->parameters();
            QJsonObject paramsObj;
            for (auto it = params.begin(); it != params.end(); ++it) {
                paramsObj[it.key()] = QJsonValue::fromVariant(it.value());
            }

            testCaseObj["params"] = paramsObj;
            testCasesArray.append(testCaseObj);
        }
    }

    QJsonDocument doc(testCasesArray);
    QString configPath = Common::getInstance()->getJsonFilePath();
    QFile file(configPath);

    if (file.open(QIODevice::WriteOnly)) {
        file.write(doc.toJson());
        file.close();
    }

    Common::getInstance()->loadJsonFile(configPath);
    setItemInfoParams();
}

void MainWindow::createTestCase(const QString& type, const QVariantMap& params)
{
    TestCaseBase* testCase = TestCaseFactory::createTestCase(type);
    if (!testCase) return;

    if (!params.isEmpty()) {
        testCase->setParameters(params);
    }

    const bool wasBlocked = ui->listWidget->blockSignals(true);

    // 获取测试项的标准顺序
    QStringList standardOrder = Common::getInstance()->getTestItemOrder();
    int insertIndex = standardOrder.indexOf(type);
    if (insertIndex == -1) {
        insertIndex = ui->listWidget->count(); // 如果找不到顺序，添加到末尾
    }

    QListWidgetItem* item = new QListWidgetItem();
    item->setFlags(Qt::ItemIsSelectable | Qt::ItemIsUserCheckable | Qt::ItemIsEnabled);
    item->setCheckState(Common::getInstance()->getItemEnable(type) ? Qt::Checked : Qt::Unchecked);
    item->setText(testCase->displayName());
    item->setData(Qt::UserRole, type);

    m_testCases[testCase->displayName()] = testCase;
    
    // 连接parametersChanged信号，以便在参数变化时提示保存
    connect(testCase, &TestCaseBase::parametersChanged, this, &MainWindow::onParametersChanged);
    
    if (type == "InputCode") {
        InputCodeItem* inputCodeItem = qobject_cast<InputCodeItem*>(testCase);
        if (inputCodeItem) {
            connect(inputCodeItem, &InputCodeItem::xlsxFileSelected, 
                    this, &MainWindow::onXlsxFileSelected);
        }
    }

    // 在指定位置插入项目
    ui->listWidget->insertItem(insertIndex, item);

    ui->listWidget->blockSignals(wasBlocked);
}

void MainWindow::updateParameterPanel()
{
    QLayout* oldLayout = m_paramPanel->layout();
    if (oldLayout) {
        QLayoutItem* item;
        while ((item = oldLayout->takeAt(0)) != nullptr) {
            if (item->widget()) {
                item->widget()->setParent(nullptr);
                item->widget()->deleteLater();
            }
            delete item;
        }
        delete oldLayout;
    }

    QListWidgetItem* currentItem = ui->listWidget->currentItem();
    if (!currentItem) return;

    TestCaseBase* testCase = m_testCases.value(currentItem->text());
    if (!testCase) return;

    QWidget* paramWidget = testCase->createParameterWidget(m_paramPanel);

    QVBoxLayout* layout = new QVBoxLayout(m_paramPanel);
    layout->addWidget(paramWidget);
    layout->addStretch();
}

void MainWindow::showContextMenu(const QPoint& pos)
{
    QMenu menu(this);
    QAction* addAction = menu.addAction("Add Item");
    QAction* removeAction = menu.addAction("Delete Item");

    // 判断是否有选中项
    removeAction->setEnabled(ui->listWidget->currentItem() != nullptr);

    QAction* selectedAction = menu.exec(ui->listWidget->viewport()->mapToGlobal(pos));

    if (selectedAction == addAction) {
        addTestCase();
    }
    else if (selectedAction == removeAction) {
        removeTestCase();
    }
}

void MainWindow::addTestCase()
{
    qDebug() << "[user][event] addTestCase button clicked";
    QStringList availableTypes = TestCaseFactory::availableTestCaseTypes();
    if (availableTypes.isEmpty()) {
        QMessageBox::warning(this, "Warning", "No test case types registered.");
        return;
    }

    m_additemdialog = new AddItemDialog(this);
    m_additemdialog->setModal(true);
    if (m_additemdialog->exec() == QDialog::Accepted) {
        QString selectedType = m_additemdialog->getSelectedType();
        if (!selectedType.isEmpty()) {
            createTestCase(selectedType);
            m_hasUnsavedChanges = true;
            showstatusbarInfo("The parameters have been changed and have not been saved yet", RED);
        }
    }

    delete m_additemdialog;
}

void MainWindow::removeTestCase()
{
    QListWidgetItem* currentItem = ui->listWidget->currentItem();
    if (!currentItem) return;

    TestCaseBase* testCase = m_testCases.take(currentItem->text());
    delete testCase;

    delete ui->listWidget->takeItem(ui->listWidget->row(currentItem));
    updateParameterPanel();
    
    m_hasUnsavedChanges = true;
    showstatusbarInfo("The parameters have been changed and have not been saved yet", RED);
}

void MainWindow::CheckMainUIInputDisplay()
{
    showAllWidgetsInGroupBox(ui->Dut1_CodeInfo_GroupBox);
    showAllWidgetsInGroupBox(ui->Dut2_CodeInfo_GroupBox);
    showAllWidgetsInGroupBox(ui->Dut3_CodeInfo_GroupBox);
    showAllWidgetsInGroupBox(ui->Dut4_CodeInfo_GroupBox);

    LoadXlsxFlag = false;
    LoadMesFlag = false;
    LoadDefaultFlag = false;
    LoadSection = false;
    LoadYnSystem = false;

    QJsonObject jsonObj = Common::getInstance()->getJsonParams(InputCodeItem::staticType());
    bool ItemEnbale = Common::getInstance()->getItemEnable(InputCodeItem::staticType());
    QJsonObject YDjsonObj = Common::getInstance()->getJsonParams(InputYDCodesItem::staticType());
    bool YDItemEnbale = Common::getInstance()->getItemEnable(InputYDCodesItem::staticType());

    if((jsonObj.isEmpty() || !ItemEnbale)
        && (YDjsonObj.isEmpty() || !YDItemEnbale))
    {
        ui->Dut1_CodeInfo_GroupBox->hide();
        ui->Dut2_CodeInfo_GroupBox->hide();
        ui->Dut3_CodeInfo_GroupBox->hide();
        ui->Dut4_CodeInfo_GroupBox->hide();
        return;
    }

    QString CodeFlag = "";
    for(UINT i=0; i< E_UNKNOWN_CODE; i++)
    {
        if(jsonObj[ECodeStr[i]].toBool() && jsonObj[ECodeStr[i]+" From"].toString() == "Input" && ItemEnbale)
            CodeFlag = CodeFlag + "1";
        else
            CodeFlag = CodeFlag + "0";

        if(jsonObj[ECodeStr[i]].toBool() && jsonObj[ECodeStr[i]+" From"].toString() == "XlsxFile" && ItemEnbale)
            LoadXlsxFlag = true;

        if(jsonObj[ECodeStr[i]].toBool() && jsonObj[ECodeStr[i]+" From"].toString() == "Mes" && ItemEnbale)
            LoadMesFlag = true;

        if(jsonObj[ECodeStr[i]].toBool() && jsonObj[ECodeStr[i]+" From"].toString() == "Default" && ItemEnbale)
            LoadDefaultFlag = true;

        if(jsonObj[ECodeStr[i]].toBool() && jsonObj[ECodeStr[i]+" From"].toString() == "Section" && ItemEnbale)
            LoadSection = true;

        if(jsonObj[ECodeStr[i]].toBool() && jsonObj[ECodeStr[i]+" From"].toString() == "YnSystem" && ItemEnbale)
            LoadYnSystem = true;

        if((jsonObj[ECodeStr[i]+" From"].toString() == "XlsxFile" || jsonObj[ECodeStr[i]+" From"].toString() == "Mes") && jsonObj[ECodeStr[i]].toBool() && ItemEnbale)
            CodeFlag.replace(0, 1, "1");
    }

    for(UINT i=0; i< E_UNKNOWN_YDCODE; i++)
    {
        if(YDjsonObj[EYDCodeStr[i]].toBool() && YDjsonObj[EYDCodeStr[i]+" From"].toString() == "Input"  && YDItemEnbale)
            CodeFlag = CodeFlag + "1";
        else
            CodeFlag = CodeFlag + "0";

        if(YDjsonObj[EYDCodeStr[i]].toBool() && YDjsonObj[EYDCodeStr[i]+" From"].toString() == "Mes" && YDItemEnbale)
            LoadMesFlag = true;
    }

    bool okflag;
    int CodeFlagInt = CodeFlag.toInt(&okflag, 2);

    if(CodeFlagInt==0)
    {
        ui->Dut1_CodeInfo_GroupBox->hide();
        ui->Dut2_CodeInfo_GroupBox->hide();
        ui->Dut3_CodeInfo_GroupBox->hide();
        ui->Dut4_CodeInfo_GroupBox->hide();
    }
    else
    {
        for(UINT i=0; i< E_UNKNOWN_CODE; i++)
        {
            bool status = CodeFlag.mid(i,1)=="1";
            switch(i)
            {
                case WRITE_BARCODE:
                    for(int nThread=0; nThread < MAX_SUPPORT_COMPORT_NUM; nThread++)
                    {
                        if(status)
                        {
                            barcodelabel[nThread]->show();
                            barcodeEdits[nThread]->show();
                        }
                        else
                        {
                            barcodelabel[nThread]->hide();
                            barcodeEdits[nThread]->hide();
                        }
                    }
                    break;

                case WRITE_IMEI:
                    for(int nThread=0; nThread < MAX_SUPPORT_COMPORT_NUM; nThread++)
                    {
                        if(status)
                        {
                            imei1label[nThread]->show();
                            imei1Edits[nThread]->show();
                        }
                        else
                        {
                            imei1label[nThread]->hide();
                            imei1Edits[nThread]->hide();
                        }
                    }
                    break;

                case WRITE_IMEI2:
                    for(int nThread=0; nThread < MAX_SUPPORT_COMPORT_NUM; nThread++)
                    {
                        if(status)
                        {
                            imei2label[nThread]->show();
                            imei2Edits[nThread]->show();
                        }
                        else
                        {
                            imei2label[nThread]->hide();
                            imei2Edits[nThread]->hide();
                        }
                    }
                    break;

                case WRITE_MEID:
                    for(int nThread=0; nThread < MAX_SUPPORT_COMPORT_NUM; nThread++)
                    {
                        if(status)
                        {
                            meidlabel[nThread]->show();
                            meidEdits[nThread]->show();
                        }
                        else
                        {
                            meidlabel[nThread]->hide();
                            meidEdits[nThread]->hide();
                        }
                    }
                    break;

                case WRITE_BT:
                    for(int nThread=0; nThread < MAX_SUPPORT_COMPORT_NUM; nThread++)
                    {
                        if(status)
                        {
                            btlabel[nThread]->show();
                            btEdits[nThread]->show();
                        }
                        else
                        {
                            btlabel[nThread]->hide();
                            btEdits[nThread]->hide();
                        }
                    }
                    break;

                case WRITE_WIFI:
                    for(int nThread=0; nThread < MAX_SUPPORT_COMPORT_NUM; nThread++)
                    {
                        if(status)
                        {
                            wifilabel[nThread]->show();
                            wifiEdits[nThread]->show();
                        }
                        else
                        {
                            wifilabel[nThread]->hide();
                            wifiEdits[nThread]->hide();
                        }
                    }
                    break;

                case WRITE_ETHERNET_MAC:
                    for(int nThread=0; nThread < MAX_SUPPORT_COMPORT_NUM; nThread++)
                    {
                        if(status)
                        {
                            EtherMaclabel[nThread]->show();
                            EtherMacEdits[nThread]->show();
                        }
                        else
                        {
                            EtherMaclabel[nThread]->hide();
                            EtherMacEdits[nThread]->hide();
                        }
                    }
                    break;

                case WRITE_SERIALNO:
                   for(int nThread=0; nThread < MAX_SUPPORT_COMPORT_NUM; nThread++)
                   {
                       if(status)
                       {
                           SerialNolabel[nThread]->show();
                           SerialNoEdits[nThread]->show();
                       }
                       else
                       {
                           SerialNolabel[nThread]->hide();
                           SerialNoEdits[nThread]->hide();
                       }
                   }
                   break;

                case WRITE_NETCODE:
                    for(int nThread=0; nThread < MAX_SUPPORT_COMPORT_NUM; nThread++)
                    {
                        if(status)
                        {
                            NetCodelabel[nThread]->show();
                            NetCodeEdits[nThread]->show();
                        }
                        else
                        {
                            NetCodelabel[nThread]->hide();
                            NetCodeEdits[nThread]->hide();
                        }
                    }
                    break;
            }
        }

        for(UINT i=0; i< E_UNKNOWN_YDCODE; i++)
        {
            bool status = CodeFlag.mid(i+E_UNKNOWN_CODE,1)=="1";
            switch(i)
            {
            case WRITE_OBSN:
                for(int nThread=0; nThread < MAX_SUPPORT_COMPORT_NUM; nThread++)
                {
                    if(status)
                    {
                        OBSNlabel[nThread]->show();
                        OBSNEdits[nThread]->show();
                    }
                    else
                    {
                        OBSNlabel[nThread]->hide();
                        OBSNEdits[nThread]->hide();
                    }
                }
                break;

            case WRITE_IWSN:
                for(int nThread=0; nThread < MAX_SUPPORT_COMPORT_NUM; nThread++)
                {
                    if(status)
                    {
                        IWSNlabel[nThread]->show();
                        IWSNEdits[nThread]->show();
                    }
                    else
                    {
                        IWSNlabel[nThread]->hide();
                        IWSNEdits[nThread]->hide();
                    }
                }
                break;

            case WRITE_BATLABEL:
                for(int nThread=0; nThread < MAX_SUPPORT_COMPORT_NUM; nThread++)
                {
                    if(status)
                    {
                        BatLabellabel[nThread]->show();
                        BatLabelEdits[nThread]->show();
                    }
                    else
                    {
                        BatLabellabel[nThread]->hide();
                        BatLabelEdits[nThread]->hide();
                    }
                }
                break;

            case WRITE_SCRILSF:
                for(int nThread=0; nThread < MAX_SUPPORT_COMPORT_NUM; nThread++)
                {
                    if(status)
                    {
                        ScrilSflabel[nThread]->show();
                        ScrilSfEdits[nThread]->show();
                    }
                    else
                    {
                        ScrilSflabel[nThread]->hide();
                        ScrilSfEdits[nThread]->hide();
                    }
                }
                break;
            }
        }
    }

    if(LoadXlsxFlag)
    {
        QString filepath = jsonObj["XlsxFilePath"].toString();
        if (!filepath.isEmpty())
        {
            onXlsxFileSelected(filepath);
        }
    }
}

void MainWindow::showAllWidgetsInGroupBox(QGroupBox* groupBox)
{
    if (!groupBox) return;

    groupBox->show();
    const auto children = groupBox->children();
    for (QObject* child : children) {
        QWidget* widget = qobject_cast<QWidget*>(child);
        if (widget) {
            widget->show();
        }
    }
}

void MainWindow::setItemInfoParams()
{
    QJsonObject jsonObj = Common::getInstance()->getJsonParams(InputCodeItem::staticType());
    if(jsonObj.isEmpty())
    {
        return;
    }

    if(jsonObj["IMEI2"].toBool())
        Common::getInstance()->setIMEINum(2);
}

void MainWindow::showstatusbarInfo(QString Info, LOGCOLOR_TYPE color)
{
    ui->statusbar->showMessage(Info);
    if(color == RED)
    {
        ui->statusbar->setStyleSheet("color:red");
    }
    else if(color == GREEN)
    {
        ui->statusbar->setStyleSheet("color:green");
    }
}

bool MainWindow::CheckScanData(const char *pStrHeader, bool bCheckHeader, const char *pInData, WriteData_Type_e dataType, UINT nThreadID)
{
    if (!pInData)
    {
        return false;
    }

    int dataLength = strlen(pInData);
    char pScanMsg[1024] = { 0 };

    switch (dataType)
    {
    case WRITE_BARCODE:
        if (dataLength > BARCODE_MAX_LENGTH || dataLength <= 0)
        {
            sprintf_s(pScanMsg, "Dut%d : Barcode = \"%s\", length must be [1,%d]", nThreadID+1,pInData, BARCODE_MAX_LENGTH);
            showstatusbarInfo(pScanMsg, RED);
            return false;
        }

        break;

    case WRITE_IMEI:
        if (dataLength != 15 /* && !(dataLength == 16 && m_sATECalibrationFlowCommData.sWriteOption.ImeiFromQRCode)*/)
        {
            sprintf_s(pScanMsg, "Dut%d : %s = \"%s\",Only when enable checksum, imei length can be 14 or 15 only!", nThreadID + 1, m_pstrScanItem, pInData);
            showstatusbarInfo(pScanMsg, RED);
            return false;
        }
        else
        {
            for (int i = 0; i < 15; i++)
            {
                if (pInData[i] > '9' || pInData[i] < '0')
                {
                    sprintf_s(pScanMsg, "Dut%d : %s = \"%s\" isn`t all digit!!", nThreadID + 1, m_pstrScanItem, pInData);
                    showstatusbarInfo(pScanMsg, RED);
                    return false;
                }
            }
        }
        break;

    case WRITE_BT:
    case WRITE_WIFI:
        if (dataLength != 12 /* && !(dataLength == 13 && m_sATECalibrationFlowCommData.sWriteOption.MacFromQRCode)*/)

        {
            sprintf_s(pScanMsg, "Dut%d : %s = \"%s\" length must be 12!", nThreadID + 1, m_pstrScanItem, pInData);
            showstatusbarInfo(pScanMsg, RED);
            return false;
        }

        for (int i = 0; i < 12; i++)
        {
            if (!((pInData[i] <= '9' && pInData[i] >= '0')
                  || (pInData[i] <= 'F' && pInData[i] >= 'A')
                  || (pInData[i] <= 'f' && pInData[i] >= 'a')))
            {
                sprintf_s(pScanMsg, "Dut%d : %s = \"%s\" have some invalid characters!!", nThreadID + 1, m_pstrScanItem, pInData);
                showstatusbarInfo(pScanMsg, RED);
                return false;
            }
        }
        break;

    case WRITE_ETHERNET_MAC:
        if (dataLength != 12)
        {
            sprintf_s(pScanMsg, "Dut%d : %s = \"%s\" length must be 12!", nThreadID + 1, m_pstrScanItem, pInData);
            showstatusbarInfo(pScanMsg, RED);
            return false;
        }
        else
        {
            for (int i = 0; i < dataLength; i++)
            {
                if (!((pInData[i] <= '9' && pInData[i] >= '0')
                      || (pInData[i] <= 'F' && pInData[i] >= 'A')
                      || (pInData[i] <= 'f' && pInData[i] >= 'a')))
                {
                    sprintf_s(pScanMsg, "Dut%d : %s = \"%s\" have some invalid characters!!", nThreadID + 1, m_pstrScanItem, pInData);
                    showstatusbarInfo(pScanMsg, RED);
                    return false;
                }
            }
        }
        break;

    case WRITE_MEID:
        if (dataLength != 14)
        {
            sprintf_s(pScanMsg, "Dut%d : %s = \"%s\" length must be 14!", nThreadID + 1, m_pstrScanItem, pInData);
            showstatusbarInfo(pScanMsg, RED);
            return false;
        }
        else
        {
            for (int i = 0; i < dataLength; i++)
            {
                if (!((pInData[i] <= '9' && pInData[i] >= '0')
                      || (pInData[i] <= 'F' && pInData[i] >= 'A')
                      || (pInData[i] <= 'f' && pInData[i] >= 'a')))
                {
                    sprintf_s(pScanMsg, "Dut%d : %s = \"%s\" have some invalid characters!!", nThreadID + 1, m_pstrScanItem, pInData);
                    showstatusbarInfo(pScanMsg, RED);
                    return false;
                }
            }
        }
        break;

    case WRITE_SERIALNO:
        if (dataLength <= 0 || dataLength > SERIAL_NO_LEN)
        {
            sprintf_s(pScanMsg, "Dut%d : %s = \"%s\" length must not be longer than %d!", nThreadID + 1, m_pstrScanItem, pInData, SERIAL_NO_LEN);
            showstatusbarInfo(pScanMsg, RED);
            return false;
        }
        break;
     
    case WRITE_NETCODE:
        if (dataLength <= 0 || dataLength > NETCODE_LEN || (dataLength != 15 && dataLength != 21))
        {
            sprintf_s(pScanMsg, "Dut%d : %s = \"%s\" length must be 15 or 21 !", nThreadID + 1, m_pstrScanItem, pInData);
            showstatusbarInfo(pScanMsg, RED);
            return false;
        }
        break;

    /*case WRITE_ESN:
        if (dataLength != 8)
        {
            sprintf_s(pScanMsg, "%s = \"%s\" length must be 8!", m_pstrScanItem, pInData);
            AppendLogs(nThreadID, QString::fromUtf8(pScanMsg));
            return false;
        }
        else
        {
            for (int i = 0; i < dataLength; i++)
            {
                if (!((pInData[i] <= '9' && pInData[i] >= '0')
                      || (pInData[i] <= 'F' && pInData[i] >= 'A')
                      || (pInData[i] <= 'f' && pInData[i] >= 'a')))
                {
                    sprintf_s(pScanMsg, "%s = \"%s\" have some invalid characters!!", m_pstrScanItem, pInData);
                    AppendLogs(nThreadID, QString::fromUtf8(pScanMsg));
                    return false;
                }
            }
        }
        break;*/

    default:
        break;
    }

    if (bCheckHeader == true)
    {
        int headLength = 0;
        if (pStrHeader)
        {
            headLength = strlen(pStrHeader);
            if (_strnicmp(pStrHeader, pInData, headLength) == 0)
            {
                showstatusbarInfo("");
                return true;
            }
            else
            {
                sprintf_s(pScanMsg, "%s = \"%s\" previous %d bits not match \"%s\" header string!",
                          m_pstrScanItem, pInData, headLength, pStrHeader);
                showstatusbarInfo(pScanMsg, RED);
                return false;
            }
        }
        else
        {
            return false;
        }
    }

    return true;
}

bool MainWindow::CheckYDScanData(const char *pStrHeader, bool bCheckHeader, const char *pInData, YDData_Type_e dataType, UINT nThreadID)
{
    if (!pInData)
    {
        return false;
    }

    int dataLength = strlen(pInData);
    char pScanMsg[1024] = { 0 };
    QJsonObject jsonObj = Common::getInstance()->getJsonParams(InputYDCodesItem::staticType());
    UINT BatLenLimit = jsonObj["BatLabel Length"].toString().toInt();

    switch (dataType)
    {
    case WRITE_OBSN:
        if (dataLength > BARCODE_MAX_LENGTH || dataLength <= 0 )
        {
            sprintf_s(pScanMsg, "外盒SN = \"%s\" 长度必须为 [1,%d]", pInData, BARCODE_MAX_LENGTH);
            showstatusbarInfo(pScanMsg, RED);
            return false;
        }
        break;

    case WRITE_IWSN:
        if (dataLength > BARCODE_MAX_LENGTH || dataLength <= 0 )
        {
            sprintf_s(pScanMsg, "内仓SN = \"%s\" 长度必须为 [1,%d]", pInData, BARCODE_MAX_LENGTH);
            showstatusbarInfo(pScanMsg, RED);
            return false;
        }
        break;

    case WRITE_BATLABEL:
        if (dataLength != BatLenLimit)
        {
            sprintf_s(pScanMsg, "电池标签 = \"%s\" 长度必须为 %d", pInData, BatLenLimit);
            showstatusbarInfo(pScanMsg, RED);
            return false;
        }

        if (dataLength > BARCODE_MAX_LENGTH || dataLength <= 0 )
        {
            sprintf_s(pScanMsg, "电池标签 = \"%s\" 长度必须为 [1,%d]", pInData, BARCODE_MAX_LENGTH);
            showstatusbarInfo(pScanMsg, RED);
            return false;
        }
        break;

    case WRITE_SCRILSF:
        if (dataLength > SCRILSF_ARRAY_LEN || dataLength <= 0 )
        {
            sprintf_s(pScanMsg, "ScrilSf = \"%s\" length must be [1,%d]", pInData, SCRILSF_LENGTH);
            showstatusbarInfo(pScanMsg, RED);
            return false;
        }
        else
        {
            for (int i = 0; i < dataLength; i++)
            {
                if ( pInData[i] > '9' || pInData[i] < '0')
                {
                    sprintf_s(pScanMsg, "%s = \"%s\" isn`t all digit!!", m_pstrScanItem, pInData);
                    showstatusbarInfo(pScanMsg, RED);
                    return false;
                }
            }
        }
        break;

    default:
        break;
    }

    if (bCheckHeader == true)
    {
        int headLength = 0;
        if (pStrHeader)
        {
            headLength = strlen(pStrHeader);
            if (_strnicmp(pStrHeader, pInData, headLength) == 0)
            {
                showstatusbarInfo("");
                return true;
            }
            else
            {
                sprintf_s(pScanMsg, "%s = \"%s\" previous %d bits not match \"%s\" header string!",
                          m_pstrScanItem, pInData, headLength, pStrHeader);
                showstatusbarInfo(pScanMsg, RED);
                return false;
            }
        }
        else
        {
            return false;
        }
    }

    return true;
}

bool MainWindow::WriteUIChange(UINT nThreadID)
{
    bool bCheckPass = false;
    memset(m_pstrScanItem, 0, sizeof(sizeof(char) * 260));

    QString BARCODE, IMEI1, IMEI2, WIFIADDR, BTADDR, MEID, EthernetMac, SerialNo, NetCode, OBSN, IWSN, BatLabel, ScrilSf;
    BARCODE     = barcodeEdits[nThreadID]->text();
    IMEI1       = imei1Edits[nThreadID]->text();
    IMEI2       = imei2Edits[nThreadID]->text();
    WIFIADDR    = wifiEdits[nThreadID]->text();
    BTADDR      = btEdits[nThreadID]->text();
    MEID        = meidEdits[nThreadID]->text();
    EthernetMac = EtherMacEdits[nThreadID]->text();
    SerialNo    = SerialNoEdits[nThreadID]->text();
    NetCode     = NetCodeEdits[nThreadID]->text();
    OBSN        = OBSNEdits[nThreadID]->text();
    IWSN        = IWSNEdits[nThreadID]->text();
    BatLabel    = BatLabelEdits[nThreadID]->text();
    ScrilSf     = ScrilSfEdits[nThreadID]->text();

    ScanData_struct m_sScanData;
    memset(&m_sScanData, 0, sizeof(ScanData_struct));

    if(CodeGroups[nThreadID] && CodeGroups[nThreadID]->isHidden())
        return true;

    if(barcodeEdits[nThreadID] && !barcodeEdits[nThreadID]->isHidden())
    {
        barcodeEdits[nThreadID]->setFocus();
        if (BARCODE.isEmpty())
        {   
            return false;
        }
        else
        {
            BARCODE = removeSpecialCharacters(BARCODE);
            memcpy(m_sScanData.strBarcode, BARCODE.toStdString().c_str(), BARCODE.length());
            char* strBarcode = m_sScanData.strBarcode;
            for (char* p = strBarcode; *p != '\0'; ++p)
            {
                if (*p == ';')
                {
                    *p = '\0';
                    break;
                }
            }
            Common::getInstance()->setScanData(nThreadID, m_sScanData);
        }
    }

    if (imei1Edits[nThreadID] && !imei1Edits[nThreadID]->isHidden())
    {
        imei1Edits[nThreadID]->setFocus();
        sprintf_s(m_pstrScanItem, sizeof(m_pstrScanItem), "IMEI1");
        bCheckPass = CheckScanData("",false,IMEI1.toUtf8().constData(), WRITE_IMEI, nThreadID);
        if (IMEI1.isEmpty() || !bCheckPass)
        {
            imei1Edits[nThreadID]->clear(); 
            return false;
        }
        else
        {
            IMEI1 = removeSpecialCharacters(IMEI1);
            memcpy(m_sScanData.strIMEI[0], IMEI1.toStdString().c_str(), IMEI1.length());
            Common::getInstance()->setScanData(nThreadID, m_sScanData);
        }
    }
    

    if (imei2Edits[nThreadID] && !imei2Edits[nThreadID]->isHidden())
    {
        imei2Edits[nThreadID]->setFocus();
        sprintf_s(m_pstrScanItem, sizeof(m_pstrScanItem), "IMEI2");
        bCheckPass = CheckScanData("", false, IMEI2.toUtf8().constData(), WRITE_IMEI, nThreadID);
        if (IMEI2.isEmpty() || !bCheckPass)
        {
            imei2Edits[nThreadID]->clear();
            return false;
        }
        else
        {
            IMEI2 = removeSpecialCharacters(IMEI2);
            memcpy(m_sScanData.strIMEI[1], IMEI2.toStdString().c_str(), IMEI2.length());
            Common::getInstance()->setScanData(nThreadID, m_sScanData);
        }
    }
    
    if (meidEdits[nThreadID] && !meidEdits[nThreadID]->isHidden())
    {
        meidEdits[nThreadID]->setFocus();
        sprintf_s(m_pstrScanItem, sizeof(m_pstrScanItem), "MEID");
        bCheckPass = CheckScanData("", false, MEID.toUtf8().constData(), WRITE_MEID, nThreadID);
        if (MEID.isEmpty() || !bCheckPass)
        {
            meidEdits[nThreadID]->clear();
            return false;
        }
        else
        {
            MEID = removeSpecialCharacters(MEID);
            memcpy(m_sScanData.strMeid, MEID.toStdString().c_str(), MEID.length());
            Common::getInstance()->setScanData(nThreadID, m_sScanData);
        }
    }

    if (btEdits[nThreadID] && !btEdits[nThreadID]->isHidden())
    {
        btEdits[nThreadID]->setFocus();
        sprintf_s(m_pstrScanItem, sizeof(m_pstrScanItem), "BT Addr");
        bCheckPass = CheckScanData("", false, BTADDR.toUtf8().constData(), WRITE_BT, nThreadID);
        if (BTADDR.isEmpty() || !bCheckPass)
        {
            btEdits[nThreadID]->clear();
            return false;
        }
        else
        {
            BTADDR = removeSpecialCharacters(BTADDR);
            memcpy(m_sScanData.strBTAddr, BTADDR.toStdString().c_str(), BTADDR.length());
            Common::getInstance()->setScanData(nThreadID, m_sScanData);
        }
    }

    if (wifiEdits[nThreadID] && !wifiEdits[nThreadID]->isHidden())
    {
        wifiEdits[nThreadID]->setFocus();
        sprintf_s(m_pstrScanItem, sizeof(m_pstrScanItem), "WIFI Addr");
        bCheckPass = CheckScanData("", false, WIFIADDR.toUtf8().constData(), WRITE_WIFI, nThreadID);
        if (WIFIADDR.isEmpty() || !bCheckPass)
        {
            wifiEdits[nThreadID]->clear();
            return false;
        }
        else
        {
            WIFIADDR = removeSpecialCharacters(WIFIADDR);
            memcpy(m_sScanData.strWifiAddr, WIFIADDR.toStdString().c_str(), WIFIADDR.length());
            Common::getInstance()->setScanData(nThreadID, m_sScanData);
        }
    }

    if (EtherMacEdits[nThreadID] && !EtherMacEdits[nThreadID]->isHidden())
    {
        EtherMacEdits[nThreadID]->setFocus();
        sprintf_s(m_pstrScanItem, sizeof(m_pstrScanItem), "Ethernet MAC");
        bCheckPass = CheckScanData("", false, EthernetMac.toUtf8().constData(), WRITE_ETHERNET_MAC, nThreadID);
        if (EthernetMac.isEmpty() || !bCheckPass)
        {
            EtherMacEdits[nThreadID]->clear(); 
            return false;
        }
        else
        {
            EthernetMac = removeSpecialCharacters(EthernetMac);
            memcpy(m_sScanData.strEthernetMac, EthernetMac.toStdString().c_str(), EthernetMac.length());
            Common::getInstance()->setScanData(nThreadID, m_sScanData);
        }
    }

    if (SerialNoEdits[nThreadID] && !SerialNoEdits[nThreadID]->isHidden())
    {
        SerialNoEdits[nThreadID]->setFocus();
        sprintf_s(m_pstrScanItem, sizeof(m_pstrScanItem), "Serial No");
        bCheckPass = CheckScanData("", false, SerialNo.toUtf8().constData(), WRITE_SERIALNO, nThreadID);
        if (SerialNo.isEmpty() || !bCheckPass)
        {
            SerialNoEdits[nThreadID]->clear();
            return false;
        }
        else
        {
            SerialNo = removeSpecialCharacters(SerialNo);
            memcpy(m_sScanData.strSerialNo, SerialNo.toStdString().c_str(), SerialNo.length());
            Common::getInstance()->setScanData(nThreadID, m_sScanData);
        }
    }

    if (NetCodeEdits[nThreadID] && !NetCodeEdits[nThreadID]->isHidden())
    {
        NetCodeEdits[nThreadID]->setFocus();
        sprintf_s(m_pstrScanItem, sizeof(m_pstrScanItem), "Net Code");
        bCheckPass = CheckScanData("", false, NetCode.toUtf8().constData(), WRITE_NETCODE, nThreadID);
        if (NetCode.isEmpty() || !bCheckPass)
        {
            NetCodeEdits[nThreadID]->clear();
            return false;
        }
        else
        {
            NetCode = removeSpecialCharacters(NetCode);
            memcpy(m_sScanData.strNetCode, NetCode.toStdString().c_str(), NetCode.length());
            Common::getInstance()->setScanData(nThreadID, m_sScanData);
        }
    }

    if (OBSNEdits[nThreadID] && !OBSNEdits[nThreadID]->isHidden())
    {
        OBSNEdits[nThreadID]->setFocus();
        sprintf_s(m_pstrScanItem, sizeof(m_pstrScanItem), "OBSN");
        bCheckPass = CheckYDScanData("", false, OBSN.toUtf8().constData(), WRITE_OBSN, nThreadID);
        if (OBSN.isEmpty() || !bCheckPass)
        {
            OBSNEdits[nThreadID]->clear();
            return false;
        }
        else
        {
            OBSN = removeSpecialCharacters(OBSN);
            memcpy(m_sScanData.strOBSN, OBSN.toStdString().c_str(), OBSN.length());
            Common::getInstance()->setScanData(nThreadID, m_sScanData);
        }
    }

    if (IWSNEdits[nThreadID] && !IWSNEdits[nThreadID]->isHidden())
    {
        IWSNEdits[nThreadID]->setFocus();
        sprintf_s(m_pstrScanItem, sizeof(m_pstrScanItem), "IWSN");
        bCheckPass = CheckYDScanData("", false, IWSN.toUtf8().constData(), WRITE_IWSN, nThreadID);
        if (IWSN.isEmpty() || !bCheckPass)
        {
            IWSNEdits[nThreadID]->clear();
            return false;
        }
        else
        {
            IWSN = removeSpecialCharacters(IWSN);
            memcpy(m_sScanData.strIWSN, IWSN.toStdString().c_str(), IWSN.length());
            Common::getInstance()->setScanData(nThreadID, m_sScanData);
        }
    }

    if (BatLabelEdits[nThreadID] && !BatLabelEdits[nThreadID]->isHidden())
    {
        QJsonObject jsonObj = Common::getInstance()->getJsonParams(InputYDCodesItem::staticType());
        QString BatlabHeader = jsonObj["BatLabel Prefix"].toString();
        bool BatlabHeaderCheck = jsonObj["BatLabel HeaderCheck"].toBool();

        BatLabelEdits[nThreadID]->setFocus();
        sprintf_s(m_pstrScanItem, sizeof(m_pstrScanItem), "BatLabel");
        bCheckPass = CheckYDScanData(BatlabHeader.toLocal8Bit().constData(), BatlabHeaderCheck, BatLabel.toUtf8().constData(), WRITE_BATLABEL, nThreadID);
        if (BatLabel.isEmpty() || !bCheckPass)
        {
            BatLabelEdits[nThreadID]->clear();
            return false;
        }
        else
        {
            BatLabel = removeSpecialCharacters(BatLabel);
            memcpy(m_sScanData.strBatteryLabel, BatLabel.toStdString().c_str(), BatLabel.length());
            Common::getInstance()->setScanData(nThreadID, m_sScanData);
        }
    }

    if (ScrilSfEdits[nThreadID] && !ScrilSfEdits[nThreadID]->isHidden())
    {
        ScrilSfEdits[nThreadID]->setFocus();
        sprintf_s(m_pstrScanItem, sizeof(m_pstrScanItem), "ScrilSf");
        bCheckPass = CheckYDScanData("", false, ScrilSf.toUtf8().constData(), WRITE_SCRILSF, nThreadID);
        if (ScrilSf.isEmpty() || !bCheckPass)
        {
            ScrilSfEdits[nThreadID]->clear();
            return false;
        }
        else
        {
            ScrilSf = removeSpecialCharacters(ScrilSf);
            memcpy(m_sScanData.strScrilSf, ScrilSf.toStdString().c_str(), ScrilSf.length());
            Common::getInstance()->setScanData(nThreadID, m_sScanData);
        }
    }

    return true;
}

void MainWindow::initCodesXlsx()
{
    QString ErrorInfo;
    if(!m_xlsxInstance->Init(Common::getInstance()->getCodesFilePath(), ErrorInfo))//path Temporarily unknown how to use
        QMessageBox::information(this, "Init Codes Xlsx", ErrorInfo, QMessageBox::Yes, QMessageBox::Yes);
}

void MainWindow::onXlsxFileSelected(const QString& filePath)
{
    qDebug() << "Selected XLSX file:" << filePath;
    
    if (!filePath.isEmpty())
    {
        Common::getInstance()->setCodesFilePath(filePath);
        initCodesXlsx();
    }
}

//Create Input data
void MainWindow::InitScanDataFromXlsx(UINT nThreadID)
{
    QJsonObject jsonObj = Common::getInstance()->getJsonParams(InputCodeItem::staticType());
    if(jsonObj.isEmpty())
    {
        return;
    }

    ScanData_struct m_sScanData;
    m_sScanData = Common::getInstance()->getScanData(nThreadID);
    Codes xlsxcodes = m_xlsxInstance->GetDataByBarcode(m_sScanData.strBarcode);

    for(UINT i=0; i< E_UNKNOWN_CODE; i++)
    {
        if(!jsonObj[ECodeStr[i]].toBool())
            continue;

        if(jsonObj[ECodeStr[i]+" From"].toString() == "XlsxFile")
        {
            switch(i)
            {
            case WRITE_BARCODE:
                break;
            case WRITE_IMEI:
                memcpy(m_sScanData.strIMEI[0], xlsxcodes.szIMEI1.value.toStdString().c_str(),
                       xlsxcodes.szIMEI1.value.length() > IMEI_MAX_LENGTH ? IMEI_MAX_LENGTH : xlsxcodes.szIMEI1.value.length());
                break;
            case WRITE_IMEI2:
                memcpy(m_sScanData.strIMEI[1], xlsxcodes.szIMEI2.value.toStdString().c_str(),
                        xlsxcodes.szIMEI2.value.length() > IMEI_MAX_LENGTH ? IMEI_MAX_LENGTH : xlsxcodes.szIMEI2.value.length());
                break;
            case WRITE_MEID:
                memcpy(m_sScanData.strMeid, xlsxcodes.szMEID.value.toStdString().c_str(),
                       xlsxcodes.szMEID.value.length() > MEID_LENGTH ? MEID_LENGTH : xlsxcodes.szMEID.value.length());
                break;
            case WRITE_BT:
                memcpy(m_sScanData.strBTAddr, xlsxcodes.szBtMac.value.toStdString().c_str(),
                       xlsxcodes.szBtMac.value.length() > BT_ADDRESS_MAX_LENGTH ? BT_ADDRESS_MAX_LENGTH : xlsxcodes.szBtMac.value.length());
                break;
            case WRITE_WIFI:
                memcpy(m_sScanData.strWifiAddr, xlsxcodes.szWifiMac.value.toStdString().c_str(),
                       xlsxcodes.szWifiMac.value.length() > WIFI_MAC_MAX_LENGTH ? WIFI_MAC_MAX_LENGTH : xlsxcodes.szWifiMac.value.length());
                break;
            case WRITE_ETHERNET_MAC:
                memcpy(m_sScanData.strEthernetMac, xlsxcodes.szEthernetMac.value.toStdString().c_str(),
                       xlsxcodes.szEthernetMac.value.length() > ETHERNET_MAC_MAX_LENGTH ? ETHERNET_MAC_MAX_LENGTH : xlsxcodes.szEthernetMac.value.length());
                break;
            case WRITE_SERIALNO:
                memcpy(m_sScanData.strSerialNo, xlsxcodes.szSerialNo.value.toStdString().c_str(),
                       xlsxcodes.szSerialNo.value.length() > SERIAL_NO_LEN ? SERIAL_NO_LEN : xlsxcodes.szSerialNo.value.length());
                break;
            case WRITE_NETCODE:
                memcpy(m_sScanData.strNetCode, xlsxcodes.szNetCode.value.toStdString().c_str(),
                       xlsxcodes.szNetCode.value.length() > NETCODE_LEN ? NETCODE_LEN : xlsxcodes.szNetCode.value.length());
                break;
            }
        }
    }

    Common::getInstance()->setScanData(nThreadID, m_sScanData);
}

bool MainWindow::CheckSNFromMes(UINT nThreadID, QString barcode)
{
    bool ret = false;
    MesDataUnit_s param;
    MesErrChar errInfo;
    memset(&param, 0, sizeof(param));
    memset(&errInfo, 0, sizeof(errInfo));
    QString Type = Common::getInstance()->getMesType();
    if(Type == "AGN_MES")
    {
        sprintf_s(param.OrderNo, "%s", Common::getInstance()->getAGNMesOrder().toStdString().c_str());
        sprintf_s(param.StationCode, "%s", Common::getInstance()->getAGNMesStation().toStdString().c_str());
    }
    else if(Type == "SD_MES")
    {
        sprintf_s(param.OrderNo, "%s", Common::getInstance()->getSDMesOrder().toStdString().c_str());
        sprintf_s(param.StationCode, "%s", Common::getInstance()->getSDMesStation().toStdString().c_str());
        sprintf_s(param.OperatorCode, "%s", Common::getInstance()->getSDMesUserCode().toStdString().c_str());
    }
    else if(Type == "YD_MES")
    {
        _snprintf_s(param.IPAddr, sizeof(param.IPAddr), "%s", Common::getInstance()->getYDMesIP().toStdString().c_str());
        _snprintf_s(param.OrderNo, sizeof(param.OrderNo), "%s", Common::getInstance()->getYDMesOrder().toStdString().c_str());
        _snprintf_s(param.StationCode, sizeof(param.StationCode), "%s", Common::getInstance()->getYDMesStation().toStdString().c_str());
        _snprintf_s(param.ResName, sizeof(param.ResName), "%s", Common::getInstance()->getYDMesResName().toStdString().c_str());
    }

    sprintf_s(param.Sn, "%s", barcode.toLocal8Bit().constData());

    ret = m_mainwindowcallback->g_pMesProxy[nThreadID]->CheckSN(&param, &errInfo);
    QString content = QString::fromLocal8Bit(errInfo, sizeof(errInfo) - 1);
    if(!ret)
    {
        QString status = QString("Dut%1 Error: %2!").arg(QString::number(nThreadID+1)).arg(content);
        showstatusbarInfo(status, RED);
    }

    return ret;
}

bool MainWindow::GetCodesFromMes(UINT nThreadID, QString barcode)
{
    ScanData_struct m_sScanData;
    m_sScanData = Common::getInstance()->getScanData(nThreadID);
    QJsonObject jsonObj = Common::getInstance()->getJsonParams(InputCodeItem::staticType());
    QJsonObject YDjsonObj = Common::getInstance()->getJsonParams(InputYDCodesItem::staticType());

    bool ret = false;
    MesDataUnit_s param;
    MesErrChar errInfo;
    memset(&param, 0, sizeof(param));
    memset(&errInfo, 0, sizeof(errInfo));
    QString Type = Common::getInstance()->getMesType();
    if(Type == "AGN_MES")
    {
        sprintf_s(param.OrderNo, "%s", Common::getInstance()->getAGNMesOrder().toStdString().c_str());
        sprintf_s(param.StationCode, "%s", Common::getInstance()->getAGNMesStation().toStdString().c_str());
        param.QType = 1;
    }
    else if(Type == "SD_MES")
    {
        sprintf_s(param.OrderNo, "%s", Common::getInstance()->getSDMesOrder().toStdString().c_str());
        sprintf_s(param.StationCode, "%s", Common::getInstance()->getSDMesStation().toStdString().c_str());
        sprintf_s(param.OperatorCode, "%s", Common::getInstance()->getSDMesUserCode().toStdString().c_str());

        //Set CodeInfo
        QStringList RelStringList;

        for(UINT i=0; i< E_UNKNOWN_CODE; i++)
        {
            if(!jsonObj[ECodeStr[i]].toBool() || jsonObj[ECodeStr[i]+" From"].toString() != "Mes")
                continue;

            switch(i)
            {
            case WRITE_IMEI:
                RelStringList << "IMEI";
                break;
            case WRITE_IMEI2:
                RelStringList << "IMEI1";
                break;
            case WRITE_MEID:
                RelStringList << "MEID";
                break;
            case WRITE_BT:
                RelStringList << "BMAC";
                break;
            case WRITE_WIFI:
                RelStringList << "WMAC";
                break;
            case WRITE_ETHERNET_MAC:
                RelStringList << "MAC";
                break;
            default:
                break;
            }
        }

        QString RelString = RelStringList.join(",");
        _snprintf_s(param.InParams, MES_SIZE_INFO-1, "%s", RelString.toStdString().c_str());
    }
    else if(Type == "YD_MES")
    {
        _snprintf_s(param.IPAddr, sizeof(param.IPAddr), "%s", Common::getInstance()->getYDMesIP().toStdString().c_str());
        _snprintf_s(param.OrderNo, sizeof(param.OrderNo), "%s", Common::getInstance()->getYDMesOrder().toStdString().c_str());
        _snprintf_s(param.StationCode, sizeof(param.StationCode), "%s", Common::getInstance()->getYDMesStation().toStdString().c_str());
        _snprintf_s(param.ResName, sizeof(param.ResName), "%s", Common::getInstance()->getYDMesResName().toStdString().c_str());
    }

    sprintf_s(param.Sn, "%s", barcode.toLocal8Bit().constData());

    ret = m_mainwindowcallback->g_pMesProxy[nThreadID]->QuerySN(&param, &errInfo);
    QString content = QString::fromLocal8Bit(errInfo, sizeof(errInfo) - 1);
    if(!ret)
    {
        QString status = QString("Dut%1 Error: %2!").arg(QString::number(nThreadID+1)).arg(content);
        showstatusbarInfo(status, RED);
    }
    else
    {
        QString CodeInfos = param.TestItems.LongString;
        if(Type == "AGN_MES")
        {
            QStringList CodeInfoList = CodeInfos.split(",");
            for(auto it = CodeInfoList.begin(); it != CodeInfoList.end(); ++it)
            {
                QStringList CodePair = (*it).split(":");
                if(CodePair.size() == 2)
                {
                    QString CodeType = CodePair[0];
                    QString CodeValue = CodePair[1];
                    qDebug() << "AGN_MES " << CodeType << ":" << CodeValue;
                    if (CodeType == "IMEI1")
                    {
                        if(jsonObj["IMEI1"].toBool() && jsonObj["IMEI1 From"].toString() == "Mes")
                            memcpy(m_sScanData.strIMEI[0], CodeValue.toStdString().c_str(), CodeValue.length() > IMEI_MAX_LENGTH ? IMEI_MAX_LENGTH : CodeValue.length());
                    }
                    else if (CodeType == "IMEI2")
                    {
                        if(jsonObj["IMEI2"].toBool() && jsonObj["IMEI2 From"].toString() == "Mes")
                            memcpy(m_sScanData.strIMEI[1], CodeValue.toStdString().c_str(), CodeValue.length() > IMEI_MAX_LENGTH ? IMEI_MAX_LENGTH : CodeValue.length());
                    }
                    else if (CodeType == "MEID1")
                    {
                        if(jsonObj["MEID"].toBool() && jsonObj["MEID From"].toString() == "Mes")
                            memcpy(m_sScanData.strMeid, CodeValue.toStdString().c_str(), CodeValue.length() > MEID_LENGTH ? MEID_LENGTH : CodeValue.length());
                    }
                    else if (CodeType == "WIFI")
                    {
                        if(jsonObj["WifiMac"].toBool() && jsonObj["WifiMac From"].toString() == "Mes")
                            memcpy(m_sScanData.strWifiAddr, CodeValue.toStdString().c_str(), CodeValue.length() > WIFI_MAC_MAX_LENGTH ? WIFI_MAC_MAX_LENGTH : CodeValue.length());
                    }
                    else if (CodeType == "BT")
                    {
                        if(jsonObj["BtMac"].toBool() && jsonObj["BtMac From"].toString() == "Mes")
                            memcpy(m_sScanData.strBTAddr, CodeValue.toStdString().c_str(), CodeValue.length() > BT_ADDRESS_MAX_LENGTH ? BT_ADDRESS_MAX_LENGTH : CodeValue.length());
                    }
                    else if (CodeType == "netcode")
                    {
                        if(jsonObj["NetCode"].toBool() && jsonObj["NetCode From"].toString() == "Mes")
                            memcpy(m_sScanData.strNetCode, CodeValue.toStdString().c_str(), CodeValue.length() > NETCODE_LEN ? NETCODE_LEN : CodeValue.length());
                    }
                    else if (CodeType == "publicKey")
                    {
                        if(jsonObj["publicKey"].toBool() && jsonObj["publicKey From"].toString() == "Mes")
                            memcpy(m_sScanData.strTLXPublicKey, CodeValue.toStdString().c_str(), CodeValue.length() > TLXPUBLICKEY_LEN ? TLXPUBLICKEY_LEN : CodeValue.length());
                    }
                }
            }
        }
        else if(Type == "SD_MES")
        {
            CodeInfos.left(CodeInfos.indexOf(" \n\r\t"));
            if(CodeInfos.contains("0["))
            {
                QString status = QString("Dut%1 Error: %2!").arg(QString::number(nThreadID+1)).arg(CodeInfos);
                showstatusbarInfo(status, RED);
                return false;
            }
            else
            {
                QStringList CodeInfoList = CodeInfos.split(",");
                for(auto it = CodeInfoList.begin(); it != CodeInfoList.end(); ++it)
                {
                    QStringList CodePair = (*it).split(":");
                    if(CodePair.size() == 2)
                    {
                        QString CodeType = CodePair[0];
                        QString CodeValue = CodePair[1];
                        qDebug() << "SD_MES " << CodeType << ":" << CodeValue;
                        if (CodeType == "IMEI")
                        {
                            if(jsonObj["IMEI1"].toBool() && jsonObj["IMEI1 From"].toString() == "Mes")
                                memcpy(m_sScanData.strIMEI[0], CodeValue.toStdString().c_str(), CodeValue.length() > IMEI_MAX_LENGTH ? IMEI_MAX_LENGTH : CodeValue.length());
                        }
                        else if (CodeType == "IMEI1")
                        {
                            if(jsonObj["IMEI2"].toBool() && jsonObj["IMEI2 From"].toString() == "Mes")
                                memcpy(m_sScanData.strIMEI[1], CodeValue.toStdString().c_str(), CodeValue.length() > IMEI_MAX_LENGTH ? IMEI_MAX_LENGTH : CodeValue.length());
                        }
                        else if (CodeType == "MEID")
                        {
                            if(jsonObj["MEID"].toBool() && jsonObj["MEID From"].toString() == "Mes")
                                memcpy(m_sScanData.strMeid, CodeValue.toStdString().c_str(), CodeValue.length() > MEID_LENGTH ? MEID_LENGTH : CodeValue.length());
                        }
                        else if (CodeType == "WMAC")
                        {
                            if(jsonObj["WifiMac"].toBool() && jsonObj["WifiMac From"].toString() == "Mes")
                                memcpy(m_sScanData.strWifiAddr, CodeValue.toStdString().c_str(), CodeValue.length() > WIFI_MAC_MAX_LENGTH ? WIFI_MAC_MAX_LENGTH : CodeValue.length());
                        }
                        else if (CodeType == "BMAC")
                        {
                            if(jsonObj["BtMac"].toBool() && jsonObj["BtMac From"].toString() == "Mes")
                                memcpy(m_sScanData.strBTAddr, CodeValue.toStdString().c_str(), CodeValue.length() > BT_ADDRESS_MAX_LENGTH ? BT_ADDRESS_MAX_LENGTH : CodeValue.length());
                        }
                        else if (CodeType == "MAC")
                        {
                            if(jsonObj["EthernetMac"].toBool() && jsonObj["EthernetMac From"].toString() == "Mes")
                                memcpy(m_sScanData.strEthernetMac, CodeValue.toStdString().c_str(), CodeValue.length() > ETHERNET_MAC_MAX_LENGTH ? ETHERNET_MAC_MAX_LENGTH : CodeValue.length());
                        }
                    }
                }
            }
        }
        else if(Type == "YD_MES")
        {
            if(jsonObj["SerialNo"].toBool() && jsonObj["SerialNo From"].toString() == "Mes")
                memcpy(m_sScanData.strSerialNo, param.CSSN, strlen(param.CSSN));
            if(jsonObj["BtMac"].toBool() && jsonObj["BtMac From"].toString() == "Mes")
                memcpy(m_sScanData.strBTAddr, param.BT, strlen(param.BT));
            if(jsonObj["WifiMac"].toBool() && jsonObj["WifiMac From"].toString() == "Mes")
                memcpy(m_sScanData.strWifiAddr, param.Wifi, strlen(param.Wifi));
            if(jsonObj["MEID"].toBool() && jsonObj["MEID From"].toString() == "Mes")
                memcpy(m_sScanData.strMeid, param.MEID, strlen(param.MEID));
            if(jsonObj["IMEI1"].toBool() && jsonObj["IMEI1 From"].toString() == "Mes")
                memcpy(m_sScanData.strIMEI[0], param.IMEI1, strlen(param.IMEI1));
            if(jsonObj["IMEI2"].toBool() && jsonObj["IMEI2 From"].toString() == "Mes")
                memcpy(m_sScanData.strIMEI[1], param.IMEI2, strlen(param.IMEI2));
            if(jsonObj["NetCode"].toBool() && jsonObj["NetCode From"].toString() == "Mes")
                memcpy(m_sScanData.strNetCode, param.NetCode, strlen(param.NetCode));
            if(YDjsonObj["Scril.Sf"].toBool() && YDjsonObj["Scril.Sf From"].toString() == "Mes")
                memcpy(m_sScanData.strScrilSf, param.CSSN, strlen(param.CSSN));
        }

        Common::getInstance()->setScanData(nThreadID, m_sScanData);
    }

    return ret;
}

bool MainWindow::UpdateResultToMes(UINT nThreadID, TestResult_Status Status, QString barcode, UINT TestTime)
{
    bool ret = false;
    MesDataUnit_s param;
    MesErrChar errInfo;
    memset(&param, 0, sizeof(param));
    memset(&errInfo, 0, sizeof(errInfo));
    QString Type = Common::getInstance()->getMesType();

    param.TestFlag = Status==PASS?MES_PASS:MES_FAIL;

    if(Type == "AGN_MES")
    {
        sprintf_s(param.OrderNo, "%s", Common::getInstance()->getAGNMesOrder().toStdString().c_str());
        sprintf_s(param.StationCode, "%s", Common::getInstance()->getAGNMesStation().toStdString().c_str());
        sprintf_s(param.OperatorCode, "%s", Common::getInstance()->getAGNMesUserCode().toStdString().c_str());
        param.ElapsedTime = TestTime;
    }
    else if(Type == "SD_MES")
    {
        sprintf_s(param.OrderNo, "%s", Common::getInstance()->getSDMesOrder().toStdString().c_str());
        sprintf_s(param.StationCode, "%s", Common::getInstance()->getSDMesStation().toStdString().c_str());
        sprintf_s(param.OperatorCode, "%s", Common::getInstance()->getSDMesUserCode().toStdString().c_str());
    }
    else if(Type == "YD_MES")
    {
        _snprintf_s(param.IPAddr, MES_SIZE_BASE, MES_SIZE_BASE-1, "%s", Common::getInstance()->getYDMesIP().toStdString().c_str());
        _snprintf_s(param.OrderNo, MES_SIZE_BASE, MES_SIZE_BASE-1, "%s", Common::getInstance()->getYDMesOrder().toStdString().c_str());
        _snprintf_s(param.StationCode, MES_SIZE_BASE, MES_SIZE_BASE-1, "%s", Common::getInstance()->getYDMesStation().toStdString().c_str());
        _snprintf_s(param.ResName, MES_SIZE_BASE, MES_SIZE_BASE-1, "%s", Common::getInstance()->getYDMesResName().toStdString().c_str());
        _snprintf_s(param.BatLabel, MES_SIZE_BASE, MES_SIZE_BASE-1, "%s", Common::getInstance()->getScanData(nThreadID).strBatteryLabel);
    }

    char SN[64] = {0};
    _snprintf_s(SN, sizeof(SN), sizeof(SN)-1, barcode.toStdString().c_str());
    char * p = strchr(SN, ' ');
    if (p)
    {
        *p = 0;
    }
    _snprintf_s(param.Sn, MES_SIZE_BASE, MES_SIZE_BASE-1, "%s", SN);

    ret = m_mainwindowcallback->g_pMesProxy[nThreadID]->MarkWork(&param, &errInfo);
    QString content = QString::fromLocal8Bit(errInfo, sizeof(errInfo) - 1);
    if(!ret)
    {
        AppendLogs(nThreadID, content, RED);
    }

    return ret;
}

void MainWindow::onParametersChanged()
{
    m_hasUnsavedChanges = true;
    showstatusbarInfo("The parameters have been changed and have not been saved yet", RED);
}

bool MainWindow::checkUnsavedChanges()
{
    if (m_hasUnsavedChanges) {
        QMessageBox::StandardButton reply = QMessageBox::question(this,
                                                                "UnSaved Changes",
                                                                "The parameters have been changed but have not been saved yet. Do you want to save them now ?",
                                                                 QMessageBox::Yes | QMessageBox::No | QMessageBox::Cancel);
        if (reply == QMessageBox::Yes) {
            on_Save_btn_clicked();
            return true;
        } else if (reply == QMessageBox::Cancel) {
            return false;
        }
    }
    return true;
}

void MainWindow::onListItemChanged(QListWidgetItem* item)
{
    if (item) {
        m_hasUnsavedChanges = true;
        showstatusbarInfo("The parameters have been changed and have not been saved yet", RED);
    }
}

void MainWindow::RefreshMesType()
{
    for(int nThread=0; nThread < MAX_SUPPORT_COMPORT_NUM; nThread++)
    {
        if (m_mainwindowcallback->g_pMesProxy[nThread] != NULL)
        {
            delete m_mainwindowcallback->g_pMesProxy[nThread];
            m_mainwindowcallback->g_pMesProxy[nThread] = NULL;
        }

        m_mainwindowcallback->g_pMesProxy[nThread] = MesFactory::GetMesProxy();
    }
}

void MainWindow::CheckMouseFocus(UINT nTreadID)
{
    BringWindowToTop();
    for(int i = nTreadID+1; i < MAX_SUPPORT_COMPORT_NUM; i++)
    {
        if(settingflag[i] && Common::getInstance()->getActiveEnable(i))
        {
            WriteUIChange(i);
            return;
        }
    }

    if(settingflag[nTreadID] && Common::getInstance()->getActiveEnable(nTreadID))
    {
        WriteUIChange(nTreadID);
        return;
    }
}

void MainWindow::bringToFront()
{
    show();
    raise();
    activateWindow();
}

void MainWindow::BringWindowToTop()
{
    QList<QWidget*> topLevels = QApplication::topLevelWidgets();
    for (QWidget* widget : topLevels) {
        if (widget->windowTitle() == this->windowTitle()) {
            qobject_cast<MainWindow*>(widget)->bringToFront();
            break;
        }
    }
}

void MainWindow::InitRelayPort(UINT nTreadID)
{
    if(Common::getInstance()->getUseRelayEnable() && Common::getInstance()->getDutPort(nTreadID, CONTROLBOX_COM) !=0)
    {
        for(int k=0; k < nTreadID; k++)
        {
            if(Common::getInstance()->getDutPort(k, CONTROLBOX_COM) == Common::getInstance()->getDutPort(nTreadID, CONTROLBOX_COM))
            {
                QTableWidget *ItemTableWidget;
                QString ItemStatus;

                switch(k)
                {
                case 0:
                    ItemTableWidget = ui->Dut1_tableWidget;
                    break;
                case 1:
                    ItemTableWidget = ui->Dut2_tableWidget;
                    break;
                case 2:
                    ItemTableWidget = ui->Dut3_tableWidget;
                    break;
                case 3:
                    ItemTableWidget = ui->Dut4_tableWidget;
                    break;
                default:
                    return;
                }

                ItemStatus = ItemTableWidget->item(0,1)->text();//取出字符串
                if(ItemStatus == "PASS")
                {
                    UpdateTestListItem(nTreadID, "Init TestBox Port", PASS);
                    TestBotton[nTreadID]->setEnabled(true);
                }
                else
                {
                    UpdateTestListItem(nTreadID, "Init TestBox Port", FAIL);
                    TestBotton[nTreadID]->setEnabled(false);
                }

                m_mainwindowcallback->m_pTestBoxInstance[nTreadID] = m_mainwindowcallback->m_pTestBoxInstance[k];
                Common::getInstance()->setneedCtrBox(nTreadID, k);
                Common::getInstance()->setCtrBoxFlag(nTreadID, true);
                qDebug() << __FUNCTION__ << ":" << nTreadID;
                return;
            }
        }

        if(!m_mainwindowcallback->m_pTestBoxInstance[nTreadID]->Init(Common::getInstance()->getDutPort(nTreadID, CONTROLBOX_COM), QSerialPort::BaudRate::Baud9600))
        {
            UpdateTestListItem(nTreadID, "Init TestBox Port", FAIL);
            TestBotton[nTreadID]->setEnabled(false);
        }
        else
        {
            UpdateTestListItem(nTreadID, "Init TestBox Port", PASS);
            TestBotton[nTreadID]->setEnabled(true);
            Common::getInstance()->setneedCtrBox(nTreadID, nTreadID);
            Common::getInstance()->setCtrBoxFlag(nTreadID, true);
            return;
        }
    }
    else if(Common::getInstance()->getDutPort(nTreadID, CONTROLBOX_COM) ==0)
    {
        TestBotton[nTreadID]->setEnabled(false);
        Common::getInstance()->setCtrBoxFlag(nTreadID, false);
    }
    else
    {
        TestBotton[nTreadID]->setEnabled(true);
    }

    if(Common::getInstance()->getDutPort(nTreadID, CONTROLBOX_COM) !=0)
        m_mainwindowcallback->m_pTestBoxInstance[nTreadID]->DeInit();

    Common::getInstance()->setneedCtrBox(nTreadID, -1);
    Common::getInstance()->setCtrBoxFlag(nTreadID, false);
    return;
}

void MainWindow::InitScanGunPort(UINT nTreadID)
{
    if(Common::getInstance()->getUseScanGunEnable() && Common::getInstance()->getDutPort(nTreadID, SCANGUN_COM) !=0)
    {
        for(int k=0; k < nTreadID; k++)
        {
            if(Common::getInstance()->getDutPort(k, CONTROLBOX_COM) == Common::getInstance()->getDutPort(nTreadID, CONTROLBOX_COM))
            {
                UpdateTestListItem(nTreadID, "Init ScanGun Port", FAIL);
                TestBotton[nTreadID]->setEnabled(false);
            }
        }

        if(!m_mainwindowcallback->m_pScanGunInstance[nTreadID]->Init(Common::getInstance()->getDutPort(nTreadID, SCANGUN_COM), QSerialPort::BaudRate::Baud115200))
        {
            UpdateTestListItem(nTreadID, "Init ScanGun Port", FAIL);
            TestBotton[nTreadID]->setEnabled(false);
        }
        else
        {
            UpdateTestListItem(nTreadID, "Init ScanGun Port", PASS);
            TestBotton[nTreadID]->setEnabled(true);
            Common::getInstance()->setneedScanGun(nTreadID, nTreadID);
            Common::getInstance()->setScanGunFlag(nTreadID, true);
            return;
        }
    }
    else if(Common::getInstance()->getUseScanGunEnable() && Common::getInstance()->getDutPort(nTreadID, SCANGUN_COM) ==0)
    {
        Common::getInstance()->setScanGunFlag(nTreadID, false);
        TestBotton[nTreadID]->setEnabled(false);
    }
    else
    {
        TestBotton[nTreadID]->setEnabled(true);
    }

    if(Common::getInstance()->getDutPort(nTreadID, SCANGUN_COM) !=0)
        m_mainwindowcallback->m_pScanGunInstance[nTreadID]->DeInit();

    Common::getInstance()->setneedScanGun(nTreadID, -1);
    Common::getInstance()->setScanGunFlag(nTreadID, false);
    return;
}

void MainWindow::CheckCtrlBoxStatus(UINT m_nTreadID, bool bEnbale)
{
    timer[m_nTreadID]->stop();
    if(m_mainwindowcallback->timer_id[m_nTreadID] !=-1)
    {
        killTimer(m_mainwindowcallback->timer_id[m_nTreadID]);
        m_mainwindowcallback->timer_id[m_nTreadID] = -1;
    }

    if(Common::getInstance()->getUseRelayEnable() && Common::getInstance()->getCtrBoxFlag(m_nTreadID) && bEnbale)
    {
        m_mainwindowcallback->CheckFixtureStatus(m_nTreadID);
        timer[m_nTreadID]->start(1000);
    }
}

QString MainWindow::stringOfBits(const QString &decimalString)
{
    bool ok;
    int decimalNumber = decimalString.toInt(&ok, 16);
    if (!ok) {
        return "Invalid decimal number";
    }

    QString binary = QString::number(decimalNumber, 2);
    int bitLength = sizeof(int) * decimalString.length();
    while (binary.length() < bitLength) {
        binary = "0" + binary;
    }
    return binary;
}

void MainWindow::Dut1CheckFixtureStatus()
{
    UINT m_nTreadID = 0;

    QString BoxRes = Common::getInstance()->getPortRequest(m_nTreadID);
    if(BoxRes.isEmpty())return;

    QString statusbit = stringOfBits(BoxRes.mid(7,1));

    if(statusbit == "Invalid decimal number")
    {
        return;
    }

    BoxStatus[m_nTreadID] = (statusbit.mid(3-m_nTreadID,1) == "1");

    if(BoxStatus[m_nTreadID] && !Common::getInstance()->getTestBoxLastStatus(m_nTreadID))
    {
        Common::getInstance()->setTestBoxLastStatus(m_nTreadID, BoxStatus[m_nTreadID]);
        timer[m_nTreadID]->stop();
        m_mainwindowcallback->ControlScanGun(m_nTreadID);
    }
    else
    {
        Common::getInstance()->setTestBoxLastStatus(m_nTreadID, BoxStatus[m_nTreadID]);
        return;
    }

    return;
}

void MainWindow::Dut2CheckFixtureStatus()
{
    UINT m_nTreadID = 1;

    QString BoxRes = Common::getInstance()->getPortRequest(m_nTreadID);
    if(BoxRes.isEmpty())return;

    QString statusbit = stringOfBits(BoxRes.mid(7,1));

    if(statusbit == "Invalid decimal number")
    {
        return;
    }

    BoxStatus[m_nTreadID] = (statusbit.mid(3-m_nTreadID,1) == "1");

    if(BoxStatus[m_nTreadID] && !Common::getInstance()->getTestBoxLastStatus(m_nTreadID))
    {
        Common::getInstance()->setTestBoxLastStatus(m_nTreadID, BoxStatus[m_nTreadID]);
        timer[m_nTreadID]->stop();
        m_mainwindowcallback->ControlScanGun(m_nTreadID);
    }
    else
    {
        Common::getInstance()->setTestBoxLastStatus(m_nTreadID, BoxStatus[m_nTreadID]);
        return;
    }

    return;
}

void MainWindow::Dut3CheckFixtureStatus()
{
    UINT m_nTreadID = 2;

    QString BoxRes = Common::getInstance()->getPortRequest(m_nTreadID);
    if(BoxRes.isEmpty())return;

    QString statusbit = stringOfBits(BoxRes.mid(7,1));

    if(statusbit == "Invalid decimal number")
    {
        return;
    }

    BoxStatus[m_nTreadID] = (statusbit.mid(3-m_nTreadID,1) == "1");

    if(BoxStatus[m_nTreadID] && !Common::getInstance()->getTestBoxLastStatus(m_nTreadID))
    {
        Common::getInstance()->setTestBoxLastStatus(m_nTreadID, BoxStatus[m_nTreadID]);
        timer[m_nTreadID]->stop();
        m_mainwindowcallback->ControlScanGun(m_nTreadID);
    }
    else
    {
        Common::getInstance()->setTestBoxLastStatus(m_nTreadID, BoxStatus[m_nTreadID]);
        return;
    }

    return;
}

void MainWindow::Dut4CheckFixtureStatus()
{
    UINT m_nTreadID = 3;

    QString BoxRes = Common::getInstance()->getPortRequest(m_nTreadID);
    if(BoxRes.isEmpty())return;

    QString statusbit = stringOfBits(BoxRes.mid(7,1));

    if(statusbit == "Invalid decimal number")
    {
        return;
    }

    BoxStatus[m_nTreadID] = (statusbit.mid(3-m_nTreadID,1) == "1");

    if(BoxStatus[m_nTreadID] && !Common::getInstance()->getTestBoxLastStatus(m_nTreadID))
    {
        Common::getInstance()->setTestBoxLastStatus(m_nTreadID, BoxStatus[m_nTreadID]);
        timer[m_nTreadID]->stop();
        m_mainwindowcallback->ControlScanGun(m_nTreadID);
    }
    else
    {
        Common::getInstance()->setTestBoxLastStatus(m_nTreadID, BoxStatus[m_nTreadID]);
        return;
    }

    return;
}

void MainWindow::on_UseRelay_checkBox_clicked()
{
    if(!ui->UseRelay_checkBox->isChecked())
        ui->UseScanGun_checkBox->setChecked(false);
}


void MainWindow::on_UseScanGun_checkBox_clicked()
{
    if(ui->UseScanGun_checkBox->isChecked())
        ui->UseRelay_checkBox->setChecked(true);
}

void MainWindow::on_PowerType_comboBox_currentIndexChanged(int index)
{
    ui->Ouput2_groupBox_1->setVisible(index == PowerType::Dual);
    ui->Ouput2_groupBox_2->setVisible(index == PowerType::Dual);
    ui->Ouput2_groupBox_3->setVisible(index == PowerType::Dual);
    ui->Ouput2_groupBox_4->setVisible(index == PowerType::Dual);

    ui->Dut1_Addr_groupBox->setVisible(index != PowerType::BlueBird);
    ui->Dut2_Addr_groupBox->setVisible(index != PowerType::BlueBird);
    ui->Dut3_Addr_groupBox->setVisible(index != PowerType::BlueBird);
    ui->Dut4_Addr_groupBox->setVisible(index != PowerType::BlueBird);
    ui->Dut1_Output1_groupBox->setVisible(index != PowerType::BlueBird);
    ui->Dut2_Output1_groupBox->setVisible(index != PowerType::BlueBird);
    ui->Dut3_Output1_groupBox->setVisible(index != PowerType::BlueBird);
    ui->Dut4_Output1_groupBox->setVisible(index != PowerType::BlueBird);
    ui->Dut1_BLU_groupBox->setVisible(index == PowerType::BlueBird);
    ui->Dut2_BLU_groupBox->setVisible(index == PowerType::BlueBird);
    ui->Dut3_BLU_groupBox->setVisible(index == PowerType::BlueBird);
    ui->Dut4_BLU_groupBox->setVisible(index == PowerType::BlueBird);

    ui->Instrument_Widget->setVisible(index != PowerType::Battery);

    if(index != PowerType::Dual)
    {
        //QMessageBox::information(this, "Title", "Single PowerType Can't Test Charge Current ! ", QMessageBox::Yes, QMessageBox::Yes);
    }
}

void MainWindow::on_UsbControl_comboBox_currentIndexChanged(int index)
{
    if(index == UsbControlType::PowerSupply)
    {
        ui->PowerType_comboBox->setCurrentIndex(PowerType::Dual);
        on_PowerType_comboBox_currentIndexChanged(PowerType::Dual);
    }
}

QString MainWindow::removeSpecialCharacters(const QString& input) {
    QString result = input;
    QRegularExpression regex(R"([\p{C}])");

    result.replace(regex, "");
    return result;
}

void MainWindow::InitScanDataFromDafault(UINT nThreadID)
{
    ScanData_struct m_sScanData;
    m_sScanData = Common::getInstance()->getScanData(nThreadID);
    QJsonObject jsonObj = Common::getInstance()->getJsonParams(InputCodeItem::staticType());

    if(jsonObj["IMEI1"].toBool() && jsonObj["IMEI1 From"].toString() == "Default")
        memcpy(m_sScanData.strIMEI[0], "111111111111119", IMEI_MAX_LENGTH);

    if(jsonObj["IMEI2"].toBool() && jsonObj["IMEI2 From"].toString() == "Default")
        memcpy(m_sScanData.strIMEI[1], "222222222222228", IMEI_MAX_LENGTH);

    if(jsonObj["MEID"].toBool() && jsonObj["MEID From"].toString() == "Default")
        memcpy(m_sScanData.strMeid, "33333333333334", MEID_LENGTH);

    Common::getInstance()->setScanData(nThreadID, m_sScanData);
}

// Convert hex QString to quint64
quint64 MainWindow::hexStringToUInt64(const QString &hexStr)
{
    //Q_ASSERT_X(hexStr.length() == 12, "hexStringToUInt64", "Input must be langer than 12 characters");

    bool ok;
    // Convert to quint64 with base 16
    quint64 value = hexStr.toULongLong(&ok, 16);

    if (!ok || value > 0x0000FFFFFFFFFFFF) {
        qWarning() << "Invalid hex string or overflow detected:" << hexStr;
        return ULLONG_MAX;
    }

    return value;
}

// Convert quint64 back to 12-digit hex QString
QString MainWindow::uInt64ToHexString(quint64 value)
{
    // check if value exceeds 48 bits
    value &= 0x0000FFFFFFFFFFFF;

    // Pad with leading zeros to 12 digits
    QString hexStr = QString("%1").arg(value, 12, 16, QLatin1Char('0'));
    return hexStr.toUpper();
}

// Hex string addition
QString MainWindow::addHexStrings(const QString &a, const QString &b)
{
    quint64 numA = hexStringToUInt64(a);
    quint64 numB = hexStringToUInt64(b);

    if (numA == ULLONG_MAX || numB == ULLONG_MAX)
        return "000000000000";

    // Check for overflow
    const quint64 maxVal = 0x0000FFFFFFFFFFFF;
    if (numA > maxVal - numB) {
        qWarning() << "Addition overflow detected";
        return uInt64ToHexString(maxVal);
    }

    return uInt64ToHexString(numA + numB);
}

// Hex string subtraction
QString MainWindow::subtractHexStrings(const QString &a, const QString &b)
{
    quint64 numA = hexStringToUInt64(a);
    quint64 numB = hexStringToUInt64(b);

    if (numA == ULLONG_MAX || numB == ULLONG_MAX)
        return "000000000000";

    if (numA < numB) {
        qWarning() << "Subtraction underflow detected";
        return uInt64ToHexString(0); // 下溢返回最小值
    }

    return uInt64ToHexString(numA - numB);
}

// Compare two hex strings
int MainWindow::compareHexStrings(const QString &a, const QString &b)
{
    quint64 numA = hexStringToUInt64(a);
    quint64 numB = hexStringToUInt64(b);

    if (numA == ULLONG_MAX || numB == ULLONG_MAX)
        return INT_MIN;

    if (numA < numB) return -1;
    if (numA > numB) return 1;
    return 0;
}

bool MainWindow::InitScanDataFromSection(UINT nThreadID)
{
    ScanData_struct m_sScanData;
    m_sScanData = Common::getInstance()->getScanData(nThreadID);
    QJsonObject jsonObj = Common::getInstance()->getJsonParams(InputCodeItem::staticType());

    if(jsonObj["BtMac"].toBool() && jsonObj["BtMac From"].toString() == "Section")
    {
        QString Prefix = jsonObj["BtMac Prefix"].toString();
        QString NextMac = jsonObj["BtMac SectionNext"].toString();
        QString Start = jsonObj["BtMac SectionStart"].toString();
        QString End = jsonObj["BtMac SectionEnd"].toString();
        QString Step = jsonObj["BtMac SectionStep"].toString();
        QString StartMac = Prefix + Start;
        QString EndMac = Prefix + End;

        if(NextMac.isEmpty() || Prefix.isEmpty() || Start.isEmpty() || End.isEmpty() || Step.isEmpty())
        {
            showstatusbarInfo("BtMac Section parameters are not set correctly!", RED);
            return false;
        }
        if(compareHexStrings(NextMac, EndMac) > 0)
        {
            showstatusbarInfo("BtMac Section Next is larger than End!", RED);
            return false;
        }
        if(compareHexStrings(NextMac, StartMac) < 0)
        {
            showstatusbarInfo("BtMac Section Next is smaller than Start!", RED);
            return false;
        }

        memcpy(m_sScanData.strBTAddr, NextMac.toStdString().c_str(), NextMac.length() > BT_ADDRESS_MAX_LENGTH ? BT_ADDRESS_MAX_LENGTH : NextMac.length());
        NextMac = addHexStrings(NextMac, Step);
        jsonObj["BtMac SectionNext"] = NextMac;
    }

    if(jsonObj["WifiMac"].toBool() && jsonObj["WifiMac From"].toString() == "Section")
    {
        QString Prefix = jsonObj["WifiMac Prefix"].toString();
        QString NextMac = jsonObj["WifiMac SectionNext"].toString();
        QString Start = jsonObj["WifiMac SectionStart"].toString();
        QString End = jsonObj["WifiMac SectionEnd"].toString();
        QString Step = jsonObj["WifiMac SectionStep"].toString();
        QString StartMac = Prefix + Start;
        QString EndMac = Prefix + End;

        if(NextMac.isEmpty() || Prefix.isEmpty() || Start.isEmpty() || End.isEmpty() || Step.isEmpty())
        {
            showstatusbarInfo("WifiMac Section parameters are not set correctly!", RED);
            return false;
        }
        if(compareHexStrings(NextMac, EndMac) > 0)
        {
            showstatusbarInfo("WifiMac Section Next is larger than End!", RED);
            return false;
        }
        if(compareHexStrings(NextMac, StartMac) < 0)
        {
            showstatusbarInfo("WifiMac Section Next is smaller than Start!", RED);
            return false;
        }

        memcpy(m_sScanData.strWifiAddr, NextMac.toStdString().c_str(), NextMac.length() > WIFI_MAC_MAX_LENGTH ? WIFI_MAC_MAX_LENGTH : NextMac.length());
        NextMac = addHexStrings(NextMac, Step);
        jsonObj["WifiMac SectionNext"] = NextMac;
    }

    Common::getInstance()->setJsonParams(InputCodeItem::staticType(), jsonObj);
    Common::getInstance()->setScanData(nThreadID, m_sScanData);
    return true;
}

bool MainWindow::InitScanDataFromYnSystem(UINT nThreadID)
{
    QString BtMac, WifiMac;
    ScanData_struct m_sScanData;
    m_sScanData = Common::getInstance()->getScanData(nThreadID);
    QJsonObject jsonObj = Common::getInstance()->getJsonParams(InputCodeItem::staticType());

    QString YnCode = jsonObj["YNCode"].toString();
    QString SN = m_sScanData.strBarcode;
    if((jsonObj["BtMac"].toBool() && jsonObj["BtMac From"].toString() == "YnSystem")
        || (jsonObj["WifiMac"].toBool() && jsonObj["WifiMac From"].toString() == "YnSystem"))
    {
        if(!m_mainwindowcallback->GetMacFromYnSystem(nThreadID, YnCode, SN, BtMac, WifiMac))
        {
            return false;
        }
        memcpy(m_sScanData.strBTAddr, BtMac.toStdString().c_str(), BtMac.length() > BT_ADDRESS_MAX_LENGTH ? BT_ADDRESS_MAX_LENGTH : BtMac.length());
        memcpy(m_sScanData.strWifiAddr, WifiMac.toStdString().c_str(), WifiMac.length() > WIFI_MAC_MAX_LENGTH ? WIFI_MAC_MAX_LENGTH : WifiMac.length());
    }

    Common::getInstance()->setScanData(nThreadID, m_sScanData);
    return true;
}
