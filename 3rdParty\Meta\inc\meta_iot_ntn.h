/*
 * Copyright (c) 2020, MediaTek Inc. All rights reserved.
 *
 * This software/firmware and related documentation ("MediaTek Software") are
 * protected under relevant copyright laws.
 * The information contained herein is confidential and proprietary to
 * MediaTek Inc. and/or its licensors.
 * Except as otherwise provided in the applicable licensing terms with
 * MediaTek Inc. and/or its licensors, any reproduction, modification, use or
 * disclosure of MediaTek Software, and information contained herein, in whole
 * or in part, shall be strictly prohibited.
*/
/*****************************************************************************/
/*!
 * \file meta_iot_ntn.h
 * \mainpage Mobile Equipment Testing Architecture Development Kit
 * \author MediaTek Inc.
 ******************************************
 * \defgroup META_Result META_RESULT
 * META_RESULT is an enumeration type, which is defined as following. If an exported function of META-DLL has returned value, the type of this returned value is always META_RESULT.
 ******************************************
 * \defgroup IOTNTN IOTNTN
 * This section describes the functions used in IOTNTN calibration
 *
 * \defgroup IOTNTNStruct Structure of IOTNTN functions
 * \ingroup IOTNTN
 * IOTNTNStruct is a subgroup of IOTNTN
 */
/*==============================================================================*/
#ifndef _META_IOTNTN_H_
#define _META_IOTNTN_H_


#ifdef __cplusplus
extern "C"
{
#endif // #ifdef __cplusplus

/** \ingroup IOTNTNStruct */
#define IRF_MAX_RX_MEAS_COUNT                   10  /**< Maximum number of Rx measurments done over IRF_TEST_CMD_MIX_RX_START request */
/** \ingroup IOTNTNStruct */
#define IRF_MAX_FHC_CAL_FREQ_NUM                16  /**< Defines maximum number of frequency compensation points per band */
/** \ingroup IOTNTNStruct */
#define IRF_MAX_FHC_TX_CENTER_PA_LEVEL_NUM      10  /**< the IoT-NTN TX PA level number */
/** \ingroup IOTNTNStruct */
#define IRF_MAX_FHC_TEMP_SECTION_NUM            8  /**< the IoT-NTN temperature section number */
/** \ingroup IOTNTNStruct */
#define IRF_MAX_FHC_RX_SEQ_NUM                  9  /**< the maximum number of rx used count */
/** \ingroup IOTNTNStruct */
#define IRF_MAX_TX_PA_MODE_NUM                  3  /**< Defines maximum number of PA gain modes supported */
/** \ingroup IOTNTNStruct */
#define IRF_MAX_FHC_CAL_SUPPORTED_BANDS         40 /**< Defines maximum number of bands that can be calibrated by FHC sequence */
/** \ingroup IOTNTNStruct */
#define IRF_MAX_RX_ANT_NUM                      2  /**< Defines the number of IoT-NTN antenna path */
/** \ingroup IOTNTNStruct */
#define IRF_MAX_TAS_ELEMENT_NUM                 100  /**< Defines maximum number of TAS element */
/** \ingroup IOTNTNStruct */
#define IOT_NTN_MAX_BAND_NUM                    256  /**< Defines maximum number of IoT-NTN band */
/** \ingroup IOTNTNStruct */
#define IRF_MAX_CAL_DATA_NUM                 50  /**< the maximum read/write calibration data number */
/** \ingroup IOTNTNStruct */
#define IL1TST_MAX_PEER_BUF_CNF_BYTE_SIZE    61440  /**< the maximum memory size for IoT-NTN command transmission (ILM: peer buffer: 60 kB ) */
/** \ingroup IOTNTNStruct */
#define IOT_NTN_LNA_MODE_NUM                 8  /**< the maximum read/write calibration data number */
/** \ingroup IOTNTNStruct */
#define IRF_NSFT_MAX_FREQ_SEQ_NUM            50  /**< the maximum NSFT Sequence number */
/** \ingroup IOTNTNStruct */
#define IRF_NSFT_MAX_TX_PWR_LEVEL_NUM        20  /**< the maximum NSFT Tx Power number */
/** \ingroup IOTNTNStruct */
#define IRF_NSFT_MAX_RX_PWR_LEVEL_NUM        5  /**< the maximum NSFT Rx Power number */
/** \ingroup IOTNTNStruct */
#define IRF_NSFT_MAX_TX_TABLE_NUM            2  /**< the maximum NSFT Tx Table number */
/** \ingroup IOTNTNStruct */
#define IOT_NTN_MAX_SCS_NUM                  2  /**< the maximum SCS number */

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] IoT-NTN calibration data
 */
typedef enum
{
    IRF_CALDATA_TPC     = 0,  /**< Tx base */
    IRF_CALDATA_TPC_TAS = 1,  /**< Tx TAS */
    IRF_CALDATA_RXM     = 2,  /**< Rx Main */
    IRF_CALDATA_RXD     = 3,  /**< Rxd */
    IRF_CALDATA_RXM_TAS = 4,  /**< Rxm TAS */
    IRF_CALDATA_RXD_TAS = 5,  /**< Rxd TAS */
    IRF_CALDATA_ITEM_COUNT    /**< Cal data count */
} IRfCalItemType;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] MD generation
 */
typedef enum
{
    IOT_NTN_MODEM_NULL   =  0,           /**< Invalid */
    IOT_NTN_MODEM_V9     =  1,           /**< M90 */
    IOT_NTN_MODEM_END    =  0x7FFFFFFF   /**< unused*/
} IOT_NTN_GEN_VERSION_E;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] Defines the number of PA gain modes supported. Also defines the index and value to be used while referring to each of the PA gain modes
 */
typedef enum
{
    CAL_TX_PA_MODE_LOW   = 0,  /**< PA Low mode */
    CAL_TX_PA_MODE_MID  =  1,  /**< PA Mid mode */
    CAL_TX_PA_MODE_HIGH  = 2,  /**< PA High mode */
    CAL_TX_PA_MODE_COUNT       /**< PA mode count */
} IRfTxPaMode;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] The IoT-NTN TRX frequency info
 */
typedef struct
{
    unsigned int start_freq_khz;    /**< the start of frequency range */
    unsigned int end_freq_khz;      /**< the end of frequency range */
    unsigned int start_earfcn;      /**< the start of earfcn range */
    unsigned int end_earfcn;        /**< the end of earfcn range */
} IL1TST_FREQ_INFO_T ;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] The IoT-NTN TRX band information
 */
typedef struct
{
    unsigned short  band;              /**< IoT-NTN band */
    unsigned short  FE_unit;           /**< the front end unit */
    IL1TST_FREQ_INFO_T tx_freq_info;   /**< the supported tx frequency range */
    IL1TST_FREQ_INFO_T rx_freq_info;   /**< the supported rx frequency range */
    unsigned char rx_div_enable;       /**< enable RXD path*/
} IL1TST_TRX_BAND_INFO_T;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] IOT-NTN RX calibration information
 */
typedef struct
{
    unsigned short  band;                                                  /**< Band */
    unsigned short  rx_seq_num;                                            /**< RX sequence number */
    short           rx_dlpow_default[IRF_MAX_FHC_RX_SEQ_NUM];              /**< default downlink power */
    unsigned char   nv_idx[IRF_MAX_RX_ANT_NUM][IRF_MAX_FHC_RX_SEQ_NUM];    /**< NVRAM position */
    unsigned char   mlna_idx[IRF_MAX_RX_ANT_NUM][IRF_MAX_FHC_RX_SEQ_NUM]; /**< MLNA gain idx */
    unsigned char   ilna_idx[IRF_MAX_RX_ANT_NUM][IRF_MAX_FHC_RX_SEQ_NUM]; /**< ILNA gain idx */
    unsigned char   aelna_idx[IRF_MAX_RX_ANT_NUM][IRF_MAX_FHC_RX_SEQ_NUM];/**< AELNA gain idx */
} IL1TST_RX_CAL_INFO_T;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] The IoT-NTN TX max power information
 */
typedef struct
{
    unsigned short  band;              /**< IoT-NTN band */
    short           max_prf;           /**< maximum TX power (unit: dBm * 256) */
    short           max_prf_offset;    /**< maximum TX power offset (unit: dBm * 256) */
} IL1TST_TX_INFO_MPRF_T;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] The request parameters of IoT-NTN modem information query(Dynamic size database)
 */
typedef struct
{
    unsigned int info_query_bitmap;  /**< supported info category */
} IRfTestCmd_IotNtnDynamicQueryReq;
/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] The currently supported data confirm of IoT-NTN modem information query count.
 */
typedef struct
{
    unsigned int  band_info_cnt;                    /**< IoT-NTN supported band number */
    unsigned int  rx_cal_info_cnt;                  /**< RX calibration information */
    unsigned int  tx_max_prf_info_cnt;              /**< TX max power information */
} IRfTestResult_IotNtnDynamicQuery_Cnt_T;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] The currently supported data confirm of IoT-NTN modem information query(Dynamic size database)
 */
typedef struct
{
    IL1TST_TRX_BAND_INFO_T  *p_band_info;            /**< TX RX band information */
    IL1TST_RX_CAL_INFO_T    *p_rx_cal_info;          /**< RX calibration information */
    IL1TST_TX_INFO_MPRF_T   *p_tx_max_prf_info;      /**< TX max power information */
} IRfTestResult_IotNtnDynamicQueryCnf_DataPtr_T;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] This structure is data structure for Dynamic size database query from modem
 */
typedef struct
{
    unsigned short entry_cnt;  /**< entry count */
    unsigned short mem_offset; /**< memory offset */
} IL1TST_Dynamic_Size_Elm_T;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] IoT-NTN dynamic query info, used for passing database cross dll(Dynamic size database usage)
 */
typedef struct
{
    unsigned int              info_cnf_bitmap;   /**< indicate cnf bitmap */
    IL1TST_Dynamic_Size_Elm_T  band_info_1;      /**< band information */
    IL1TST_Dynamic_Size_Elm_T  rx_cal_info_2;   /**<  rx calibration information */
    IL1TST_Dynamic_Size_Elm_T  tx_max_prf_info_3; /**< TX max power information */
} IRfTestCmd_IotNtnDynamicQueryConfig_CnfParam;

/**
 * \ingroup IOTNTNStruct
 * \details The bit fields description for IoT-NTN calibration capability
 */
typedef struct
{
    unsigned int is_capable: 1;    /**< indicate if this item is supported by SW or HW */
    unsigned int is_mandatory: 1;  /**< indicate if this item is mandatory to be calibrated */
    unsigned int parameter: 30;    /**< reserve bit. */
} IrfCalibrationItem;


/**
 * \ingroup IOTNTNStruct
 * \details The IoT-NTN calibration items
 * \warning DO NOT change the calibration item's orders. New calibration item must be added at the last position.
 */
typedef struct
{
    IrfCalibrationItem generation_version;           /**< [0] IoT-NTN modem generation version,is_capable (0: not supported, 1: supported) */
    IrfCalibrationItem support_max_prf_query;        /**< [1] support the query function for max power information, is_capable (0: not supported, 1: supported) */
} IrfCalibrationItemSet;

/**
 * \ingroup IOTNTNStruct
 * \details This message is in response to the capability queried by the test command
 * IRF_TEST_CMD_GET_RF_CAPABILITY. UE reports back various capability info as
 * supported by the UE HW and SW.
 */
typedef struct
{
    int                    valid;    /**< the response data is valid or not*/
    int                    status;   /**< execution status code ( 0: successful, others: failed ) */
    unsigned int           rfId;     /**< the IoT-NTN RF chip ID */
    IrfCalibrationItemSet  calibrationItems;   /**< the supported calibration items */
} IRFTestResult_RfCapabilityCnf;

/**
 * \ingroup IOTNTNStruct
 * \details This message is used to stop any RF ongoing operations invoked by a IRF test
 * command such as IRF_TEST_CMD_NPUSCH_TX_START, IRF_TEST_CMD_MIX_RX_START etc.
 */
typedef struct
{
    unsigned char    dummy;  /**< Dummy payload. */
} IRfTestCmd_StopReq;

/**
 * \ingroup IOTNTNStruct
 * \details This message is in response to IRF_TEST_CMD_TEST_STOP command,
 * and UE reports back whether it was sucessful or not.
 */
typedef struct
{
    unsigned char    result; /**< Result stating if previous command stopped 0 = false, 1 = true. */
} IRfTestResult_StopCnf;

/**
 * \ingroup IOTNTNStruct
 * \details This message is used to start NPUSCH transmission by UE. The transmission
 * will continue until IRF_TEST_CMD_TEST_STOP command is sent.
 */
typedef struct
{
    unsigned char     ul_numerology;  /**< UL Carrier spacing, Range: 0 = 3.75KHz, 1 = 15KHz. */
    /**
    * <pre>
    * Subcarrier Indication, Range: 0-47 (6 bits), as per 36.213 16.5.1.1.
    * (1) 3.75KHz spacing:
    *      0-47: single tone start at position 0-47
    * (2) 15KHz spacing:
    *      0-11: single tone start at position 0-11
    *     12-15: 3 tones start at position 0, 3, 6, 9
    *     16-17: 6 tones start at position 0, 6
    *        18: 12 tones start at position 0
    * </pre>
    */
    unsigned char     iSc;
    unsigned char     mcs;      /**< Modulation scheme, as per 36.213 16.5.1.2. */
    unsigned char     tx_type;  /**< Previously for configuring the PUSCH transmission type, but it is no longer in use. */
    unsigned short    rnti;     /**< C-RNTI, Range: 0-65523. */
    unsigned short    cell_id;  /**< E-UTRA Physical Cell ID, Range: 0-65535. */
    unsigned int      tx_earfcn;/**< UL Earfcn. */
    char      tx_earfcn_offset; /**< UL Earfcn offset, Format: S6.1. */
    unsigned char     iRu;      /**< Resource assignment field (Iru), Range: 0-7, as per 36.213 16.5.1.1-2. */
    unsigned char     iRep;     /**< Number of repetitions (N_Rep) for NPUSCH. */
    unsigned char     dummy;    /**< Dummy */
    unsigned int      SchPeriod;/**< Scheduled period for UL transition. */
    unsigned char     is_continous; /**< 0: burst mode*/
    unsigned char     tpc_mode; /**< 0/1/2: normal/ fixed power mode/ manual mode */
    unsigned char     pa_idx;   /**< PA gain index */
    unsigned char     tx_tas_state;  /**< Tx Tas state*/
    short             cpl_gain; /**< ddpc gain, S8.5 */
    short             pa_gain;  /**< PA gain, format: S8.5 */
    short             tx_power; /**< Transmit power in dBm, Format: S7.8. */
    unsigned char pa_table_idx; /**< PA VCC table usage  */
    unsigned char enableCsr; /**< 0x0: no sync, 0x1: cell search */
    unsigned int dlEarfcn;/**< downlink earfcn( works in enableCsr = 1 ). */
} IRfTestCmd_PuschTxReq;

/**
 * \ingroup IOTNTNStruct
 * \details This message indicates whether UE is able to successfully start transmission
 * for the previous IRF_TEST_CMD_NPUSCH_TX_START command.
 */
typedef struct
{
    unsigned char    result;  /**< 0 = false, 1 = true. */
} IRfTestResult_PuschTxCnf;

/**
 * \ingroup IOTNTNStruct
 * \details This message is used to start NPRACH transmission by UE. The transmission
 * will continue until IRF_TEST_CMD_TEST_STOP command is sent.
 */
typedef struct
{
    unsigned short    cell_id;  /**< E-UTRA Physical Cell ID, Range: 0-65535. */
    unsigned char     n_sc;     /**< Subcarrier Indication, Range: 0-47 (6 bits), as per as per 36.213 16.5.1.1. */
    unsigned char     tx_type;  /**< Previously for configuring the PRACH transmission type, but it is no longer in use. */
    unsigned int      tx_earfcn;/**< UL Earfcn. */
    char              tx_earfcn_offset;  /**< UL Earfcn offset, Format: S6.1. */
    unsigned char     tpc_mode; /**< 0/1/2: normal/ fixed power mode/ manual mode */
    unsigned char     pa_idx;   /**< PA gain index. */
    unsigned char     tx_tas_state;  /**< Tx tas state. */
    short             tx_power; /**< Transmit power in dBm, Format: S7.8. */
    unsigned char     pa_table_idx; /**< PA VCC table  */
    unsigned char     dummy;    /**< Dummy */
    short             cpl_gain; /**< ddpc gain format, S8.5 */
    short             pa_gain;  /**< PA gain, format: S8.5. */
} IRfTestCmd_PrachTxReq;

/**
 * \ingroup IOTNTNStruct
 * \details This message indicates whether UE is able to successfully start transmission
 * for the previous IRF_TEST_CMD_NPRACH_TX_START command.
 */
typedef struct
{
    unsigned char    result;  /**< 0 = false, 1 = true. */
} IRfTestResult_PrachTxCnf;

/**
 * \ingroup IOTNTNStruct
 * \details This message is used to open the UE receiver for reception. UE will continue
 * to receive until IRF_TEST_CMD_TEST_STOP command is requested. Meanwhile Rx
 * measurements could be obtained via message IRF_TEST_CMD_MIX_RX_GET_RPT.
 */
typedef struct
{
    unsigned short    band;  /**< E-UTRA Band number, band number for which the following report is sent. */
    unsigned char     meas_cnt;  /**< Counts for MIB+SIB1 decoding attempts, Fixed: 1. */
    char      rx_earfcn_offset;  /**< DL Earfcn offset, Format: S6.1. */
    unsigned int      rx_earfcn; /**< DL Earfcn. */
    unsigned short    freq_error;/**< Frequency error in Hz. */
    unsigned char     ant_mask;  /**< Antenna mask, bit0: RXM, bit1: RXD, bit2: RXM & RXD. */
    unsigned char     dummy;     /**< Dummy */
} IRfTestCmd_MixRxReq;

/**
 * \ingroup IOTNTNStruct
 * \details This message indicates whether UE is able to successfully start reception
 * based on previous IRF_TEST_CMD_MIX_RX_START command.
 */
typedef struct
{
    unsigned char     result;  /**< 0 = false, 1 = true. */
} IRfTestResult_MixRxCnf;

/**
 * \ingroup IOTNTNStruct
 * \details This message is used after UE is requested to start reception by the test
 * command IRF_TEST_CMD_MIX_RX_START. This message requests measurement reports
 * obtained by the UE during this reception. The measurement resports will be
 * delivered via IRF_TEST_CNF_MIX_RX_GET_RPT response message.
 */
typedef struct
{
    unsigned char    dummy;  /**< Dummy payload. */
} IRfTestCmd_MixRxRptReq;

/**
 * \ingroup IOTNTNStruct
 * \details This message gives the measurement reports collected by UE during the ongoing
 * reception opened by IRF_TEST_CMD_MIX_RX_START message.
 */
typedef struct
{
    short    rssi[IRF_MAX_RX_ANT_NUM][IRF_MAX_RX_MEAS_COUNT];  /**< Measured RSSI in dBm, Format: S7.8. */
    short    rsrp[IRF_MAX_RX_ANT_NUM][IRF_MAX_RX_MEAS_COUNT];  /**< Measured RSRP in dBm, Format: S8.7. */
    short    rsrq[IRF_MAX_RX_ANT_NUM][IRF_MAX_RX_MEAS_COUNT];  /**< Measured RSRQ. */
    short    snr[IRF_MAX_RX_ANT_NUM][IRF_MAX_RX_MEAS_COUNT];   /**< Measured SNR format S7.8. */
    unsigned int    crcOK_cnt;  /**< CRC Pass count for MIB+SIB1 decoding for given Measurement counts (meas_cnt). */
    unsigned int    crcNG_cnt;  /**< CRC Fail count for MIB+SIB1 decoding for given Measurement counts (meas_cnt). */
    unsigned char     meas_report_count; /**< Measurement counts, from previously sent measurement request. */
} IRfTestResult_MixRxRptCnf;

/**
 * \ingroup IOTNTNStruct
 * \details This message is used to reset measurement reports and to restart measurement
 * as requested in the previous IRF_TEST_CMD_MIX_RX_START message.
 */
typedef struct
{
    unsigned char     dummy;  /**< Dummy payload. */
} IRfTestCmd_MixRxResetReq;

/**
 * \ingroup IOTNTNStruct
 * \details This message is in response to IRF_TEST_CMD_MIX_RX_RESET_CNT request. UE
 * states whether it was able to succesfully reset the measurement reports.
 */
typedef struct
{
    unsigned char    result;  /**< 0 = false, 1 = true. */
} IRfTestResult_MixRxResetCnf;

/**
 * \ingroup IOTNTNStruct
 * \details TX setting of one PA section
 */
typedef struct
{
    unsigned char paMode;      /**< PA mode ( 0: high, 1: middle, 2: low ) */
    unsigned char dummy[3];    /**< dummy */
    short prf;                 /**< the power of PA 8 levels ( unit: dBm ) */
    unsigned char dc2dcLevel0;  /**< PA voltage ( unit: mV ) */
    unsigned char dc2dcLevel1;  /**< PA voltage ( unit: mV ) */
} IotNtnTestPaSection;

/**
 * \ingroup IOTNTNStruct
 * \details TX calibration data
 */
typedef struct
{
    unsigned char paSectionNum;   /**< number of PA sections */
    unsigned char dummy[3];       /**< dummy */
    IotNtnTestPaSection paSection[IRF_MAX_FHC_TX_CENTER_PA_LEVEL_NUM]; /**< PA section array, index is from 0 to 9, TX power and PA mode order is from low to high */
    char temperature[IRF_MAX_FHC_TEMP_SECTION_NUM];       /**< temperature */
    unsigned int frequency[IRF_MAX_FHC_CAL_FREQ_NUM]; /**< frequency,uint 1khz */
    short paGainComp[IRF_MAX_FHC_TX_CENTER_PA_LEVEL_NUM][IRF_MAX_FHC_TEMP_SECTION_NUM][IRF_MAX_FHC_CAL_FREQ_NUM]; /**< PA gain compensation, indexed by [PA section:10][temperature:8][channel:16] */
    short paGainSubTable0[IRF_MAX_FHC_TX_CENTER_PA_LEVEL_NUM][IRF_MAX_FHC_CAL_FREQ_NUM]; /**< PA gain, indexed by [PA section:10][channel:16] */
    short paGainSubTable1[IRF_MAX_FHC_TX_CENTER_PA_LEVEL_NUM][IRF_MAX_FHC_CAL_FREQ_NUM]; /**< PA gain, indexed by [PA section:10][channel:16] */
    short couplerLossComp[IRF_MAX_TX_PA_MODE_NUM][IRF_MAX_FHC_TEMP_SECTION_NUM][IRF_MAX_FHC_CAL_FREQ_NUM]; /**< coupler loss compensation, indexed by [PA mode:3][temperature:8][channel:16] */
    short couplerLossSub[IRF_MAX_TX_PA_MODE_NUM][IRF_MAX_FHC_CAL_FREQ_NUM]; /**< coupler loss, indexed by [PA mode:3][channel:16] */
} IRfTxCalibrationData;

/**
 * \ingroup IOTNTNStruct
 * \details TX calibration TAS data
 */
typedef struct
{
    short paGainSubTable0[IRF_MAX_FHC_TX_CENTER_PA_LEVEL_NUM][IRF_MAX_FHC_CAL_FREQ_NUM]; /**< PA gain, indexed by [PA section:10][channel:16] */
    short paGainSubTable1[IRF_MAX_FHC_TX_CENTER_PA_LEVEL_NUM][IRF_MAX_FHC_CAL_FREQ_NUM]; /**< PA gain, indexed by [PA section:10][channel:16] */
    short couplerLossSub[IRF_MAX_TX_PA_MODE_NUM][IRF_MAX_FHC_CAL_FREQ_NUM]; /**< coupler loss, indexed by [PA mode:3][channel:16] */
} IRfTxCalibrationTasData;

/**
 * \ingroup IOTNTNStruct
 * \details RX calibration data
 */
typedef struct
{
    char temperature[IRF_MAX_FHC_TEMP_SECTION_NUM];       /**< temperature */
    unsigned int frequency[IRF_MAX_FHC_CAL_FREQ_NUM]; /**< frequency unit 1khz*/
    short lossComp[IOT_NTN_LNA_MODE_NUM][IRF_MAX_FHC_TEMP_SECTION_NUM][IRF_MAX_FHC_CAL_FREQ_NUM]; /**< high power mode loss, indexed by [LNA mode:8][temperature:8][channel:16] */
    short loss[IOT_NTN_LNA_MODE_NUM][IRF_MAX_FHC_CAL_FREQ_NUM]; /**< high power mode loss, indexed by [LNA mode:8][channel:16] */
} IRfRxCalibrationData, IRfRxmCalibrationData, IRfRxdCalibrationData;

/**
 * \ingroup IOTNTNStruct
 * \details RX TAS calibration data
 */
typedef struct
{
    short loss[IOT_NTN_LNA_MODE_NUM][IRF_MAX_FHC_CAL_FREQ_NUM]; /**< high power mode loss, indexed by [LNA mode:8][channel:16] */
} IRfRxCalibrationTasData, IRfRxmCalibrationTasData, IRfRxdCalibrationTasData;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] The IoT-NTN calibration data information
 */
typedef struct
{
    unsigned short band;            /**< band number */
    unsigned char  type;            /**< data type */
    unsigned char  carkit;          /**< carkit index */
    unsigned short raw_data_offset; /**< offset in raw data */
} IL1TST_DATA_ENTRY_T;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] The IoT-NTN PA control data for one PA level
 */
typedef struct
{
    unsigned char  pa_mode_idx; /**< PA mode ( 0: high, 1: middle, 2: low )  */
    unsigned char  dc2dc_lvl; /**< DC2DC level (range: 0.1~5.5(V), (resolution: 1/10)  */
    short          prf; /**< PRF value (resolution: 1/256)  */
} IL1TST_PA_LEVHANDLE_T;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] The IoT-NTN TX calibration data.
 */
typedef struct
{
    unsigned char  pa_section_used; /**< PA control level (range: 0~9) */
    unsigned char  pa_mode_used; /**< Pa mode number  */
    unsigned short dummy1; /**< dummy1 */
    unsigned char  pa_level_idx[IRF_MAX_TX_PA_MODE_NUM]; /**< calibration used, indicate which Prf used for cpl calibration*/
    unsigned char  dummy2; /**< dummy2*/
    unsigned char  used_pwr_step_num[IRF_MAX_FHC_CAL_FREQ_NUM]; /**< the TX power steps for each sub-band. */
    unsigned int   subband_freq_khz[IRF_MAX_FHC_CAL_FREQ_NUM]; /**< sub-band frequency */
    short          prf [IRF_MAX_FHC_CAL_FREQ_NUM][IRF_MAX_FHC_TX_CENTER_PA_LEVEL_NUM]; /**< prf by subband */
    IL1TST_PA_LEVHANDLE_T pa_level_handle[IRF_MAX_FHC_TX_CENTER_PA_LEVEL_NUM]; /**< PA control data for PA level 0~9 */
} TxSetting;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] The IoT-NTN RX calibration data.
 */
typedef struct
{
    unsigned char   rx_cal_step; /**< the steps of LNA mode to be calibrated */
    unsigned char   dummy; /**< dummy */
    short           dl_power[IRF_MAX_FHC_RX_SEQ_NUM];  /**< the downlink power to be used, 1/256dB */
    unsigned int    dl_freq[IRF_MAX_FHC_CAL_FREQ_NUM]; /**< the downlink frequency (unit: 1 kHz) */
} RxSetting;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] The IoT-NTN TPC information.
 */
typedef struct
{
    unsigned short band;  /**< band number */
    unsigned char carkit; /**< carkit index */
    IRfTxCalibrationData txData; /**< Tx calibration data */
} IL1TST_TX_INFO;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] The IoT-NTN TAS carkit information.
 */
typedef struct
{
    unsigned short band;  /**< band number */
    unsigned char carkit; /**< carkit index */
    IRfTxCalibrationTasData txTasData;/**< Tx tpc data */
} IL1TST_TX_INFO_BY_TAS;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] The IoT-NTN Rx information.
 */
typedef struct
{
    unsigned short band;  /**< band number */
    unsigned char carkit; /**< carkit index */
    IRfRxCalibrationData rxData;  /**< Rx pathloss data */
} IL1TST_RX_INFO;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] The IoT-NTN Rx information.
 */
typedef struct
{
    unsigned short band;  /**< band number */
    unsigned char carkit; /**< carkit index */
    IRfRxCalibrationTasData rxTasData;  /**< Rx pathloss data */
} IL1TST_RX_INFO_BY_TAS;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] Get RF calibration data result request parameter
 */
typedef struct
{
    unsigned char       entry_count;  /**< TRX calibration data count */
    unsigned char       dummy[3];     /**< dummy */
    IL1TST_DATA_ENTRY_T entry_setting[IRF_MAX_CAL_DATA_NUM]; /**< TRX calibration data information */
} IRfTestCmd_GetRFCalDataReq;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] Get RF calibration data result confirm parameter
 */
typedef struct
{
    unsigned char        result; /**< Confirm result, 0: Fail, 1: success*/
    unsigned char        entry_count;  /**< Confirm trx calibration data count*/
    unsigned short       dummy;      /**< Dummy*/
    IL1TST_DATA_ENTRY_T  entry_setting[IRF_MAX_CAL_DATA_NUM]; /**< Confirm trx calibration data information*/
} IRfTestResult_GetRFCalDataCnfLocal;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] Get RF calibration data result confirm parameter
 */
typedef struct
{
    unsigned char result; /**< Confirm result, 0: Fail, 1: success*/
    unsigned short tx_default_data_count; /**< Confirm tx default calibration data count*/
    unsigned short rxm_default_data_count; /**< Confirm rxm default calibration data count*/
    unsigned short rxd_default_data_count; /**< Confirm rxd default calibration data count*/
    IL1TST_TX_INFO tx_data_info[IRF_MAX_CAL_DATA_NUM];   /**< Confirm tx information*/
    IL1TST_RX_INFO rxm_data_info[IRF_MAX_CAL_DATA_NUM];   /**< Confirm rxm information*/
    IL1TST_RX_INFO rxd_data_info[IRF_MAX_CAL_DATA_NUM];   /**< Confirm rxd information*/
} IRFTestResult_GetRFCalDataCnf;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] Get RF calibration data result confirm parameter
 */
typedef struct
{
    unsigned char result; /**< Confirm result, 0: Fail, 1: success*/
    unsigned short tx_tas_data_count; /**< Confirm tx tas calibration data count*/
    unsigned short rxm_tas_data_count; /**< Confirm rxm tas calibration data count*/
    unsigned short rxd_tas_data_count; /**< Confirm rxd tas calibration data count*/
    IL1TST_TX_INFO_BY_TAS tx_data_info_by_tas[IRF_MAX_CAL_DATA_NUM];   /**< Confirm tx TAS information*/
    IL1TST_RX_INFO_BY_TAS rxm_data_info_by_tas[IRF_MAX_CAL_DATA_NUM];   /**< Confirm rxm TAS information*/
    IL1TST_RX_INFO_BY_TAS rxd_data_info_by_tas[IRF_MAX_CAL_DATA_NUM];   /**< Confirm rxd TAS information*/
} IRFTestResult_GetRFCalDataTasCnf;

/**
 * \ingroup IOTNTNStruct
 * \details This message is used to write specific calibration data item to the NVRAM
 * (Non-volatile memory) of UE.
 */
typedef struct
{
    unsigned char        is_update; /**< update data to NVRAM or not  ( 0: update to runtime data, 1: update to NVRAM) */
    unsigned char        entry_count; /**< Write TRX calibration data count */
    unsigned short       dummy;     /**< Dummy */
    IL1TST_DATA_ENTRY_T  entry_setting[IRF_MAX_CAL_DATA_NUM]; /**< Write TRX calibration data information */
} IRfTestCmd_SetRFCalDataReqLocal;

/**
 * \ingroup IOTNTNStruct
 * \details This message is used to write specific calibration data item to the NVRAM
 * (Non-volatile memory) of UE.
 */
typedef struct
{
    unsigned char        raw_data[IL1TST_MAX_PEER_BUF_CNF_BYTE_SIZE]; /**< TRX calibration raw data */
} IRfTestCmd_SetRFCalDataReqPeer;

/**
 * \ingroup IOTNTNStruct
 * \details This message is used to write specific calibration data item to the NVRAM
 * (Non-volatile memory) of UE.
 */
typedef struct
{
    unsigned short tx_default_data_count; /**< Confirm tx default calibration data count*/
    unsigned short rxm_default_data_count; /**< Confirm rxm default calibration data count*/
    unsigned short rxd_default_data_count; /**< Confirm rxd default calibration data count*/
    IL1TST_TX_INFO tx_data_info[IRF_MAX_CAL_DATA_NUM]; /**< Request tx information*/
    IL1TST_RX_INFO rxm_data_info[IRF_MAX_CAL_DATA_NUM]; /**< Request rxm information*/
    IL1TST_RX_INFO rxd_data_info[IRF_MAX_CAL_DATA_NUM]; /**< Request rxd information*/
} IRFTestCmd_SetRFCalDataReq;

/**
 * \ingroup IOTNTNStruct
 * \details This message is in response to the IRF_TEST_CMD_SET_RF_CAL_DATA request, and
 * it reports whether UE was able to succesfully write given data to NVRAM.
 */
typedef struct
{
    unsigned char     result;  /**< 0 = data unsuccessfully updated to NVRAM, 1 = data successfully updated to NVRAM. */
} IRfTestResult_SetRFCalDataCnf;

/**
 * \ingroup IOTNTNStruct
 * \details This message is used to write specific calibration data item to the NVRAM
 * (Non-volatile memory) of UE.
 */
typedef struct
{
    unsigned short tx_tas_data_count; /**< Request tx calibration data count*/
    unsigned short rxm_tas_data_count; /**< Request rx calibration data count*/
    unsigned short rxd_tas_data_count; /**< Request rx calibration data count*/
    IL1TST_TX_INFO_BY_TAS tx_data_info_by_tas[IRF_MAX_CAL_DATA_NUM]; /**< Request tx TAS information*/
    IL1TST_RX_INFO_BY_TAS rxm_data_info_by_tas[IRF_MAX_CAL_DATA_NUM]; /**< Request rx TAS information*/
    IL1TST_RX_INFO_BY_TAS rxd_data_info_by_tas[IRF_MAX_CAL_DATA_NUM]; /**< Request rx TAS information*/
} IRFTestCmd_SetRFCalDataTasReq;

/**
 * \ingroup IOTNTNStruct
 * \details This message is in response to the IRF_TEST_CMD_SET_RF_CAL_DATA request, and
 * it reports whether UE was able to succesfully write given data to NVRAM.
 */
typedef struct
{
    unsigned char     result;  /**< 0 = data unsuccessfully updated to NVRAM, 1 = data successfully updated to NVRAM. */
} IRfTestResult_SetRFCalDataTasCnf;


/**
 * \ingroup IOTNTNStruct
 * \details This message is used to start single tone transmission by UE. The transmission
 * will continue until IRF_TEST_CMD_TEST_STOP is requested.
 */
typedef struct
{
    unsigned int     ul_earfcn;    /**< UL Earfcn. */
    char      ul_earfcn_offset;    /**< UL Earfcn offset, Format: S6.1. */
    unsigned char tx_type;         /**< 0: TTG BB, 1: TTG RF, 2: SDR0 Modulation, 3: SDR1 Modulation, 4: SDR2 Modulation, 5: SDR3 Modulation, 6: SDR4 Modulation, 7: SDR5 Modulation, 8: SDR6 Modulation, 9: SDR7 Modulation, 10: SDR8 Modulation, 11: SDR9 Modulation, 12: SDR10 Modulation, 13: SDR11 Modulation, 14: SDR12 Modulation, 15: SDR13 Modulation, 16: SDR14 Modulation, 17: SDR15 Modulation. */
    short     tx_power;            /**< TX power in dBm, Format: S7.8. */
    unsigned char tpc_mode;        /**< 0/1/2: normal/ fixed power mode/ manual mode */
    unsigned char pa_idx;          /**< Pa gain index */
    unsigned char tx_tas_state;    /**< Tx state */
    unsigned char pa_table_idx;    /**< PA table index */
    short  cpl_gain;               /**< coupler gain in DB2 format, S8.5 */
    short  pa_gain;                /**< PA gain, format: S8.5 */
    unsigned char is_tx_update;    /**< 0: Tx on, 1: Tx update */
    unsigned char ns;                /**< 0: Il1RfswTpcNs01, 1: Il1RfswTpcNs02N, 2: Il1RfswTpcNs03N, 3: Il1RfswTpcNs04N, 4: Il1RfswTpcNs05N, 5: Il1RfswTpcNs24 */
} IRfTestCmd_SingleToneTxReq;

/**
 * \ingroup IOTNTNStruct
 * \details This message is in response to IRF_TEST_CMD_SINGLE_TONE_TX_START request,
 * and it reports back whether UE is able to start transmission succesfully.
 */
typedef struct
{
    unsigned char    result;    /**< 0 = false, 1 = true. */
} IRfTestResult_SingleToneTxCnf;

/**
 * \ingroup IOTNTNStruct
 * \details This message is used to open the RF and BB hardware (limited) to receive
 * IQ samples and make RSSI measurements. RSSI measurements could be requested
 * via IRF_TEST_CMD_RX_RSSI_REQ message. UE will continue the reception until
 * IRF_TEST_CMD_TEST_STOP command is requested.
 */
typedef struct
{
    unsigned int     dl_earfcn;         /**< DL earfcn. */
    char             dl_earfcn_offset;  /**< DL earfcn offset, Format: S6.1. */
    unsigned char    ant_mask;          /**< Antenna mask, bit0: RXM, bit1: RXD. */
    short            rx_level;          /**< RX input signal power in dBm, Format: S7.8. */
    unsigned char    agc_mode;          /**< 0: fixed gain mode, 1: manual mode, 2: normal mode */
    unsigned char    lna_idx;           /**< lna index. */
    unsigned char    pga_idx;           /**< pga index. */
    unsigned char    rx_tas_state;      /**< rx state. */
    int              dig_gain;          /**< digital gain in DB2 format, S8.5. */
    unsigned char    adcdc_fix_bmp;     /**< decide which parameter to be fixed, bit0~3: rf gain\rf DC\dig gain\ dig DC. */
    unsigned char    ib_ms_flag;        /**< 1: ib power meas is from FW, 0: ib power meas is from DFE */
    int              windowLength;          /**< waiting time between start continuous and fetch power */
} IRfTestCmd_ContRxReq;

/**
 * \ingroup IOTNTNStruct
 * \details This message is in response to IRF_TEST_CMD_CONT_RX_START request, and it
 * reports back whether UE is able to open the UE for reception succesfully.
 */
typedef struct
{
    unsigned char    result;  /**< 0 = false, 1 = true. */
} IRfTestResult_ContRxCnf;

/**
 * \ingroup IOTNTNStruct
 * \details This message is in response to IRF_TEST_CMD_RX_GET_RSSI confirm
 */
typedef struct
{
    int             gain[IRF_MAX_RX_ANT_NUM]; /**< Net Gain applied in the receiver in dB, 1/32, [0]: RXM, [1]: RXD. */
    unsigned int    agc_state[IRF_MAX_RX_ANT_NUM];                /**< AGC mode. */
    int             rssi[IRF_MAX_RX_ANT_NUM]; /**< RSSI in dBm, [0]: RXM, [1]: RXD, format: S7.8. */
    int             wb_rssi[IRF_MAX_RX_ANT_NUM]; /**< WB RSSI in dBm, [0]: RXM, [1]: RXD, format: S7.8. */
    int             digital_gain_hw[IRF_MAX_RX_ANT_NUM];     /**< digital gain hardware format: S8.5. */
    int             digital_gain_fw[IRF_MAX_RX_ANT_NUM];      /**< digital gain firmware format: S8.5. */
} IRFTestResult_GetRssiCnf;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] The IoT-NTN calibration setting
 */
typedef struct
{
    unsigned short  band;            /**< band number. */
    unsigned char   calRoomTempIdx;  /**< temperature index. */
    unsigned char   paVccTableIdx;   /**< pa Vcc table */
    TxSetting       txSetting;       /**< tx calibration settings */
    RxSetting       rxSetting;       /**< rx calibration settings */
    unsigned char   txTasState;      /**< tx tas state. */
    unsigned char   rxTasState;      /**< rx tas state. */
    unsigned char   antMask;         /**< antenna mask. */
    unsigned char   iBMeasFlag;      /**< rssi measure flag. */
} IRfFhcBandParam;
/**
 * \ingroup IOTNTNStruct
 * \details This message is used to trigger the FHC sequence to do transmit power level
 * calibration measurements and rx path loss calibration measurements.
 */
typedef struct
{
    unsigned char   switch_time; /**< Switching time between Tx and Rx, Unit:ms */
    unsigned char   cal_band_number;    /**< The number of band to be calibrated. */
    unsigned char   tx_power_step_time; /**< Tx step time, Unit:1ms*N */
    unsigned char   rx_power_step_time; /**< Rx step time, Unit:2ms*N */
} IRfTestCmd_FhcTrxReqParam;

/**
 * \ingroup IOTNTNStruct
 * \details This message is used to trigger the FHC sequence to do transmit power level
 * calibration measurements and rx path loss calibration measurements.
 */
typedef struct
{
    IRfFhcBandParam band_param[IRF_MAX_FHC_CAL_SUPPORTED_BANDS];  /**< band parameter */
} IRfTestCmd_FhcTrxReqPdu;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] The IoT-NTN RX report
 */
typedef struct
{
    unsigned int   dl_freq;  /**< downlink freq, 1KHz */
    int            rxm_rssi[IRF_MAX_FHC_RX_SEQ_NUM];  /**< The RXM RSSI result, 1/32 */
    int            rxd_rssi[IRF_MAX_FHC_RX_SEQ_NUM];  /**< The RXD RSSI result, 1/32 */
} IRfFhcRssiInfo;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] The IoT-NTN TX Power detected report
 */
typedef struct
{
    unsigned int     ul_freq;  /**< uplink freq, 1KHz */
    short            pd_value[IRF_MAX_TX_PA_MODE_NUM]; /**< high/middl/low PD value */
    short            dummy;    /**< Dummy */
} IRfFhcPdInfo;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] The IoT-NTN TX and RX calibration report
 */
typedef struct
{
    unsigned short         band;  /**< band number */
    unsigned char          txTasState; /**< tx tas state */
    unsigned char          rxTasState; /**< rx tas state */
    unsigned char          paVccTableIdx; /**< Pa VCC table */
    unsigned char          dummy[3];   /**< Dummy */
    IRfFhcPdInfo           pd_result[IRF_MAX_FHC_CAL_FREQ_NUM]; /**< the power detector result */
    IRfFhcRssiInfo         rssi_result[IRF_MAX_FHC_CAL_FREQ_NUM]; /**< the RSSI result */
} IRfFhcBandReport;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] The IoT-NTN  calibration report
 */
typedef struct
{
    unsigned char ok; /**< the status of calibration execution (1: OK, others: failed) */
    unsigned char band_number; /**< the valid band number for accessing band_report[] */
} IRfTestResult_FhcTrxCnfParam;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] The IoT-NTN  calibration report
 */
typedef struct
{
    IRfFhcBandReport band_report[IRF_MAX_FHC_CAL_SUPPORTED_BANDS]; /**< the calibration result */
} IRfTestResult_FhcTrxCnfPeer;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] get TPC information request.
 */
typedef struct
{
    unsigned char dummy;  /**< dummy param */

} IRfTestCmd_GetTpcInfoReq;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] get TPC information confirm parameter.
 */
typedef struct
{
    unsigned char result;      /**< 0: fail, 1: success */
    unsigned char tpc_mode;    /**< TPC mode setting, 0/1/2: normal/ fixed gain/ manual mode */
    unsigned char pa_index;    /**< PA index */
    unsigned char pga_index;   /**< PGA index */
    short pa_gain;               /**< PA gain,format S8.5 */
    short pga_gain;              /**< PGA gain,format S8.5 */
    short dig_gain;              /**< digital gain in db,format S8.5 */
    short ddpc_pwr;              /**< Tx Power detector,format S8.5 */
    unsigned char pa_table_idx;/**< PA VCC table index */
} IRfTestResult_GetTpcInfoCnf;

/**
 * <pre>
 * \ingroup IOTNTN
 * \details [M90 series] Get IoT-NTN TPC information from target
 *
 * \param [in]  meta_handle target context
 * \param [in]  ms_timeout the timeout interval ( unit: milliseconds )
 * \param [in]  req the TPC query info
 * \param [out] cnf the TPC data
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_TIMEOUT Target was no response when the given TIMEOUT interval expired.
 * \retval META_FAILED  The function was failed.
 * \retval others Check the definitions in META_GetErrorString
 * </pre>
 */
META_RESULT __stdcall META_Iotntn_GetTpcInfo_r(const int meta_handle, const unsigned int ms_timeout, IRfTestCmd_GetTpcInfoReq *req, IRfTestResult_GetTpcInfoCnf *cnf);

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] TRX carkit inforamtion.
 */
typedef struct
{
    unsigned char      ant_idx;              /**< antenna index */
    unsigned char      carkit_idx;           /**< carkit index */
} IL1RfTasPortMap;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] TRX TAS inforamtion.
 */
typedef struct
{
    unsigned char is_default_tx_state;     /**< is default state or not */
    unsigned char is_default_rx_state;     /**< is default state or not */
    unsigned char tx_tas_state; /**< the state of TX path */
    unsigned char rx_tas_state; /**< the state of RX path */
} IL1RfTasStatePair;

/**
 * \ingroup IOTNTNStruct
 * \details The element of TAS verify list
 */
typedef struct
{
    unsigned short band;          /**< band */
    IL1RfTasStatePair tas_state;  /**< forced TAS state */
    IL1RfTasPortMap tx_mapping;   /**< TX port mapping */
    IL1RfTasPortMap rxm_mapping;  /**< RX main port mapping */
    IL1RfTasPortMap rxd_mapping;  /**< RX diversity port mapping */
} IL1RfTasVerifyListElement;

/**
 * \ingroup IOTNTNStruct
 * \details The TAS verification list
 */
typedef struct
{
    unsigned char   status;              /**< query status */
    unsigned short  iot_ntn_count;       /**< the count of IoT-NTN elements */
    IL1RfTasVerifyListElement iot_ntn_list[IRF_MAX_TAS_ELEMENT_NUM];     /**< the IoT-NTN element array */
} IRfTestResultQueryTasVerifyList;

/**
 * \ingroup IOTNTN
 * \details [M90 series] Query the TAS verification list from target.
 *
 * \param [in]  meta_handle target context
 * \param [in]  ms_timeout the timeout interval ( unit: milliseconds )
 * \param [out] cnf the response parameter for TAS verify list
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_TIMEOUT Target was no response when the given TIMEOUT interval expired.
 * \retval META_FAILED  The function was failed.
 * \retval others Check the definitions in META_GetErrorString.
 *
 */
META_RESULT __stdcall META_Iotntn_QueryTasVerifyList_r(const int meta_handle, const unsigned int ms_timeout, IRfTestResultQueryTasVerifyList *cnf);

/**
 * \ingroup IOTNTNStruct
 * \details The force TAS state request parameter
 */
typedef struct
{
    unsigned char forced_enable; /**< 0: disable, 1: enable */
    unsigned char tx_tas_state;  /**< the state of TX path */
    unsigned char rx_tas_state;  /**< the state of RX path */
} IRfTestCmd_ForceTasStateReq;

/**
 * \ingroup IOTNTNStruct
 * \details The result of force TAS state request
 */
typedef struct
{
    unsigned char result;   /**< 0:fail , 1:Success */
} IRfTestResult_ForceTasStateCnf;

/**
 * \ingroup IOTNTN
 * \details [M90 series] Force the TAS state.
 *
 * \param [in]  meta_handle target context
 * \param [in]  ms_timeout the timeout interval ( unit: milliseconds )
 * \param [in]  req the request parameter
 * \param [out] cnf the response parameter
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_TIMEOUT Target was no response when the given TIMEOUT interval expired.
 * \retval META_FAILED  The function was failed.
 * \retval others Check the definitions in META_GetErrorString.
 *
 */
META_RESULT __stdcall META_Iotntn_ForceTasState_r(const int meta_handle, const unsigned int ms_timeout, const IRfTestCmd_ForceTasStateReq *req, IRfTestResult_ForceTasStateCnf *cnf);


/******************************************************************************
 * CALIBRATION DATA STRUCTURES                                                *
 *****************************************************************************/
/**
 * \ingroup IOTNTN
 * \details Get target RF capability, This function must be called once at first before calling other APIs after entered test mode
 *
 * \param [in]  meta_handle target context
 * \param [in]  ms_timeout the timeout interval ( unit: milliseconds )
 * \param [out] cnf the response parameter
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_TIMEOUT Target was no response when the given TIMEOUT interval expired.
 * \retval META_FAILED  The function was failed.
 * \retval others Check the definitions in META_GetErrorString
 */
META_RESULT __stdcall META_Iotntn_GetRfCapability_r(const int meta_handle, const unsigned int ms_timeout, IRFTestResult_RfCapabilityCnf *cnf);

/**
 * \ingroup IOTNTN
 * \details Stop any RF ongoing operations
 *
 * \param [in]  meta_handle target context
 * \param [in]  ms_timeout the timeout interval ( unit: milliseconds )
 * \param [in]  req the request parameter
 * \param [out] cnf the response parameter
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_TIMEOUT Target was no response when the given TIMEOUT interval expired.
 * \retval META_FAILED  The function was failed.
 * \retval others Check the definitions in META_GetErrorString
 */
META_RESULT __stdcall META_Iotntn_TestStop_r(const int meta_handle, const unsigned int ms_timeout, IRfTestCmd_StopReq *req, IRfTestResult_StopCnf *cnf);

/**
 * \ingroup IOTNTN
 * \details Start NPUSCH transmission
 *
 * \param [in]  meta_handle target context
 * \param [in]  ms_timeout the timeout interval ( unit: milliseconds )
 * \param [in]  req the request parameter
 * \param [out] cnf the response parameter
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_TIMEOUT Target was no response when the given TIMEOUT interval expired.
 * \retval META_FAILED  The function was failed.
 * \retval others Check the definitions in META_GetErrorString
 */
META_RESULT __stdcall META_Iotntn_NpuschTxStart_r(const int meta_handle, const unsigned int ms_timeout, IRfTestCmd_PuschTxReq *req, IRfTestResult_PuschTxCnf *cnf);

/**
 * \ingroup IOTNTN
 * \details Start NPRACH transmission
 *
 * \param [in]  meta_handle target context
 * \param [in]  ms_timeout the timeout interval ( unit: milliseconds )
 * \param [in]  req the request parameter
 * \param [out] cnf the response parameter
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_TIMEOUT Target was no response when the given TIMEOUT interval expired.
 * \retval META_FAILED  The function was failed.
 * \retval others Check the definitions in META_GetErrorString
 */
META_RESULT __stdcall META_Iotntn_NprachTxStart_r(const int meta_handle, const unsigned int ms_timeout, IRfTestCmd_PrachTxReq *req, IRfTestResult_PrachTxCnf *cnf);

/**
 * \ingroup IOTNTN
 * \details Open the UE receiver for reception
 *
 * \param [in]  meta_handle target context
 * \param [in]  ms_timeout the timeout interval ( unit: milliseconds )
 * \param [in]  req the request parameter
 * \param [out] cnf the response parameter
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_TIMEOUT Target was no response when the given TIMEOUT interval expired.
 * \retval META_FAILED  The function was failed.
 * \retval others Check the definitions in META_GetErrorString
 */
META_RESULT __stdcall META_Iotntn_MixRxStart_r(const int meta_handle, const unsigned int ms_timeout, IRfTestCmd_MixRxReq *req, IRfTestResult_MixRxCnf *cnf);

/**
 * \ingroup IOTNTN
 * \details Get UE RX measurement reports
 *
 * \param [in]  meta_handle target context
 * \param [in]  ms_timeout the timeout interval ( unit: milliseconds )
 * \param [in]  req the request parameter
 * \param [out] cnf the response parameter
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_TIMEOUT Target was no response when the given TIMEOUT interval expired.
 * \retval META_FAILED  The function was failed.
 * \retval others Check the definitions in META_GetErrorString
 */
META_RESULT __stdcall META_Iotntn_MixRxGetRpt_r(const int meta_handle, const unsigned int ms_timeout, IRfTestCmd_MixRxRptReq *req, IRfTestResult_MixRxRptCnf *cnf);

/**
 * \ingroup IOTNTN
 * \details Reset RX measurement reports and restart measurement
 *
 * \param [in]  meta_handle target context
 * \param [in]  ms_timeout the timeout interval ( unit: milliseconds )
 * \param [in]  req the request parameter
 * \param [out] cnf the response parameter
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_TIMEOUT Target was no response when the given TIMEOUT interval expired.
 * \retval META_FAILED  The function was failed.
 * \retval others Check the definitions in META_GetErrorString
 */
META_RESULT __stdcall META_Iotntn_MixRxRestCnt_r(const int meta_handle, const unsigned int ms_timeout, IRfTestCmd_MixRxResetReq *req, IRfTestResult_MixRxResetCnf *cnf);

/**
 * \ingroup IOTNTN
 * \details Get RF calibration data from NVRAM
 *
 * \param [in]  meta_handle target context
 * \param [in]  ms_timeout the timeout interval ( unit: milliseconds )
 * \param [in]  req the request parameter
 * \param [out] cnf the response parameter
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_TIMEOUT Target was no response when the given TIMEOUT interval expired.
 * \retval META_FAILED  The function was failed.
 * \retval others Check the definitions in META_GetErrorString
 */
META_RESULT __stdcall META_Iotntn_GetRfCalData_r(const int meta_handle, const unsigned int ms_timeout, IRfTestCmd_GetRFCalDataReq *req, IRFTestResult_GetRFCalDataCnf *cnf);

/**
 * \ingroup IOTNTN
 * \details Get RF calibration data from NVRAM
 *
 * \param [in]  meta_handle target context
 * \param [in]  ms_timeout the timeout interval ( unit: milliseconds )
 * \param [in]  req the request parameter
 * \param [out] cnf the response parameter
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_TIMEOUT Target was no response when the given TIMEOUT interval expired.
 * \retval META_FAILED  The function was failed.
 * \retval others Check the definitions in META_GetErrorString
 */
META_RESULT __stdcall META_Iotntn_GetRfCalDataTas_r(const int meta_handle, const unsigned int ms_timeout, IRfTestCmd_GetRFCalDataReq *req, IRFTestResult_GetRFCalDataTasCnf *cnf);

/**
 * \ingroup IOTNTN
 * \details Set RF calibration data to NVRAM
 *
 * \param [in]  meta_handle target context
 * \param [in]  ms_timeout the timeout interval ( unit: milliseconds )
 * \param [in]  req the request parameter
 * \param [out] cnf the response parameter
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_TIMEOUT Target was no response when the given TIMEOUT interval expired.
 * \retval META_FAILED  The function was failed.
 * \retval others Check the definitions in META_GetErrorString
 */
META_RESULT __stdcall META_Iotntn_SetRfCalData_r(const int meta_handle, const unsigned int ms_timeout, IRFTestCmd_SetRFCalDataReq *req, IRfTestResult_SetRFCalDataCnf *cnf);

/**
 * \ingroup IOTNTN
 * \details Set RF calibration data to NVRAM
 *
 * \param [in]  meta_handle target context
 * \param [in]  ms_timeout the timeout interval ( unit: milliseconds )
 * \param [in]  req the request parameter
 * \param [out] cnf the response parameter
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_TIMEOUT Target was no response when the given TIMEOUT interval expired.
 * \retval META_FAILED  The function was failed.
 * \retval others Check the definitions in META_GetErrorString
 */
META_RESULT __stdcall META_Iotntn_SetRfCalDataTas_r(const int meta_handle, const unsigned int ms_timeout, IRFTestCmd_SetRFCalDataTasReq *req, IRfTestResult_SetRFCalDataTasCnf *cnf);

/**
 * \ingroup IOTNTN
 * \details Start continuous wave transmission
 *
 * \param [in]  meta_handle target context
 * \param [in]  ms_timeout the timeout interval ( unit: milliseconds )
 * \param [in]  req the request parameter
 * \param [out] cnf the response parameter
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_TIMEOUT Target was no response when the given TIMEOUT interval expired.
 * \retval META_FAILED  The function was failed.
 * \retval others Check the definitions in META_GetErrorString
 */
META_RESULT __stdcall META_Iotntn_SingleToneTxStart_r(const int meta_handle, const unsigned int ms_timeout, IRfTestCmd_SingleToneTxReq *req, IRfTestResult_SingleToneTxCnf *cnf);

/**
 * \ingroup IOTNTN
 * \details Start receiving continuously
 *
 * \param [in]  meta_handle target context
 * \param [in]  ms_timeout the timeout interval ( unit: milliseconds )
 * \param [in]  req the request parameter
 * \param [out] cnf the response parameter
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_TIMEOUT Target was no response when the given TIMEOUT interval expired.
 * \retval META_FAILED  The function was failed.
 * \retval others Check the definitions in META_GetErrorString
 */
META_RESULT __stdcall META_Iotntn_ContRxStart_r(const int meta_handle, const unsigned int ms_timeout, IRfTestCmd_ContRxReq *req, IRfTestResult_ContRxCnf *cnf);

/**
 * \ingroup IOTNTN
 * \details Get RSSI measured by UE
 *
 * \param [in]  meta_handle target context
 * \param [in]  ms_timeout the timeout interval ( unit: milliseconds )
 * \param [out] cnf the response parameter
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_TIMEOUT Target was no response when the given TIMEOUT interval expired.
 * \retval META_FAILED  The function was failed.
 * \retval others Check the definitions in META_GetErrorString
 */
META_RESULT __stdcall META_Iotntn_RxGetRssi_r(const int meta_handle, const unsigned int ms_timeout, IRFTestResult_GetRssiCnf *cnf);

/**
 * \ingroup IOTNTN
 * \details Start FHC transmission and reception sequence
 *
 * \param [in]  meta_handle target context
 * \param [in]  ms_timeout the timeout interval ( unit: milliseconds )
 * \param [in]  reqLocal the request local parameter
 * \param [in]  reqPeer the request peer parameter
 * \param [out] cnfLocal the local response parameter
 * \param [out] cnfPeer the peer response parameter
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_TIMEOUT Target was no response when the given TIMEOUT interval expired.
 * \retval META_FAILED  The function was failed.
 * \retval others Check the definitions in META_GetErrorString
 */
META_RESULT __stdcall META_Iotntn_FhcTrxList_r(const int meta_handle, const unsigned int ms_timeout, const IRfTestCmd_FhcTrxReqParam *reqLocal, const IRfTestCmd_FhcTrxReqPdu *reqPeer, IRfTestResult_FhcTrxCnfParam *cnfLocal, IRfTestResult_FhcTrxCnfPeer *cnfPeer);

#define QUERY_IOT_NTN_BAND_INFO_BITMAP               ((0x1)<<0)
#define QUERY_IOT_NTN_RX_CAL_INFO_BITMAP             ((0x1)<<1)
#define QUERY_IOT_NTN_TX_MAX_POWER_INFO_BITMAP       ((0x1)<<2)

/**
 * <pre>
 * \ingroup IOTNTN
 * \details [M90 series] Query IoT-NTN modem information with dynamic size
 * \sa META_Iotntn_DynamicQuery_r
 * \param [in]  meta_handle target context
 * \param [in]  ms_timeout the timeout interval ( unit: milliseconds )
 * \param [out] cnf the size of configuration
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_TIMEOUT Target was no response when the given TIMEOUT interval expired.
 * \retval META_FAILED  The function was failed.
 * \retval others Check the definitions in META_GetErrorString
 * </pre>
 **/
META_RESULT __stdcall META_Iotntn_QueryConfigDqCnt_r(const int meta_handle, const unsigned int ms_timeout, IRfTestResult_IotNtnDynamicQuery_Cnt_T *cnf_cnt);

/**
 * <pre>
 * \ingroup IOTNTN
 * \details [M90 series] Query IoT-NTN modem information with data pointer
 * \sa META_Iotntn_DynamicQuery_r
 * \param [in]  meta_handle target context
 * \param [in]  ms_timeout the timeout interval ( unit: milliseconds )
 * \param [in]  req the request parameter
 * \param [out] cnf the data and the size of configuration
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_TIMEOUT Target was no response when the given TIMEOUT interval expired.
 * \retval META_FAILED  The function was failed.
 * \retval others Check the definitions in META_GetErrorString
 * </pre>
 **/
META_RESULT __stdcall META_Iotntn_QueryConfigDqData_r(const int meta_handle, const unsigned int ms_timeout, const IRfTestResult_IotNtnDynamicQuery_Cnt_T *req, IRfTestResult_IotNtnDynamicQueryCnf_DataPtr_T *cnf);

/**
 * \ingroup IOTNTNStruct
 * \details BSI parameters for set command
 */
typedef struct
{
    unsigned short bsiId;    /**< BSI ID */
    unsigned short reserved; /**< reserved data */
    unsigned int bsiAddr;    /**< BSI address */
    unsigned int bsiData;    /**< BSI data */
} IRfTestCmd_SetBSIDataReq;

/**
 * \ingroup IOTNTNStruct
 * \details Set BSI parameters confirm
 */
typedef struct
{
    unsigned char result;    /**< 0:fail 1:Success */
} IRfTestResult_SetBSIDataCnf;

/**
 * \ingroup IOTNTNStruct
 * \details BSI parameters for get command
 */
typedef struct
{
    unsigned short bsiId;    /**< BSI ID */
    unsigned short reserved; /**< reserved data */
    unsigned int bsiAddr;    /**< BSI address */
} IRfTestCmd_GetBSIDataReq;

/**
 * \ingroup IOTNTNStruct
 * \details Get BSI parameters confirm
 */
typedef struct
{
    unsigned int bsiData;    /**< BSI data */
} IRfTestResult_GetBSIDataCnf;

/**
 * \ingroup IOTNTN
 * \details Set BSI data to target
 *
 * \param [in] meta_handle target context
 * \param [in] ms_timeout the timeout interval ( unit: milliseconds )
 * \param [in]  req the request parameter
 * \param [out] cnf the response parameter
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_TIMEOUT Target was no response when the given TIMEOUT interval expired.
 * \retval META_FAILED  The function was failed.
 * \retval others Check the definitions in META_GetErrorString.
 */
META_RESULT __stdcall META_Iotntn_SetBSIData_r(const int meta_handle, unsigned int ms_timeout, const IRfTestCmd_SetBSIDataReq *req, IRfTestResult_SetBSIDataCnf *cnf);

/**
 * \ingroup IOTNTN
 * \details Get BSI data to target
 *
 * \param [in] meta_handle target context
 * \param [in] ms_timeout the timeout interval ( unit: milliseconds )
 * \param [in]  req the request parameter
 * \param [out] cnf the response parameter
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_TIMEOUT Target was no response when the given TIMEOUT interval expired.
 * \retval META_FAILED  The function was failed.
 * \retval others Check the definitions in META_GetErrorString.
 */
META_RESULT __stdcall META_Iotntn_GetBSIData_r(const int meta_handle, unsigned int ms_timeout, const IRfTestCmd_GetBSIDataReq *req, IRfTestResult_GetBSIDataCnf *cnf);

/**
 * \ingroup IOTNTNStruct
 * \details MIPI code word parameters for set command
 */
typedef struct
{
    unsigned short mipiPort; /**< MIPI port */
    unsigned short reserved; /**< reserved data */
    unsigned int mipiUsid;   /**< MIPI USID */
    unsigned int mipiAddr;   /**< MIPI address */
    unsigned int mipiData;   /**< MIPI data */
} IRfTestCmd_SetMIPICodeWordReq;

/**
 * \ingroup IOTNTNStruct
 * \details Set MIPI code word parameters confirm
 */
typedef struct
{
    unsigned char result;    /**< 0:fail 1:Success */
} IRfTestResult_SetMIPICodeWordCnf;

/**
 * \ingroup IOTNTNStruct
 * \details MIPI code word parameters for get command
 */
typedef struct
{
    unsigned short mipiPort; /**< MIPI port */
    unsigned short reserved; /**< reserved data */
    unsigned int mipiUsid;   /**< MIPI USID */
    unsigned int mipiAddr;   /**< MIPI address */
} IRfTestCmd_GetMIPICodeWordReq;

/**
 * \ingroup IOTNTNStruct
 * \details MIPI code word data of get command
 */
typedef struct
{
    unsigned int mipiData;   /**< MIPI data */
} IRfTestResult_GetMIPICodeWordCnf;

/**
 * \ingroup IOTNTN
 * \details Set MIPI code word to target
 *
 * \param [in] meta_handle target context
 * \param [in] ms_timeout the timeout interval ( unit: milliseconds )
 * \param [in]  req the request parameter
 * \param [out] cnf the response parameter
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_TIMEOUT Target was no response when the given TIMEOUT interval expired.
 * \retval META_FAILED  The function was failed.
 * \retval others Check the definitions in META_GetErrorString.
 */
META_RESULT __stdcall META_Iotntn_SetMIPICodeWord_r(const int meta_handle, unsigned int ms_timeout, const IRfTestCmd_SetMIPICodeWordReq *req, IRfTestResult_SetMIPICodeWordCnf *cnf);

/**
 * \ingroup IOTNTN
 * \details Get MIPI code word to target
 *
 * \param [in] meta_handle target context
 * \param [in] ms_timeout the timeout interval ( unit: milliseconds )
 * \param [in]  req the request parameter
 * \param [out] cnf the response parameter
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_TIMEOUT Target was no response when the given TIMEOUT interval expired.
 * \retval META_FAILED  The function was failed.
 * \retval others Check the definitions in META_GetErrorString.
 */
META_RESULT __stdcall META_Iotntn_GetMIPICodeWord_r(const int meta_handle, unsigned int ms_timeout, const IRfTestCmd_GetMIPICodeWordReq *req, IRfTestResult_GetMIPICodeWordCnf *cnf);

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] Set BPI data command
 */
typedef struct
{
    unsigned int bpiData;             /**< BPI data */
    unsigned short dieIdx;            /**< DIE Index */
} IRfTestCmd_SetBPIDataReq;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] Set BPI data confirm
 */
typedef struct
{
    unsigned char result;    /**< 0:fail 1:Success */
} IRfTestResult_SetBPIDataCnf;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] Get BPI data confirm
 */
typedef struct
{
    unsigned int bpiData;             /**< BPI data */
} IRfTestResult_GetBPIDataCnf;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] Get BPI data command
 */
typedef struct
{
    unsigned short dieIdx;            /**< DIE Index */
} IRfTestCmd_GetBPIDieIdxReq;
/**
 * \ingroup IOTNTN
 * \details [M90 series] Set BPI data to target
 *
 * \param [in] meta_handle target context
 * \param [in] ms_timeout the timeout interval ( unit: milliseconds )
 * \param [in]  req the request parameter
 * \param [out] cnf the response parameter
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_TIMEOUT Target was no response when the given TIMEOUT interval expired.
 * \retval META_FAILED  The function was failed.
 * \retval others Check the definitions in META_GetErrorString.
 */
META_RESULT __stdcall META_Iotntn_SetBpiData_r(const int meta_handle, unsigned int ms_timeout, const IRfTestCmd_SetBPIDataReq *req, IRfTestResult_SetBPIDataCnf *cnf);

/**
 * \ingroup IOTNTN
 * \details [M90 series] Get BPI data from target
 *
 * \param [in] meta_handle target context
 * \param [in] ms_timeout the timeout interval ( unit: milliseconds )
 * \param [in]  req the request parameter
 * \param [out] cnf the response parameter
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_TIMEOUT Target was no response when the given TIMEOUT interval expired.
 * \retval META_FAILED  The function was failed.
 * \retval others Check the definitions in META_GetErrorString.
 */
META_RESULT __stdcall META_Iotntn_GetBpiData_r(const int meta_handle, unsigned int ms_timeout, const IRfTestCmd_GetBPIDieIdxReq *req, IRfTestResult_GetBPIDataCnf *cnf);

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] TRX group delay calibration request parameter.
 */
typedef struct
{
    unsigned int trx_type; /**< 0:RX GD CAL, 1: TX GD CAL, 2: TX&RX GD CAL */
    unsigned int bw_1khz;  /**< bandwidth */
    unsigned int ttg_freq_1khz;   /**< Carrier frequency, TTG trigger */
    unsigned int trx_window;   /**< TX &RX IQ dump window length */
} IRfTestCmd_TrxGroupDelayCalReq;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] TRX group delay calibration confirm parameter.
 */
typedef struct
{
    unsigned char dummy; /**< dummy  */
} IRfTestResult_TrxGroupDelayCalCnf;


/**
 * \ingroup IOTNTN
 * \details [M90 series] Trigger TRX group delay search calibration
 *
 * \param [in] meta_handle target context
 * \param [in] ms_timeout the timeout interval ( unit: milliseconds )
 * \param [in]  req the request parameter
 * \param [out] cnf the response parameter
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_TIMEOUT Target was no response when the given TIMEOUT interval expired.
 * \retval META_FAILED  The function was failed.
 * \retval others Check the definitions in META_GetErrorString.
 */
META_RESULT __stdcall META_Iotntn_StartTrxGroupDelayCal_r(const int meta_handle, unsigned int ms_timeout, const IRfTestCmd_TrxGroupDelayCalReq *req, IRfTestResult_TrxGroupDelayCalCnf *cnf);

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] TRX IQ Dump request parameter.
 */
typedef struct
{
    unsigned short duration_time; /**< unit ms */
    unsigned char dump_node;  /**< dump mode */
    unsigned char path_bmp;   /**< Only support one path per dump: 0x01/0x02 (TRX/DRX)  */
    unsigned int type;   /**< 0/1 (TX/RX)  */
} IRfTestCmd_TrxIQDumpReq;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] TRX group delay calibration confirm parameter.
 */
typedef struct
{
    unsigned char result; /**< 0:fail 1:Success  */
} IRfTestResult_TrxIQDumpCnf;

/**
 * \ingroup IOTNTN
 * \details [M90 series] Trigger Trx IQ Dump
 *
 * \param [in] meta_handle target context
 * \param [in] ms_timeout the timeout interval ( unit: milliseconds )
 * \param [in]  req the request parameter
 * \param [out] cnf the response parameter
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_TIMEOUT Target was no response when the given TIMEOUT interval expired.
 * \retval META_FAILED  The function was failed.
 * \retval others Check the definitions in META_GetErrorString.
 */
META_RESULT __stdcall META_Iotntn_StartTrxIqDump_r(const int meta_handle, unsigned int ms_timeout, const IRfTestCmd_TrxIQDumpReq *req, IRfTestResult_TrxIQDumpCnf *cnf);

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] NSFT request parameter.
 */
typedef struct
{
    unsigned short freqSeqNum;  /**< total requested test freq amount, max 50  */
    short          dlSyncPwr;   /**< Cell search power for CS freeze gain, format S7.8  */
    unsigned short cellId;      /**< cell id  */
    unsigned short crcNum;      /**< CRC test count per power level  */
    unsigned short dummyCrcNum; /**< Dummy CRC test count per power level  */
    unsigned short rnti;        /**< c-rnti for npdsch  */
    unsigned char  rxBitMap;    /**< Invalid: 0x00, RXM: 0x01, RXD: 0x02, RXM+RXD: 0x03*/
    unsigned char  enable15k;   /**< 1: 15k, 0: 3.75k  */
    unsigned char  dummy[2];    /**< dummy  */
} IL1TSTCmd_NSFT_ReqLocal;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] NSFT common request parameter.
 */
typedef struct
{
    unsigned short band;   /**< band number  */
    unsigned short dummy;  /**< dummy  */
} IL1NsftCommonCfg;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] NSFT Tx request parameter.
 */
typedef struct
{
    unsigned int    txFreq;                                 /**< Tx UL Freq, only 1 Tx frequency used per NSFT sequence, unit 1khz */
    short           pwrTx[IRF_NSFT_MAX_TX_PWR_LEVEL_NUM];   /**< Tx power */
    unsigned char   iSc[IRF_NSFT_MAX_TX_PWR_LEVEL_NUM];     /**< Subcarrier Indication */
    unsigned char   iMcs[IRF_NSFT_MAX_TX_PWR_LEVEL_NUM];    /**< Modulation and Coding Scheme */
    unsigned char   txPwrValidNum;                          /**< Tx power steps */
    unsigned char   txTasState;                             /**< Tx TAS State */
    unsigned char   dummy[2];                               /**< dummy */
} IL1NsftTxCfg;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] NSFT Rx request parameter.
 */
typedef struct
{
    unsigned int   rxFreq;                                /**< Rx DL Freq, only 1 Rx frequency used per NSFT sequence, unit:1khz */
    short          pwrRx[IRF_NSFT_MAX_RX_PWR_LEVEL_NUM];  /**< Rx power */
    unsigned char  rxPwrValidNum;                         /**< Rx power level numbers */
    unsigned char  rxTasState;                            /**< Rx TAS State */
    unsigned char  dummyRxFlag;                           /**< Set dummy RX For RX Sync */
    unsigned char  dummy[3];                              /**< dummy */
} IL1NsftRxCfg;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] NSFT request parameter.
 */
typedef struct
{
    IL1NsftCommonCfg commCfg;  /**< Common nsft peer buffer configuration  */
    IL1NsftTxCfg     txCfg;    /**< Nsft Tx Configuration  */
    IL1NsftRxCfg     rxCfg;    /**< Nsft Rx Configuration  */
} IL1RfNsftCfg;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] NSFT request parameter.
 */
typedef struct
{
    IL1RfNsftCfg nsftCfg[IRF_NSFT_MAX_FREQ_SEQ_NUM];  /**< NSFT configuration*/
} IL1TSTCmd_NSFT_ReqPdu;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] NSFT confirm parameter.
 */
typedef struct
{
    unsigned int     rxFreq;                                                   /**< Rx frequency, unit:1khz */
    unsigned short   band;                                                     /**< Band number */
    unsigned char    rxPwrValidNum;                                            /**< rx power level numbers */
    unsigned char    dummyRxFlag;                                              /**< Set dummy Rx for Rx Sync */
    short            rssi[IRF_NSFT_MAX_RX_PWR_LEVEL_NUM][IRF_MAX_RX_ANT_NUM];  /**< RSSI, format:S7.8*/
    short            rsrp[IRF_NSFT_MAX_RX_PWR_LEVEL_NUM][IRF_MAX_RX_ANT_NUM];  /**< RSRP, format:S8.7 */
    short            rsrq[IRF_NSFT_MAX_RX_PWR_LEVEL_NUM][IRF_MAX_RX_ANT_NUM];  /**< RSRQ, format:S15.0 */
    short            snr[IRF_NSFT_MAX_RX_PWR_LEVEL_NUM][IRF_MAX_RX_ANT_NUM];   /**< SNR, format:S7.8 */
    unsigned short   crcOkCnt[IRF_NSFT_MAX_RX_PWR_LEVEL_NUM];                  /**< CRC pass counter */
    unsigned short   crcNokCnt[IRF_NSFT_MAX_RX_PWR_LEVEL_NUM];                 /**< CRC fail counter */
} IL1RfTestCmd_NSFT_List_Rx_Band_Rpt;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] NSFT confirm parameter.
 */
typedef struct
{
    short result;              /**< 0:OK, 1:Invalid Params Received, 2:NSFT engine already running, 3:CS, MIB Decode timeout expired*/
    unsigned short freqSeqNum; /**< Tested freq nums*/
    unsigned char  rxBitMap;   /**< Invalid:0x00, RXM:0x01, RXD:0x02. RXM+RXD:0x03*/
    unsigned char  dummy[3];   /**< Dummy */
} IL1TSTCmd_NSFT_CnfLocal;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] NSFT confirm parameter.
 */
typedef struct
{
    IL1RfTestCmd_NSFT_List_Rx_Band_Rpt nsftReport[IRF_NSFT_MAX_FREQ_SEQ_NUM];  /**< NSFT report */
} IL1TSTCmd_NSFT_CnfPdu;

/**
 * \ingroup IOTNTN
 * \details Start NSFT transmission and reception sequence
 *
 * \param [in]  meta_handle target context
 * \param [in]  ms_timeout the timeout interval ( unit: milliseconds )
 * \param [in]  reqLocal the request local parameter
 * \param [in]  reqPdu the request peer parameter
 * \param [out] cnfLocal the local response parameter
 * \param [out] cnfPdu the peer response parameter
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_TIMEOUT Target was no response when the given TIMEOUT interval expired.
 * \retval META_FAILED  The function was failed.
 * \retval others Check the definitions in META_GetErrorString
 */
META_RESULT __stdcall META_Iotntn_StartTrxNsft_r(const int meta_handle, unsigned int ms_timeout, const IL1TSTCmd_NSFT_ReqLocal *reqLocal, IL1TSTCmd_NSFT_ReqPdu *reqPdu, IL1TSTCmd_NSFT_CnfLocal *cnfLocal, IL1TSTCmd_NSFT_CnfPdu *cnfPdu);

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] intialize UBIN to target request parameter.
 */
typedef struct
{
    unsigned char flag;  /**< 0:deinit 1:init */
} IRfTestCmd_UbinInitReq;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] intialize UBIN to target confirm parameter.
 */
typedef struct
{
    unsigned char result; /**< 0:fail 1:Success  */
} IRfTestResult_UbinInitCnf;

/**
 * \ingroup IOTNTN
 * \details [M90 series] intialize UBIN to target
 *
 * \param [in] meta_handle target context
 * \param [in] ms_timeout the timeout interval ( unit: milliseconds )
 * \param [in]  req the request parameter
 * \param [out] cnf the response parameter
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_TIMEOUT Target was no response when the given TIMEOUT interval expired.
 * \retval META_FAILED  The function was failed.
 * \retval others Check the definitions in META_GetErrorString.
 */
META_RESULT __stdcall META_Iotntn_UbinModeSetup_r(const int meta_handle, unsigned int ms_timeout, const IRfTestCmd_UbinInitReq *req, IRfTestResult_UbinInitCnf *cnf);

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] The band number for getting maximum TX power(Dynamic size database usage)
 */
typedef struct
{
    unsigned int  tx_max_prf_cnt;   /**< number of Band */
} IRfTestResult_MaxPrf_DynSize_Cnt_T;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series]  The band number query info for getting maximum TX power, used for passing database cross dll(Dynamic size database usage)
 */
typedef struct
{
    IL1TST_TX_INFO_MPRF_T    *p_tx_max_prf_info; /**< Tx max power information */
} IRfTestResult_MaxPrf_DynSize_DataPtr_T;

/**
 * <pre>
 * \ingroup IOTNTN
 * \details [M90 series] Query the Maximum TX power per band for NSFT(Dynamic size database, no size limitation)
 * For Dynamic size database query, need to get database size first via this function,
 * Then reserve corresponding memory allocation to fill up related data via META_Iotntn_GetMaxPrfInfo_DQ_data_r
 *
 * \param [in]  meta_handle target context
 * \param [in]  ms_timeout the timeout interval ( unit: milliseconds )
 * \param [out] cnf_cnt confirmation data
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_TIMEOUT Target was no response when the given TIMEOUT interval expired.
 * \retval META_FAILED  The function was failed.
 * \retval others Check the definitions in META_GetErrorString
 * </pre>
 */
META_RESULT __stdcall META_Iotntn_GetMaxPrfInfo_DQ_cnt_r(const int meta_handle, const unsigned int ms_timeout, IRfTestResult_MaxPrf_DynSize_Cnt_T *cnf_cnt);

/**
 * <pre>
 * \ingroup IOTNTN
 * \details [M90 series] Query the Maximum TX power per band for NSFT(Dynamic size database, no size limitation)
 * For Dynamic size database query, need to get database size first via META_Iotntn_GetMaxPrfInfo_DQ_cnt_r,
 * Then reserve corresponding memory allocation to fill up related data
 *
 * \param [in]  meta_handle target context
 * \param [in]  ms_timeout the timeout interval ( unit: milliseconds )
 * \param [out] cnf confirmation data
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_TIMEOUT Target was no response when the given TIMEOUT interval expired.
 * \retval META_FAILED  The function was failed.
 * \retval others Check the definitions in META_GetErrorString
 * </pre>
 */
META_RESULT __stdcall META_Iotntn_GetMaxPrfInfo_DQ_data_r(const int meta_handle, const unsigned int ms_timeout, IRfTestResult_MaxPrf_DynSize_DataPtr_T *cnf);

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] MCS enumeration
 */
typedef enum
{
    IL1TST_BPSK = 0, /**< BPSK modulation */
    IL1TST_QPSK = 1, /**< QPSK modulation */
    IL1TST_MCS_NUM,  /**< maximum number of MCS */
    IL1TST_MCS_INVALID  = 0xFF  /**< unused */
} IL1TST_MCS_E;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] Sub-Carrier Number enumeration
 */
typedef enum
{
    IL1TST_1TONE      = 0, /**< 1 tone */
    IL1TST_3TONE      = 1, /**< 3 tone */
    IL1TST_6TONE      = 2, /**< 6 tone */
    IL1TST_12TONE     = 3, /**< 12 tone */
    IL1TST_TONE_NUM,       /**< maximum number of sub-carrier */
    IL1TST_TONE_INVALID  = 0xFF  /**< unused */
} IL1TST_SUBCARRIER_NUMBER_E;

/**
 * \ingroup IOTNTNStruct
 * \details The enumeration for SCS
 */
typedef enum
{
    IL1TST_SCS_3P75KHZ    = 0,   /**< sub-carrier 3.75KHZ */
    IL1TST_SCS_15KHZ      = 1,   /**< sub-carrier 15KHZ */
    IL1TST_SCS_CNT,              /**< sub-carrier count */
    IL1TST_SCS_INVALID    = 0XFF /**< not used */
} IL1TST_SCS_E;

/**
 * \ingroup IOTNTNStruct
 * \details The enumeration for APT2.0 Table Index
 */
typedef enum
{
    IL1_RFSW_APT2P0_TABLE_0,              /**< APT Table 0 */
    IL1_RFSW_APT2P0_TABLE_1,              /**< APT Table 1 */
    IL1_RFSW_APT2P0_TABLE_NUM,            /**< APT Table count */
    IL1_RFSW_APT2P0_TABLE_INVALID = 0XFF  /**< not used */
} IL1TST_APT2P0_TABLE_INDEX_E;


/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] Query MPR Table input.
 */
typedef struct
{
    unsigned char    modulation_type;        /**< modulation type */
    unsigned char    sub_carrier_num;        /**< subcarrier number */
    unsigned char    sub_carrier_start_location; /**< subcarrier start location */
    unsigned char    scs;                    /**< tx sub carrier spacing */
    short            max_power_mpr;          /**< max power MPR (unit: 1/256dBm) */
    unsigned char    table_index;           /**< apt2.0 table index */
} IL1TST_MPR_INFO_QUERY_INPUT_T;

/**
 * \ingroup IOTNTNStruct
 * \details This message is used to Query MPR Table confirm parameter
 */
typedef struct
{
    unsigned char    dummy;  /**< Dummy payload. */
} IRfTestCmd_QueryMprTableReq;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] Query MPR Table confirm parameter.
 */
typedef struct
{
    unsigned int result; /**< result, 0:pass, 1:fail */
} IRfTestResult_QueryMprTableCnfParam;

/**
 * \ingroup IOTNTNStruct
 * \details [M90 series] Query MPR Table confirm peer buffer.
 */
typedef struct
{
    unsigned int validNumber; /**< the valid number for accessing MPR table */
    IL1TST_MPR_INFO_QUERY_INPUT_T mpr_table[200]; /**< MPR Table */
} IRfTestResult_QueryMprTableCnfPeer;

/**
 * <pre>
 * \ingroup IOTNTN
 * \details [M90 series] Get IoT-NTN MPR table
 *
 * \param [in]  meta_handle target context
 * \param [in]  ms_timeout the timeout interval ( unit: milliseconds )
 * \param [in]  req request parameter of query mpr table
 * \param [out] cnfLocal confirmation parameter of query mpr table command
 * \param [out] cnfPeer confirmation peer buffer of query mpr table command
 *
 * \retval META_SUCCESS The operation succeeded.
 * \retval META_TIMEOUT Target was no response when the given TIMEOUT interval expired.
 * \retval META_FAILED  The function was failed.
 * \retval others Check the definitions in META_GetErrorString
 * </pre>
 */
META_RESULT __stdcall META_Iotntn_QueryMprTable_r(const int meta_handle, const unsigned int ms_timeout, const IRfTestCmd_QueryMprTableReq *req, IRfTestResult_QueryMprTableCnfParam *cnfLocal, IRfTestResult_QueryMprTableCnfPeer *cnfPeer);


#ifdef __cplusplus
}
#endif // #ifdef __cplusplus
#endif // #ifndef _META_IOTNTN_H_
