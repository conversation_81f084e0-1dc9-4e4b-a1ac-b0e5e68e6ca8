#ifndef JSONHANDLER_H
#define JSONHANDLER_H

#include <QCoreApplication>
#include <QFile>
#include <QIODevice>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QMap>
#include <QList>
#include <QString>
#include <QTextStream>
#include <QDebug>

class JsonHandler {
public:
    JsonHandler() : root() {}

    bool setjson(const QString &JsonQString) {
        QJsonParseError parseError;
        QJsonDocument doc = QJsonDocument::fromJson(JsonQString.toUtf8(), &parseError);
        if (parseError.error != QJsonParseError::NoError) {
            qWarning() << "JSON parsing error at offset" << parseError.offset << ":" << qPrintable(parseError.errorString());
            return false;
        }
        root = doc.object();
        return true;
    }

    int getIntValue(const QString &key)
    {
        QJsonValue value = root.value(key);
        if(value.isNull() || value.isUndefined())
            return -1;
        else
            return value.toInt();
    }

    QString getQStringValue(const QString &key)
    {
        QJsonValue value = root.value(key);
        if(value.isNull() || value.isUndefined())
            return "";
        else
            return value.toString();
    }

    QJsonArray getArrayValue(const QString &key)
    {
        QJsonValue object_value = root.value(key);

        if(object_value.isArray())
        {
            return object_value.toArray();
        }

        return QJsonArray();
    }

    bool getBoolValue(const QString &key)
    {
        QJsonValue object_value = root.value(key);

        if(object_value.isBool())
        {
            return object_value.toBool();
        }

        return false;
    }

    QJsonValue getObjectValue(const QString &key)
    {
        QJsonValue value = root.value(key);
        return value;
    }

    // 加载JSON文件
    bool load(const QString &fileName) {
        QFile file(fileName);
        if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
            qWarning() << "Failed to open file for reading:" << qPrintable(file.errorString());
            return false;
        }
        QJsonParseError parseError;
        QJsonDocument doc = QJsonDocument::fromJson(file.readAll(), &parseError);
        if (parseError.error != QJsonParseError::NoError) {
            qWarning() << "JSON parsing error at offset" << parseError.offset << ":" << qPrintable(parseError.errorString());
            return false;
        }
        root = doc.object();
        return true;
    }

    // 保存JSON到文件
    bool save(const QString &fileName) const {
        QFile file(fileName);
        if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
            qWarning() << "Failed to open file for writing:" << qPrintable(file.errorString());
            return false;
        }
        QJsonDocument doc(root);
        QTextStream stream(&file);
        stream << doc.toJson(QJsonDocument::Indented);
        file.close(); // Ensure the file is properly closed
        return true;
    }

    // 将JSON对象转换为QMap<QString, QJsonValue>
    QMap<QString, QJsonValue> jsonObjectToMap() const {
        QMap<QString, QJsonValue> map;
        for (auto it = root.begin(); it != root.end(); ++it) {
            map.insert(it.key(), it.value());
        }
        return map;
    }


private:
    QJsonObject root;
};

#endif // JSONHANDLER_H
