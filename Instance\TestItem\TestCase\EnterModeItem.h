#ifndef ENTERMODEITEM_H
#define ENTERMODEITEM_H

#include "../testcasebase.h"
#include <QCheckBox>
#include <QComboBox>
#include <QLineEdit>
#include <QFormLayout>

class EnterModeItem : public TestCaseBase
{
    Q_OBJECT
public:
    EnterModeItem();

    QString type() const override { return "EnterMode"; }
    static QString staticType() { return "EnterMode"; }
    QString displayName() const override { return "Enter Mode"; }

    QWidget* createParameterWidget(QWidget* parent) override;

    QVariantMap parameters() const override { return m_params; }
    void setParameters(const QVariantMap& params) override { m_params = params; }
};

#endif // ENTERMODEITEM_H
