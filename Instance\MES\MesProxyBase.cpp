#include "MesProxyBase.h"


MesProxyBase::MesProxyBase(void)
{
}


MesProxyBase::~MesProxyBase(void)
{
}

bool MesProxyBase::PreWork(MesDataUnit_s *unit, MesErrChar *errInfo)
{
	return true;
}

bool MesProxyBase::CheckSN(MesDataUnit_s *unit, MesErrChar *errInfo)
{
	return true;
}

bool MesProxyBase::QuerySN(MesDataUnit_s *unit, MesErrChar *errInfo)
{
	return true;
}

bool MesProxyBase::MarkWork(MesDataUnit_s *unit, MesErrChar *errInfo)
{
	return true;
}
