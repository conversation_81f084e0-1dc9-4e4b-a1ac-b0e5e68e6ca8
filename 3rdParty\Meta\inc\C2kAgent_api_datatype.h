/*
 * Copyright (c) 2020, MediaTek Inc. All rights reserved.
 *
 * This software/firmware and related documentation ("MediaTek Software") are
 * protected under relevant copyright laws.
 * The information contained herein is confidential and proprietary to
 * MediaTek Inc. and/or its licensors.
 * Except as otherwise provided in the applicable licensing terms with
 * MediaTek Inc. and/or its licensors, any reproduction, modification, use or
 * disclosure of MediaTek Software, and information contained herein, in whole
 * or in part, shall be strictly prohibited.
*/
#ifndef _C2KAGENT_API_DATATYPE_H_
#define _C2KAGENT_API_DATATYPE_H_

#define C2K_BAND_SUPPORT_BC0             (1<<0)
#define C2K_BAND_SUPPORT_BC1             (1<<1)
#define C2K_BAND_SUPPORT_BC2             (1<<2)
#define C2K_BAND_SUPPORT_BC3             (1<<3)
#define C2K_BAND_SUPPORT_BC4             (1<<4)
#define C2K_BAND_SUPPORT_BC5             (1<<5)
#define C2K_BAND_SUPPORT_BC6             (1<<6)
#define C2K_BAND_SUPPORT_BC7             (1<<7)
#define C2K_BAND_SUPPORT_BC8             (1<<8)
#define C2K_BAND_SUPPORT_BC9             (1<<9)
#define C2K_BAND_SUPPORT_BC10            (1<<10)
#define C2K_BAND_SUPPORT_BC11            (1<<11)
#define C2K_BAND_SUPPORT_BC12            (1<<12)
#define C2K_BAND_SUPPORT_BC13            (1<<13)
#define C2K_BAND_SUPPORT_BC14            (1<<14)
#define C2K_BAND_SUPPORT_BC15            (1<<15)

#define C2K_SUPPORT_BAND_COUNT           16

#define C2K_RF_ID_UNKNOWN                0x00000000
#define C2K_RF_ID_MT6158                 0x00000003
#define C2K_RF_ID_MT6176                 0x00000004
#define C2K_RF_ID_MT6179                 0x00000005
#define C2K_RF_ID_MT6177L                0x00000012
#define C2K_RF_ID_MT6177M                0x00000013
#define C2K_RF_ID_MT6185                 0x00000014
#define C2K_RF_ID_MT6186                 0x00000015
#define C2K_RF_ID_MT6186M                0x00000016
#define C2K_RF_ID_MT6190                 0x0000001B

#define C2K_LNA_MODE_NUM                 8
#define C2K_NSFT_LIST_MODE_NUM           50
#define C2K_TX_POWER_BACKOFF_NUM         2
#define C2K_FREQ_COMP_NUM                16
#define C2K_TEMP_COMP_NUM                8
#define C2K_PA_SECTION_NUM               8

#define C2K_FHC_MAX_STEP                 2000
#define C2K_FHC_RX_HIGH_POWER_MODE       0x00
#define C2K_FHC_RX_LOW_POWER_MODE        0x08
#define C2K_FHC_RX_MAIN_DIV              0x30
#define C2K_FHC_RX_MAIN_SHDR             0x50
#define C2K_FHC_RX_SHDR                  0x40

#ifdef __cplusplus
extern "C" {
#endif

/**
 * \ingroup C2KStruct
 * \details The enumeration for communication type
 */
typedef enum
{
    C2K_COM_SERIAL, /**< unused */
    C2K_COM_USB,    /**< unused */
    C2K_COM_USBLTE, /**< unused */
    C2K_COM_VC      /**< virtual channel */
}
C2K_COM_TYPE;

/**
 * \ingroup C2KStruct
 * \details Deprecated
 */
typedef enum
{
    C2K_RX_CAL_MAIN_DIV,
    C2K_RX_CAL_MAIN,
    C2K_RX_CAL_DIV
} C2K_RX_CALIBRATION_TYPE;

/**
 * \ingroup C2KStruct
 * \details Deprecated
 */
typedef enum
{
    C2K_AFC_TCXO,
    C2K_AFC_DCXO
} C2K_AFC_CONTROL_MODE;

/**
 * \ingroup C2KStruct
 * \details The enumeration for SPI get/set type
 */
typedef enum
{
    C2K_TX_SPI, /**< SPI TX path */
    C2K_RX_SPI  /**< SPI RX path */
} C2K_SPI_TYPE;

/**
 * \ingroup C2KStruct
 * \details The enumeration for RF mode
 */
typedef enum
{
    C2K_RF_MODE_1XRTT, /**< 1xRTT mode */
    C2K_RF_MODE_EVDO,  /**< EVDO mode */
    C2K_RF_MODE_BOTH   /**< unused */
} C2K_RF_MODE;

/**
 * \ingroup C2KStruct
 * \details The enumeration for TX AGC control mode
 */
typedef enum
{
    C2K_CTRL_MODE_AUTO,    /**< automatic mode */
    C2K_CTRL_MODE_DISABLE, /**< unused */
    C2K_CTRL_MODE_MANUAL   /**< manual mode */
} C2K_CTRL_MODE;

/**
 * \ingroup C2KStruct
 * \details Deprecated
 */
typedef enum
{
    C2K_LNA_MODE_HIGH,
    C2K_LNA_MODE_MIDDLE,
    C2K_LNA_MODE_LOW
} C2K_LNA_MODE;

/**
 * \ingroup C2KStruct
 * \details Deprecated
 */
typedef enum
{
    C2K_SIDB_FSI,
    C2K_SIDB_FFS
} C2K_SIDB_ACCESS_MODE;

/**
 * \ingroup C2KStruct
 * \details The enumeration for which RF data to be initialized
 */
typedef enum
{
    C2K_NVRAM_RF_PARAM, /**< RF parameters */
    C2K_NVRAM_RF_CAL,   /**< RF calibration data */
    C2K_NVRAM_MIPI,     /**< RF MIPI data */
    C2K_NVRAM_POC       /**< RF power on calibration data */
} C2K_NVRAM_TYPE;

/**
 * \ingroup C2KStruct
 * \details The enumeration for NVRAM ID
 */
typedef enum
{
    // DB segment ID
    C2K_DB_AFC,                         /**< AFC \sa AFC_TABLE_SEG */
    C2K_DB_TEMPERATURE,                 /**< temperature ADC \sa TEMPERATURE_TABLE_SEG */
    C2K_DB_TXAGC,                       /**< TX APC \sa TXAGC_TABLE_SEG */
    C2K_DB_TXAGC_FREQ_ADJ,              /**< TX APC frequency compensation \sa TXFREQADJ_TABLE_SEG */
    C2K_DB_TXAGC_TEMP_ADJ,              /**< TX APC temperature compensation \sa TXTEMPADJ_TABLE_SEG */
    C2K_DB_TX_COUPLE_LOSS,              /**< coupler loss \sa TXCOUPLELOSS_TABLE_SEG */
    C2K_DB_TX_COUPLE_LOSS_FREQ_ADJ,     /**< coupler loss frequency compensation \sa TXCOUPLELOSSFREQADJ_TABLE_SEG */
    C2K_DB_RXAGC_FREQ_ADJ,              /**< RX main path frequency compensation \sa RXFREQADJ_TABLE_SEG */
    C2K_DB_RXAGC_TEMP_ADJ,              /**< RX main path temperature compensation \sa RXTEMPADJ_TABLE_SEG */
    C2K_DB_DIV_RXAGC_FREQ_ADJ,          /**< RX diversity path frequency compensation \sa RXFREQADJ_TABLE_SEG */
    C2K_DB_DIV_RXAGC_TEMP_ADJ,          /**< RX diversity path temperature compensation \sa RXTEMPADJ_TABLE_SEG */
    C2K_DB_RF_VERSION,                  /**< RF version \sa RFVERSION_TABLE */
    C2K_DB_MEID,                        /**< MEID \sa MEID_TABLE */
    C2K_DB_CUSTOM_DATA,                 /**< custom data \sa CUSTOMDATA_TABLE */
    C2K_DB_TAS_DATA,                    /**< TAS \sa TASDATA_TABLE */
    //MT6176 LID
    C2K_LID_POC_DATA,                   /**< power on calibration data */
    C2K_LID_AFC,                        /**< AFC \sa AFC_TABLE_SEG */
    C2K_LID_TEMPERATURE,                /**< temperature ADC \sa TEMPERATURE_TABLE_SEG */
    C2K_LID_RX_LOSS_HIGH_POWER,         /**< RX main path loss of high power \sa RX_LOSS_TABLE */
    C2K_LID_RX_DIV_LOSS_HIGH_POWER,     /**< RX diversity path loss of high power \sa RX_LOSS_TABLE */
    C2K_LID_RX_SHDR_LOSS_HIGH_POWER,    /**< RX second path loss of high power \sa RX_LOSS_TABLE */
    C2K_LID_RX_LOSS_LOW_POWER,          /**< RX main path loss of low power \sa RX_LOSS_TABLE */
    C2K_LID_RX_DIV_LOSS_LOW_POWER,      /**< RX diversity path loss of low power \sa RX_LOSS_TABLE */
    C2K_LID_RX_SHDR_LOSS_LOW_POWER,     /**< RX second path loss of low power \sa RX_LOSS_TABLE */
    C2K_LID_TX_APC,                     /**< TX APC table \sa TXAGC_TABLE_SEG */
    C2K_LID_TX_COUPLER_LOSS,            /**< coupler loss \sa TXCOUPLELOSS_TABLE_SEG */
    C2K_LID_TX_PA_GAIN_COMP,            /**< EVDO PA gain compensation \sa PA_GAIN_COMP_TABLE */
    C2K_LID_TX_COUPLER_LOSS_COMP,       /**< EVDO coupler loss compensation \sa COUPLER_LOSS_COMP_TABLE */
    C2K_LID_TX_PA_GAIN_COMP_1XRTT,      /**< 1xRTT PA gain compensation \sa PA_GAIN_COMP_TABLE */
    C2K_LID_TX_COUPLER_LOSS_COMP_1XRTT, /**< 1xRTT coupler loss compensation \sa COUPLER_LOSS_COMP_TABLE */
    C2K_LID_AFC_S_CURVE,                /**< AFC S-Curve \sa S_CURVE_TABLE */
    C2K_LID_TX_POWER_BACKOFF,           /**< TX power backoff \sa TX_POWER_BACKOFF_TABLE */
    C2K_LID_AGPS_GROUP_DELAY,           /**< AGPS group delay */
    C2K_LID_TEMPERATURE_2               /**< temperature ADC 2 \sa TEMPERATURE_TABLE_SEG */
} C2K_DB_SEGMENT;

/**
 * \ingroup C2KStruct
 * \details Library initialization parameters(ANSI character version)
 */
typedef struct
{
    int bLog;            /**< indicate enable(1)/disable(0) to save log in file */
    char *szLogPath;     /**< pointer to the null-terminated log file name string */
    int bScript;         /**< unused, always set to 0 */
    char *szScriptPath;  /**< unused, always set to NULL */
} C2K_LIBCONFIGPARMS, *LPC2K_LIBCONFIGPARMS;

/**
 * \ingroup C2KStruct
 * \details Library initialization parameters(wide character version)
 */
typedef struct
{
    int bLog;                 /**< indicate enable(1)/disable(0) to save log in file */
    const wchar_t *szLogPath; /**< pointer to the null-terminated log file name string(wide character) */
    int bScript;              /**< unused, always set to 0 */
    char *szScriptPath;       /**< unused, always set to NULL */
} C2K_LIBCONFIGPARMSW;

/**
 * \ingroup C2KStruct
 * \details Deprecated
 */
typedef struct
{
    C2K_AFC_CONTROL_MODE eAfcControlMode;
    C2K_RF_MODE eRfMode;
    C2K_RX_CALIBRATION_TYPE eRxCalibrationType;
    C2K_SIDB_ACCESS_MODE eSidbAccessMode;
    int bWriteSIDBFlag;
    unsigned int uBarcodeLength;
    unsigned int uPllSettleTime;
    unsigned int uRxCtrlSettleTime;
    unsigned int uRxGainStateSettleTime;
    unsigned int uRxAGCGainTableSelectSettleTime;
    unsigned int uEnableRxTxSpySettleTime;
    unsigned int uTxCtrlSettleTime;
    unsigned int uTxTestRateSettleTime;
    unsigned int uTxAGCConfigSettleTime;
    int bAFCSlopeStepPerPpmWithQ6;
} PHONEATTRIBUTE_PARMS, *LPPHONEATTRIBUTE_PARMS;

/**
 * \ingroup C2KStruct
 * \details Connect target parameters
 */
typedef struct
{
    C2K_COM_TYPE eComType; /**< communication type, always set to C2K_COM_VC */
    unsigned int uComNum;  /**< virtual channel index, always set to 2 */
} C2K_CONNECT_PARAMS, *LPC2K_CONNECT_PARMS;

/**
 * \ingroup C2KStruct
 * \details Target capability information
 */
typedef struct
{
    unsigned int bandSupport;    /**< bit map of band class supported by target, |...|band3|band2|band1|band0| */
    unsigned int rxdBandSupport; /**< bit map of RX diversity band support, |...|band3|band2|band1|band0| */
    unsigned int rxdEnabled;     /**< indicate if RX diversity is enabled ( 0: disabled, 1: enabled ) */
    unsigned int paOctLevel;     /**< indicate PA 8 levels control or not ( 1: 8 levels, 0: 25 levels ) */
    unsigned int rfId;           /**< RF chip ID */
    unsigned char cpMajor;       /**< CBP software major version */
    unsigned char cpMinor;       /**< CBP software minor version */
    unsigned char cpRev;         /**< CBP software revision */
} C2K_MS_CAPABILITY;

/**
 * \ingroup C2KStruct
 * \details Deprecated
 */
typedef struct
{
    unsigned char ucAction;        /**< 0: off, 1: on */
    unsigned char ucRfMode;        /**< 0: 1xRTT, 1: EVDO, 2: SHDR */
    unsigned char ucAntennaBitmap; /**< Bit0: Rx main, Bit1: Rx diversity, Bit2: Tx path; 0: invalid, 1: valid */
    unsigned char ucSampleRate;    /**< 0: 8x, 1: 4x, 2: 2x, 3: 1x */
    unsigned char ucLogMode;       /**< 0: assert mode, 1: period mode */
    unsigned short usLogPeriod;    /**< unit: half slot or half PCG */
    unsigned short usLogOffset1;   /**< unit: half slot or half PCG */
    unsigned short usLogOffset2;   /**< unit: chip */
    unsigned short usLogLength1;   /**< unit: half slot or half PCG */
    unsigned short usLogLength2;   /**< unit: chip */
} LOG_IQ_REQ;

/**
 * \ingroup C2KStruct
 * \details TX AGC configuration parameters
 */
typedef struct
{
    C2K_RF_MODE rfMode;        /**< RF mode ( 0: 1xRTT, 1: EVDO ) */
    C2K_CTRL_MODE ctrlMode;    /**< control mode ( 0: auto mode, 2: manual mode ) */
    unsigned char ucPaMode;    /**< PA mode ( 0: high, 1: middle, 2: low ) */
    double prf;                /**< expected TX power ( unit: dBm ) */
    double paGain;             /**< PA gain ( unit: dB ) */
    double coupleLoss;         /**< coupler loss ( unit: dB ) */
    unsigned char ucVm1;       /**< VM1 of PA control */
    unsigned char ucVm2;       /**< VM2 of PA control */
    unsigned short usVoltCtrl; /**< PA voltage ( unit: mV ) */
    unsigned short ddpcCtrl;   /**< DDPC control ( 0: bypass DDPC, 6: enable DDPC loop, 7: enable DDPC measurement ) */
} TXAGC_CONFIG_REQ;

/**
 * \ingroup C2KStruct
 * \details RSSI measured by target
 */
typedef struct
{
    double dMainPower;    /**< main path power ( unit: dBm ) */
    double dMainDigiGain; /**< unused */
    int nMainBitSel;      /**< unused */
    int nMainDigiGain;    /**< unused */
    int nMainGainState;   /**< unused */
    double dDivPower;     /**< diversity path power ( unit: dBm ) */
    double dDivDigiGain;  /**< unused */
    int nDivBitSel;       /**< unused */
    int nDivDigiGain;     /**< unused */
    int nDivGainState;    /**< unused */
} C2K_RSSI_CNF;

/**
 * \ingroup C2KStruct
 * \details FHC common parameters
 */
typedef struct
{
    unsigned short nTxRxDelay;        /**< RX delay offset to TX ( unit: microsecond ) */
    unsigned short nTxStepLength;     /**< TX power step length ( unit: microsecond ) */
    unsigned short nTxRetuneLength;   /**< TX frequency retune length ( unit: microsecond ) */
    unsigned short nRxStepLength;     /**< RX power step length ( unit: microsecond ) */
    unsigned short nRxRetuneLength;   /**< RX frequency retune length ( unit: microsecond ) */
    unsigned char caVm1[22][3];       /**< VM1 of PA control, indexed by [band:22][PA mode:3] */
    unsigned char caVm2[22][3];       /**< VM2 of PA control, indexed by [band:22][PA mode:3] */
    unsigned short naVoltCtrl[22][8]; /**< PA voltage, indexed by [band:22][PA section:8] */
} FHC_COMMON_REQ;

/**
 * \ingroup C2KStruct
 * \details FHC TX parameters of one step
 */
typedef struct
{
    unsigned char cRetuneInd;    /**< retune indicator of the step ( 0: no, 1: yes ) */
    unsigned char cBand;         /**< band class number */
    unsigned short nChannel;     /**< channel number */
    unsigned char cPaMode;       /**< PA mode ( 0: high, 1: middle, 2: low ) */
    unsigned char cSectionIndex; /**< PA section index ( 0 ~ 7 ) */
    double prf;                  /**< expected TX power ( unit: dBm ) */
    double paGain;               /**< PA gain ( unit: dB ) */
    double coupleLoss;           /**< coupler loss ( unit: dB ) */
} FHC_TX_FREQ_STEP;

/**
 * \ingroup C2KStruct
 * \details FHC TX step parameters
 */
typedef struct
{
    unsigned short usTxStepNum;                     /**< number of TX steps in array */
    FHC_TX_FREQ_STEP txFreqSteps[C2K_FHC_MAX_STEP]; /**< TX steps array */
} FHC_TX_REQ;

/**
 * \ingroup C2KStruct
 * \details FHC RX parameters of one step
 */
typedef struct
{
    unsigned char cRetuneInd; /**< retune indicator of the step ( 0: no, 1: yes ) */
    unsigned char cBand;      /**< band class number */
    unsigned short nChannel;  /**< channel number */
    /**
     * <pre>
     * bit2 ~ bit0: LNA mode index
     * bit3: RX power mode ( 0: high power mode, 1: low power mode )
     * bit6 ~ bit4: bit map of RX path indicator, |SHDR path|diversity path|main path| ( 1: enabled, 0: disabled )
     * </pre>
     */
    unsigned char cLnaMode;
    double cellPower;         /**< unused, input cell power level */
} FHC_RX_FREQ_STEP;

/**
 * \ingroup C2KStruct
 * \details FHC RX step parameters
 */
typedef struct
{
    unsigned short usRxStepNum;                     /**< number of RX steps in array */
    FHC_RX_FREQ_STEP rxFreqSteps[C2K_FHC_MAX_STEP]; /**< RX steps array */
} FHC_RX_REQ;

/**
 * \ingroup C2KStruct
 * \details FHC request parameters
 */
typedef struct
{
    unsigned char cReqType;   /**< request type ( 0: common, 1: TX, 2: RX ) */
    unsigned char cReqNum;    /**< number of requests */
    unsigned char cReqIndex;  /**< index of request */
    FHC_COMMON_REQ commonReq; /**< common parameter */
    FHC_TX_REQ txReq;         /**< TX parameter */
    FHC_RX_REQ rxReq;         /**< RX parameter */
} C2K_FHC_REQ;

/**
 * \ingroup C2KStruct
 * \details FHC TX confirmation, power detector measurement result
 */
typedef struct
{
    unsigned short usCnfNum;           /**< number of results in array */
    double pdResult[C2K_FHC_MAX_STEP]; /**< power detector result ( unit: dBm ) */
} FHC_TX_CNF;

/**
 * \ingroup C2KStruct
 * \details FHC RX confirmation, power measurement result
 */
typedef struct
{
    unsigned short usCnfNum;               /**< number of results in array */
    double mainDigiGain[C2K_FHC_MAX_STEP]; /**< main path power ( unit: dBm ) */
    double divDigiGain[C2K_FHC_MAX_STEP];  /**< diversity path power ( unit: dBm ) */
    char caTemperature[C2K_FHC_MAX_STEP];  /**< unused, temperature */
} FHC_RX_CNF;

/**
 * \ingroup C2KStruct
 * \details FHC confirmation result
 */
typedef struct
{
    unsigned char cCnfId;    /**< confirmation ID */
    unsigned char cCnfNum;   /**< number of confirmations */
    unsigned char cCnfIndex; /**< index of confirmation */
    FHC_TX_CNF txCnf;        /**< TX confirmation */
    FHC_RX_CNF rxCnf;        /**< RX confirmation */
} C2K_FHC_CNF;

/*********** NVRAM Data Structure Definition for Calibration ***********/
/**
 * \ingroup C2KStruct
 * \details MEID get/set parameters
 */
typedef struct
{
    unsigned int idType; /**< ID type ( 0: MEID, 1: ESN ) */
    char meid[17];       /**< Null-terminated 14 hexadecimal format MEID string */
    char esn[9];         /**< Null-terminated 8 hexadecimal format ESN string */
} MEID_TABLE, *LPMEID_TABLE;

/**
 * \ingroup C2KStruct
 * \details AFC calibration data
 */
typedef struct
{
    unsigned short initDacValue; /**< initial DAC value */
    short afcSlopeInv;           /**< reciprocal of AFC slope ( resolution: 1/4096 ) */
    unsigned int capId;          /**< CAP ID */
} AFC_TABLE_SEG, *LPAFC_TABLE_SEG;

/**
 * \ingroup C2KStruct
 * \details Temperature and ADC value pair
 */
typedef struct
{
    char tempCelsius;        /**< temperature ( unit: degree Celsius ) */
    unsigned short adcValue; /**< ADC value */
} TEMPERATURE_PAIR;

/**
 * \ingroup C2KStruct
 * \details Temperature calibration data
 */
typedef struct
{
    TEMPERATURE_PAIR tempPair[8]; /**< temperature array */
} TEMPERATURE_TABLE_SEG, *LPTEMPERATURE_TABLE_SEG;

/**
 * \ingroup C2KStruct
 * \details RX compensation data of one channel
 */
typedef struct
{
    unsigned short channel;           /**< channel number */
    double adjData[C2K_LNA_MODE_NUM]; /**< compensation value */
} RXFREQADJ_PAIR;

/**
 * \ingroup C2KStruct
 * \details RX channel compensation data
 */
typedef struct
{
    RXFREQADJ_PAIR freqAdj[16]; /**< RX frequency compensation array */
} RXFREQADJ_TABLE_SEG, *LPRXFREQADJ_TABLE_SEG;

/**
 * \ingroup C2KStruct
 * \details RX channel compensation data of all paths
 */
typedef struct
{
    RXFREQADJ_TABLE_SEG mainTable; /**< table of main path */
    RXFREQADJ_TABLE_SEG divTable;  /**< table of diversity path */
} RXFREQADJ_TABLE_SEG_ALL, *LPRXFREQADJ_TABLE_SEG_ALL;

/**
 * \ingroup C2KStruct
 * \details RX compensation data of one temperature
 */
typedef struct
{
    char tempCelsius; /**< temperature ( unit: degree Celsius ) */
    double adjData;   /**< compensation value */
} RXTEMPADJ_PAIR;

/**
 * \ingroup C2KStruct
 * \details RX temperature compensation data
 */
typedef struct
{
    RXTEMPADJ_PAIR tempAdj[8]; /**< RX temperature compensation array */
} RXTEMPADJ_TABLE_SEG, *LPRXTEMPADJ_TABLE_SEG;

/**
 * \ingroup C2KStruct
 * \details RX temperature compensation data of all paths
 */
typedef struct
{
    RXTEMPADJ_TABLE_SEG mainTable; /**< table of main path */
    RXTEMPADJ_TABLE_SEG divTable;  /**< table of diversity path */
} RXTEMPADJ_TABLE_SEG_ALL, *LPRXTEMPADJ_TABLE_SEG_ALL;

/**
 * \ingroup C2KStruct
 * \details TX setting of one PA section
 */
typedef struct
{
    double paGain;           /**< PA gain ( unit: dB ) */
    double prf;              /**< the power of PA 8 levels ( unit: dBm ) */
    unsigned short paMode;   /**< PA mode ( 0: high, 1: middle, 2: low ) */
    unsigned short vm1;      /**< VM1 of PA control */
    unsigned short vm2;      /**< VM2 of PA control */
    unsigned short voltCtrl; /**< PA voltage ( unit: mV ) */
} PA_SECTION;

/**
 * \ingroup C2KStruct
 * \details TX hysteresis
 */
typedef struct
{
    double start; /**< PA hysteresis start ( unit: dBm ) */
    double end;   /**< PA hysteresis end ( unit: dBm ) */
} TX_HYSTERESIS;

/**
 * \ingroup C2KStruct
 * \details TX AGC calibration data
 */
typedef struct
{
    unsigned char paSectionNum; /**< number of PA sections */
    PA_SECTION paSection[8];    /**< PA section array, index is from 0 to 7, TX power and PA mode order is from high to low */
    TX_HYSTERESIS txHyst[2];    /**< PA hysteresis ( txHyst[0]: PA high <-> PA middle, txHyst[1]: PA middle <-> PA low ) */
    short paPhaseComp[3];       /**< PA phase compensation ( [0]: PA high, [1]: PA middle, [2]: PA low ) */
} TXAGC_TABLE_SEG, *LPTXAGC_TABLE_SEG;

/**
 * \ingroup C2KStruct
 * \details TX compensation data of one channel
 */
typedef struct
{
    unsigned short channel; /**< channel number */
    double adjData[3];      /**< compensation value */
} TXFREQADJ_PAIR;

/**
 * \ingroup C2KStruct
 * \details TX channel compensation data
 */
typedef struct
{
    TXFREQADJ_PAIR freqAdj[16]; /**< TX frequency compensation array */
} TXFREQADJ_TABLE_SEG, *LPTXFREQADJ_TABLE_SEG;

/**
 * \ingroup C2KStruct
 * \details TX compensation data of one temperature
 */
typedef struct
{
    char tempCelsius;  /**< temperature ( unit: degree Celsius ) */
    double adjData[3]; /**< compensation value */
} TXTEMPADJ_PAIR;

/**
 * \ingroup C2KStruct
 * \details TX temperature compensation data
 */
typedef struct
{
    TXTEMPADJ_PAIR tempAdj[8]; /**< TX temperature compensation array */
} TXTEMPADJ_TABLE_SEG, *LPTXTEMPADJ_TABLE_SEG;

/**
 * \ingroup C2KStruct
 * \details TX coupler loss calibration data
 */
typedef struct
{
    double coupleLoss[3]; /**< coupler loss ( [0]: PA high, [1]: PA middle, [2]: PA low, unit: dB ) */
} TXCOUPLELOSS_TABLE_SEG, *LPTXCOUPLELOSS_TABLE_SEG;

/**
 * \ingroup C2KStruct
 * \details Coupler loss compensation data of one channel
 */
typedef struct
{
    unsigned short channel; /**< channel number */
    double adjData;         /**< compensation value */
} TXCOUPLELOSSFREQADJ_PAIR;

/**
 * \ingroup C2KStruct
 * \details Coupler loss channel compensation data
 */
typedef struct
{
    TXCOUPLELOSSFREQADJ_PAIR freqAdj[16]; /**< coupler loss frequency compensation array */
} TXCOUPLELOSSFREQADJ_TABLE_SEG, *LPTXCOUPLELOSSFREQADJ_TABLE_SEG;

/**
 * \ingroup C2KStruct
 * \details Deprecated
 */
typedef struct
{
    unsigned char sysBandClass;
    unsigned char supported;
    unsigned int subClass;
} SUPPORTED_BAND_CLASS_T;

/**
 * \ingroup C2KStruct
 * \details Deprecated
 */
typedef struct
{
    /* BAND A BPI mask */
    unsigned int MASK_BAND_A_PR1;
    unsigned int MASK_BAND_A_PR2;
    unsigned int MASK_BAND_A_PR2B;
    unsigned int MASK_BAND_A_PR3;
    unsigned int MASK_BAND_A_PR3A;
    unsigned int MASK_BAND_A_PT1;
    unsigned int MASK_BAND_A_PT2;
    unsigned int MASK_BAND_A_PT2B;
    unsigned int MASK_BAND_A_PT3;
    unsigned int MASK_BAND_A_PT3A;
    unsigned int MASK_BAND_A_RXD_PR1;
    unsigned int MASK_BAND_A_RXD_PR2;
    unsigned int MASK_BAND_A_RXD_PR2B;
    unsigned int MASK_BAND_A_RXD_PR3;
    unsigned int MASK_BAND_A_RXD_PR3A;

    /* BAND B BPI mask */
    unsigned int MASK_BAND_B_PR1;
    unsigned int MASK_BAND_B_PR2;
    unsigned int MASK_BAND_B_PR2B;
    unsigned int MASK_BAND_B_PR3;
    unsigned int MASK_BAND_B_PR3A;
    unsigned int MASK_BAND_B_PT1;
    unsigned int MASK_BAND_B_PT2;
    unsigned int MASK_BAND_B_PT2B;
    unsigned int MASK_BAND_B_PT3;
    unsigned int MASK_BAND_B_PT3A;
    unsigned int MASK_BAND_B_RXD_PR1;
    unsigned int MASK_BAND_B_RXD_PR2;
    unsigned int MASK_BAND_B_RXD_PR2B;
    unsigned int MASK_BAND_B_RXD_PR3;
    unsigned int MASK_BAND_B_RXD_PR3A;

    /* BAND C BPI mask */
    unsigned int MASK_BAND_C_PR1;
    unsigned int MASK_BAND_C_PR2;
    unsigned int MASK_BAND_C_PR2B;
    unsigned int MASK_BAND_C_PR3;
    unsigned int MASK_BAND_C_PR3A;
    unsigned int MASK_BAND_C_PT1;
    unsigned int MASK_BAND_C_PT2;
    unsigned int MASK_BAND_C_PT2B;
    unsigned int MASK_BAND_C_PT3;
    unsigned int MASK_BAND_C_PT3A;
    unsigned int MASK_BAND_C_RXD_PR1;
    unsigned int MASK_BAND_C_RXD_PR2;
    unsigned int MASK_BAND_C_RXD_PR2B;
    unsigned int MASK_BAND_C_RXD_PR3;
    unsigned int MASK_BAND_C_RXD_PR3A;

    /* Band A Gate BPI mask */
    unsigned int MASK_BAND_A_PRG1;       /* Main Rx On */
    unsigned int MASK_BAND_A_PRG2;
    unsigned int MASK_BAND_A_PRG2B;
    unsigned int MASK_BAND_A_PRG3;       /* Main Rx Off */
    unsigned int MASK_BAND_A_PRG3A;
    unsigned int MASK_BAND_A_PTG1;       /* Tx On */
    unsigned int MASK_BAND_A_PTG2;
    unsigned int MASK_BAND_A_PTG2B;
    unsigned int MASK_BAND_A_PTG3;       /* Tx Off */
    unsigned int MASK_BAND_A_PTG3A;
    unsigned int MASK_BAND_A_RXD_PRG1;   /* Diversity Rx On */
    unsigned int MASK_BAND_A_RXD_PRG2;
    unsigned int MASK_BAND_A_RXD_PRG2B;
    unsigned int MASK_BAND_A_RXD_PRG3;   /* Diversity Rx Off */
    unsigned int MASK_BAND_A_RXD_PRG3A;

    /* Band B Gate BPI mask */
    unsigned int MASK_BAND_B_PRG1;       /* Main Rx On */
    unsigned int MASK_BAND_B_PRG2;
    unsigned int MASK_BAND_B_PRG2B;
    unsigned int MASK_BAND_B_PRG3;       /* Main Rx Off */
    unsigned int MASK_BAND_B_PRG3A;
    unsigned int MASK_BAND_B_PTG1;       /* Tx On */
    unsigned int MASK_BAND_B_PTG2;
    unsigned int MASK_BAND_B_PTG2B;
    unsigned int MASK_BAND_B_PTG3;       /* Tx Off */
    unsigned int MASK_BAND_B_PTG3A;
    unsigned int MASK_BAND_B_RXD_PRG1;   /* Diversity Rx On */
    unsigned int MASK_BAND_B_RXD_PRG2;
    unsigned int MASK_BAND_B_RXD_PRG2B;
    unsigned int MASK_BAND_B_RXD_PRG3;   /* Diversity Rx Off */
    unsigned int MASK_BAND_B_RXD_PRG3A;

    /* Band C Gate BPI mask */
    unsigned int MASK_BAND_C_PRG1;       /* Main Rx On */
    unsigned int MASK_BAND_C_PRG2;
    unsigned int MASK_BAND_C_PRG2B;
    unsigned int MASK_BAND_C_PRG3;       /* Main Rx Off */
    unsigned int MASK_BAND_C_PRG3A;
    unsigned int MASK_BAND_C_PTG1;       /* Tx On */
    unsigned int MASK_BAND_C_PTG2;
    unsigned int MASK_BAND_C_PTG2B;
    unsigned int MASK_BAND_C_PTG3;       /* Tx Off */
    unsigned int MASK_BAND_C_PTG3A;
    unsigned int MASK_BAND_C_RXD_PRG1;   /* Diversity Rx On */
    unsigned int MASK_BAND_C_RXD_PRG2;
    unsigned int MASK_BAND_C_RXD_PRG2B;
    unsigned int MASK_BAND_C_RXD_PRG3;   /* Diversity Rx Off */
    unsigned int MASK_BAND_C_RXD_PRG3A;

    /* Power Control mask */
    unsigned int MASK_PRPC1;       /* Main Rx On */
    unsigned int MASK_PRPC2;
    unsigned int MASK_PRPC2B;
    unsigned int MASK_PRPC3;       /* Main Rx Off */
    unsigned int MASK_PRPC3A;
    unsigned int MASK_PTPC1;       /* Tx On */
    unsigned int MASK_PTPC2;
    unsigned int MASK_PTPC2B;
    unsigned int MASK_PTPC3;       /* Tx Off */
    unsigned int MASK_PTPC3A;
    unsigned int MASK_RXD_PRPC1;   /* Diversity Rx On */
    unsigned int MASK_RXD_PRPC2;
    unsigned int MASK_RXD_PRPC2B;
    unsigned int MASK_RXD_PRPC3;   /* Diversity Rx Off */
    unsigned int MASK_RXD_PRPC3A;
} BPI_MASK_T;

/**
 * \ingroup C2KStruct
 * \details Deprecated
 */
typedef struct
{
    /* BAND A BPI data */
    unsigned int DATA_BAND_A_PR1;
    unsigned int DATA_BAND_A_PR2;
    unsigned int DATA_BAND_A_PR2B;
    unsigned int DATA_BAND_A_PR3;
    unsigned int DATA_BAND_A_PR3A;
    unsigned int DATA_BAND_A_PT1;
    unsigned int DATA_BAND_A_PT2;
    unsigned int DATA_BAND_A_PT2B;
    unsigned int DATA_BAND_A_PT3;
    unsigned int DATA_BAND_A_PT3A;
    unsigned int DATA_BAND_A_RXD_PR1;
    unsigned int DATA_BAND_A_RXD_PR2;
    unsigned int DATA_BAND_A_RXD_PR2B;
    unsigned int DATA_BAND_A_RXD_PR3;
    unsigned int DATA_BAND_A_RXD_PR3A;

    /* BAND B BPI data */
    unsigned int DATA_BAND_B_PR1;
    unsigned int DATA_BAND_B_PR2;
    unsigned int DATA_BAND_B_PR2B;
    unsigned int DATA_BAND_B_PR3;
    unsigned int DATA_BAND_B_PR3A;
    unsigned int DATA_BAND_B_PT1;
    unsigned int DATA_BAND_B_PT2;
    unsigned int DATA_BAND_B_PT2B;
    unsigned int DATA_BAND_B_PT3;
    unsigned int DATA_BAND_B_PT3A;
    unsigned int DATA_BAND_B_RXD_PR1;
    unsigned int DATA_BAND_B_RXD_PR2;
    unsigned int DATA_BAND_B_RXD_PR2B;
    unsigned int DATA_BAND_B_RXD_PR3;
    unsigned int DATA_BAND_B_RXD_PR3A;

    /* BAND C BPI data */
    unsigned int DATA_BAND_C_PR1;
    unsigned int DATA_BAND_C_PR2;
    unsigned int DATA_BAND_C_PR2B;
    unsigned int DATA_BAND_C_PR3;
    unsigned int DATA_BAND_C_PR3A;
    unsigned int DATA_BAND_C_PT1;
    unsigned int DATA_BAND_C_PT2;
    unsigned int DATA_BAND_C_PT2B;
    unsigned int DATA_BAND_C_PT3;
    unsigned int DATA_BAND_C_PT3A;
    unsigned int DATA_BAND_C_RXD_PR1;
    unsigned int DATA_BAND_C_RXD_PR2;
    unsigned int DATA_BAND_C_RXD_PR2B;
    unsigned int DATA_BAND_C_RXD_PR3;
    unsigned int DATA_BAND_C_RXD_PR3A;

    /* Band A TXGATE data */
    unsigned int DATA_BAND_A_PRG1;       /* Main Rx On */
    unsigned int DATA_BAND_A_PRG2;
    unsigned int DATA_BAND_A_PRG2B;
    unsigned int DATA_BAND_A_PRG3;       /* Main Rx Off */
    unsigned int DATA_BAND_A_PRG3A;
    unsigned int DATA_BAND_A_PTG1;       /* Tx On */
    unsigned int DATA_BAND_A_PTG2;
    unsigned int DATA_BAND_A_PTG2B;
    unsigned int DATA_BAND_A_PTG3;       /* Tx Off */
    unsigned int DATA_BAND_A_PTG3A;
    unsigned int DATA_BAND_A_RXD_PRG1;   /* Diversity Rx On */
    unsigned int DATA_BAND_A_RXD_PRG2;
    unsigned int DATA_BAND_A_RXD_PRG2B;
    unsigned int DATA_BAND_A_RXD_PRG3;   /* Diversity Rx Off */
    unsigned int DATA_BAND_A_RXD_PRG3A;

    /* Band B TXGATE data */
    unsigned int DATA_BAND_B_PRG1;       /* Main Rx On */
    unsigned int DATA_BAND_B_PRG2;
    unsigned int DATA_BAND_B_PRG2B;
    unsigned int DATA_BAND_B_PRG3;       /* Main Rx Off */
    unsigned int DATA_BAND_B_PRG3A;
    unsigned int DATA_BAND_B_PTG1;       /* Tx On */
    unsigned int DATA_BAND_B_PTG2;
    unsigned int DATA_BAND_B_PTG2B;
    unsigned int DATA_BAND_B_PTG3;       /* Tx Off */
    unsigned int DATA_BAND_B_PTG3A;
    unsigned int DATA_BAND_B_RXD_PRG1;   /* Diversity Rx On */
    unsigned int DATA_BAND_B_RXD_PRG2;
    unsigned int DATA_BAND_B_RXD_PRG2B;
    unsigned int DATA_BAND_B_RXD_PRG3;   /* Diversity Rx Off */
    unsigned int DATA_BAND_B_RXD_PRG3A;

    /* Band C TXGATE data */
    unsigned int DATA_BAND_C_PRG1;       /* Main Rx On */
    unsigned int DATA_BAND_C_PRG2;
    unsigned int DATA_BAND_C_PRG2B;
    unsigned int DATA_BAND_C_PRG3;       /* Main Rx Off */
    unsigned int DATA_BAND_C_PRG3A;
    unsigned int DATA_BAND_C_PTG1;       /* Tx On */
    unsigned int DATA_BAND_C_PTG2;
    unsigned int DATA_BAND_C_PTG2B;
    unsigned int DATA_BAND_C_PTG3;       /* Tx Off */
    unsigned int DATA_BAND_C_PTG3A;
    unsigned int DATA_BAND_C_RXD_PRG1;   /* Diversity Rx On */
    unsigned int DATA_BAND_C_RXD_PRG2;
    unsigned int DATA_BAND_C_RXD_PRG2B;
    unsigned int DATA_BAND_C_RXD_PRG3;   /* Diversity Rx Off */
    unsigned int DATA_BAND_C_RXD_PRG3A;

    /* TX Power Control data */
    unsigned int DATA_PRPC1;       /* Main Rx On */
    unsigned int DATA_PRPC2;
    unsigned int DATA_PRPC2B;
    unsigned int DATA_PRPC3;       /* Main Rx Off */
    unsigned int DATA_PRPC3A;
    unsigned int DATA_PTPC1;       /* Tx On */
    unsigned int DATA_PTPC2;
    unsigned int DATA_PTPC2B;
    unsigned int DATA_PTPC3;       /* Tx Off */
    unsigned int DATA_PTPC3A;
    unsigned int DATA_RXD_PRPC1;   /* Diversity Rx On */
    unsigned int DATA_RXD_PRPC2;
    unsigned int DATA_RXD_PRPC2B;
    unsigned int DATA_RXD_PRPC3;   /* Diversity Rx Off */
    unsigned int DATA_RXD_PRPC3A;
} BPI_DATA_T;

/**
 * \ingroup C2KStruct
 * \details Deprecated
 */
typedef struct
{
    // RF Window timing
    unsigned short TC_PR1;
    unsigned short TC_PR2;
    unsigned short TC_PR2B;

    unsigned short TC_PR3;
    unsigned short TC_PR3A;

    unsigned short TC_RXD_PR1;
    unsigned short TC_RXD_PR2;
    unsigned short TC_RXD_PR2B;

    unsigned short TC_RXD_PR3;
    unsigned short TC_RXD_PR3A;

    unsigned short TC_PT1;
    unsigned short TC_PT2;
    unsigned short TC_PT2B;

    unsigned short TC_PT3;
    unsigned short TC_PT3A;

    // RF Gate timing
    unsigned short TC_PRG1;
    unsigned short TC_PRG2;
    unsigned short TC_PRG2B;

    unsigned short TC_PRG3;
    unsigned short TC_PRG3A;

    unsigned short TC_RXD_PRG1;
    unsigned short TC_RXD_PRG2;
    unsigned short TC_RXD_PRG2B;

    unsigned short TC_RXD_PRG3;
    unsigned short TC_RXD_PRG3A;

    unsigned short TC_PTG1;
    unsigned short TC_PTG2;
    unsigned short TC_PTG2B;

    unsigned short TC_PTG3;
    unsigned short TC_PTG3A;

    // RF TX Power Control timing
    short TC_PRPC1;
    short TC_PRPC2;
    short TC_PRPC2B;

    short TC_PRPC3;
    short TC_PRPC3A;

    short TC_RXD_PRPC1;
    short TC_RXD_PRPC2;
    short TC_RXD_PRPC2B;

    short TC_RXD_PRPC3;
    short TC_RXD_PRPC3A;

    short TC_PTPC1;
    short TC_PTPC2;
    short TC_PTPC2B;

    short TC_PTPC3;
    short TC_PTPC3A;
} BPI_TIMING_T;

/**
 * \ingroup C2KStruct
 * \details Deprecated
 */
typedef struct
{
    short dc2DcSettlingTime;
} PA_TIMING_T;

/**
 * \ingroup C2KStruct
 * \details Deprecated
 */
typedef struct
{
    unsigned char BAND_A_CHANNEL_SEL;
    unsigned char BAND_B_CHANNEL_SEL;
    unsigned char BAND_C_CHANNEL_SEL;
} RX_LNA_PORT_SEL_T;

/**
 * \ingroup C2KStruct
 * \details Deprecated
 */
typedef struct
{
    unsigned char BAND_A_OUTPUT_SEL;
    unsigned char BAND_B_OUTPUT_SEL;
    unsigned char BAND_C_OUTPUT_SEL;
} TX_PATH_SEL_T;

/**
 * \ingroup C2KStruct
 * \details Deprecated
 */
typedef struct
{
    unsigned char structVersion; //reserved
    unsigned char isDataUpdate; //reserved
    SUPPORTED_BAND_CLASS_T supportedBand[3];
    BPI_MASK_T bpiMask;
    BPI_DATA_T bpiData;
    BPI_TIMING_T bpiTiming;
    PA_TIMING_T paTiming;
    RX_LNA_PORT_SEL_T rxLnaPortSel;
    RX_LNA_PORT_SEL_T rxDivLnaPortSel;
    TX_PATH_SEL_T txPathSel;
    unsigned char rxDiversityEnable;
    unsigned char rxDiversityTestEnable;
    unsigned char paVddPmuControlEnable;
    unsigned char paVddBattControlEnable;
    unsigned char paVddDc2DcControlEnable;
    unsigned char customTmEnable;
} CUSTOMDATA_TABLE, *LPCUSTOMDATA_TABLE;

/**
 * \ingroup C2KStruct
 * \details Deprecated
 */
typedef struct
{
    char rfOption[21];
    char rfDriverRev[11];
} RFVERSION_TABLE, *LPRFVERSION_TABLE;

/**
 * \ingroup C2KStruct
 * \details NSFT list mode steps parameter
 */
typedef struct
{
    unsigned int uCount;                                 /**< count of list steps */
    bool bAFCEnable;                                     /**< AFC enabled ( true: enabled, false: disabled ) */
    unsigned int uIndex[C2K_NSFT_LIST_MODE_NUM];         /**< index of step */
    unsigned int uProtocol[C2K_NSFT_LIST_MODE_NUM];      /**< number of RSSI test levels */
    unsigned int uOffset[C2K_NSFT_LIST_MODE_NUM];        /**< occupied frames of the step */
    unsigned int uBand[C2K_NSFT_LIST_MODE_NUM];          /**< band class number of the step */
    unsigned int uChannel[C2K_NSFT_LIST_MODE_NUM];       /**< channel number of the step */
    unsigned int uWashCode[C2K_NSFT_LIST_MODE_NUM];      /**< traffic channel walsh code of the step */
    unsigned int uRadioConfig[C2K_NSFT_LIST_MODE_NUM];   /**< reverse radio configuration of the step */
    unsigned int uNumFrames[C2K_NSFT_LIST_MODE_NUM];     /**< number of FER test frames of the step */
    unsigned int uPowerCtrlMode[C2K_NSFT_LIST_MODE_NUM]; /**< unused, power control mode */
    float fTxPower[C2K_NSFT_LIST_MODE_NUM];              /**< expected maximum TX power of the step ( unit: dBm ) */
} NSTLISTMODE_PARMS, *LPNSTLISTMODE_PARMS;

/**
 * \ingroup C2KStruct
 * \details Deprecated
 */
typedef struct
{
    unsigned char tasEnable;
    unsigned char tasInitAntIndex;
    unsigned char forceTxAntEnable;
    unsigned char forceTxAntIndex;
    unsigned int tasMask;
    unsigned int tasData[3][7];
} TASDATA_TABLE, *LPTASDATA_TABLE;

/*********** MT6176 Data Structure ***********/
/**
 * \ingroup C2KStruct
 * \details The enumeration for how RF data initialize
 */
typedef enum
{
    C2K_CAL_INIT_NVRAM,  /**< initialize from NVRAM data */
    C2K_CAL_INIT_DEFAULT /**< initialize from default data */
} C2K_CAL_INIT_TYPE;

/**
 * \ingroup C2KStruct
 * \details Deprecated
 */
typedef enum
{
    C2K_NVRAM_DATA_RF,
    C2K_NVRAM_DATA_POC
} C2K_NVRAM_DATA_TYPE;

/**
 * \ingroup C2KStruct
 * \details RX channel and temperature compensation data
 */
typedef struct
{
    unsigned short channel[16]; /**< channel number */
    char temp[8];               /**< temperature */
    double comp[8][8][16];      /**< compensation value, indexed by [temp celsius][LNA mode][channel] */
} RX_LOSS_TABLE, *LPRX_LOSS_TABLE;

/**
 * \ingroup C2KStruct
 * \details TX PA gain channel and temperature compensation data
 */
typedef struct
{
    unsigned short channel[16]; /**< channel number */
    char temp[8];               /**< temperature */
    double comp[3][8][16];      /**< compensation value, indexed by [PA mode][temp celsius][channel] */
} PA_GAIN_COMP_TABLE, *LPPA_GAIN_COMP_TABLE;

/**
 * \ingroup C2KStruct
 * \details Coupler loss channel and temperature compensation data
 */
typedef struct
{
    unsigned short channel[16]; /**< channel number */
    char temp[8];               /**< temperature */
    double comp[3][8][16];      /**< compensation value, indexed by [PA mode][temp celsius][channel] */
} COUPLER_LOSS_COMP_TABLE, *LPCOUPLER_LOSS_COMP_TABLE;

/**
 * \ingroup C2KStruct
 * \details AFC configuration parameters
 */
typedef struct
{
    C2K_RF_MODE rfMode;     /**< RF mode ( 0: 1xRTT, 1: EVDO ) */
    unsigned int ctrlMode;  /**< control mode ( 0: auto mode, 1: manual mode ) */
    bool capIdValid;        /**< indicate if capId value is used by target ( true: used, false: unused ) */
    unsigned short capId;   /**< CAP ID */
    bool dacValid;          /**< indicate if initDac value is used by target ( true: used, false: unused ) */
    unsigned short initDac; /**< initial DAC value */
    bool foeValid;          /**< unused, set to false */
    short foePpb;           /**< unused, set to 0 */
} AFC_CONFIG_REQ;

/**
 * \ingroup C2KStruct
 * \details AFC S-Curve coefficients
 */
typedef struct
{
    double c0;  /**< constant coefficient */
    double c1;  /**< 1 order coefficient */
    double c2;  /**< 2 order coefficient */
    double c3;  /**< 3 order coefficient */
} S_CURVE_TABLE, *LPS_CURVE_TABLE;

/**
 * \ingroup C2KStruct
 * \details Modem SW version information
 */
typedef struct
{
    char    BB_CHIP[64];   /**< BaseBand chip version */
    char    SW_VER[100];   /**< S/W version */
    char    SW_BRANCH[64]; /**< Build branch information */
    char    SW_FLAVOR[64]; /**< Build flavor information */
} C2K_VER_INFO;

/**
 * \ingroup C2KStruct
 * \details TX maximum power back off hysteresis
 */
typedef struct
{
    short temp;          /**< temperature */
    double powerBackoff; /**< maximum power back off value */
} TX_POWER_BACKOFF;

/**
 * \ingroup C2KStruct
 * \details TX maximum power back off data
 */
typedef struct
{
    TX_POWER_BACKOFF txPowerBackoff[C2K_TX_POWER_BACKOFF_NUM]; /**< maximum power back off hysteresis */
} TX_POWER_BACKOFF_TABLE, *LPTX_POWER_BACKOFF_TABLE;

/**
 * \ingroup C2KStruct
 * \details Single RSSI measurement result
 */
typedef struct
{
    unsigned char ok;        /**< status */
    double rssiMain;         /**< main path RSSI ( unit: dBm ) */
    double rssiRxd;          /**< diversity path RSSI ( unit: dBm ) */
    unsigned short pnOffset; /**< PN offset */
    short strength;          /**< signal strength */
} C2K_NSFT_RSSI_CNF;

/**
 * \ingroup C2KStruct
 * \details NSFT list mode RSSI measurement result
 */
typedef struct
{
    unsigned short listCount;                    /**< count of list steps */
    unsigned short rssiCount;                    /**< count of RSSI levels */
    double rssiMain[C2K_NSFT_LIST_MODE_NUM][10]; /**< main path RSSI levels result ( unit: dBm ) */
    double rssiRxd[C2K_NSFT_LIST_MODE_NUM][10];  /**< diversity path RSSI levels result ( unit: dBm ) */
} C2K_NSFT_LIST_MODE_RSSI_CNF;

/**
 * \ingroup C2KStruct
 * \details NSFT list mode TX power test levels
 */
typedef struct
{
    unsigned char powerCount; /**< count of TX power levels */
    double power[10];         /**< TX power levels to be tested ( unit: dBm ) */
} C2K_NSFT_LIST_MODE_SET_POWER_REQ;


/*************************** M21/M50/M70 series Data Structure ****************************/
#define C2K_FHC_MAX_TX_STEP                 1000
#define C2K_FHC_MAX_RX_STEP                 1000
#define C2K_NSFT_LIST_MODE_TX_PWR_COUNT     20
#define C2K_NSFT_LIST_MODE_RSSI_COUNT       20

/**
 * \ingroup C2KStruct
 * \details The enumeration for transmit signal type
 */
typedef enum
{
    C2K_TX_SIG_RF_TONE,    /**< continuous wave signal generated by RF */
    C2K_TX_SIG_BB_TONE,    /**< continuous wave signal generated by baseband */
    C2K_TX_SIG_1xRTT,      /**< 1xRTT modulation signal */
    C2K_TX_SIG_EVDO_PILOT, /**< EVDO pilot modulation signal */
    C2K_TX_SIG_EVDO_ST2    /**< EVDO subtype2 modulation signal */
} C2K_TX_SIG_TYPE;

/**
 * \ingroup C2KStruct
 * \details The enumeration for PA control mode
 */
typedef enum
{
    C2K_PA_TYPE_APT_MODE = 0, /**< APT control mode */
    C2K_PA_TYPE_DPD_MODE      /**< DPD control mode */
} C2K_PA_TYPE;

/**
 * \ingroup C2KStruct
 * \details The enumeration for transmit power control mode
 */
typedef enum
{
    C2K_POWER_CTRL_MODE_FIX = 0, /**< power is fixed */
    C2K_POWER_CTRL_MODE_MANUAL   /**< power is controlled by C2kTestCmdTxAgcConfig parameters */
} C2K_POWER_CTRL_MODE;

/**
 * \ingroup C2KStruct
 * \details Target capability information
 */
typedef struct
{
    unsigned short rfId;               /**< RF chip ID */
    unsigned short xoType;             /**< crystal type ( 0: VCTCXO, 1: TSX, 2: TCXO ) */
    unsigned int bandSupport;          /**< bit map of band class supported by target, |...|band3|band2|band1|band0| */
    unsigned int rxdBandSupport;       /**< bit map of RXD supported band, |...|band3|band2|band1|band0| */
    unsigned int mipiBandSupport;      /**< bit map of MIPI supported band, |...|band3|band2|band1|band0| */
    unsigned int dpdBandSupport;       /**< bit map of DPD PA supported band, |...|band3|band2|band1|band0| */
    unsigned int rxdPathNum;           /**< number of RXD paths */
    /**
     * <pre>
     * indicate how many frames per TX power step occupys in NSFT list mode ( frame length: 20 milliseconds )
     * 0: per TX power step occupys 2 frames
     * 1: per TX power step occupys 5 frames
     * </pre>
     */
    unsigned int nsftListModeType;
    /**
     * <pre>
     * bit0: indicates using modulation or CW signal for RX calibration ( 0: modulation signal, 1: CW signal )
     * bit1: indicates if target supports M70 series new APIs ( 0: not support, 1: support )
     * bit2: indicates if target supports read/write DPD calibration flag ( 0: not support, 1: support )
     * bit3: indicates if Gen93 supports wide temperature ( 0: not support, 1: support )
     * bit4: indicates if target supports EVDO mode ( 0: support, 1: not support )
     * </pre>
     */
    unsigned int rxCalCW;
} C2kMsCapability;

/**
 * \ingroup C2KStruct
 * \details MIPI code word parameters for set command
 */
typedef struct
{
    unsigned short mipiPort; /**< MIPI port */
    unsigned short reserved; /**< reserved data */
    unsigned int mipiUsid;   /**< MIPI USID */
    unsigned int mipiAddr;   /**< MIPI address */
    unsigned int mipiData;   /**< MIPI data */
} C2kTestCmdSetMIPICodeWord;

/**
 * \ingroup C2KStruct
 * \details MIPI code word parameters for get command
 */
typedef struct
{
    unsigned short mipiPort; /**< MIPI port */
    unsigned short reserved; /**< reserved data */
    unsigned int mipiUsid;   /**< MIPI USID */
    unsigned int mipiAddr;   /**< MIPI address */
} C2kTestCmdGetMIPICodeWord;

/**
 * \ingroup C2KStruct
 * \details MIPI code word data of get command
 */
typedef struct
{
    unsigned int mipiData;   /**< MIPI data */
} C2kTestResultGetMIPICodeWord;

/**
 * \ingroup C2KStruct
 * \details BSI parameters for set command
 */
typedef struct
{
    unsigned short bsiId;    /**< BSI ID */
    unsigned short reserved; /**< reserved data */
    unsigned int bsiAddr;    /**< BSI address */
    unsigned int bsiData;    /**< BSI data */
} C2kTestCmdSetBSI;

/**
 * \ingroup C2KStruct
 * \details BSI parameters for get command
 */
typedef struct
{
    unsigned short bsiId;    /**< BSI ID */
    unsigned short reserved; /**< reserved data */
    unsigned int bsiAddr;    /**< BSI address */
} C2kTestCmdGetBSI;

/**
 * \ingroup C2KStruct
 * \details BSI data of get command
 */
typedef struct
{
    unsigned int bsiData;    /**< BSI data */
} C2kTestResultGetBSI;

/**
 * \ingroup C2KStruct
 * \details 1xRTT TX channel parameters
 */
typedef struct
{
    unsigned char rc;              /**< reverse radio configuration ( 0: RC1, 1: RC2, 2: RC3, 3: RC4, 4: RC5 ) */
    unsigned char chType;          /**< channel type ( 0: access, 1: FCH, 2: FCH + SCH ) */
    unsigned char rate;            /**< data rate ( 0: full rate, 1: 1/2 rate, 2: 1/4 rate, 3: 1/8 rate ) */
    unsigned short txPreamble;     /**< TX preamble ( 0: pilot + data, 1: pilot only ) */
} C2k1xRTTChannelParam;

/**
 * \ingroup C2KStruct
 * \details TX control parameters
 */
typedef struct
{
    unsigned char rfMode;          /**< RF mode ( 0: 1xRTT, 1: EVDO ) */
    unsigned char action;          /**< TX path switch ( 0: off, 1: on ) */
    unsigned char sigType;         /**< signal type, value of enum C2K_TX_SIG_TYPE */
    unsigned char band;            /**< band class number ( 0 ~ 21 ) */
    unsigned short channel;        /**< channel number */
    unsigned short reserved;       /**< reserved data */
    unsigned int frequency;        /**< transmit frequency, only used for sigType is C2K_TX_SIG_RF_TONE or C2K_TX_SIG_BB_TONE ( unit: Hz ) */
    unsigned int bbTone;           /**< indicate how to generate CW tone singal ( 0: RF tone, 1: baseband tone ) */
    C2k1xRTTChannelParam chParam;  /**< only used for C2K_TX_SIG_1xRTT */
} C2kTestCmdTxControl;

/**
 * \ingroup C2KStruct
 * \details RX control parameters
 */
typedef struct
{
    unsigned char rfMode;      /**< RF mode ( 0: 1xRTT, 1: EVDO ) */
    unsigned char action;      /**< RX path switch ( 0: off, 1: on ) */
    unsigned char rxPath;      /**< bit map of RX path, |second path|diversity path|main path| */
    unsigned char band;        /**< band class number ( 0 ~ 21 ) */
    unsigned short channel;    /**< channel number */
    unsigned short ctrlMode;   /**< control mode ( 0: RX AGC auto loop, 1: manual ) */
} C2kTestCmdRxControl;

/**
 * \ingroup C2KStruct
 * \details AFC control parameters
 */
typedef struct
{
    unsigned char ctrlMode;    /**< control mode ( 1: manual ) */
    unsigned char capIdValid;  /**< indicate if capId is used by target ( 0: unused, 1: used ) */
    unsigned short capId;      /**< CAP ID */
    unsigned char reserved;    /**< reserved data */
    unsigned char dacValid;    /**< indicate if dacValue is used by target ( 0: unused, 1: used ) */
    unsigned short dacValue;   /**< DAC value */
} C2kTestCmdAfcConfig;

/**
 * \ingroup C2KStruct
 * \details TX AGC configuration parameters
 */
typedef struct
{
    unsigned char rfMode;      /**< RF mode ( 0: 1xRTT, 1: EVDO ) */
    unsigned char paType;      /**< PA control mode ( 0: APT mode, 1: DPD mode ) */
    unsigned char ctrlMode;    /**< power control mode ( 0: fix power, 1: manual ) */
    unsigned char ilpcEnable;  /**< DDPC control ( 0: disable, 1: enable ) */
    unsigned char paTblIndex;  /**< 8 levels PA table index */
    unsigned char paMode;      /**< PA mode ( 0: high, 1: middle, 2: low ) */
    double antennaPower;       /**< the power want to be transmitted at antenna port ( unit: dBm ) */
    double txPower;            /**< the power of PA 8 levels ( unit: dBm ) */
    double paGain;             /**< PA gain ( unit: dB ) */
    double couplerLoss;        /**< coupler loss ( unit: dB ) */
    unsigned short am;         /**< unused, set to 0 */
    unsigned short pm;         /**< unused, set to 0 */
    unsigned char vm1;         /**< VM1 of PA control */
    unsigned char vm2;         /**< VM2 of PA control */
    unsigned short dc2dcLevel; /**< PA voltage ( unit: mV ) */
} C2kTestCmdTxAgcConfig;

/**
 * \ingroup C2KStruct
 * \details TX HT/LT Response
 */
typedef struct
{
    unsigned char band;              /**< band class number ( 0 ~ 21 ) */
    unsigned char rfMode;            /**< RF mode ( 0: 1xRTT, 1: EVDO ) */
    unsigned short dc2dcLevelHT[8];  /**< PA voltage at high temperature ( unit: mV ) */
    unsigned short dc2dcLevelLT[8];  /**< PA voltage at low temperature ( unit: mV ) */
} C2kTestCmdGetSetTxPaBias;

/**
 * \ingroup C2KStruct
 * \details RX AGC configuration parameters
 */
typedef struct
{
    unsigned char rfMode;      /**< RF mode ( 0: 1xRTT, 1: EVDO ) */
    unsigned char rxPath;      /**< bit map of RX path, |second path|diversity path|main path| */
    unsigned char ctrlMode;    /**< control mode ( 0: RX AGC auto loop, 1: manual ) */
    unsigned char powerMode;   /**< power mode ( 0: high power mode, 1: low power mode ) */
    unsigned char lnaMode;     /**< LNA mode index ( 0 ~ 7 ) */
    unsigned char reserved[3]; /**< reserved data */
} C2kTestCmdRxAgcConfig;

/**
 * \ingroup C2KStruct
 * \details Get RSSI command parameters
 */
typedef struct
{
    unsigned char rfMode;      /**< RF mode ( 0: 1xRTT, 1: EVDO ) */
    unsigned char rxPath;      /**< bit map of RX path, |second path|diversity path|main path| */
    unsigned short reserved;   /**< reserved data */
} C2kTestCmdRssi;

/**
 * \ingroup C2KStruct
 * \details RSSI measurement result
 */
typedef struct
{
    double mainPower;          /**< main path power ( unit: dBm ) */
    double divPower;           /**< diversity/second path power ( unit: dBm ) */
} C2kTestResultRssi;

/**
 * \ingroup C2KStruct
 * \details AFC calibration data
 */
typedef struct
{
    unsigned short initDacValue; /**< initial DAC value */
    short afcSlopeInv;           /**< reciprocal of AFC slope ( resolution: 1/4096 ) */
    unsigned short capId;        /**< CAP ID */
    unsigned short reserved;     /**< reserved data */
} C2kTestCmdGetSetAfcData;

/**
 * \ingroup C2KStruct
 * \details TX setting of one PA section
 */
typedef struct
{
    unsigned char paMode;      /**< PA mode ( 0: high, 1: middle, 2: low ) */
    short prf;                 /**< the power of PA 8 levels ( unit: dBm ) */
    double paGain;             /**< PA gain ( unit: dB ) */
    unsigned char vm1;         /**< VM1 of PA control */
    unsigned char vm2;         /**< VM2 of PA control */
    unsigned short dc2dcLevel; /**< PA voltage ( unit: mV ) */
} C2kTestPaSection;

/**
 * \ingroup C2KStruct
 * \details TX calibration data
 */
typedef struct
{
    char hysteresisStart0;        /**< hysteresis start of high <-> middle */
    char hysteresisEnd0;          /**< hysteresis end of high <-> middle */
    char hysteresisStart1;        /**< hysteresis start of middle <-> low */
    char hysteresisEnd1;          /**< hysteresis end of middle <-> low */
    unsigned char paSectionNum;   /**< number of PA sections */
    C2kTestPaSection paSection[C2K_PA_SECTION_NUM]; /**< PA section array, index is from 0 to 7, TX power and PA mode order is from low to high */
    double couplerLoss[3];        /**< coupler loss ( [0]: PA high, [1]: PA middle, [2]: PA low ) */
    char temperature[C2K_TEMP_COMP_NUM];       /**< temperature */
    unsigned short channel[C2K_FREQ_COMP_NUM]; /**< channel number */
    double paGainComp[C2K_PA_SECTION_NUM][C2K_TEMP_COMP_NUM][C2K_FREQ_COMP_NUM]; /**< PA gain compensation, indexed by [PA section:8][temperature:8][channel:16] */
    double couplerLossComp[3][C2K_TEMP_COMP_NUM][C2K_FREQ_COMP_NUM]; /**< coupler loss compensation, indexed by [PA mode:3][temperature:8][channel:16] */
    short paPhaseComp[3]; /**< PA phase compensation ( [0]: PA high, [1]: PA middle, [2]: PA low ) */
} C2kTestCmdGetSetTxData;

/**
 * \ingroup C2KStruct
 * \details RX calibration data
 */
typedef struct
{
    char temperature[C2K_TEMP_COMP_NUM];       /**< temperature */
    unsigned short channel[C2K_FREQ_COMP_NUM]; /**< channel number */
    double lossHpm[C2K_LNA_MODE_NUM][C2K_TEMP_COMP_NUM][C2K_FREQ_COMP_NUM]; /**< high power mode loss, indexed by [LNA mode:8][temperature:8][channel:16] */
    double lossLpm[C2K_LNA_MODE_NUM][C2K_TEMP_COMP_NUM][C2K_FREQ_COMP_NUM]; /**< low power mode loss, indexed by [LNA mode:8][temperature:8][channel:16] */
} C2kTestCmdGetSetRxData;

/**
 * \ingroup C2KStruct
 * \details Deprecated
 */
typedef struct
{
    unsigned char paControlLevel;
    unsigned char roomTempIndex;
    char hysteresisStart0;
    char hysteresisEnd0;
    char hysteresisStart1;
    char hysteresisEnd1;
    unsigned char paMode[C2K_PA_SECTION_NUM];
    short prf[C2K_PA_SECTION_NUM];
    short paGain[C2K_PA_SECTION_NUM];
    unsigned short dc2dcLevel[C2K_PA_SECTION_NUM];
    unsigned char vm1[C2K_PA_SECTION_NUM];
    unsigned char vm2[C2K_PA_SECTION_NUM];
    unsigned short channel[C2K_FREQ_COMP_NUM];
    short paGainComp[C2K_PA_SECTION_NUM][C2K_TEMP_COMP_NUM][C2K_FREQ_COMP_NUM]; //[PA section:8][temperature:8][channel:16]
    short couplerLoss[3];
    short couplerLossComp[3][C2K_TEMP_COMP_NUM][C2K_FREQ_COMP_NUM]; //[PA mode:3][temperature:8][channel:16]
    unsigned short delay[15];
    unsigned short dpdPaArray[512];
} C2kTestCmdGetSetDpdPaData;

/**
 * \ingroup C2KStruct
 * \details Deprecated
 */
typedef struct
{
    unsigned short channel[C2K_FREQ_COMP_NUM];
    unsigned char lutAm[C2K_PA_SECTION_NUM][C2K_FREQ_COMP_NUM][16];
    char lutPm[C2K_PA_SECTION_NUM][C2K_FREQ_COMP_NUM][16];
    unsigned short dpdPaArray[512];
} C2kTestCmdGetSetDpdAmPmData;

/**
 * \ingroup C2KStruct
 * \details FHC TX one step
 */
typedef struct
{
    unsigned char rfMode;              /**< RF mode ( 0: 1xRTT, 1: EVDO ) */
    /**
     * <pre>
     * indicate the type of step
     * 0: next step is normal step
     * 1: next step is retune step
     * 2: current step is the last step
     * 3: next step is RF mode switch step
     * </pre>
     */
    unsigned char stepInd;
    unsigned char band;                /**< band class number */
    unsigned short channel;            /**< channel number */
    double txPower;                    /**< TX power ( unit: dBm ) */
} FhcTxStep;

/**
 * \ingroup C2KStruct
 * \details FHC RX one step
 */
typedef struct
{
    /**
     * <pre>
     * indicate the type of step
     * 0: next step is normal step
     * 1: next step is retune step
     * 2: current step is the last step
     * </pre>
     */
    unsigned char stepInd;
    unsigned char band;                /**< band class number */
    unsigned short channel;            /**< channel number */
    unsigned char path;                /**< bit map of RX path, |second path|diversity path|main path| */
    unsigned char powerMode;           /**< power mode ( 0: high power mode, 1: low power mode ) */
    unsigned char lnaMode;             /**< LNA mode index ( 0 ~ 7 ) */
} FhcRxStep;

/**
 * \ingroup C2KStruct
 * \details FHC command parameters
 */
typedef struct
{
    unsigned short txRxDelay;               /**< RX delay offset to TX ( unit: microsecond ) */
    unsigned short txStepLength;            /**< TX power step length, 1000 ~ 20000 ( unit: microsecond ) */
    unsigned short txRetuneLength;          /**< TX frequency retune length, >=500 ( unit: microsecond ) */
    unsigned short rfModeSwitchLength;      /**< RF mode switch length, >=1000 ( unit: microsecond ) */
    unsigned short rxStepLength;            /**< RX power step length, 1000 ~ 20000 ( unit: microsecond ) */
    unsigned short rxRetuneLength;          /**< RX frequency retune length, >=500 ( unit: microsecond ) */
    unsigned short txStepNum;               /**< number of TX steps */
    FhcTxStep txSteps[C2K_FHC_MAX_TX_STEP]; /**< TX steps */
    unsigned short rxStepNum;               /**< number of RX steps */
    FhcRxStep rxSteps[C2K_FHC_MAX_RX_STEP]; /**< RX steps */
} C2kTestCmdFhc;

/**
 * \ingroup C2KStruct
 * \details FHC result
 */
typedef struct
{
    unsigned short pdNum;                  /**< number of power detector results */
    unsigned short rssiNum;                /**< number of RSSI results */
    double pdResult[C2K_FHC_MAX_TX_STEP];  /**< power detector results ( unit: dBm ) */
    double rssiMain[C2K_FHC_MAX_RX_STEP];  /**< main path RSSI results ( unit: dBm ) */
    double rssiDiv[C2K_FHC_MAX_RX_STEP];   /**< diversity path RSSI results ( unit: dBm ) */
} C2kTestResultFhc;

/**
 * \ingroup C2KStruct
 * \details NSFT power up parameters
 */
typedef struct
{
    unsigned short band;       /**< band class number */
    unsigned short channel;    /**< channel number */
    unsigned char walshCode;   /**< traffic channel walsh code */
    unsigned char radioConfig; /**< reverse radio configuration */
} C2kNsftCmdPowerUp;

/**
 * \ingroup C2KStruct
 * \details NSFT status of command execution
 */
typedef struct
{
    unsigned int status;       /**< NSFT status */
} C2kNsftResultStatus;

/**
 * \ingroup C2KStruct
 * \details NSFT get FER command parameters
 */
typedef struct
{
    unsigned short band;      /**< band class number */
    unsigned short channel;   /**< channel number */
    unsigned short numFrames; /**< number of FER test frames */
    unsigned char enableAfc;  /**< enable AFC ( 0: disable, 1: enable ) */
} C2kNsftCmdFer;

/**
 * \ingroup C2KStruct
 * \details NSFT get FER result
 */
typedef struct
{
    unsigned int status;        /**< NSFT status */
    unsigned short badFrames;   /**< number of bad frames */
    unsigned short totalFrames; /**< number of total test frames */
} C2kNsftResultFer;

/**
 * \ingroup C2KStruct
 * \details NSFT set TX power command parameters
 */
typedef struct
{
    unsigned short band;        /**< band class number */
    unsigned short channel;     /**< channel number */
    double txPower;             /**< TX power ( unit: dBm ) */
} C2kNsftCmdSetTxPower;

/**
 * \ingroup C2KStruct
 * \details NSFT get RSSI command parameters
 */
typedef struct
{
    unsigned short band;    /**< band class number */
    unsigned short channel; /**< channel number */
} C2kNsftCmdRssi;

/**
 * \ingroup C2KStruct
 * \details NSFT get RSSI result
 */
typedef struct
{
    unsigned int status;     /**< NSFT status */
    unsigned short pnOffset; /**< PN offset */
    unsigned short strength; /**< signal strength */
    double mainRssi;         /**< main path RSSI ( unit: dBm ) */
    double divRssi;          /**< diversity path RSSI ( unit: dBm ) */
} C2kNsftResultRssi;

/**
 * \ingroup C2KStruct
 * \details NSFT list mode command parameters
 */
typedef struct
{
    unsigned char count;                                                          /**< count of list steps */
    unsigned char offset[C2K_NSFT_LIST_MODE_NUM];                                 /**< occupied frames of the step */
    unsigned short band[C2K_NSFT_LIST_MODE_NUM];                                  /**< band class number of the step */
    unsigned short channel[C2K_NSFT_LIST_MODE_NUM];                               /**< channel number of the step */
    unsigned char walshCode[C2K_NSFT_LIST_MODE_NUM];                              /**< traffic channel walsh code of the step */
    unsigned char radioConfig[C2K_NSFT_LIST_MODE_NUM];                            /**< reverse radio configuration of the step */
    unsigned short numFrames[C2K_NSFT_LIST_MODE_NUM];                             /**< number of FER test frames of the step */
    unsigned char txPowerCount[C2K_NSFT_LIST_MODE_NUM];                           /**< count of TX power levels of the step */
    double txPowerLevel[C2K_NSFT_LIST_MODE_NUM][C2K_NSFT_LIST_MODE_TX_PWR_COUNT]; /**< TX power levels array of the step ( unit: dBm ) */
    unsigned char rssiCount[C2K_NSFT_LIST_MODE_NUM];                              /**< count of RSSI levels of the step */
} C2kNsftCmdListMode;

/**
 * \ingroup C2KStruct
 * \details NSFT list mode result
 */
typedef struct
{
    unsigned int status;                                                    /**< NSFT status */
    unsigned char count;                                                    /**< count of list steps */
    unsigned char index[C2K_NSFT_LIST_MODE_NUM];                            /**< index of the step */
    unsigned short band[C2K_NSFT_LIST_MODE_NUM];                            /**< band class number of the step */
    unsigned short channel[C2K_NSFT_LIST_MODE_NUM];                         /**< channel number of the step */
    unsigned char badFrames[C2K_NSFT_LIST_MODE_NUM];                        /**< number of bad frames of the step */
    unsigned char totalFrames[C2K_NSFT_LIST_MODE_NUM];                      /**< number of total frames of the step */
    double mainRssi[C2K_NSFT_LIST_MODE_NUM][C2K_NSFT_LIST_MODE_RSSI_COUNT]; /**< main path RSSI levels of the step ( unit: dBm ) */
    double divRssi[C2K_NSFT_LIST_MODE_NUM][C2K_NSFT_LIST_MODE_RSSI_COUNT];  /**< diversity path RSSI levels of the step ( unit: dBm ) */
} C2kNsftResultListMode;

/**
 * \ingroup C2KStruct
 * \details NSFT test mode command
 */
typedef struct
{
    unsigned int reserved; /**< reserved data */
} C2kNsftCmdTestMode;

/**
 * \ingroup C2KStruct
 * \details MEID get/set parameters
 */
typedef struct
{
    char meid[17]; /**< Null-terminated 14 hexadecimal format MEID string */
    char esn[9];   /**< Null-terminated 8 hexadecimal format ESN string, unused in set command */
} C2kTestCmdGetSetMeid;

/**
 * \ingroup C2KStruct
 * \details Specify which bands to get the LNA configuration
 */
typedef struct
{
    unsigned char bandNum;      /**< number of band classes in array */
    unsigned char bandClass[5]; /**< band class number */
} C2kTestCmdGetRxLnaConfig;

/**
 * \ingroup C2KStruct
 * \details How many LNA modes are supported and calibration power of each
 */
typedef struct
{
    unsigned char lnaNum;          /**< number of LNA modes */
    short value[C2K_LNA_MODE_NUM]; /**< calibration power value ( unit: dBm ) */
} C2kLnaCalibrationPower;

/**
 * \ingroup C2KStruct
 * \details RX LNA calibration power parameters
 */
typedef struct
{
    C2kLnaCalibrationPower hpm; /**< high power mode parameter */
    C2kLnaCalibrationPower lpm; /**< low power mode parameter */
} C2kRxLnaCalibrationPower;

/**
 * \ingroup C2KStruct
 * \details Limit value of calibration power
 */
typedef struct
{
    short min; /**< minimum limit */
    short max; /**< maximum limit */
} C2kRxPowerRange;

/**
 * \ingroup C2KStruct
 * \details RX LNA calibration power range
 */
typedef struct
{
    C2kRxPowerRange hpm[C2K_LNA_MODE_NUM]; /**< high power mode limit */
    C2kRxPowerRange lpm[C2K_LNA_MODE_NUM]; /**< low power mode limit */
} C2kRxLnaPowerRange;

/**
 * \ingroup C2KStruct
 * \details LNA configuration of one band
 */
typedef struct
{
    C2kRxLnaCalibrationPower power[3]; /**< calibration power ( [0]: main path, [1]: diversity path, [2]: second path ) */
    C2kRxLnaPowerRange range[3];       /**< limit range ( [0]: main path, [1]: diversity path, [2]: second path ) */
} C2kRxLnaConfig;

/**
 * \ingroup C2KStruct
 * \details LNA configurations
 */
typedef struct
{
    C2kRxLnaConfig lnaConfig[5];      /**< LNA configuration */
} C2kTestResultRxLnaConfig;

/**
 * \ingroup C2KStruct
 * \details [M70 series] Get TX gain setting command
 */
typedef struct
{
    unsigned int rfMode;              /**< RF mode ( 0: 1xRTT, 1: EVDO ) */
} C2kTestCmdGetTxGainSetting;

/**
 * \ingroup C2KStruct
 * \details [M70 series] TX gain setting result
 */
typedef struct
{
    unsigned char rfMode;             /**< RF mode ( 0: 1xRTT, 1: EVDO ) */
    unsigned char opMode;             /**< PA operating mode ( 0: ET mode, 1: DPD mode, 2: APT mode ) */
    unsigned char pgaIndex;           /**< PGA index */
    unsigned char paIndex;            /**< PA index */
    unsigned char powerDetectorIndex; /**< power detector index */
    unsigned char pgaMode;            /**< PGA A/B table mode */
    short desiredTxGain;                /**< desiredTxGain = pgaGain + digitalGain */
    short targetGain;                   /**< targetGain = paGain + pgaGain + digitalGain - compensation */
    short paGain;                       /**< PA gain ( unit: dB, resolution: 1/32 ) */
    short pgaGain;                      /**< PGA gain ( unit: dB, resolution: 1/32 ) */
    short digitalGain;                  /**< digital gain ( unit: dB, resolution: 1/32 ) */
    short powerDetectorGain;            /**< power detector gain ( unit: dB, resolution: 1/32 ) */
    short couplerLossGain;              /**< coupler loss gain ( unit: dB, resolution: 1/32 ) */
    short backOffGain;                  /**< back off gain ( unit: dB, resolution: 1/32 ) */
    short compensation;                 /**< compensation ( unit: dB, resolution: 1/32 ) */
    int paOutputPower;                  /**< DDPC det var from TPC HW, S24.0 */
    short powerDetectorValue;           /**< power detector value ( unit: dBm, resolution: 1/32 ) */
    short rxPowerFilter;                 /**< RX rssi for TXAGC, S7.6*/
} C2kTestResultGetTxGainSetting;

/**
 * \ingroup C2KStruct
 * \details [M70 series] Get RX gain setting command
 */
typedef struct
{
    unsigned int rfMode;              /**< RF mode ( 0: 1xRTT, 1: EVDO ) */
    unsigned int path;                /**< path index ( 0: main path, 1: diversity path, 2: second path ) */
} C2kTestCmdGetRxGainSetting;

/**
 * \ingroup C2KStruct
 * \details [M70 series] RX gain setting result
 */
typedef struct
{
    unsigned char rfMode;             /**< RF mode ( 0: 1xRTT, 1: EVDO ) */
    unsigned char pathBitmap;         /**< bit map of RX path, |second path|diversity path|main path| */
    unsigned char pgaIndex;           /**< PGA index */
    unsigned char lnaIndex;           /**< LNA index */
    unsigned char eLnaIndex;          /**< eLNA index */
    unsigned char mLnaIndex;          /**< mLNA index */
    int digitalGain;                  /**< digital gain ( unit: dB, resolution: 1/32 ) */
    int rfGain;                       /**< RF gain ( unit: dB, resolution: 1/32 ) */
    int pathLoss;                     /**< path loss ( unit: dB, resolution: 1/32 ) */
    int wideBandPower;                /**< wide-band power ( unit: dBm, resolution: 1/32 ) */
    int inBandPower;                  /**< in-band power ( unit: dBm, resolution: 1/32 ) */
    int rssi;                         /**< RSSI ( unit: dBm, resolution: 1/32 ) */
    int dcEstI;                       /**< DC of I path */
    int dcEstQ;                       /**< DC of Q path */
} C2kTestResultGetRxGainSetting;

/**
 * \ingroup C2KStruct
 * \details [M70 series] Set BPI data command
 */
typedef struct
{
    unsigned int rfMode;              /**< RF mode ( 0: 1xRTT, 1: EVDO ) */
    unsigned int bpiData;             /**< BPI data */
} C2kTestCmdSetBpiData;

/**
 * \ingroup C2KStruct
 * \details [M70 series] Get BPI data command
 */
typedef struct
{
    unsigned int rfMode;              /**< RF mode ( 0: 1xRTT, 1: EVDO ) */
} C2kTestCmdGetBpiData;

/**
 * \ingroup C2KStruct
 * \details [M70 series] Get BPI data result
 */
typedef struct
{
    unsigned int bpiData;             /**< BPI data */
} C2kTestResultGetBpiData;

/**
 * \ingroup C2KStruct
 * \details [M70 series] RX AGC configuration parameters
 */
typedef struct
{
    unsigned char rfMode;             /**< RF mode ( 0: 1xRTT, 1: EVDO ) */
    unsigned char pathBitmap;         /**< bit map of RX path, |second path|diversity path|main path| */
    unsigned char testMode;           /**< test mode ( 0: fixed gain mode, 1: manual mode ) */
    unsigned char agcFsm;             /**< AGC Finite State Machine ( 0: fast, 1: steady, 2: ICS, 3: calibration, 4: fix gain ) */
    unsigned char powerMode;          /**< power mode ( 0: high power mode, 1: low power mode ) */
    unsigned char lnaMode;            /**< LNA mode index ( 0 ~ 7 ) */
    unsigned char pgaIndex;           /**< PGA index */
    unsigned char agcDcFixBmp;        /**< bit map to indicate which parameter to be fixed, |0|0|0|0|dig_dc|dig_gain|rf_dc|rf_gain| */
    int digitalGain;                  /**< digital gain ( unit: dB, resolution: 1/32 ) */
} C2kTestCmdRxAgcConfigV7;

/**
 * \ingroup C2KStruct
 * \details Set DPD calibration flag command structure
 */
typedef struct
{
    unsigned char updateNvram;        /**< indicate whether update flag to NVRAM or not ( 0: not update, 1: update ) */
    unsigned char reserved[3];        /**< reserved data */
    unsigned int dpdFlag;             /**< DPD calibration flag ( 0: fail, 1: pass ) */
} C2kTestCmdSetDpdCalFlag;

/**
 * \ingroup C2KStruct
 * \details Get DPD calibration flag command structure
 */
typedef struct
{
    unsigned int reserved;            /**< reserved data */
} C2kTestCmdGetDpdCalFlag;

/**
 * \ingroup C2KStruct
 * \details Get DPD calibration flag result structure
 */
typedef struct
{
    unsigned int dpdFlag;             /**< DPD calibration flag ( 0: fail, 1: pass ) */
} C2kTestResultGetDpdCalFlag;

/**
 * \ingroup C2KStruct
 * \details [M70 series] Configure RX IQ dump data command structure
 */
typedef struct
{
    unsigned int rfMode;              /**< RF mode ( 0: 1xRTT, 1: EVDO ) */
    unsigned int pathIndex;           /**< RX path index ( 0: main path, 1: diversity path, 2: second path ) */
    unsigned int dumpNode;            /**< dump node */
    unsigned int iqNum;               /**< IQ number */
} C2kTestCmdConfigRxIqDump;

/**
 * \ingroup C2KStruct
 * \details [M70 series] Configure TX IQ dump data command structure
 */
typedef struct
{
    unsigned int rfMode;              /**< RF mode ( 0: 1xRTT, 1: EVDO ) */
    /**
     * <pre>
     * dump selector
     * 0: TXK_REF
     * 1: TXDFE_RF
     * 2: TXDFE_BB
     * 3: TXDFE_ET
     * 4: TXK_DET
     * </pre>
     */
    unsigned int dumpSel;
    /**
     * <pre>
     * node selector
     * for dumpSel is TXDFE_RF ( 0: gain BB, 1: DPD comp, 2: gain )
     * for dumpSel is TXDFE_BB ( 0: BB input, 1: firad input, 2: src )
     * </pre>
     */
    unsigned int nodeSel;
    unsigned int iqNum;               /**< the number of IQ data */
} C2kTestCmdConfigTxIqDump;

/**
 * \ingroup C2KStruct
 * \details [M70 series] Configure TXK IQ dump data command structure
 */
typedef struct
{
    unsigned int rfMode;              /**< RF mode ( 0: 1xRTT, 1: EVDO ) */
    /**
     * <pre>
     * dump selector
     * 0: TXK_REF
     * 1: TXDFE_RF
     * 2: TXDFE_BB
     * 3: TXDFE_ET
     * 4: TXK_DET
     * </pre>
     */
    unsigned int dumpSel;
    /**
     * <pre>
     * node selector
     * for dumpSel is TXK_REF ( 0: scale out, 1: i delay )
     * for dumpSel is TXK_DET ( 0: afifo out, 1: cic out )
     * </pre>
     */
    unsigned int nodeSel;
    unsigned int iqNum;               /**< the number of IQ data */
} C2kTestCmdConfigTxkIqDump;

/**
 * \ingroup C2KStruct
 * \details [M70 series] Configure RX/TX/TXK IQ dump data result structure
 */
typedef struct
{
    unsigned int bufferSize;          /**< the size of IQ data to be dumped */
    unsigned int blockNum;            /**< the block number of IQ data buffer */
} C2kTestResultConfigIqDump;

#ifdef __cplusplus
}
#endif

#endif
