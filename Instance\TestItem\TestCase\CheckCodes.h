#ifndef CHECKCODES_H
#define CHECKCODES_H

#include "../testcasebase.h"
#include <QCheckBox>
#include <QComboBox>
#include <QLineEdit>
#include <QFormLayout>

class CheckCodesItem : public TestCaseBase
{
    Q_OBJECT
public:
    CheckCodesItem();

    QString type() const override { return "CheckCodes"; }
    static QString staticType() { return "CheckCodes"; }
    QString displayName() const override { return "Check Codes"; }

    QWidget* createParameterWidget(QWidget* parent) override;

    QVariantMap parameters() const override { return m_params; }
    void setParameters(const QVariantMap& params) override { m_params = params; }
};

#endif // CHECKCODES_H
