#include "MainWindowCallback.h"
#include "MainWindow.h"

MainWindowCallback::MainWindowCallback(MainWindow *mainwindow)
    : m_mainwindow(mainwindow)
{
    //设置自定义的传参类型
    qRegisterMetaType<TestResult_Status>("TestResult_Status");
    qRegisterMetaType<LOGCOLOR_TYPE>("LOGCOLOR_TYPE");
    qRegisterMetaType<PORT_TYPE>("PORT_TYPE");
    qRegisterMetaType<UINT>("UINT");
    setParent(nullptr);

    for(UINT i=0; i<MAX_SUPPORT_COMPORT_NUM; i++)
    {
        m_pMetaInstance[i] = new SmartPhoneSN(this, i);
        connect(m_pMetaInstance[i], &SmartPhoneSN::signal_MetaUpdateUILog, this, &MainWindowCallback::slot_UpdateUILog, Qt::QueuedConnection);
        g_pMesProxy[i] = MesFactory::GetMesProxy();
        globalBox_port[i] = new QSerialPort();
        m_pTestBoxInstance[i] = new SerialPort(this, i, globalBox_port[i]);
        connect(m_pTestBoxInstance[i], &SerialPort::signal_UpdateUILog, this, &MainWindowCallback::slot_UpdateUILog, Qt::QueuedConnection);
        globalScan_port[i] = new QSerialPort();
        m_pScanGunInstance[i] = new SerialPort(this, i, globalScan_port[i]);
        connect(m_pScanGunInstance[i], &SerialPort::signal_UpdateUILog, this, &MainWindowCallback::slot_UpdateUILog, Qt::QueuedConnection);
		m_pVisaInstance[i] = new ConnectAgilent(this, i);
        connect(m_pVisaInstance[i], &ConnectAgilent::signal_VisaUpdateUILog, this, &MainWindowCallback::slot_UpdateUILog, Qt::QueuedConnection);

        m_httpclient[i] = new HttpClient();
        m_serviceproxy[i] = new ServerApiProxy(this, m_httpclient[i], i);
    }
}

MainWindowCallback::~MainWindowCallback()
{
    for(UINT i=0; i<MAX_SUPPORT_COMPORT_NUM; i++)
    {
        if (m_pMetaInstance[i] != nullptr)
        {
            delete m_pMetaInstance[i];
            m_pMetaInstance[i] = NULL;
        }
        if (m_pVisaInstance[i] != nullptr)
        {
            delete m_pVisaInstance[i];
            m_pVisaInstance[i] = NULL;
        }
        if (g_pMesProxy[i] != nullptr)
        {
            delete g_pMesProxy[i];
            g_pMesProxy[i] = NULL;
        }
    }
}

void MainWindowCallback::connectSignals(UINT nTreadID, WorkerThread *m_workerThread)
{
    connect(m_workerThread, &WorkerThread::signal_EnableUIItem, this, &MainWindowCallback::slot_EnableUIItem, Qt::QueuedConnection);
    connect(m_workerThread, &WorkerThread::signal_ClearUIlogs, this, &MainWindowCallback::slot_ClearUILogs, Qt::QueuedConnection);
    connect(m_workerThread, &WorkerThread::signal_UpdateResult, this, &MainWindowCallback::slot_UpdateResult, Qt::QueuedConnection);
    connect(m_workerThread, &WorkerThread::signal_UpdateResultToMes, this, &MainWindowCallback::slot_UpdateResultToMes, Qt::QueuedConnection);
    connect(m_workerThread, &WorkerThread::signal_UpdateUILog, this, &MainWindowCallback::slot_UpdateUILog, Qt::QueuedConnection);
    connect(m_workerThread, &WorkerThread::signal_UpdateMainUIItem, this, &MainWindowCallback::slot_UpdateMainUIItem, Qt::QueuedConnection);
    connect(m_workerThread, &WorkerThread::signal_UpdateBarcodeToUI, this, &MainWindowCallback::slot_UpdateBarcodeToUI, Qt::QueuedConnection);
    connect(m_workerThread, &WorkerThread::signal_UpdateUIPortInfo, this, &MainWindowCallback::slot_UpdateUIPortInfo, Qt::QueuedConnection);
    connect(m_workerThread, &WorkerThread::signal_InitItemListView, this, &MainWindowCallback::slot_InitItemListView, Qt::QueuedConnection);
    connect(m_workerThread, &WorkerThread::signal_SetLogPath, this, &MainWindowCallback::slot_SetLogPath, Qt::QueuedConnection);
    connect(m_workerThread, &WorkerThread::signal_checkstatus, this, &MainWindowCallback::slot_checkstatus, Qt::QueuedConnection);
    connect(m_workerThread, &WorkerThread::signal_StartTime, this, &MainWindowCallback::slot_StartTime, Qt::QueuedConnection);
    connect(m_workerThread, &WorkerThread::signal_send_data, this, &MainWindowCallback::slot_send_data, Qt::QueuedConnection);
    connect(m_workerThread, &WorkerThread::signal_UploadMacToYnSystem, this, &MainWindowCallback::slot_UploadMacToYnSystem, Qt::QueuedConnection);
}

void MainWindowCallback::Thread_Start(UINT nTreadID)
{
    ControlTime[nTreadID] = 0;
    g_TestStopFlag[nTreadID] = 0;
    m_mainwindow->DisableUIItem(nTreadID);

    // 在主线程中创建WorkerThread实例，并启动线程
    WorkerThread *workerThread = new WorkerThread(this,
                                                  m_pMetaInstance[nTreadID],
                                                  m_pVisaInstance[nTreadID],
                                                  g_pMesProxy[nTreadID],
                                                  nTreadID);
    connectSignals(nTreadID, workerThread);

    workerThread->start();
}

void MainWindowCallback::Thread_Stop(UINT nTreadID)
{
    g_TestStopFlag[nTreadID] = BOOT_STOP;
}

void MainWindowCallback::slot_EnableUIItem(UINT nTreadID)
{
    m_mainwindow->EnableUIItem(nTreadID);
}

void MainWindowCallback::slot_ClearUILogs(UINT nTreadID)
{
    m_mainwindow->ClearLogs(nTreadID);
}

void MainWindowCallback::slot_UpdateResult(UINT nTreadID, TestResult_Status Status)
{
    m_mainwindow->UpdateOperateResult(nTreadID, Status);
}

void MainWindowCallback::slot_UpdateResultToMes(UINT nTreadID, TestResult_Status Status, QString barcode, UINT TestTime)
{
    m_mainwindow->UpdateResultToMes(nTreadID, Status, barcode, TestTime);
}

void MainWindowCallback::slot_UpdateUILog(UINT nTreadID, QString log, LOGCOLOR_TYPE textcolor)
{
    QString *rs = new QString;
    *rs = log;
    m_mainwindow->AppendLogs(nTreadID, log, textcolor);
}

void MainWindowCallback::slot_UpdateMainUIItem(UINT nTreadID, QString m_ItemName, TestResult_Status m_Status, QString value)
{
    m_mainwindow->UpdateTestListItem(nTreadID, m_ItemName, m_Status, value);
}

void MainWindowCallback::slot_UpdateBarcodeToUI(UINT nTreadID, QString m_Barcode)
{
    m_mainwindow->UpdateBarcodeToUI(nTreadID, m_Barcode);
}

void MainWindowCallback::slot_UpdateUIPortInfo(UINT nTreadID)
{
    m_mainwindow->UpdateUIPortInfo(nTreadID);
}

void MainWindowCallback::slot_InitItemListView(UINT nTreadID)
{
    m_mainwindow->InitItemListView(nTreadID);
}

void MainWindowCallback::slot_SetLogPath(UINT nTreadID)
{
    m_mainwindow->SetLogFilePath(nTreadID);
}

void MainWindowCallback::slot_StartTime(UINT nTreadID)
{
    m_mainwindow->slot_start_timer(nTreadID);
}

void MainWindowCallback::CheckFixtureStatus(UINT nTreadID)
{
    slot_checkstatus(nTreadID);
}

void MainWindowCallback::slot_checkstatus(UINT nTreadID)
{
    if(Common::getInstance()->getCtrBoxFlag(nTreadID) && timer_id[nTreadID] == -1)
    {
        if(Common::getInstance()->getneedCtrBox(nTreadID) == nTreadID)
            timer_id[Common::getInstance()->getneedCtrBox(nTreadID)] = startTimer(1000);
        else if(timer_id[Common::getInstance()->getneedCtrBox(nTreadID)] != -1)
            timer_id[nTreadID] = timer_id[Common::getInstance()->getneedCtrBox(nTreadID)];
        else
        {
            for(int i=0;i<4;i++)
            {
                if(Common::getInstance()->getneedCtrBox(i) == Common::getInstance()->getneedCtrBox(nTreadID))
                    Common::getInstance()->setneedCtrBox(i, nTreadID);
            }
            timer_id[nTreadID] = startTimer(1000);
        }
    }
    else
        Common::getInstance()->setCtrBoxFlag(nTreadID, false);
}

void MainWindowCallback::slot_send_data(UINT nTreadID, QString data)
{
    Common::getInstance()->setSleepFlag(true);

    qDebug() << QString("Write Command: %1").arg(data);
    QString request = m_pTestBoxInstance[nTreadID]->WriteCommand(data);
    qDebug() << QString("Response: %1").arg(request);

    Common::getInstance()->setSleepFlag(false);
}

void MainWindowCallback::timerEvent(QTimerEvent *event)
{
    QVector<int> checkTimer;
    for(int i=0;i<4;i++)
    {
        //if(checkTimer.contains(timer_id[i])) continue;

        if (event->timerId() == timer_id[i]) {
            checkTimer.push_back(timer_id[i]);
            Common::getInstance()->setPortRequest(i, "");
            Common::getInstance()->setSignalRet(i, false);

            QString request = "";
            for(int retry=3;retry>0;retry--)
            {
                request = m_pTestBoxInstance[i]->CheckStatus(ECOMMANDStr[E_CHECK_STATUS]);

                if(!request.isEmpty())
                {
                    ControlTime[i] = 0;
                    break;
                }
                ControlTime[i]++;

                if(retry==1)
                    ResetSerialPort(i);

                if(ControlTime[i] >5)
                {
                    for(int j=0;j<4;j++)
                    {
                        if(Common::getInstance()->getneedCtrBox(j) == Common::getInstance()->getneedCtrBox(i))
                        {
                            g_TestStopFlag[j] = BOOT_STOP;
                            m_mainwindow->AppendLogs(j, "Init TestBox Fail! ", RED);
                            if(timer_id[j] != -1)
                            {
                                killTimer(timer_id[j]);
                                timer_id[j] = -1;
                            }
                        }
                    }
                }
            }

            Common::getInstance()->setPortRequest(i, request);
            Common::getInstance()->setSignalRet(i, true);
        }
    }
}

void MainWindowCallback::ResetSerialPort(UINT nTreadID)
{
    m_pTestBoxInstance[nTreadID]->DeInit();
    Sleep(200);
    if(!m_pTestBoxInstance[nTreadID]->Init(Common::getInstance()->getDutPort(nTreadID, CONTROLBOX_COM), QSerialPort::BaudRate::Baud9600))
    {
        for(int i=0;i<4;i++)
        {
            if(Common::getInstance()->getneedCtrBox(i) == Common::getInstance()->getneedCtrBox(nTreadID))
            {
                g_TestStopFlag[i] = BOOT_STOP;
                m_mainwindow->AppendLogs(i, "Init TestBox Fail! ", RED);
                if(timer_id[i] != -1)
                {
                    killTimer(timer_id[i]);
                    timer_id[i] = -1;
                }
            }
        }
    }
}

void MainWindowCallback::ControlScanGun(UINT nTreadID)
{
    ScanGunTime[nTreadID] = 0;
    if(Common::getInstance()->getUseScanGunEnable() && Common::getInstance()->getScanGunFlag(nTreadID))
    {
        Common::getInstance()->setScanGunRequest(nTreadID, "");
        Common::getInstance()->setScanGunSignal(nTreadID, false);
        switch(nTreadID)
        {
        case 0:
            connect(&timeoutTimer[nTreadID], &QTimer::timeout, this, &MainWindowCallback::slot_GetScanGunData1);
            break;
        case 1:
            connect(&timeoutTimer[nTreadID], &QTimer::timeout, this, &MainWindowCallback::slot_GetScanGunData2);
            break;
        case 2:
            connect(&timeoutTimer[nTreadID], &QTimer::timeout, this, &MainWindowCallback::slot_GetScanGunData3);
            break;
        case 3:
            connect(&timeoutTimer[nTreadID], &QTimer::timeout, this, &MainWindowCallback::slot_GetScanGunData4);
            break;
        }
        timeoutTimer[nTreadID].start(200);

        m_pScanGunInstance[nTreadID]->ControlScanGun();
    }
    else
    {
        m_mainwindow->UpdateStartButton(nTreadID);
    }
}

void MainWindowCallback::slot_GetScanGunData1()
{
    ScanGunTime[0]++;
    if(Common::getInstance()->getScanGunSignal(0))
    {
        m_mainwindow->UpdateBarcodeToUI(0, Common::getInstance()->getScanGunRequest(0));
        timeoutTimer[0].stop();
        disconnect(&timeoutTimer[0], &QTimer::timeout, this, &MainWindowCallback::slot_GetScanGunData1);
        m_mainwindow->UpdateStartButton(0);
        return;
    }

    if(ScanGunTime[0] > CONTROL_GUN_TIME)
    {
        timeoutTimer[0].stop();
        disconnect(&timeoutTimer[0], &QTimer::timeout, this, &MainWindowCallback::slot_GetScanGunData1);
        m_mainwindow->CheckCtrlBoxStatus(0);
    }
}

void MainWindowCallback::slot_GetScanGunData2()
{
    ScanGunTime[1]++;
    if(Common::getInstance()->getScanGunSignal(1))
    {
        m_mainwindow->UpdateBarcodeToUI(1, Common::getInstance()->getScanGunRequest(1));
        timeoutTimer[1].stop();
        disconnect(&timeoutTimer[1], &QTimer::timeout, this, &MainWindowCallback::slot_GetScanGunData2);
        m_mainwindow->UpdateStartButton(1);
        return;
    }

    if(ScanGunTime[1] > CONTROL_GUN_TIME)
    {
        timeoutTimer[1].stop();
        disconnect(&timeoutTimer[1], &QTimer::timeout, this, &MainWindowCallback::slot_GetScanGunData2);
        m_mainwindow->CheckCtrlBoxStatus(1);
    }
}

void MainWindowCallback::slot_GetScanGunData3()
{
    ScanGunTime[2]++;
    if(Common::getInstance()->getScanGunSignal(2))
    {
        m_mainwindow->UpdateBarcodeToUI(2, Common::getInstance()->getScanGunRequest(2));
        timeoutTimer[2].stop();
        disconnect(&timeoutTimer[2], &QTimer::timeout, this, &MainWindowCallback::slot_GetScanGunData3);
        m_mainwindow->UpdateStartButton(2);
        return;
    }

    if(ScanGunTime[2] > CONTROL_GUN_TIME)
    {
        timeoutTimer[2].stop();
        disconnect(&timeoutTimer[2], &QTimer::timeout, this, &MainWindowCallback::slot_GetScanGunData3);
        m_mainwindow->CheckCtrlBoxStatus(2);
    }
}

void MainWindowCallback::slot_GetScanGunData4()
{
    ScanGunTime[3]++;
    if(Common::getInstance()->getScanGunSignal(3))
    {
        m_mainwindow->UpdateBarcodeToUI(3, Common::getInstance()->getScanGunRequest(3));
        timeoutTimer[3].stop();
        disconnect(&timeoutTimer[3], &QTimer::timeout, this, &MainWindowCallback::slot_GetScanGunData4);
        m_mainwindow->UpdateStartButton(3);
        return;
    }

    if(ScanGunTime[3] > CONTROL_GUN_TIME)
    {
        timeoutTimer[3].stop();
        disconnect(&timeoutTimer[3], &QTimer::timeout, this, &MainWindowCallback::slot_GetScanGunData4);
        m_mainwindow->CheckCtrlBoxStatus(3);
    }
}

bool MainWindowCallback::GetMacFromYnSystem(UINT nTreadID, QString &Order, QString &SN, QString &BtMac, QString &WifiMac)
{
    JsonHandler jsonHandler;
    QString request = m_serviceproxy[nTreadID]->GetMacFromZentao(Order, SN);
    if(request.isEmpty())
    {
        m_mainwindow->AppendLogs(nTreadID, "Get Mac Address Failed!", RED);
        return false;
    }

    request = Common::getInstance()->convertUtf8ToGbk(request);
    qDebug() << "[Thread " << nTreadID+1 << "] GetMacFromZentao response: " << request;
    if (jsonHandler.setjson(request)) {
        QString data = jsonHandler.getQStringValue("data");
        QString status = jsonHandler.getQStringValue("status");

        if(status != "success" || data.contains("Error"))
        {
            m_mainwindow->AppendLogs(nTreadID, data, RED);
            return false;
        }
        else
        {
            QStringList parts = data.split(";", Qt::SkipEmptyParts);
            for (const QString &part : parts) {
                if (part.startsWith("BT:")) {
                    BtMac = part.mid(3);
                }
                else if (part.startsWith("WIFI:")) {
                    WifiMac = part.mid(5);
                }
            }
        }
    }
    else{
        m_mainwindow->AppendLogs(nTreadID, "Request is not a Json String!", RED);
        return false;
    }

    return true;
}

void MainWindowCallback::UploadMacToYnSystem(UINT nTreadID, QString Order, QString SN, QString BtMac, QString WifiMac)
{
    QString ErrInfo;
    JsonHandler jsonHandler;
    QString request = m_serviceproxy[nTreadID]->UploadMacToZentao(Order, SN, BtMac, WifiMac);
    if(request.isEmpty())
    {
        ErrInfo = "Error: Upload Mac Address Failed!";
        goto End;
    }

    request = Common::getInstance()->convertUtf8ToGbk(request);
    qDebug() << "[Thread " << nTreadID+1 << "] UploadMacToYnSystem response: " << request;
    if (jsonHandler.setjson(request)) {
        QString data = jsonHandler.getQStringValue("data");
        QString status = jsonHandler.getQStringValue("status");

        if(status != "success")
        {
            ErrInfo = "Error: request is fail !";
            goto End;
        }
        if(data.contains("Error"))
        {
            ErrInfo = data;
            goto End;
        }
    }
    else{
        ErrInfo = "Error: Request is not a Json String!";
        goto End;
    }

End:
    Common::getInstance()->setPortRequest(nTreadID, ErrInfo);
    Common::getInstance()->setSignalRet(nTreadID, true);
    return;
}

bool MainWindowCallback::CheckMacFromYnSystem(UINT nTreadID, QString &Order, QString &BtMac, QString &WifiMac)
{
    JsonHandler jsonHandler;
    QString request = m_serviceproxy[nTreadID]->CheckMacFromZentao(Order, BtMac, WifiMac);
    if(request.isEmpty())
    {
        m_mainwindow->AppendLogs(nTreadID, "Check Mac Address Failed!", RED);
        return false;
    }

    request = Common::getInstance()->convertUtf8ToGbk(request);
    qDebug() << "[Thread " << nTreadID+1 << "] CheckMacFromYnSystem response: " << request;
    if (jsonHandler.setjson(request)) {
        QString data = jsonHandler.getQStringValue("data");
        QString status = jsonHandler.getQStringValue("status");

        if(status != "success" || data.contains("Error"))
        {
            m_mainwindow->AppendLogs(nTreadID, data, RED);
            return false;
        }
    }
    else{
        m_mainwindow->AppendLogs(nTreadID, "Request is not a Json String!", RED);
        return false;
    }

    return true;
}

void MainWindowCallback::slot_UploadMacToYnSystem(UINT nTreadID, QString Order, QString SN, QString BtMac, QString WifiMac)
{
    UploadMacToYnSystem(nTreadID, Order, SN, BtMac, WifiMac);
}
