#ifndef FACTORYRESETITEM_H
#define FACTORYRESETITEM_H

#include "../testcasebase.h"
#include <QCheckBox>
#include <QComboBox>
#include <QLineEdit>
#include <QFormLayout>

class FactoryResetItem : public TestCaseBase
{
    Q_OBJECT
public:
    FactoryResetItem();

    QString type() const override { return "FactoryReset"; }
    static QString staticType() { return "FactoryReset"; }
    QString displayName() const override { return "Factory Reset"; }

    QWidget* createParameterWidget(QWidget* parent) override;

    QVariantMap parameters() const override { return m_params; }
    void setParameters(const QVariantMap& params) override { m_params = params; }
};

#endif // FACTORYRESETITEM_H
