#ifndef CHECKCARDITEM_H
#define CHECKCARDITEM_H

#include "../testcasebase.h"
#include <QCheckBox>
#include <QComboBox>
#include <QLineEdit>
#include <QFormLayout>

class CheckCardItem : public TestCaseBase
{
public:
    CheckCardItem();

    QString type() const override { return "CheckCard"; }
    static QString staticType() { return "CheckCard"; }
    QString displayName() const override { return "Check Card"; }

    QWidget* createParameterWidget(QWidget* parent) override;

    QVariantMap parameters() const override { return m_params; }
    void setParameters(const QVariantMap& params) override { m_params = params; }
};

#endif // CHECKCARDITEM_H
