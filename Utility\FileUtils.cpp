﻿#include "FileUtils.h"

#ifdef _WIN32
#include <windows.h>
#include <io.h>
#else
#include <sys/io.h>
#include <sys/utsname.h>
#endif

#include <sys/stat.h>
#include <sys/types.h>
#include <QCoreApplication>
#include <QDir>
#include <QFile>
#include <QFileInfo>
#include <QFileInfoList>

#ifndef _MAX_PATH
#define _MAX_PATH 1024
#endif

namespace FileUtils{
using std::string;
}

E_CHECK_DIR_STATUS FileUtils::CheckDirectory(const std::string &dir, bool createIfNotExist) {
    QDir dir_obj(QString::fromStdString(dir));
    if(dir_obj.exists()){
        //LOG("%s(): the dir(%s) already exists.", __FUNCTION__, qPrintable(dir_obj.absolutePath()));
        return DIR_EXIST;
    }

    if(!createIfNotExist){
        //LOG("%s(): the dir(%s) does not exist.", __FUNCTION__, qPrintable(dir_obj.absolutePath()));
        return DIR_NOT_EXIST;
    }

    if(!dir_obj.mkpath(dir_obj.absolutePath())){
        //LOG("%s(): create the dir(%s) failed.", __FUNCTION__, qPrintable(dir_obj.absolutePath()));
        return CREATE_DIR_FAIL; //create directory fail
    }
    else
    {
        _chmod(dir.c_str(), _S_IREAD | _S_IWRITE);

        //LOG("%s(): create the dir(%s) succeed.", __FUNCTION__, qPrintable(dir_obj.absolutePath()));
        return CREATE_DIR_SUCCESS; //create directory successfully
    }
}

/**
 * Check whether the file exists
 *
 * @param file to be checked
 *
 * @return whether the file exists
 */
bool FileUtils::IsFileExist(const  string & file) {
    struct stat fileInfo;
    int ret = stat(file.c_str(), &fileInfo);
    return ( (ret == 0) && ((fileInfo.st_mode & S_IFMT) == S_IFREG));
}

/**
 * Delete a directory
 *
 * @param directory to be deleted
 *
 * @return whether the directory is deleted
 */

bool FileUtils::QDeleteDirectory(const std::string &dir_name)
{
    bool result = true;
    QString dir_temp(dir_name.c_str());
    QDir dir(dir_temp);

    if (dir.exists(dir_temp)) {
        Q_FOREACH(QFileInfo info,
                  dir.entryInfoList(QDir::NoDotAndDotDot
                                    | QDir::System
                                    | QDir::Hidden
                                    | QDir::AllDirs
                                    | QDir::Files, QDir::DirsFirst)) {
            if (info.isDir()) {
                result = QDeleteDirectory(info.absoluteFilePath().toLocal8Bit().constData());
            }
            else {
                result = QFile::remove(info.absoluteFilePath());
            }

            if (!result) {
                return result;
            }
        }
        result = dir.rmdir(dir_temp);
    }

    return result;
}

const std::string &FileUtils::GetAppDirectory()
{
    return g_app_path;
}

void FileUtils::UpdateAppPath(const std::string &app_path) {
    g_app_path = app_path;
}

std::string FileUtils::AbsolutePath(const char *name)
{
    return g_app_path + QDir::separator().toLatin1() + name;
}

std::string FileUtils::AbsolutePath(const std::string &name)
{
    return g_app_path + QDir::separator().toLatin1() + name;
}

bool FileUtils::FindFile(const std::string& path, std::list<std::string> &files,
                         const std::string& pattern, COMPARE_METHOD compare_m, bool recursive)
{
    QDir dir(path.c_str());

    if(!dir.exists())
        return false;

    dir.setFilter(QDir::Dirs | QDir::Files);

    QFileInfoList list = dir.entryInfoList();
    int i = 0;
    bool isDirectory;
    std::string fileName;

    if(list.empty())
        return false;

    do
    {
        QFileInfo fileInfo = list.at(i);

        if(fileInfo.fileName() == "." || fileInfo.fileName() == "..")
        {
            i++;
            continue;
        }

        isDirectory = fileInfo.isDir();

        if(isDirectory && recursive)
        {
            FindFile(fileInfo.filePath().toStdString(), files, pattern, compare_m, recursive);
        }
        else
        {
            fileName = fileInfo.fileName().toStdString();

            if(compare_m)
            {
                if(compare_m(fileName, pattern))
                {
                    files.push_back(fileName);
                }
            }
            else
            {
                if(pattern == "")
                {
                    files.push_back(fileName);
                }
                else if(pattern == fileName)
                {
                    files.push_back(fileName);
                }
            }
        }

        i++;
    }while(i < list.size());

    return true;
}

//new added
bool FileUtils::validFile(const QString &file_name, QString &error_msg)
{
    QFileInfo fileinfo(file_name);
    if (file_name.isEmpty()) {
        error_msg = QObject::tr("filename is empty.").arg(file_name);
        return false;
    }
    if (!fileinfo.exists(file_name)) {
        error_msg = QObject::tr("%1 is not exist.").arg(file_name);
        return false;
    }
    if (!fileinfo.isFile()) {
        error_msg = QObject::tr("%1 is not a regular file.").arg(file_name);
        return false;
    }
    if (!fileinfo.isReadable()) {
        error_msg = QObject::tr("%1 is not a readable file.").arg(file_name);
        return false;
    }
    return true;
}
