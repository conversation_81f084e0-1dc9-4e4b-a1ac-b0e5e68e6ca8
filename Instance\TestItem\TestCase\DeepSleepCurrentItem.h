#ifndef DEEPSLEEPCURRENTITEM_H
#define DEEPSLEEPCURRENTITEM_H

#include "../testcasebase.h"
#include <QCheckBox>
#include <QComboBox>
#include <QLineEdit>
#include <QFormLayout>

class DeepSleepCurrentItem : public TestCaseBase
{
    Q_OBJECT
public:
    DeepSleepCurrentItem();

    QString type() const override { return "DeepSleepCurrent"; }
    static QString staticType() { return "DeepSleepCurrent"; }
    QString displayName() const override { return "DeepSleep Current"; }

    QWidget* createParameterWidget(QWidget* parent) override;

    QVariantMap parameters() const override { return m_params; }
    void setParameters(const QVariantMap& params) override { m_params = params; }
};


#endif // DEEPSLEEPCURRENTITEM_H
