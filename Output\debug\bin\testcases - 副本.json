[{"enable": true, "params": {"BtMac": true, "BtMac From": "<PERSON><PERSON>", "BtMac Prefix": "000CE7", "BtMac SectionEnd": "FFFFFE", "BtMac SectionNext": "000CE7000000", "BtMac SectionStart": "000000", "BtMac SectionStep": "2", "EthernetMac": false, "EthernetMac From": "Input", "IMEI1": true, "IMEI1 From": "<PERSON><PERSON>", "IMEI2": true, "IMEI2 From": "<PERSON><PERSON>", "MEID": false, "MEID From": "<PERSON><PERSON>", "NetCode": false, "NetCode From": "Input", "SN": true, "SN From": "Input", "SerialNo": false, "SerialNo From": "Input", "WifiMac": true, "WifiMac From": "<PERSON><PERSON>", "WifiMac Prefix": "000CE7", "WifiMac SectionEnd": "FFFFFF", "WifiMac SectionNext": "000CE7000001", "WifiMac SectionStart": "000001", "WifiMac SectionStep": "2", "XlsxFilePath": "", "YNCode": ""}, "type": "InputCode"}, {"enable": false, "params": {"BatLabel": true, "BatLabel From": "Input", "BatLabel HeaderCheck": false, "BatLabel Length": "20", "BatLabel Prefix": "", "IWSN": false, "IWSN From": "Input", "OBSN": false, "OBSN From": "Input", "Scril.Sf": false, "Scril.Sf From": "Input"}, "type": "InputYDCodes"}, {"enable": false, "params": {"MaxCurrent": "0.6", "MinCurrent": "0.03", "Sleep": "3000", "TestTime": "5", "TriggerCurrent": "0.6", "TriggerTimeout": "15"}, "type": "BottomCurrent"}, {"enable": true, "params": {}, "type": "EnterMode"}, {"enable": true, "params": {}, "type": "LoadSN"}, {"enable": false, "params": {"System Property": "ro.build.display.id", "verify": "20250418"}, "type": "ReadSwVer"}, {"enable": false, "params": {"Flag": "59:P;60:1;61:0;62:P"}, "type": "CheckBarcodeFlag"}, {"enable": true, "params": {}, "type": "WriteCodes"}, {"enable": false, "params": {"TEE Version": "v4.6.1.0"}, "type": "TeeSupport"}, {"enable": true, "params": {}, "type": "CheckCodes"}, {"enable": false, "params": {"Check NoCard": false, "Check SIM1": true, "Check SIM2": true, "Check TCard": true}, "type": "CheckCard"}, {"enable": false, "params": {"Max": "100", "Min": "40"}, "type": "CheckBatLevel"}, {"enable": false, "params": {}, "type": "<PERSON>e<PERSON><PERSON><PERSON>"}, {"enable": false, "params": {}, "type": "FactoryReset"}, {"enable": false, "params": {}, "type": "Reboot"}, {"enable": false, "params": {"MaxCurrent": "700", "MinCurrent": "50", "Sleep": "0", "TestTime": "10", "TriggerCurrent": "100", "TriggerTimeout": "20"}, "type": "PowerOnCurrent"}, {"enable": false, "params": {"MaxCurrent": "6", "MinCurrent": "2", "Sleep": "0", "TestTime": "5", "TriggerCurrent": "6", "TriggerTimeout": "120"}, "type": "DeepSleepCurrent"}, {"enable": false, "params": {"MaxCurrent": "1005", "MinCurrent": "300", "Sleep": "0", "TestTime": "5", "TriggerCurrent": "400", "TriggerTimeout": "20"}, "type": "ChargeCurrent"}, {"enable": true, "params": {}, "type": "PowerOff"}, {"enable": false, "params": {"MaxCurrent": "0.6", "MinCurrent": "0.03", "Sleep": "3000", "TestTime": "5", "TriggerCurrent": "0.6", "TriggerTimeout": "15"}, "type": "PowerOffCurrent"}]