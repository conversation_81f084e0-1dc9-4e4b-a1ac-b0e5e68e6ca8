#ifndef LOADSNITEM_H
#define LOADSNITEM_H

#include "../testcasebase.h"
#include <QCheckBox>
#include <QComboBox>
#include <QLineEdit>
#include <QFormLayout>

class LoadSnItem : public TestCaseBase
{
    Q_OBJECT
public:
    LoadSnItem();

    QString type() const override { return "LoadSN"; }
    static QString staticType() { return "LoadSN"; }
    QString displayName() const override { return "Load SN"; }

    QWidget* createParameterWidget(QWidget* parent) override;

    QVariantMap parameters() const override { return m_params; }
    void setParameters(const QVariantMap& params) override { m_params = params; }
};

#endif // LOADSNITEM_H
