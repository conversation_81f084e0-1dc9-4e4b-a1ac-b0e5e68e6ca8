<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MesSettingDialog</class>
 <widget class="QDialog" name="MesSettingDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>448</width>
    <height>338</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Mes Setting</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QGroupBox" name="MesSelect_groupBox">
     <property name="font">
      <font>
       <family>Arial</family>
       <bold>true</bold>
      </font>
     </property>
     <property name="title">
      <string>Mes Select</string>
     </property>
     <layout class="QGridLayout" name="gridLayout_2" columnstretch="0,1" columnminimumwidth="0,0">
      <item row="0" column="0">
       <widget class="QLabel" name="label">
        <property name="font">
         <font>
          <family>Arial</family>
          <bold>false</bold>
         </font>
        </property>
        <property name="text">
         <string>    Mes Type:</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QComboBox" name="MesSelect_comboBox">
        <property name="font">
         <font>
          <family>Arial</family>
          <bold>false</bold>
         </font>
        </property>
        <item>
         <property name="text">
          <string>AGN_MES</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>SD_MES</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>YD_MES</string>
         </property>
        </item>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="MesInfo_groupBox">
     <property name="font">
      <font>
       <family>Arial</family>
       <bold>true</bold>
      </font>
     </property>
     <property name="title">
      <string>Mes Info</string>
     </property>
     <layout class="QGridLayout" name="gridLayout">
      <item row="5" column="0">
       <widget class="QLabel" name="Line_label">
        <property name="font">
         <font>
          <family>Arial</family>
          <bold>false</bold>
         </font>
        </property>
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="text">
         <string>Line: </string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
       </widget>
      </item>
      <item row="5" column="2">
       <widget class="QLineEdit" name="line_Edit">
        <property name="font">
         <font>
          <family>Arial</family>
          <bold>false</bold>
         </font>
        </property>
       </widget>
      </item>
      <item row="3" column="2">
       <widget class="QLineEdit" name="operator_lineEdit">
        <property name="font">
         <font>
          <family>Arial</family>
          <bold>false</bold>
         </font>
        </property>
       </widget>
      </item>
      <item row="0" column="0">
       <widget class="QLabel" name="IP_label">
        <property name="font">
         <font>
          <family>Arial</family>
          <bold>false</bold>
         </font>
        </property>
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="text">
         <string>IP Address :</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
       </widget>
      </item>
      <item row="0" column="2">
       <widget class="QLineEdit" name="IP_Edit">
        <property name="font">
         <font>
          <family>Arial</family>
          <bold>false</bold>
         </font>
        </property>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="station_label">
        <property name="font">
         <font>
          <family>Arial</family>
          <bold>false</bold>
         </font>
        </property>
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="text">
         <string>Station:</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="order_label">
        <property name="font">
         <font>
          <family>Arial</family>
          <bold>false</bold>
         </font>
        </property>
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="text">
         <string>Order:</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
       </widget>
      </item>
      <item row="2" column="2">
       <widget class="QLineEdit" name="Station_lineEdit">
        <property name="font">
         <font>
          <family>Arial</family>
          <bold>false</bold>
         </font>
        </property>
       </widget>
      </item>
      <item row="3" column="0">
       <widget class="QLabel" name="operator_label">
        <property name="font">
         <font>
          <family>Arial</family>
          <bold>false</bold>
         </font>
        </property>
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="text">
         <string>Operator:</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
       </widget>
      </item>
      <item row="1" column="2">
       <widget class="QLineEdit" name="Order_lineEdit">
        <property name="font">
         <font>
          <family>Arial</family>
          <bold>false</bold>
         </font>
        </property>
       </widget>
      </item>
      <item row="6" column="0">
       <widget class="QLabel" name="Res_label">
        <property name="font">
         <font>
          <family>Arial</family>
          <bold>false</bold>
         </font>
        </property>
        <property name="text">
         <string>ResName:</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
       </widget>
      </item>
      <item row="6" column="2">
       <widget class="QLineEdit" name="ResName_lineEdit"/>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QCheckBox" name="Mes_Offline_checkBox">
       <property name="font">
        <font>
         <family>Arial</family>
        </font>
       </property>
       <property name="text">
        <string>Mes Offline Mode</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QCheckBox" name="NotCheckStation_checkBox">
       <property name="font">
        <font>
         <family>Arial</family>
        </font>
       </property>
       <property name="text">
        <string>Not Check Station</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QCheckBox" name="NotUpdateStation_checkBox">
       <property name="font">
        <font>
         <family>Arial</family>
        </font>
       </property>
       <property name="text">
        <string>Not Update Station</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="font">
      <font>
       <family>Arial</family>
      </font>
     </property>
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::Cancel|QDialogButtonBox::Ok</set>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>MesSettingDialog</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>248</x>
     <y>254</y>
    </hint>
    <hint type="destinationlabel">
     <x>157</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>buttonBox</sender>
   <signal>rejected()</signal>
   <receiver>MesSettingDialog</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>316</x>
     <y>260</y>
    </hint>
    <hint type="destinationlabel">
     <x>286</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
