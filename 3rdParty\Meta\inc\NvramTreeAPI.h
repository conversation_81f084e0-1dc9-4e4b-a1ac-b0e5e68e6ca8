/*
 * Copyright (c) 2020, MediaTek Inc. All rights reserved.
 *
 * This software/firmware and related documentation ("MediaTek Software") are
 * protected under relevant copyright laws.
 * The information contained herein is confidential and proprietary to
 * MediaTek Inc. and/or its licensors.
 * Except as otherwise provided in the applicable licensing terms with
 * MediaTek Inc. and/or its licensors, any reproduction, modification, use or
 * disclosure of MediaTek Software, and information contained herein, in whole
 * or in part, shall be strictly prohibited.
*/
#ifndef __NVRAM_TREE_API_H__
#define __NVRAM_TREE_API_H__

#include <vector>
#include <string>

#include "CrossToolCommon.h"

typedef void *NvramTree_Handle;
typedef void *NvramTree_LidHandle;
typedef void *NvramTree_FieldHandle;

DYNAMIC_LIBRARY_CROSSTOOLNVRAMEDITORCOMMON_API NvramTree_Handle      NvramTree_New(const uintptr_t dbAddr);
DYNAMIC_LIBRARY_CROSSTOOLNVRAMEDITORCOMMON_API void                  NvramTree_Delete(NvramTree_Handle *pHandle);
DYNAMIC_LIBRARY_CROSSTOOLNVRAMEDITORCOMMON_API bool                  NvramTree_Start2GetAllLIDs(const NvramTree_Handle handle);
DYNAMIC_LIBRARY_CROSSTOOLNVRAMEDITORCOMMON_API const char           *NvramTree_GetNextLID(const NvramTree_Handle handle, int *pNvramID = NULL);
DYNAMIC_LIBRARY_CROSSTOOLNVRAMEDITORCOMMON_API NvramTree_LidHandle   NvramTree_NewLID(const NvramTree_Handle handle, const char *lid);
DYNAMIC_LIBRARY_CROSSTOOLNVRAMEDITORCOMMON_API void                  NvramTree_DeleteLID(NvramTree_LidHandle *pHandle);
DYNAMIC_LIBRARY_CROSSTOOLNVRAMEDITORCOMMON_API int                   NvramTree_GetRecSize(const NvramTree_LidHandle lidHandle);
DYNAMIC_LIBRARY_CROSSTOOLNVRAMEDITORCOMMON_API int                   NvramTree_GetRecCount(const NvramTree_LidHandle lidHandle);
DYNAMIC_LIBRARY_CROSSTOOLNVRAMEDITORCOMMON_API NvramTree_FieldHandle NvramTree_GetRecStruct(const NvramTree_LidHandle lidHandle, int recIdx);
DYNAMIC_LIBRARY_CROSSTOOLNVRAMEDITORCOMMON_API int                   NvramTree_GetChildCount(const NvramTree_FieldHandle fieldHandle);
DYNAMIC_LIBRARY_CROSSTOOLNVRAMEDITORCOMMON_API NvramTree_FieldHandle NvramTree_GetChild(const NvramTree_FieldHandle fieldHandle, const int index);
DYNAMIC_LIBRARY_CROSSTOOLNVRAMEDITORCOMMON_API const char           *NvramTree_GetName(const NvramTree_FieldHandle fieldHandle);
DYNAMIC_LIBRARY_CROSSTOOLNVRAMEDITORCOMMON_API const char           *NvramTree_GetType(const NvramTree_Handle treeHandle, const NvramTree_FieldHandle fieldHandle);
DYNAMIC_LIBRARY_CROSSTOOLNVRAMEDITORCOMMON_API const char           *NvramTree_GetDescription(const NvramTree_Handle treeHandle, const NvramTree_FieldHandle fieldHandle);
DYNAMIC_LIBRARY_CROSSTOOLNVRAMEDITORCOMMON_API int                   NvramTree_GetOffset(const NvramTree_FieldHandle fieldHandle);
DYNAMIC_LIBRARY_CROSSTOOLNVRAMEDITORCOMMON_API int                   NvramTree_GetSize(const NvramTree_Handle treeHandle, const NvramTree_FieldHandle fieldHandle);
DYNAMIC_LIBRARY_CROSSTOOLNVRAMEDITORCOMMON_API char                  NvramTree_GetEditorType(const NvramTree_Handle treeHandle, const NvramTree_FieldHandle fieldHandle);
DYNAMIC_LIBRARY_CROSSTOOLNVRAMEDITORCOMMON_API int                   NvramTree_GetComboBoxStringCount(const NvramTree_FieldHandle fieldHandle);
DYNAMIC_LIBRARY_CROSSTOOLNVRAMEDITORCOMMON_API const char           *NvramTree_GetComboBoxString(const NvramTree_FieldHandle fieldHandle, const int index);
DYNAMIC_LIBRARY_CROSSTOOLNVRAMEDITORCOMMON_API char                  NvramTree_GetMode(const NvramTree_FieldHandle fieldHandle);
DYNAMIC_LIBRARY_CROSSTOOLNVRAMEDITORCOMMON_API const char           *NvramTree_GetValue(const NvramTree_Handle treeHandle, const NvramTree_FieldHandle fieldHandle, const char *buffer);
DYNAMIC_LIBRARY_CROSSTOOLNVRAMEDITORCOMMON_API bool                  NvramTree_GetDirty(const NvramTree_FieldHandle fieldHandle);
DYNAMIC_LIBRARY_CROSSTOOLNVRAMEDITORCOMMON_API void                  NvramTree_SetMode(NvramTree_FieldHandle fieldHandle, const char mode);
DYNAMIC_LIBRARY_CROSSTOOLNVRAMEDITORCOMMON_API bool                  NvramTree_SetValue(char *buffer, bool *pUpdateParent, const NvramTree_Handle treeHandle, const NvramTree_FieldHandle fieldHandle, const char *valueStr);
DYNAMIC_LIBRARY_CROSSTOOLNVRAMEDITORCOMMON_API void                  NvramTree_SetDirty(NvramTree_FieldHandle fieldHandle, bool dirty);
// GPRF Tool
DYNAMIC_LIBRARY_CROSSTOOLNVRAMEDITORCOMMON_API int                   NvramTree_GetCmdCatCount(const NvramTree_Handle handle, const char *enumName);
DYNAMIC_LIBRARY_CROSSTOOLNVRAMEDITORCOMMON_API const char           *NvramTree_GetCmdCat(const NvramTree_Handle handle, const char *enumName, const int index);
DYNAMIC_LIBRARY_CROSSTOOLNVRAMEDITORCOMMON_API int                   NvramTree_GetCmdTypeCount(const NvramTree_Handle handle, const char *enumName);
DYNAMIC_LIBRARY_CROSSTOOLNVRAMEDITORCOMMON_API const char           *NvramTree_GetCmdType(const NvramTree_Handle handle, const char *enumName, const int index);
DYNAMIC_LIBRARY_CROSSTOOLNVRAMEDITORCOMMON_API NvramTree_LidHandle   NvramTree_NewGprfStruct(const NvramTree_Handle handle, const char *structName);
DYNAMIC_LIBRARY_CROSSTOOLNVRAMEDITORCOMMON_API int                   NvramTree_GetEnumValue(const NvramTree_Handle handle, const char *enumName, const char *enumId);
DYNAMIC_LIBRARY_CROSSTOOLNVRAMEDITORCOMMON_API int                   NvramTree_GetStructSize(const NvramTree_Handle handle, const char *structName);
DYNAMIC_LIBRARY_CROSSTOOLNVRAMEDITORCOMMON_API int                   NvramTree_GetTypeDefOffset(const NvramTree_Handle handle, const char *structName, const char *typedefName);
DYNAMIC_LIBRARY_CROSSTOOLNVRAMEDITORCOMMON_API int                   NvramTree_GetTypeDefSize(const NvramTree_Handle handle, const char *structName, const char *typedefName);

#endif
