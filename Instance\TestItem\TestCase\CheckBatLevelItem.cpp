#include "CheckBatLevelItem.h"

CheckBatLevelItem::CheckBatLevelItem()
{
    m_params = {
        {"Min", "40"},
        {"Max", "100"},
    };
}

QWidget* CheckBatLevelItem::createParameterWidget(QWidget* parent)
{
    QWidget* widget = new QWidget(parent);
    QFormLayout* layout = new QFormLayout(widget);
    widget->setStyleSheet(R"(font: 9pt "Arial";)");

    QLineEdit *MinEdit = new QLineEdit(widget);
    MinEdit->setText(m_params["Min"].toString());
    connect(MinEdit, &QLineEdit::textChanged, [this](const QString& text) {
        m_params["Min"] = text;
        emit parametersChanged();
    });
    layout->addRow("Min: ", MinEdit);

    QLineEdit *MaxEdit = new QLineEdit(widget);
    MaxEdit->setText(m_params["Max"].toString());
    connect(MaxEdit, &QLineEdit::textChanged, [this](const QString& text) {
        m_params["Max"] = text;
        emit parametersChanged();
    });
    layout->addRow("Max: ", MaxEdit);

    return widget;
}
