#include "BottomCurrentItem.h"

BottomCurrentItem::BottomCurrentItem()
{
    m_params = {
                {"MaxCurrent", "0.6"},
                {"MinCurrent", "0.03"},
                {"TestTime", "5"},
                {"TriggerCurrent", "0.6"},
                {"TriggerTimeout", "15"},
                {"Sleep", "3000"},
                };
}

QWidget* BottomCurrentItem::createParameterWidget(QWidget* parent)
{
    QWidget* widget = new QWidget(parent);
    QFormLayout* layout = new QFormLayout(widget);
    widget->setStyleSheet(R"(font: 9pt "Arial";)");

    QIntValidator *IntValidator = new QIntValidator(0, 10000, this);
    QDoubleValidator *DoubleValidator = new QDoubleValidator(0.00, 10000.00, 2, this);

    QLineEdit* MaxCurrentEdit = new QLineEdit(widget);
    MaxCurrentEdit->setText(m_params["MaxCurrent"].toString());
    MaxCurrentEdit->setValidator(DoubleValidator);
    connect(MaxCurrentEdit, &QLineEdit::textChanged, [this](const QString& text) {
        m_params["MaxCurrent"] = text;
        emit parametersChanged();
    });
    layout->addRow("Max Current(mA):", MaxCurrentEdit);

    QLineEdit* MinCurrentEdit = new QLineEdit(widget);
    MinCurrentEdit->setText(m_params["MinCurrent"].toString());
    MinCurrentEdit->setValidator(DoubleValidator);
    connect(MinCurrentEdit, &QLineEdit::textChanged, [this](const QString& text) {
        m_params["MinCurrent"] = text;
        emit parametersChanged();
    });
    layout->addRow("Min Current(mA):", MinCurrentEdit);

    QLineEdit* TestTimeEdit = new QLineEdit(widget);
    TestTimeEdit->setText(m_params["TestTime"].toString());
    TestTimeEdit->setValidator(IntValidator);
    connect(TestTimeEdit, &QLineEdit::textChanged, [this](const QString& text) {
        m_params["TestTime"] = text;
        emit parametersChanged();
    });
    layout->addRow("Test Time(s):", TestTimeEdit);

    QLineEdit* TriggerCurrentEdit = new QLineEdit(widget);
    TriggerCurrentEdit->setText(m_params["TriggerCurrent"].toString());
    TriggerCurrentEdit->setValidator(DoubleValidator);
    connect(TriggerCurrentEdit, &QLineEdit::textChanged, [this](const QString& text) {
        m_params["TriggerCurrent"] = text;
        emit parametersChanged();
    });
    layout->addRow("Trigger Current(mA):", TriggerCurrentEdit);

    QLineEdit* TriggerTimeoutEdit = new QLineEdit(widget);
    TriggerTimeoutEdit->setText(m_params["TriggerTimeout"].toString());
    TriggerTimeoutEdit->setValidator(IntValidator);
    connect(TriggerTimeoutEdit, &QLineEdit::textChanged, [this](const QString& text) {
        m_params["TriggerTimeout"] = text;
        emit parametersChanged();
    });
    layout->addRow("Trigger Timeout(s):", TriggerTimeoutEdit);

    QLineEdit* SleepEdit = new QLineEdit(widget);
    SleepEdit->setText(m_params["Sleep"].toString());
    SleepEdit->setValidator(IntValidator);
    connect(SleepEdit, &QLineEdit::textChanged, [this](const QString& text) {
        m_params["Sleep"] = text;
        emit parametersChanged();
    });
    layout->addRow("Sleep Before Test(ms):", SleepEdit);

    return widget;
}

