#ifndef CHECKBATLEVELITEM_H
#define CHECKBATLEVELITEM_H

#include "../testcasebase.h"
#include <QCheckBox>
#include <QComboBox>
#include <QLineEdit>
#include <QFormLayout>

class CheckBatLevelItem: public TestCaseBase
{
    Q_OBJECT
public:
    CheckBatLevelItem();

    QString type() const override { return "CheckBatLevel"; }
    static QString staticType() { return "CheckBatLevel"; }
    QString displayName() const override { return "Check BatLevel"; }

    QWidget* createParameterWidget(QWidget* parent) override;

    QVariantMap parameters() const override { return m_params; }
    void setParameters(const QVariantMap& params) override { m_params = params; }
};

#endif // CHECKBATLEVELITEM_H
