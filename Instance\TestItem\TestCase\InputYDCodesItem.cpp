#include "InputYDCodesItem.h"

InputYDCodesItem::InputYDCodesItem()
{
    m_params = {
                {"IWSN", true},
                {"IWSN From", "Input"},
                {"OBSN", true},
                {"OBSN From", "Input"},
                {"BatLabel", true},
                {"BatLabel From", "Input"},
                {"BatLabel HeaderCheck", false},
                {"BatLabel Prefix", ""},
                {"BatLabel Length", "20"},
                {"Scril.Sf", true},
                {"Scril.Sf From", "Input"},
    };
}

QWidget* InputYDCodesItem::createParameterWidget(QWidget* parent)
{
    QWidget* widget = new QWidget(parent);
    QFormLayout* layout = new QFormLayout(widget);
    widget->setStyleSheet(R"(font: 9pt "Arial";)");

    //IWSN
    QCheckBox* IWSNEnable = new QCheckBox("Enable", widget);
    IWSNEnable->setChecked(m_params["IWSN"].toBool());
    connect(IWSNEnable, &QCheckBox::toggled, [this](bool checked) {
        m_params["IWSN"] = checked;
        emit parametersChanged();
    });

    QComboBox* IWSNCombo = new QComboBox(widget);
    IWSNCombo->addItems({"Input"});
    IWSNCombo->setCurrentText(m_params["IWSN From"].toString());
    connect(IWSNCombo, &QComboBox::currentTextChanged, [this](const QString& text) {
        m_params["IWSN From"] = text;
        emit parametersChanged();
    });

    QGroupBox* IWSNGroup = new QGroupBox("IWSN", widget);
    IWSNGroup->setStyleSheet(styleSheet);
    QFormLayout* IWSNLayout = new QFormLayout(IWSNGroup);
    IWSNGroup->setLayout(IWSNLayout);
    IWSNLayout->addRow("IWSN: ", IWSNEnable);
    IWSNLayout->addRow("IWSN From:", IWSNCombo);
    layout->addRow(IWSNGroup);


    //OBSN
    QCheckBox* OBSNEnable = new QCheckBox("Enable", widget);
    OBSNEnable->setChecked(m_params["OBSN"].toBool());
    connect(OBSNEnable, &QCheckBox::toggled, [this](bool checked) {
        m_params["OBSN"] = checked;
        emit parametersChanged();
    });

    QComboBox* OBSNCombo = new QComboBox(widget);
    OBSNCombo->addItems({"Input"});
    OBSNCombo->setCurrentText(m_params["OBSN From"].toString());
    connect(OBSNCombo, &QComboBox::currentTextChanged, [this](const QString& text) {
        m_params["OBSN From"] = text;
        emit parametersChanged();
    });

    QGroupBox* OBSNGroup = new QGroupBox("OBSN", widget);
    OBSNGroup->setStyleSheet(styleSheet);
    QFormLayout* OBSNLayout = new QFormLayout(OBSNGroup);
    OBSNGroup->setLayout(OBSNLayout);
    OBSNLayout->addRow("OBSN: ", OBSNEnable);
    OBSNLayout->addRow("OBSN From:", OBSNCombo);
    layout->addRow(OBSNGroup);


    //BatteryLable
    QCheckBox* BatLableEnable = new QCheckBox("Enable", widget);
    BatLableEnable->setChecked(m_params["BatLabel"].toBool());
    connect(BatLableEnable, &QCheckBox::toggled, [this](bool checked) {
        m_params["BatLabel"] = checked;
        emit parametersChanged();
    });

    QComboBox* BatLableCombo = new QComboBox(widget);
    BatLableCombo->addItems({"Input"});
    BatLableCombo->setCurrentText(m_params["BatLabel From"].toString());
    connect(BatLableCombo, &QComboBox::currentTextChanged, [this](const QString& text) {
        m_params["BatLabel From"] = text;
        emit parametersChanged();
    });

    QCheckBox* BatLableLimitEnable = new QCheckBox("Enable", widget);
    BatLableLimitEnable->setChecked(m_params["BatLabel HeaderCheck"].toBool());
    connect(BatLableLimitEnable, &QCheckBox::toggled, [this](bool checked) {
        m_params["BatLabel HeaderCheck"] = checked;
        emit parametersChanged();
    });

    QLineEdit* BatLablePrefix = new QLineEdit(widget);
    BatLablePrefix->setText(m_params["BatLabel Prefix"].toString());
    BatLablePrefix->setValidator(STRINGValidator);
    connect(BatLablePrefix, &QLineEdit::textChanged, [this](const QString& text) {
        m_params["BatLabel Prefix"] = text;
        emit parametersChanged();
    });

    QLineEdit* BatLableLength = new QLineEdit(widget);
    BatLableLength->setText(m_params["BatLabel Length"].toString());
    BatLableLength->setValidator(UINTValidator);
    connect(BatLableLength, &QLineEdit::textChanged, [this](const QString& text) {
        m_params["BatLabel Length"] = text;
        emit parametersChanged();
    });

    QGroupBox* BatLabelGroup = new QGroupBox("BatLabel", widget);
    BatLabelGroup->setStyleSheet(styleSheet);
    QFormLayout* BatLabelLayout = new QFormLayout(BatLabelGroup);
    BatLabelGroup->setLayout(BatLabelLayout);
    BatLabelLayout->addRow("BatLabel: ", BatLableEnable);
    BatLabelLayout->addRow("BatLabel From:", BatLableCombo);
    BatLabelLayout->addRow("Header Check: ", BatLableLimitEnable);
    BatLabelLayout->addRow("Prefix: ", BatLablePrefix);
    BatLabelLayout->addRow("Length: ", BatLableLength);
    layout->addRow(BatLabelGroup);


    //Scril.Sf
    QCheckBox* ScrilSfEnable = new QCheckBox("Enable", widget);
    ScrilSfEnable->setChecked(m_params["Scril.Sf"].toBool());
    connect(ScrilSfEnable, &QCheckBox::toggled, [this](bool checked) {
        m_params["Scril.Sf"] = checked;
        emit parametersChanged();
    });

    QComboBox* ScrilSfCombo = new QComboBox(widget);
    ScrilSfCombo->addItems({"Input", "Mes"});
    ScrilSfCombo->setCurrentText(m_params["Scril.Sf From"].toString());
    connect(ScrilSfCombo, &QComboBox::currentTextChanged, [this](const QString& text) {
        m_params["Scril.Sf From"] = text;
        emit parametersChanged();
    });

    QGroupBox* ScrilSfGroup = new QGroupBox("ScrilSf", widget);
    ScrilSfGroup->setStyleSheet(styleSheet);
    QFormLayout* ScrilSfLayout = new QFormLayout(ScrilSfGroup);
    ScrilSfGroup->setLayout(ScrilSfLayout);
    ScrilSfLayout->addRow("Scril.Sf: ", ScrilSfEnable);
    ScrilSfLayout->addRow("Scril.Sf From:", ScrilSfCombo);
    layout->addRow(ScrilSfGroup);


    return widget;
}
