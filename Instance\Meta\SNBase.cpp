#include "SNBase.h"

SNBase::SNBase(QObject *parent, UINT nThreadID)
    : QThread(parent)
    , m_nThreadID(nThreadID)
{
    m_sNVRAM_OPID = 0;
    m_bWriteNvram = false;
    m_hWriteToNVRAMEvent = NULL;
}

void SNBase::SNThread_Init()
{
    m_hWriteToNVRAMEvent = NULL;
    memset(m_sIMEISV, 0, sizeof(IMEISV_struct_T)*MAX_IMEI_NUMS);
    memset(&m_sScanData, 0, sizeof(ScanData_struct));
    m_sScanData = Common::getInstance()->getScanData(m_nThreadID);
}

META_RESULT SNBase::ConductBarcodeData(char *pOutData, unsigned short RID_para, char *pInDatabuf, int outBufSize)
{
    char flag_wire;     // wireless flag
    char flag_cal[2];   // calibration flag
    char flag_nsft;     // nsft flag

    flag_wire = pOutData[59];
    flag_cal[0] = pOutData[60];
    flag_cal[1] = pOutData[61];
    flag_nsft = pOutData[62];

    memcpy(pOutData, pInDatabuf, BARCODE_MAX_LENGTH);

    if (flag_wire != '\0' && flag_cal[0] != '\0' && flag_cal[1] != '\0' && flag_nsft != '\0')
    {
        int barcode_len;
        barcode_len = strlen(pOutData);
        if (barcode_len < 59)
            memset(pOutData + barcode_len, 0x20, 59 - barcode_len);

        pOutData[59] = flag_wire;
        pOutData[60] = flag_cal[0];
        pOutData[61] = flag_cal[1];
        pOutData[62] = flag_nsft;
        pOutData[63] = '\0';
    }

    // Sync to AP Prod_Info
    memcpy(m_sScanData.strBarcode, pOutData, BARCODE_MAX_LENGTH);

    return META_SUCCESS;
}

META_RESULT SNBase::ConductIMEIData(char *pOutData, unsigned short RID_para, char *pInDatabuf, int outBufSize)
{
    if (pOutData == NULL || pInDatabuf == NULL || outBufSize <= 0)
        return META_INVALID_ARGUMENTS;

    /*
    //UI input = "123459876543210" storage in nvram will be:
    //imei[0] = 0x21
    //imei[1] = 0x43
    //imei[2] = 0x95
    //imei[3] = 0x78
    //imei[4] = 0x56
    //imei[5] = 0x34
    //imei[6] = 0x12
    //imei[7] = 0xf0
    */
    META_RESULT MetaResult;
    char *pFuncName;

    if (m_bWriteNvram == false)//read from nvram
    {
        MTRACE (m_hDebugTrace[m_nThreadID], "SNBase::Decompose_IMEI(): Start to Decompose IMEI[%d]...", RID_para-1);
        pFuncName = "SNBase::Decompose_IMEI(): Decompose IMEI";
        MetaResult = Decompose_IMEI(pOutData, RID_para, pInDatabuf, outBufSize);
    }
    else //write to nvram
    {
        MTRACE (m_hDebugTrace[m_nThreadID], "SNBase::Compose_IMEI(): Start to Compose IMEI[%d]...", RID_para-1);
        pFuncName = "SNBase::Compose_IMEI(): Compose IMEI";
        MetaResult = Compose_IMEI( pOutData, RID_para, pInDatabuf, outBufSize, true);
    }

    if(MetaResult == META_SUCCESS)
    {
        MTRACE (m_hDebugTrace[m_nThreadID], "%s successfully!", pFuncName);
    }
    else
    {
        MTRACE_ERR (m_hDebugTrace[m_nThreadID], "%s Fail! MetaResult = %s",  pFuncName, Common::getInstance()->ResultToString(MetaResult));
    }
    return MetaResult;
}

META_RESULT SNBase::ConductBTAddrData(char *pOutData, unsigned short RID_para, char *pInDatabuf, int outBufSize)
{
    if (pOutData == NULL || pInDatabuf == NULL || outBufSize <= 0)
        return META_INVALID_ARGUMENTS;

    /*
    *** Feature phone ***
    *  UI input = "1234567890AC" storage in Modem nvram will be:
    * btAddr[0] = 0xAC
    * btAddr[1] = 0x90
    * btAddr[2] = 0x78
    * btAddr[3] = 0x56
    * btAddr[4] = 0x34
    * btAddr[5] = 0x12
    ---------------------------------------------------------
    *** Smart Phone ***
    * UI input = "1234567890AC" storage in AP nvram will be:
    * btAddr[0] = 0x12
    * btAddr[1] = 0x34
    * btAddr[2] = 0x56
    * btAddr[3] = 0x78
    * btAddr[4] = 0x90
    * btAddr[5] = 0xAC
    */

    int tmpCh;
    if (!m_bWriteNvram)
    {
        for (int i = 0; i < outBufSize; i++)
        {
            tmpCh = (pInDatabuf[i] & 15);  //15 -->0000 1111, get Low 4Bits
            if (0 <= tmpCh && tmpCh <= 9)  //0 - 9
            {
                pOutData[(i*2 + 1)] = tmpCh + '0';
            }
            else if (10 <= tmpCh && tmpCh <= 15) //A - F
            {
                pOutData[(i*2 + 1)] = (tmpCh - 10) + 'A';
            }

            //tmpCh = ((pInDatabuf[i] >> 4)  & 15);
            tmpCh = ((pInDatabuf[i] & 240) >> 4); //240 -->1111 0000, get High 4Bits
            if (0 <= tmpCh && tmpCh <= 9)  //0 - 9
            {
                pOutData[i*2] = tmpCh + '0';
            }
            else if (10 <= tmpCh && tmpCh <= 15) //A - F
            {
                pOutData[i*2] = (tmpCh - 10) + 'A';
            }
        }
    }
    else if(m_bWriteNvram)
    {
        _strupr_s(pInDatabuf, strlen(pInDatabuf)+1);
        for (int i = 0; i < outBufSize; i++)
        {
            if ('0'<= pInDatabuf[i*2] && pInDatabuf[i*2] <= '9')
            {
                tmpCh = pInDatabuf[i*2] - '0';
                tmpCh = ((tmpCh << 4) & 240);  //240 -->1111 0000, get High 4bits
            }
            else if('A'<= pInDatabuf[i*2] && pInDatabuf[i*2] <= 'F')
            {
                tmpCh = pInDatabuf[i*2] - 'A' + 10;
                tmpCh = ((tmpCh << 4) & 240);  //240 -->1111 0000, get High 4bits
            }

            if ('0'<= pInDatabuf[(i*2 + 1)] && pInDatabuf[(i*2 + 1)] <= '9')
            {
                tmpCh += pInDatabuf[(i*2 + 1)] - '0'; //get Low 4bits
            }
            else if('A'<= pInDatabuf[(i*2 + 1)] && pInDatabuf[(i*2 + 1)] <= 'F')
            {
                tmpCh += pInDatabuf[(i*2 + 1)] - 'A' + 10; //get Low 4bits
            }

            pOutData[i] = tmpCh;
        }
    }

    return META_SUCCESS;
}

META_RESULT SNBase::ConductWifiAddrData(char *pOutData, unsigned short RID_para, char *pInDatabuf, int outBufSize)
{
    if (pOutData == NULL || pInDatabuf == NULL || outBufSize <= 0)
        return META_INVALID_ARGUMENTS;

    //strupr(pInDatabuf);       //转换字符串中的小写字母为大写
    //strlwr(pInDatabuf);       //转换字符串的大写字母为小写
    /*
    * UI input = "1234567890AC" storage in AP nvram will be:
    * wifiAddr[0] = 0x12
    * wifiAddr[1] = 0x34
    * wifiAddr[2] = 0x56
    * wifiAddr[3] = 0x78
    * wifiAddr[4] = 0x90
    * wifiAddr[5] = 0xAC
    */

    int tmpCh;
    if (m_bWriteNvram == false)
    {
        for (int i = 0; i < outBufSize; i++)
        {
            tmpCh = (pInDatabuf[i] & 15);  //15 -->0000 1111, get Low 4Bits
            if (0 <= tmpCh && tmpCh <= 9)  //0 - 9
            {
                pOutData[(i*2 + 1)] = tmpCh + '0';
            }
            else if (10 <= tmpCh && tmpCh <= 15) //a - f
            {
                pOutData[(i*2 + 1)] = (tmpCh - 10) + 'A';
            }

            //tmpCh = ((pInDatabuf[i] >> 4)  & 15);
            tmpCh = ((pInDatabuf[i] & 240) >> 4); //240 -->1111 0000, get High 4Bits
            if (0 <= tmpCh && tmpCh <= 9)  //0 - 9
            {
                pOutData[i*2] = tmpCh + '0';
            }
            else if (10 <= tmpCh && tmpCh <= 15) //A - F
            {
                pOutData[i*2] = (tmpCh - 10) + 'A';
            }
        }
    }
    else if(m_bWriteNvram == true)
    {
        _strupr_s(pInDatabuf, strlen(pInDatabuf)+1);
        for (int i = 0; i < outBufSize; i++)
        {
            if ('0'<= pInDatabuf[i*2] && pInDatabuf[i*2] <= '9')
            {
                tmpCh = pInDatabuf[i*2] - '0';
                tmpCh = ((tmpCh << 4) & 240);  //240 -->1111 0000, get High 4bits
            }
            else if('A'<= pInDatabuf[i*2] && pInDatabuf[i*2] <= 'F')
            {
                tmpCh = pInDatabuf[i*2] - 'A' + 10;
                tmpCh = ((tmpCh << 4) & 240);  //240 -->1111 0000, get High 4bits
            }

            if ('0'<= pInDatabuf[(i*2 + 1)] && pInDatabuf[(i*2 + 1)] <= '9')
            {
                tmpCh += pInDatabuf[(i*2 + 1)] - '0'; //get Low 4bits
            }
            else if('A'<= pInDatabuf[(i*2 + 1)] && pInDatabuf[(i*2 + 1)] <= 'F')
            {
                tmpCh += pInDatabuf[(i*2 + 1)] - 'A' + 10; //get Low 4bits
            }

            pOutData[i] = tmpCh;
        }
    }

    return META_SUCCESS;
}

META_RESULT SNBase::ConductEthernetMacAddrData(char *pOutData, unsigned short RID_para, char *pInDatabuf, int outBufSize)
{
    if (pOutData == NULL || pInDatabuf == NULL || outBufSize <= 0)
        return META_INVALID_ARGUMENTS;

    /*
    * UI input = "1234567890AC" storage in AP nvram will be:
    * ethMacAddr[0] = 0x12
    * ethMacAddr[1] = 0x34
    * ethMacAddr[2] = 0x56
    * ethMacAddr[3] = 0x78
    * ethMacAddr[4] = 0x90
    * ethMacAddr[5] = 0xAC
    *
    * The same as wifi address, so just need to call ConductWifiAddrData() function
    */
    return ConductWifiAddrData(pOutData, RID_para, pInDatabuf, outBufSize);
}

META_RESULT SNBase::ConductMEIDData(char *pOutData, unsigned short RID_para, char *pInDatabuf, int outBufSize)
{
    if (pOutData == NULL || pInDatabuf == NULL || outBufSize <= 0)
        return META_INVALID_ARGUMENTS;

    /*
    * UI input = "12345678901234" storage in MD nvram will be:
    //pOutData (sNVRAM_ReadCnf.buf):
    //buf[0] = 0x01 ---- mobileldType(1:ESN;2MEID)
    //buf[1] = 0x00 ---- Not Use
    //buf[2] = 0x40 ---- Not Use
    //buf[3] = 0x02 ---- Not Use
    //buf[4] = 0x00 ---- Not Use
    //buf[5] = 0xF2 ---- Not Use
    //buf[6] = 0xF2 ---- Not Use
    //buf[7] = 0xF2 ---- Not Use
    //buf[8] = 0x34 ---- High 4Bits:Input[12];Low 4Bits:Input[13]
    //buf[9] = 0x12 ---- High 4Bits:Input[10];Low 4Bits:Input[11]
    //buf[10] = 0x90 --- High 4Bits:Input[8] ;Low 4Bits:Input[9]
    //buf[11] = 0x78 --- High 4Bits:Input[6] ;Low 4Bits:Input[7]
    //buf[12] = 0x56 --- High 4Bits:Input[4] ;Low 4Bits:Input[5]
    //buf[13] = 0x34 --- High 4Bits:Input[2] ;Low 4Bits:Input[3]
    //buf[14] = 0x12 --- High 4Bits:Input[0] ;Low 4Bits:Input[1]
    //buf[15] = 0x00 --- Not Use
    */
    META_RESULT MetaResult = META_SUCCESS;
    char *pFuncName;

    unsigned char tmpCh = 0;
    unsigned char   tmp[7];
    int i;

    memset(tmp, 0xff, sizeof(tmp));
    pFuncName = "SNBase::ConductMEIDData()";
    if (m_bWriteNvram == false)//read from nvram
    {
        MTRACE (m_hDebugTrace[m_nThreadID], "SNBase::Decompose_IMEI(): Start to Decompose MEID...");

        for (i = 0; i < 16; i++)
        {
            tmpCh = (unsigned char)pInDatabuf[i];
            MTRACE(m_hDebugTrace[m_nThreadID], "SNBase::ConductMEIDData():read nvram buff[%d] = 0x%02x",i, tmpCh);
        }

        for (i = 0; i < 7; i++)
        {
            tmpCh = (unsigned char)pInDatabuf[14 - i];
            unsigned char highNibble = (tmpCh & 0xF0) >> 4;
            unsigned char lowNibble = tmpCh & 0x0F;

            pOutData[2*i] = highNibble + 48;
            pOutData[2*i + 1] = lowNibble + 48;
        }
    }
    else //write to nvram
    {
        MTRACE (m_hDebugTrace[m_nThreadID], "SNBase::ConductMEIDData(): Start to Compose MEID...");

        for (i = 0; i < 16; i++)
        {
            tmpCh = (unsigned char)pOutData[i];
            MTRACE(m_hDebugTrace[m_nThreadID], "SNBase::ConductMEIDData():read nvram buff[%d] = 0x%02x",i, tmpCh);
        }

        for (i = 0; i < 14; i++)
        {
            tmpCh = pInDatabuf[i] - 48;
            MTRACE(m_hDebugTrace[m_nThreadID], "SNBase::ConductMEIDData():pInDatabuf[%d] = 0x%02x",i, pInDatabuf[i]);
            if (0 == i % 2)
            {
                tmp[i / 2] = ((tmpCh << 4) & 0xf0) | (tmp[i / 2] & 0x0f);
                //MTRACE(m_hDebugTrace[m_nThreadID], "SNBase::ConductMEIDData():tmp[%d] = 0x%02x",i/2, tmp[i/2]);
            }
            else
            {
                tmp[i / 2]  = (tmpCh & 0x0f) | (tmp[i / 2] & 0xf0);
                //MTRACE(m_hDebugTrace[m_nThreadID], "SNBase::ConductMEIDData():tmp[%d] = 0x%02x",i/2, tmp[i/2]);
            }
        }
        /*
        pOutData[14] =  tmp[0];
        pOutData[13] =  tmp[1];
        pOutData[12] =  tmp[2];
        pOutData[11] =  tmp[3];
        pOutData[10] =  tmp[4];
        pOutData[09] =  tmp[5];
        pOutData[08] =  tmp[6];
        */
        for (i = 0; i < 7; i++)
        {
            MTRACE(m_hDebugTrace[m_nThreadID], "SNBase::ConductMEIDData():tmp[%d] = 0x%02x",i, tmp[i]);
            pOutData[14-i] = tmp[i];
        }

        for (i = 0; i < 16; i++)
        {
            tmpCh = (unsigned char)pOutData[i];
            MTRACE(m_hDebugTrace[m_nThreadID], "SNBase::ConductMEIDData():Compose buff[%d] = 0x%02x",i, tmpCh);
        }
    }

    if(MetaResult == META_SUCCESS)
    {
        MTRACE (m_hDebugTrace[m_nThreadID], "%s successfully!", pFuncName);
    }
    else
    {
        MTRACE_ERR (m_hDebugTrace[m_nThreadID], "%s Fail! MetaResult = %s",  pFuncName, Common::getInstance()->ResultToString(MetaResult));
    }
    return MetaResult;



    return MetaResult;
}

META_RESULT SNBase::Decompose_IMEI(char *pOutData, unsigned short RID_para,  char *pInDatabuf, int bufSize)
{
    META_RESULT MetaResult = META_NVRAM_Decompose_IMEISV(&m_sIMEISV[RID_para-1], pInDatabuf, bufSize);
    if (MetaResult == META_SUCCESS)
    {
        for (int i=0; i<IMEI_ARRAY_LEN; i++)
        {
            pOutData[i] = m_sIMEISV[RID_para-1].imei[i];
        }
    }

    return MetaResult;
}

META_RESULT SNBase::Compose_IMEI(char *pOutData, unsigned short RID_para, char *pInDatabuf, int bufSize, bool bChecksum)
{
    META_RESULT MetaResult = META_SUCCESS;
    if (bChecksum)
    {
        int ret_i;
        char checksum_c;
        unsigned short checksum_v;

        MTRACE(m_hDebugTrace[m_nThreadID], "SNBase::Compose_IMEI(): Calculate checksum bit start...");
        MetaResult = META_NVRAM_Calculate_IMEI_CD(pInDatabuf, &checksum_v);
        if (MetaResult == META_SUCCESS)
        {
            checksum_c = checksum_v + '0';

            /*if (pInDatabuf[IMEI_MAX_LENGTH - 1] != '\0' && pInDatabuf[IMEI_MAX_LENGTH - 1] != checksum_c)
            {
                char szInfo[256];

                MTRACE_WARN (m_hDebugTrace[m_nThreadID], "SNBase::Compose_IMEI(): check IMEI checksum bit fail.");
                sprintf_s(szInfo, "The input IMEI %s checksum bit dismatch:\r\n"
                    "origin checksum %c, right checksum %c\r\n"
                    "Are you sure to continue anyway?",
                    pInDatabuf, pInDatabuf[IMEI_MAX_LENGTH - 1], checksum_c);
                ret_i = ::MessageBox(NULL, szInfo, "Warning",
                    MB_YESNO|MB_ICONWARNING|MB_DEFBUTTON2|MB_SYSTEMMODAL|MB_SETFOREGROUND|MB_TOPMOST);
                if (ret_i != IDYES)
                    return META_FAILED;

                pInDatabuf[IMEI_MAX_LENGTH - 1] = checksum_c;
            }*/
            pInDatabuf[IMEI_MAX_LENGTH - 1] = checksum_c;
        }
        else
        {
            MTRACE_ERR(m_hDebugTrace[m_nThreadID], "SNBase::Compose_IMEI(): Calculate checksum bit fail, MetaResult = %s", Common::getInstance()->ResultToString(MetaResult));
            return MetaResult;
        }
        MTRACE(m_hDebugTrace[m_nThreadID], "SNBase::Compose_IMEI(): Calculate checksum bit end...");
    }

    for (int i = 0; i < IMEI_ARRAY_LEN; i++)
    {
        m_sIMEISV[RID_para - 1].imei[i] = pInDatabuf[i];
    }

    MetaResult = META_NVRAM_Compose_IMEISV_ex(&m_sIMEISV[RID_para - 1], pOutData, bufSize, bChecksum);
    return MetaResult;
}

META_RESULT SNBase::REQ_ReadFromModemNVRAM(FT_NVRAM_READ_REQ *psNVRAM_ReadReq, FT_NVRAM_4BYTES_LID_READ_CNF *psNVRAM_ReadCnf)
{
    META_RESULT meta_result;
    meta_result = /*META_NVRAM_Read_Ex_r*/META_NVRAM_4Bytes_LID_Read_Ex_r(m_hMauiMetaHandle,
                                                                           15000,
                                                                           psNVRAM_ReadReq,
                                                                           psNVRAM_ReadCnf);

    if (meta_result != META_SUCCESS)
    {
        MTRACE_ERR (m_hDebugTrace[m_nThreadID], "SNBase::META_NVRAM_4Bytes_LID_Read_Ex_r(): Read nvram data fail! MetaResult = %s",
                   Common::getInstance()->ResultToString(meta_result));
    }

    return meta_result;
}

META_RESULT SNBase::REQ_WriteToModemNVRAM(FT_NVRAM_WRITE_REQ *psNVRAM_WriteReq, FT_NVRAM_4BYTES_LID_WRITE_CNF *psNVRAM_WriteCnf)
{
    META_RESULT meta_result;
    meta_result = /*META_NVRAM_Write_Ex_r*/META_NVRAM_4Bytes_LID_Write_Ex_r(m_hMauiMetaHandle,
                                                                             15000,
                                                                             psNVRAM_WriteReq,
                                                                             psNVRAM_WriteCnf);

    if (meta_result != META_SUCCESS)
    {
        MTRACE_ERR (m_hDebugTrace[m_nThreadID], "SNBase::META_NVRAM_Write_Ex_r(): Write nvram data fail! MetaResult = %s", Common::getInstance()->ResultToString(meta_result));
        return meta_result;
    }

    return meta_result;
}
