#include "HttpClient.h"
#include <QNetworkRequest>
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QEventLoop>
#include <QTextCodec>
#include <QFileInfo>
#include <QMessageBox>
#include <QUrl>
#include <QDir>
#include <QString>
#include <Common/Common.h>

HttpClient::HttpClient()
{
    m_strContentType = "text/xml;charset=utf-8";
}

QString HttpClient::HttpGet(const QString url)
{
    QNetworkAccessManager qnam;
    const QUrl aurl( url );
    QNetworkRequest qnr( aurl );
    qnr.setRawHeader("Content-Type",m_strContentType.toLatin1());
    QNetworkReply *reply = qnam.get( qnr );

    QEventLoop eventloop;
    connect( reply,SIGNAL(finished()),&eventloop,SLOT(quit()));
    eventloop.exec( QEventLoop::ExcludeUserInputEvents);

    QTextCodec *codec = QTextCodec::codecForName("utf8");
    QString replyData = codec->toUnicode( reply->readAll() );

    reply->deleteLater();
    reply = 0;

    return replyData;
}

QString HttpClient::HttpPost(const QString url, const QString data)
{
    QNetworkAccessManager qnam;
    const QUrl aurl( url );
    QNetworkRequest qnr( aurl );
    //qnr.setRawHeader("Content-Type",m_strContentType);
    qnr.setHeader(QNetworkRequest::ContentTypeHeader
                  , m_strContentType);
    QNetworkReply *reply = qnam.post( qnr, data.toLocal8Bit() );

    QEventLoop eventloop;
    connect( reply,SIGNAL(finished()),&eventloop,SLOT(quit()));
    eventloop.exec( QEventLoop::ExcludeUserInputEvents);

    QTextCodec *codec = QTextCodec::codecForName("utf8");
    QString replyData = codec->toUnicode( reply->readAll() );

    reply->deleteLater();
    reply = 0;

    return replyData;
}

void HttpClient::SetContentType(QString strContentType)
{
    m_strContentType = strContentType;
}
