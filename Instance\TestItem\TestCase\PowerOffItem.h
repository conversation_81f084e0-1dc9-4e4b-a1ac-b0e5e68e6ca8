#ifndef POWEROFFITEM_H
#define POWEROFFITEM_H

#include "../testcasebase.h"
#include <QCheckBox>
#include <QComboBox>
#include <QLineEdit>
#include <QFormLayout>

class PowerOffItem: public TestCaseBase
{
    Q_OBJECT
public:
    PowerOffItem();

    QString type() const override { return "PowerOff"; }
    static QString staticType() { return "PowerOff"; }
    QString displayName() const override { return "Power Off"; }

    QWidget* createParameterWidget(QWidget* parent) override;

    QVariantMap parameters() const override { return m_params; }
    void setParameters(const QVariantMap& params) override { m_params = params; }
};

#endif // POWEROFFITEM_H
