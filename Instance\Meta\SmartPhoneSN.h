// META
#include "Instance/Meta/SNBase.h"
#include "C2kAgent_api.h"

class PortEnumHelper
{
    static const unsigned int ms_iFilterLen;
    static const unsigned int ms_iFilterNum;
    static const unsigned int ms_iPortNum;

    char * m_szFilter;

    int * m_aiFlag;
    SP_COM_FILTER_LIST_S m_stFiler;

    SP_COM_PROPERTY_S * m_astPort;

public:
    PortEnumHelper::PortEnumHelper();
    PortEnumHelper::~PortEnumHelper();

    // suport multiport, delim by space/tab/comma
    void SetFilter(SP_FILTER_TYPE_E type, const char * filter, int flag = 0, bool bAppend = false);
    SP_COM_FILTER_LIST_S * GetFilter();
    int GetFlag(int index);
    SP_COM_PROPERTY_S * GetPorts(bool bClear = false);
};


class SmartPhoneSN : public SNBase
{
    Q_OBJECT
private:
    int            m_hSPMetaHandle;
    MODEM_CAPABILITY_LIST_CNF m_SpMdCapList;
    SP_MODEMInfo_s m_sMdInfo;

    bool m_bWorldPhone;
    bool m_bDualModem;
    bool m_bWithoutMD;
    bool m_bWriteProdInfo;
    //For MT6595 DSDA project
    bool m_bDSDAProject;
    bool m_bInitExtMDdb;

    UINT m_iMDChannelIndex[MAX_MD_CHANNEL_NUM];
    UINT m_iCurMDChanelIndex;

    SP_METAMODE_e m_eMetaMode;

    SP_BOOT_ARG_S m_stModeArg;
    META_Connect_Ex_Req m_tMetaReq_Ex;
    META_Connect_Report m_tMetaConnReport_Ex;
    UINT m_nKernelComport;

    UINT m_nTreadID;
    int  *m_pMetaStopFlag;

    bool   m_bTargetInMetaMode;
    bool   m_bStopBeforeUSBInsert;

    HANDLE  m_hReadFromNVRAMEvent;

    QJsonObject testCaseObj;
    QJsonObject YDtestCaseObj;

public:
    int m_iC2kProject;

public:
    SmartPhoneSN(QObject *parent = nullptr, UINT nTreadID = 0);
    ~SmartPhoneSN(void);

    virtual META_RESULT BootMetaMode();
    virtual META_RESULT InitAPMeta();
    virtual META_RESULT InitMDMeta();

    virtual META_RESULT ReadBarcodeFromMd(char* barcode);
    virtual META_RESULT ReadBarcodeFromAp(char* barcode);
    virtual META_RESULT ExitMetaMode(bool bpass = false);
    virtual META_RESULT ExitMetaModeToReboot(bool bpass = false);
    virtual META_RESULT REQ_BackupNvram2BinRegion_Start();
    virtual void WaitKernelPortDisappear();
    virtual META_RESULT REQ_ReadFromAPNvram(const char* pLID,  unsigned short iRID, unsigned char *pOutDataBuf, const unsigned int nBufLen);
    virtual META_RESULT REQ_WriteModem_NVRAM_Start(WriteData_Type_e dataType, char *pInData, unsigned short iRID);

    virtual META_RESULT EnterC2KGen90(bool WriteFlag = true, char* m_szReadBack = nullptr);
    virtual int EnterC2KGen93(bool WriteFlag = true, char* m_szReadBack = nullptr);
    virtual int WriteMEID90(bool WriteFlag = true, char* m_szReadBack = nullptr);
    virtual int WriteESN90(bool WriteFlag = true, char* m_szReadBack = nullptr);
    virtual int WriteMEID93(bool WriteFlag = true, char* m_szReadBack = nullptr);

    virtual META_RESULT REQ_WriteAP_NVRAM_Start(WriteData_Type_e dataType, char *pInData, unsigned short iRID);

    virtual META_RESULT REQ_WriteAP_PRODINFO_Start();

    bool CheckMdBarcode(char *szReadBackSN,int iReadBackSNLen);

    bool CheckAPBarcode(char *szReadBackSN,int iReadBackSNLen);

    bool CheckIMEI1(char *szReadBackImei);

    bool CheckIMEI2(char *szReadBackImei);

    bool CheckMEID(char *szReadBackMeid);

    bool CheckBtAddr(char *szReadBackBT);

    bool CheckWifiMac(char *szReadBackWifi);

    bool FactoryReset();

    bool Reboot();

    bool CheckSIM(int num);

    bool CheckTCard();

    META_RESULT ReadSwVer(char* Prop, char* m_szReadVer);

    META_RESULT GetBatLevel(int *Level);

    bool CheckBarcodeFlag();

    META_RESULT TEE_Authorize();

    META_RESULT TEE_Check();

    bool CheckMdSN(char *szReadBackSN,int iReadBackSNLen, int codetype = 0);//0->OBSN; 1->IWSN

    bool CheckAPSN(char *szReadBackSN,int iReadBackSNLen, int codetype = 0);//0->OBSN; 1->IWSN

    bool CheckSerialNo();

    bool GetScrilSf(char* ScrilSf);

    bool CheckEthernetMac();

    bool CheckNetCode();

    bool CheckScrilSf(int ScrilSfType = 0);

private:
    virtual void Init();
    virtual META_RESULT MetaHandle_Init();
    virtual META_RESULT ModemMetaHandle_Init();
    virtual META_RESULT APMetaHandle_Init();

    virtual void MetaHandle_DeInit();
    virtual void ModemMetaHandle_DeInit();
    virtual void APMetaHandle_DeInit();

    virtual void SetupMetaModeParameters();
    virtual int TryToOpenSPKernelComport(int KernelCom_Num);

    virtual META_RESULT EnterAPMetaMode();
    virtual META_RESULT ExitAPMetaMode();

    virtual META_RESULT EnableModemMeta();

    virtual int ConnectWithPreloader();
    virtual int TryOpenComportUntilReady(const UINT nComport);
    virtual int ConnectWithKernelPort_Ex();

    virtual int GetSPModemInfo_Ex();
    virtual META_RESULT QueryDBFromDUT();
    virtual META_RESULT GetAPDBFromDUT();
    virtual META_RESULT GetMDDBFromDUT();
    virtual META_RESULT LoadModemDatabase(int MDindex);
    virtual META_RESULT LoadAPDatabase();

    virtual META_RESULT MDSLA_Connect();

    virtual META_RESULT REQ_ReadFromModemNVRAM(FT_NVRAM_READ_REQ *psNVRAM_ReadReq, FT_NVRAM_4BYTES_LID_READ_CNF *psNVRAM_ReadCnf);
    virtual META_RESULT REQ_ReadModem_NVRAM_Start(WriteData_Type_e dataType, char *pOutData, unsigned short iRID);

    virtual META_RESULT ReadFromAPNvram(AP_FT_NVRAM_READ_REQ *psNVRAM_ReadReq, AP_FT_NVRAM_READ_CNF *psNVRAM_ReadCnf);

    virtual META_RESULT REQ_ReadAP_NVRAM_Start(WriteData_Type_e dataType, char *pOutData, unsigned short iRID);

    virtual META_RESULT REQ_ReadFromAPNVRAM(AP_FT_NVRAM_READ_REQ *psNVRAM_ReadReq, AP_FT_NVRAM_READ_CNF *psNVRAM_ReadCnf);

    virtual META_RESULT ReadBarcodefromNVRAM(char * SN, int len);

    virtual int WriteMEIDToFTNVRAM(bool WriteFlag = true, char* m_szReadBack = nullptr);

    virtual META_RESULT REQ_WriteToAPNVRAM(AP_FT_NVRAM_WRITE_REQ sNVRAM_WriteReq);

    virtual META_RESULT ConductProdInfoData(unsigned char *pBuf, int nBufLen);

    void UpdateUIMsg(QString log, LOGCOLOR_TYPE textcolor=BLACK);

signals:
    void signal_MetaUpdateUILog(UINT nTreadID, QString log, LOGCOLOR_TYPE textcolor = BLACK);

};

META_MD_Query_Result_T __stdcall MdQueryHandler(void* MdQuery_CB_Arg);
int __stdcall MdTypeSwitchHandler(META_MDTYPE_Switch_Param_T mdtype_switch_param, void* MdTypeSwitch_CB_Arg);
typedef META_RESULT (SmartPhoneSN::*pConductDataFunc)(char *pOutData, unsigned short RID_para, char *pInDatabuf, int bufSize);
void __stdcall CNF_ReadFromAPNvram(const AP_FT_NVRAM_READ_CNF *cnf, const short token, void *usrData);
void __stdcall CNF_WriteToAPNvram ( const AP_FT_NVRAM_WRITE_CNF *cnf, const short token, void *usrData);
