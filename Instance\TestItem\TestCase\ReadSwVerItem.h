#ifndef READSWVERITEM_H
#define READSWVERITEM_H

#include "../testcasebase.h"
#include <QCheckBox>
#include <QComboBox>
#include <QLineEdit>
#include <QFormLayout>

class ReadSwVerItem : public TestCaseBase
{
    Q_OBJECT
public:
    ReadSwVerItem();

    QString type() const override { return "ReadSwVer"; }
    static QString staticType() { return "ReadSwVer"; }
    QString displayName() const override { return "Read SwVer"; }

    QWidget* createParameterWidget(QWidget* parent) override;

    QVariantMap parameters() const override { return m_params; }
    void setParameters(const QVariantMap& params) override { m_params = params; }
};

#endif // READSWVERITEM_H
