/*
 * Copyright (c) 2020, MediaTek Inc. All rights reserved.
 *
 * This software/firmware and related documentation ("MediaTek Software") are
 * protected under relevant copyright laws.
 * The information contained herein is confidential and proprietary to
 * MediaTek Inc. and/or its licensors.
 * Except as otherwise provided in the applicable licensing terms with
 * MediaTek Inc. and/or its licensors, any reproduction, modification, use or
 * disclosure of MediaTek Software, and information contained herein, in whole
 * or in part, shall be strictly prohibited.
*/
#ifndef __META_COMMON_DEFINITION__
#define __META_COMMON_DEFINITION__

#if defined(_MSC_VER) && (_MSC_VER < 1600) || (__GNUC__ && __cplusplus < 201103L)
#define CONCAT_(a, b) a##b
#define CONCAT(a, b) CONCAT_(a, b)
#define static_assert(e, msg) typedef char CONCAT(Assert, __LINE__)[(e)?1:-1];
#endif

/* RX ANT Mask */
#define ANTMASK_MAIN (1 << 0)
#define ANTMASK_DIV (1 << 1)

/**
 * \ingroup General
 * \details The enumeration for modem generation of multi RAT
 */
typedef enum
{
    MODEM_GEN_TYPE_INVALID = -1, /**< Invalid */
    MODEM_GEN_TYPE_GEN90 = 90,   /**< M20 */
    MODEM_GEN_TYPE_GEN92 = 92,   /**< M30 */
    MODEM_GEN_TYPE_GEN93 = 93,   /**< M21 */
    MODEM_GEN_TYPE_GEN95 = 95,   /**< M50 */
    MODEM_GEN_TYPE_GEN97 = 97,   /**< M70 */
    MODEM_GEN_TYPE_GEN98 = 98,   /**< M80 */
    MODEM_GEN_TYPE_GEN99 = 99    /**< M90 */
} E_MODEM_GEN_TYPE;

/**
 * \ingroup General
 * \details The enumeration for signal direction type
 */
typedef enum
{
    SIGNAL_DIRECTION_TX = 0,
    SIGNAL_DIRECTION_RX,
    SIGNAL_DIRECTION_NUM
}
E_SIGNAL_DIRECTION;

/**
 * \ingroup General
 * \details The strings for DIR items
 */
static const char *g_dirStr[] =
{
    "TX", "RX"
};
static_assert(sizeof(g_dirStr) / sizeof(const char *) == SIGNAL_DIRECTION_NUM, "dirStr & enum size mismtach.");

/**
 * \ingroup General
 * \details The RAT index enumeration
 */
typedef enum
{
    RAT_UNDEFINED = 0,
    RAT_GENERAL,
    RAT_GSM,
    RAT_TDSCDMA,
    RAT_C2K,
    RAT_WCDMA,
    RAT_LTE,
    RAT_NR,
    RAT_NR_FR2,
    RAT_GPS,
    RAT_WIFI,
    RAT_BT,
    RAT_IOT_NTN,
    RAT_NUM
} E_RAT_INDEX_T;

/**
 * \ingroup General
 * \details The enumeration for all types of META item
 */
typedef enum
{
    META_ITEM_UNDEFINED = 0,
    // General
    META_ITEM_TOOL_INIT,
    META_ITEM_IMEI,
    META_ITEM_MEID,
    META_ITEM_BARCODE,
    META_ITEM_OSLK,
    // Calibration
    META_ITEM_SELF_CAL,
    META_ITEM_FR1_SELF_CAL,
    META_ITEM_FR2_SELF_CAL,
    META_ITEM_COMBINE_SELF_CAL,
    META_ITEM_FR2_AIF_SELF_CAL,
    META_ITEM_FR2_ARF_SELF_CAL,
    META_ITEM_AFC, // legacy by RAT
    META_ITEM_MMRF_AFC,
    META_ITEM_GPS_COCLOCK,
    META_ITEM_MULTIRAT_TADC,
    META_ITEM_DCXO_LPM,
    META_ITEM_RX_PATH_LOSS,
    META_ITEM_TPC,
    META_ITEM_W_COEFFICIENT,
    META_ITEM_GMSK_LEVEL_RAMP,
    META_ITEM_EPSK_LEVEL_RAMP,
    META_ITEM_GAIN_RF,
    META_ITEM_INTERSLOT,
    META_ITEM_GMSK_OPEN_LOOP_TXPC,
    META_ITEM_EPSK_OPEN_LOOP_TXPC,
    META_ITEM_TEMPERATURE_ADC, // legacy item by RAT, META_ITEM_MULTIRAT_TADC is used in new generation
    META_ITEM_PRESELF_CAL,
    META_ITEM_POSTSELF_CAL,
    META_ITEM_TX_ROLLBACK,
    META_ITEM_RX_DIVERSITY_PATH_LOSS,
    META_ITEM_PA_DRIFT_COMPENSATION,
    META_ITEM_PRACH_TX_TEMPERATURE_COMPENSATION,
    META_ITEM_DPD,
    META_ITEM_DPD_CAL,
    META_ITEM_DPD_TUNING,
    META_ITEM_FR2_DPD,
    META_ITEM_ET,
    META_ITEM_ET_CAL,
    META_ITEM_ET_TUNING,
    META_ITEM_MIPI_TPA_PROFILE,
    META_ITEM_POC,
    META_ITEM_DPD_PA_PROFILE,
    META_ITEM_CIM3,
    META_ITEM_MIPI_PA,
    META_ITEM_MAX_PA_INPUT,
    META_ITEM_TAS_TX,
    META_ITEM_TAS_RX,
    META_ITEM_MULTI_COUPLER,
    META_ITEM_DPD_PROFILE,
    META_ITEM_MIPI,
    META_ITEM_APT_PROFILE,
    META_ITEM_SRS,
    META_ITEM_COTMS_CAL,
    META_ITEM_CAL,
    META_ITEM_TAS_CAL,
    META_ITEM_FHC,
    META_ITEM_TPC_CAL_ONLY,
    META_ITEM_RX_PATH_LOSS_CAL_ONLY,
    META_ITEM_TRANSCEIVER_POWER_CAL,
    META_ITEM_CDDC,
    META_ITEM_FHC_DTS,
    META_ITEM_FHC_UTS,
    META_ITEM_ET_DPD,
    META_ITEM_RFFE_CHECK,
    META_ITEM_LM_CAL,
    // Tuning
    META_TUNNING,
    META_ITEM_TPC_TUNING_ONLY,
    META_ITEM_RX_PATH_LOSS_TUNING_ONLY,
    // Testing
    META_ITEM_NSFT,
    META_ITEM_TAS_NSFT,
    META_ITEM_BIFT,
    META_ITEM_PATH_TEST,
    META_ITEM_PRETEST,
    META_ITEM_NUM
} E_META_ITEM;

/**
 * \ingroup General
 * \details The enumeration for all kind of mode in META item
 */
typedef enum
{
    META_ITEM_MODE_NULL = 0,
    META_ITEM_MODE_TRAD,
    META_ITEM_MODE_LIST,
    META_ITEM_MODE_DPD,
    META_ITEM_MODE_ET,
    META_ITEM_MODE_APT,
    META_ITEM_MODE_NUM
} E_META_ITEM_MODE;

/**
 * \ingroup General
 * \details The enumeration for TDSCDMA Band
 */
typedef enum
{
    E_TD_A_NVRAM_BAND33_35_37_39 = 0, // band F
    E_TD_A_NVRAM_BAND34, // band A
    E_TD_A_NVRAM_BAND36,
    E_TD_A_NVRAM_BAND38,
    E_TD_A_NVRAM_BAND40, // band E
    E_TD_A_NVRAM_BAND_NUM
} E_TD_A_NVRAM_BAND_T;

/**
 * \ingroup General
 * \details The enumeration for GSM Band
 */
typedef enum
{
    GSM_BAND_GSM450,
    GSM_BAND_GSM850,
    GSM_BAND_GSM900,
    GSM_BAND_DCS1800,
    GSM_BAND_PCS1900,
    GSM_BAND_NUM
} E_GSM_BAND;

/**
 * \ingroup General
 * \details The strings RAT index
 */
static const char *g_ratStr[] =
{
    "",
    "General",
    "GSM",
    "TDSCDMA",
    "C2K",
    "WCDMA",
    "LTE",
    "NR",
    "NR FR2",
    "GPS",
    "WIFI",
    "BT",
    "IoT-NTN"
};
static_assert(sizeof(g_ratStr) / sizeof(const char *) == RAT_NUM, "ratStr & enum size mismtach.");

/**
 * \ingroup General
 * \details The strings for all types of META item
 */
static const char *g_itemStr[] =
{
    "Undefined Item",
    // General
    "Tool initial",
    "IMEI",
    "MEID",
    "Barcode",
    "OSLK",
    "Self Cal",
    "FR1 Self-Calibration",
    "FR2 Self-Calibration",
    "Pre FR1/FR2 Self Calibration",
    "FR2 IF Self-Calibration",
    "FR2 RF Self-Calibration",
    // Calibration
    "AFC",
    "MMRF AFC",
    "GPS Co-Clock",
    "MultiRat Tadc",
    "DCXO LPM",
    "RX Path Loss",
    "TPC",
    "W Coefficient",
    "GMSK Level, Ramp",
    "EPSK Level, Ramp",
    "Gain RF",
    "Interslot",
    "GMSK, Open loop TXPC",
    "EPSK, Open loop TXPC",
    "Temperature ADC",
    "Pre-self Calibration",
    "Post-self Calibration",
    "TX Rollback",
    "RX Diversity Path Loss",
    "PA Drift Compensation",
    "PRACH Tx Temperature Compensation",
    "DPD",
    "DPD Calibration",
    "DPD Tuning",
    "FR2 DPD",
    "ET",
    "ET Calibration",
    "ET Tuning",
    "Mipi TPA Profile",
    "POC",
    "DPD PA Profile",
    "CIM3",
    "Mipi PA",
    "Max PA Input",
    "TAS Tx",
    "TAS Rx",
    "Multi Coupler",
    "DPD Profile",
    "MIPI",
    "APT Profile",
    "SRS",
    "CO-TMS Calibration",
    "Calibration",
    "TAS Calibration",
    "FHC",
    "TPC Calibration", // META_ITEM_TPC_CAL_ONLY
    "RX Path Loss Calibration", // META_ITEM_RX_PATH_LOSS_CAL_ONLY
    "Transceiver Power Calibration",
    "CDDC",
    "FHC DTS",
    "FHC UTS",
    "ET/DPD",
    "MIPI RFFE Check",
    "LM",
    // Tuning
    "Tuning",
    "TPC Tuning", // META_ITEM_TPC_TUNING_ONLY
    "RX Path Loss Tuning", // META_ITEM_RX_PATH_LOSS_TUNING_ONLY
    // Testing
    "NSFT",
    "TAS NSFT",
    "BIFT",
    "PATH TEST",
    "PreTest"
};
static_assert(sizeof(g_itemStr) / sizeof(const char *) == META_ITEM_NUM, "itemStr & enum size mismtach.");

/**
 * \ingroup General
 * \details The strings for all kind of mode in META item
 */
static const char *g_itemModeStr[] =
{
    "",
    "Traditional",
    "List",
    "DPD",
    "ET",
    "APT",
};
static_assert(sizeof(g_itemModeStr) / sizeof(const char *) == META_ITEM_MODE_NUM, "itemModeStr & enum size mismtach.");

/**
 * \ingroup General
 * \details The strings for TDSCDMA Band
 */
static const char *g_tdscdmaBandStr[] =
{
    "BAND_F",
    "BAND_A",
    "BAND_C",
    "BAND_D",
    "BAND_E"
};
static_assert(sizeof(g_tdscdmaBandStr) / sizeof(const char *) == E_TD_A_NVRAM_BAND_NUM, "g_tdscdmaBandStr & enum size mismtach.");

/**
 * \ingroup General
 * \details The strings for GSM Band
 */
static const char *g_gsmBandStr[] =
{
    "GSM450",
    "GSM850",
    "GSM900",
    "DCS1800",
    "PCS1900"
};
static_assert(sizeof(g_gsmBandStr) / sizeof(const char *) == GSM_BAND_NUM, "g_gsmBandStr & enum size mismtach.");
// DQ timeout setting for Gen99
#define DQ_COMMAND_TIMEOUT_V9 15000

#endif // #ifndef __META_COMMON_DEFINITION__
