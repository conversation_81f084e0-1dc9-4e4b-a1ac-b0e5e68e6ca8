/*
 * Copyright (c) 2020, MediaTek Inc. All rights reserved.
 *
 * This software/firmware and related documentation ("MediaTek Software") are
 * protected under relevant copyright laws.
 * The information contained herein is confidential and proprietary to
 * MediaTek Inc. and/or its licensors.
 * Except as otherwise provided in the applicable licensing terms with
 * MediaTek Inc. and/or its licensors, any reproduction, modification, use or
 * disclosure of MediaTek Software, and information contained herein, in whole
 * or in part, shall be strictly prohibited.
*/
#ifndef __APP_SUPPLY__
#define __APP_SUPPLY__

#include <string>
#include "message_box.h"

using namespace std;

class  CAppSupply
{
private:
    CAppSupply() {};
    static MsgBoxFunc m_MessageToAppFunction;
    static wstring m_AppExeName;
    static wstring m_ConfigureFileName;
    static wstring m_DebugLogFileName;
public:
    static CAppSupply *GetInstance();
    static void SetMsgBoxFunc(MsgBoxFunc func);
    static int DisplayMsgBox(const char *msg, const char *title, int code);
    static void SetAppExeName(const wchar_t *name);
    static void SetConfigFileName(const wchar_t *name);
    static void SetDebugLogFileName(const wchar_t *name);
    static wstring GetAppExeName();
    static wstring GetConfigFileName();
    static wstring GetDebugLogFileName();
};

#define APP_EXE_NAME ((CAppSupply::GetInstance())->GetAppExeName().c_str())
#define CFG_FILE_NAME ((CAppSupply::GetInstance())->GetConfigFileName().c_str())
#define DBG_FILE_NAME ((CAppSupply::GetInstance())->GetDebugLogFileName().c_str())

#endif // #ifndef __APP_SUPPLY__
