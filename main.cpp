#include "mainwindow.h"

#include <QApplication>
#include <QLocale>
#include <QTranslator>
#include <QDir>
#include <QMessageBox>
#include <QSharedMemory>
#include "./Utility/version.h"
#include "Common/Common.h"

static void init_app_path(char* argv[])
{
    QString app_path = QString::fromLocal8Bit(argv[0]);
    QDir dir = QFileInfo(app_path).absoluteDir();
    app_path = dir.absolutePath();
    FileUtils::UpdateAppPath(app_path.toStdString());
}

static void setApplicationInfo(QCoreApplication &app)
{
    app.setApplicationName(ToolInfo::ToolName().c_str());
    app.setApplicationVersion(ToolInfo::VersionNum().c_str());
}

int main(int argc, char *argv[])
{
	Utils::setTextCodec();
    init_app_path(argv);
	
    QApplication app(argc, argv);
	setApplicationInfo(app);

    QSharedMemory sharedMemory(qApp->applicationName());    // 设置绑定的共享内存段的key值
    if(sharedMemory.attach()){
        QMessageBox::warning(nullptr, "Error", "An instance of the application is already running.");
        return 1;
    }else{
        sharedMemory.create(1); // 创建1byte大小的共享内存段
    }

    QTranslator translator;
    const QStringList uiLanguages = QLocale::system().uiLanguages();
    for (const QString &locale : uiLanguages) {
        const QString baseName = "Agenew_MultiCodes_Tool_" + QLocale(locale).name();
        if (translator.load(":/i18n/" + baseName)) {
            app.installTranslator(&translator);
            break;
        }
    }
    MainWindow w;
    w.show();
    return app.exec();
}
