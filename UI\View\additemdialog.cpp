#include "additemdialog.h"
#include "ui_additemdialog.h"
#include "MainWindow.h"
#include "Common/Common.h"
#include "Instance/TestItem/testcasefactory.h"

AddItemDialog::AddItemDialog(MainWindow *mainwindow, QWidget *parent)
    : QDialog(parent)
    , ui(new Ui::AddItemDialog)
	, m_mainwindow(mainwindow)
{
    ui->setupUi(this);
    this->setWindowFlags(Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint);

    // 获取所有可用的测试项类型
    QStringList availableTypes = TestCaseFactory::availableTestCaseTypes();
    
    // 获取已存在的测试项类型
    QList<QListWidgetItem*> existingItems = mainwindow->getListWidget()->findItems("*", Qt::MatchWildcard);
    QStringList existingTypes;
    for (QListWidgetItem* item : existingItems) {
        existingTypes.append(item->data(Qt::UserRole).toString());
    }
    
    // 过滤掉已存在的测试项
    for (const QString& type : availableTypes) {
        if (!existingTypes.contains(type)) {
            ui->comboBox->addItem(type);
        }
    }

    if (ui->comboBox->count() == 0) {
        QMessageBox::warning(this, tr("Warning"), tr("No available test items to add."));
    }
}

AddItemDialog::~AddItemDialog()
{
    delete ui;
}

QString AddItemDialog::getSelectedType()
{
    return ui->comboBox->currentText();
}
