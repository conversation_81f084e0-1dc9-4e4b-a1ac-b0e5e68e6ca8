#ifndef BOTTOMCURRENTITEM_H
#define BOTTOMCURRENTITEM_H

#include "../testcasebase.h"
#include <QCheckBox>
#include <QComboBox>
#include <QLineEdit>
#include <QFormLayout>

class BottomCurrentItem : public TestCaseBase
{
    Q_OBJECT
public:
    BottomCurrentItem();

    QString type() const override { return "BottomCurrent"; }
    static QString staticType() { return "BottomCurrent"; }
    QString displayName() const override { return "Bottom Current"; }

    QWidget* createParameterWidget(QWidget* parent) override;

    QVariantMap parameters() const override { return m_params; }
    void setParameters(const QVariantMap& params) override { m_params = params; }
};

#endif // BOTTOMCURRENTITEM_H
