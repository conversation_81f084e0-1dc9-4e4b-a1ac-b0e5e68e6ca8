#ifndef DRAGGABLETREEWIDGET_H
#define DRAGGABLETREEWIDGET_H

#include <QTreeWidget>
#include <QDropEvent>

class DraggableTreeWidget : public QTreeWidget
{
    Q_OBJECT

public:
    explicit DraggableTreeWidget(QWidget *parent = nullptr) : QTreeWidget(parent) {}

signals:
    void itemMoved();

protected:
    void dropEvent(QDropEvent *event) override {
        QModelIndex droppedIndex = indexAt(event->pos());
        
        QTreeWidget::dropEvent(event);
        
        emit itemMoved();
    }
};

#endif // DRAGGABLETREEWIDGET_H 