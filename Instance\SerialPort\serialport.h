#ifndef SERIALPORT_H
#define SERIALPORT_H

#include <QObject>
#include <QThread>
#include <QSerialPort>
#include <QSerialPortInfo>
#include <QRegularExpression>
#include <iostream>
#include <stdint.h>
#include <sstream>
#include <iomanip>
#include "Common/Common.h"

#define RETRYTIME 10

class SerialPort : public QThread
{
    Q_OBJECT
public:
    SerialPort(QObject *parent = nullptr, UINT nTreadID = 0, QSerialPort* PortServer = nullptr);
    ~SerialPort(void);

    bool Init(UINT port, QSerialPort::BaudRate BaudRate);
    bool DeInit();
    QString CheckStatus(QString Cmd=ECOMMANDStr[E_CHECK_STATUS]);
    void ControlScanGun();
	QString WriteCommand(QString command);

signals:
    void signal_UpdateUILog(UINT nTreadID, QString log, LOGCOLOR_TYPE textcolor = BLACK);

private slots:
    void    slot_GetScanGunData();

private:
    void UpdateUIMsg(QString log, LOGCOLOR_TYPE textcolor = BLACK);

    UINT m_nTreadID;
    QSerialPort *global_port;
    QTimer timeoutTimer;
};

#endif // SERIALPORT_H
