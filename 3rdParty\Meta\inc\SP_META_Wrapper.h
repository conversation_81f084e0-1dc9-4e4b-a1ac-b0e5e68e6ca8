/*
 * Copyright (c) 2020, MediaTek Inc. All rights reserved.
 *
 * This software/firmware and related documentation ("MediaTek Software") are
 * protected under relevant copyright laws.
 * The information contained herein is confidential and proprietary to
 * MediaTek Inc. and/or its licensors.
 * Except as otherwise provided in the applicable licensing terms with
 * MediaTek Inc. and/or its licensors, any reproduction, modification, use or
 * disclosure of MediaTek Software, and information contained herein, in whole
 * or in part, shall be strictly prohibited.
*/

#ifndef __SP_META_WRAPPER_H__
#define __SP_META_WRAPPER_H__
#include "meta.h"

#ifdef __cplusplus
extern "C" {
#endif // #ifdef __cplusplus


#ifdef _WIN32
#if defined(METACONN_EXPORTS) // inside DLL
#define APMETAAPI __declspec(dllexport) __stdcall
#else // outside DLL
#define APMETAAPI   __declspec(dllimport) __stdcall
#endif  // METACONN_EXPORTS
#else // #ifdef _WIN32
#define APMETAAPI
#endif

typedef enum
{
    SP_META_WRAPPER_SUCCESS = 0,
    SP_META_WRAPPER_TIMEOUT = 1,
    SP_META_WRAPPER_FAILED  = 2,
    SP_META_WRAPPER_EXT_MD_DOWNLOAD_FAILED = 3,
    SP_META_WRAPPER_INVALID_ARGUMENT = 4,
    SP_META_WRAPPER_INVALID_FILE = 5,
    SP_META_WRAPPER_CANCEL = 6,
    SP_META_WRAPPER_SP_META_FUNC_NOT_IMPLEMENT_YET = 7,
    //    SP_META_WRAPPER_SP_META_FUNC_LOAD_FAILED = 8,
    SP_META_WRAPPER_VERIFY_PASSWORD_FAILED = 9,
    SP_META_WRAPPER_STOP_BOOTUP_PROCEDURE = 10,
    SP_META_WRAPPER_LAST_ERROR
} SP_META_WRAPPER_RESULT;

#define SP_FT_NVRAM_READ_REQ AP_FT_NVRAM_READ_REQ
#define SP_FT_NVRAM_READ_CNF AP_FT_NVRAM_READ_CNF
#define SP_FT_NVRAM_WRITE_REQ AP_FT_NVRAM_WRITE_REQ
#define SP_FT_NVRAM_WRITE_CNF AP_FT_NVRAM_WRITE_CNF

SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_MapToSPMetaWrapperResult(int spMetaResult);
const char *__stdcall SP_META_Wrapper_GetErrorString(SP_META_WRAPPER_RESULT ErrCode);
const char *__stdcall SP_META_Wrapper_GetLastExecuteSPMetaApiName();
const char *__stdcall SP_META_Wrapper_GetLastExecuteSPMetaApiName_r(const int meta_handle);

SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_GetSPMetaHandle(int handle, int *spMetaHandle);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_SetSPMetaHandle(int handle, int *spMetaHandle);

/**
 * init Wrapper API for SP META DLL
 */
/* allocate sp meta handle and report handle value */
SP_META_WRAPPER_RESULT APMETAAPI SP_META_WrapperAllocateHandle(int *handle);
/* allocate 20 sp meta handle and init sp meta */
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_Init(void);
/* init sp meta handle */
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_InitSingle(int handle);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_Deinit(int handle);

/**
 * Trace on /off API for SP META Wrapper
 */
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_TraceOn(void);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_TraceClear(void);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_TraceOff(void);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_TraceOn_With_FilePath(const char *filename);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_TraceOn_With_FilePathW(const wchar_t *filename);

/**
 * Trace on /off Wrapper API for SP META DLL
 */
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_DebugOn(void);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_DebugOn_ex(const int meta_handle);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_DebugOff(void);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_DebugClear(void);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_DebugOnThePath(const char *path);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_DebugOnThePathW(const wchar_t *path);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_DebugOnThePath_r(const int meta_handle, const char *path);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_DebugOnThePathW_r(const int meta_handle, const wchar_t *path);

#ifdef _WIN32
/**
 * non-reentrant version of SP_META_Wrapper_ConnectTargetByUart_r
 */
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_ConnectTargetByUart(unsigned int ms_timeout, int com_port);
/**
 * \brief connect AP with 921600 baud rate
 * \param metaHandle meta handle
 * \param connectionSetting connection setting to the UART interface
 */
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_ConnectTargetByUart_r(const int meta_handle, unsigned int ms_timeout, int com_port);

#endif //#ifdef _WIN32

typedef enum _SP_META_WRAPPER_UART_BAUDRATE_E
{
    SP_META_WRAPPER_BAUDRATE_END = -1,
    SP_META_WRAPPER_BAUDRATE_57600 = 0,
    SP_META_WRAPPER_BAUDRATE_115200 = 1,
    SP_META_WRAPPER_BAUDRATE_230400 = 2,
    SP_META_WRAPPER_BAUDRATE_460800 = 3,
    SP_META_WRAPPER_BAUDRATE_921600 = 4,
    SP_META_WRAPPER_BAUDRATE_1500000 = 5,
    SP_META_WRAPPER_BAUDRATE_NUM = SP_META_WRAPPER_BAUDRATE_1500000 + 1
} SP_META_WRAPPER_UART_BAUDRATE_E;
typedef struct _SP_META_WRAPPER_CONN_UART_SETTING_T
{
    int comPort;
    SP_META_WRAPPER_UART_BAUDRATE_E baudrateSetting[SP_META_WRAPPER_BAUDRATE_NUM];
} SP_META_WRAPPER_CONN_UART_SETTING_T;

#ifdef _WIN32

/**
 * non-reentrant version of SP_META_Wrapper_ConnectTargetByUartEx_r
 */
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_ConnectTargetByUartEx(unsigned int msTimeout, const SP_META_WRAPPER_CONN_UART_SETTING_T *connectionSetting, SP_META_WRAPPER_UART_BAUDRATE_E *currentBaudRate);
/**
 * \brief connect AP with configurable connection setting
 * \param metaHandle meta handle
 * \param connectionSetting connection setting to the UART interface
 */
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_ConnectTargetByUartEx_r(const int metaHandle, unsigned int msTimeout, const SP_META_WRAPPER_CONN_UART_SETTING_T *connectionSetting, SP_META_WRAPPER_UART_BAUDRATE_E *currentBaudRate);
#endif // #ifdef _WIN32
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_ConnectTargetByUsb(unsigned int ms_timeout, int com_port);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_ConnectTargetByUsb_r(const int meta_handle, unsigned int ms_timeout, META_COMPORT_INFO *com_info);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_DisconnectInMetaMode(void);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_DisconnectInMetaMode_r(const int meta_handle);

SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_SetCommTrace(const int meta_handle, META_COMM_TRACE_PARA_T *commTracePara);
typedef struct
{
    char    BB_CHIP[64];    // BaseBand chip version
    char    ECO_VER[4];     // ECO version
    char    SW_TIME[64];
    char    DSP_FW[64];     // DSP firmware version
    char    DSP_PATCH[64];  // DSP patch version
    char    SW_VER[64];     // S/W version
    char    HW_VER[64];     // H/W board version
    char    MELODY_VER[64]; // Melody version
    char    BUILD_DISP_ID[64];  //build version
} SP_META_WRAPPER_VERINFO;

SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_GetTargetVerInfo(SP_META_WRAPPER_VERINFO *verinfo);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_GetTargetVerInfo_r(const int meta_handle, SP_META_WRAPPER_VERINFO *verinfo);

SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_GetModemInfo(unsigned int ms_timeout, unsigned int *number_of_md, unsigned int *active_md_idx);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_GetModemInfo_r(const int meta_handle, unsigned int ms_timeout, unsigned int *number_of_md, unsigned int *active_md_idx);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_QueryEncryptedSupport(unsigned int ms_timeout, unsigned char *support);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_QueryEncryptedSupport_r(const int meta_handle, unsigned int ms_timeout, unsigned char *support);
#ifdef _WIN32
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_VerifyEncryptedPwd(const unsigned char *pwd, unsigned int ms_timeout, const int  length, unsigned char *result);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_VerifyEncryptedPwd_r(const int meta_handle, unsigned int ms_timeout, const unsigned char *pwd, const int  length, unsigned char *result);
#endif //#ifdef _WIN32

typedef struct
{
    META_MODEM_SRV md_service; /*!< logging_protocol type: 1: TST;2: DHL*/
    META_MODEM_CH_TYPE ch_type; /*!< channel type: 0/1: Native Channel; 2: Tunneling without checksum; 3: Tunneling with checksum */
    unsigned char reserved;
} SP_MODEM_CAPABILITY;

typedef struct
{
    SP_MODEM_CAPABILITY modem_cap[8];
} SP_MODEM_CAPABILITY_LIST_CNF;

SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_QueryModemCapability(unsigned int ms_timeout, SP_MODEM_CAPABILITY_LIST_CNF *md_cap_list);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_QueryModemCapability_r(const int meta_handle, unsigned int ms_timeout, SP_MODEM_CAPABILITY_LIST_CNF *md_cap_list);

SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_SetModemType(unsigned int ms_timeout, unsigned int active_md_idx, unsigned int active_md_type);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_SetModemType_r(const int meta_handle, unsigned int ms_timeout, unsigned int active_md_idx, unsigned int active_md_type);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_GetCurrentModemType(unsigned int ms_timeout, unsigned int current_md_idx, unsigned int *current_md_type);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_GetCurrentModemType_r(const int meta_handle, unsigned int ms_timeout, unsigned int current_md_idx, unsigned int *current_md_type);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_QueryMdImgType(unsigned int ms_timeout, unsigned int current_md_idx, unsigned int *current_mdimg_type_idx);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_QueryMdImgType_r(const int meta_handle, unsigned int ms_timeout, unsigned int current_md_idx, unsigned int *current_mdimg_type_idx);
//Modify naming
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_MODEM_Set_ModemType(unsigned int ms_timeout, unsigned int active_md_idx, unsigned int active_md_type);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_MODEM_Set_ModemType_r(const int meta_handle, unsigned int ms_timeout, unsigned int active_md_idx, unsigned int active_md_type);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_MODEM_Get_CurrentModemType(unsigned int ms_timeout, unsigned int current_md_idx, unsigned int *current_md_type);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_MODEM_Get_CurrentModemType_r(const int meta_handle, unsigned int ms_timeout, unsigned int current_md_idx, unsigned int *current_md_type);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_MODEM_Query_MDIMGType(unsigned int ms_timeout, unsigned int current_md_idx, unsigned int *current_mdimg_type_idx);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_MODEM_Query_MDIMGType_r(const int meta_handle, unsigned int ms_timeout, unsigned int current_md_idx, unsigned int *current_mdimg_type_idx);

typedef struct
{
    unsigned char reserved;
} SP_META_WRAPPER_MODEM_QUERY_DOWNLOAD_STATUS_REQ;

typedef struct
{
    unsigned int percentage;
    unsigned int status_code;
} SP_META_WRAPPER_MODEM_QUERY_DOWNLOAD_STATUS_CNF;


SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_MODEM_Query_Download_Status(unsigned int ms_timeout, SP_META_WRAPPER_MODEM_QUERY_DOWNLOAD_STATUS_REQ *pReq, SP_META_WRAPPER_MODEM_QUERY_DOWNLOAD_STATUS_CNF *pCnf);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_MODEM_Query_Download_Status_r(const int meta_handle, unsigned int ms_timeout, SP_META_WRAPPER_MODEM_QUERY_DOWNLOAD_STATUS_REQ *pReq, SP_META_WRAPPER_MODEM_QUERY_DOWNLOAD_STATUS_CNF *pCnf);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_MODEM_Query_ExternalModemDownloadSupport(unsigned int ms_timeout);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_MODEM_Query_ExternalModemDownloadSupport_r(const int meta_handle, unsigned int ms_timeout);

/********************************************
 *      NVRAM access SP META DLL WRAPPER    *
 ********************************************/

// NVRAM database initial function
SP_META_WRAPPER_RESULT  APMETAAPI SP_META_Wrapper_NVRAM_Init(const char *PathName, unsigned long *p_nvram_CatcherTranAddr);
SP_META_WRAPPER_RESULT  APMETAAPI SP_META_Wrapper_NVRAM_Init_r(const int meta_handle, const char *PathName, unsigned long *p_nvram_CatcherTranAddr);
SP_META_WRAPPER_RESULT  APMETAAPI SP_META_Wrapper_NVRAM_Init_rW(const int meta_handle, const wchar_t *PathName, unsigned long *p_nvram_CatcherTranAddr);

// NVRAM read/write operations
SP_META_WRAPPER_RESULT  APMETAAPI SP_META_Wrapper_NVRAM_Read(unsigned int ms_timeout, const AP_FT_NVRAM_READ_REQ *req, AP_FT_NVRAM_READ_CNF *cnf);
SP_META_WRAPPER_RESULT  APMETAAPI SP_META_Wrapper_NVRAM_Read_r(const int meta_handle, unsigned int ms_timeout, const AP_FT_NVRAM_READ_REQ *req, AP_FT_NVRAM_READ_CNF *cnf);
SP_META_WRAPPER_RESULT  APMETAAPI SP_META_Wrapper_NVRAM_Write(unsigned int ms_timeout, const AP_FT_NVRAM_WRITE_REQ *req);
SP_META_WRAPPER_RESULT  APMETAAPI SP_META_Wrapper_NVRAM_Write_r(const int meta_handle, unsigned int ms_timeout, const AP_FT_NVRAM_WRITE_REQ *req);

// NVRAM query operations
SP_META_WRAPPER_RESULT  APMETAAPI SP_META_Wrapper_NVRAM_GetRecLen(const char *LID, int *len);
SP_META_WRAPPER_RESULT  APMETAAPI SP_META_Wrapper_NVRAM_SetRecFieldValue(const char *LID, const char *field, char *buf, const int buf_len, void *value, const int value_len);
SP_META_WRAPPER_RESULT  APMETAAPI SP_META_Wrapper_NVRAM_GetRecFieldValue(const char *LID, const char *field, const char *buf, const int buf_len, void *value, const int value_len);
SP_META_WRAPPER_RESULT  APMETAAPI SP_META_Wrapper_NVRAM_QueryIsLIDExist(const char *LID);

#ifdef _WIN32
// NVRAM export/import
SP_META_WRAPPER_RESULT  APMETAAPI SP_META_Wrapper_NVRAM_Export(unsigned int ms_timeout, const char *file1, const char *file2);
SP_META_WRAPPER_RESULT  APMETAAPI SP_META_Wrapper_NVRAM_Export_r(const int meta_handle, unsigned int ms_timeout, const char *file1, const char *file2);
SP_META_WRAPPER_RESULT  APMETAAPI SP_META_Wrapper_NVRAM_Import(unsigned int ms_timeout, const char *file1, const char *file2);
SP_META_WRAPPER_RESULT  APMETAAPI SP_META_Wrapper_NVRAM_Import_r(const int meta_handle, unsigned int ms_timeout, const char *file1, const char *file2);
#endif //#ifdef _WIN32

/********************************************
 *      Platform related flow API
 ********************************************/
#define SP_META_WRAPPER_STOP_CHECK_EXTERNAL_MODEM_DOWNLOAD_STATUS 0x5566
typedef void (__stdcall *SP_META_Wrapper_ExternalModemDownloadStatusCallback_T)(unsigned int progress, unsigned int statusCode, void *usrData);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_Flow_CheckExternalModemDownloadStatus(unsigned int ms_timeout, const int *pStop, int *pDownloadStatus, SP_META_Wrapper_ExternalModemDownloadStatusCallback_T reportCallback, void *usrData);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_Flow_CheckExternalModemDownloadStatus_r(const int meta_handle, unsigned int ms_timeout, const int *pStop, int *pDownloadProgress, int *pDownloadStatus, SP_META_Wrapper_ExternalModemDownloadStatusCallback_T reportCallback, void *usrData);


SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_QueryIfFunctionSupportedByTarget(unsigned int ms_timeout, const char *func);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_QueryIfFunctionSupportedByTarget_r(const int meta_handle, unsigned int ms_timeout, const char *func);
#ifdef _WIN32
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_ADC_SetCarTuneVal_r(const int meta_handle, unsigned int ms_timeout, int val);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_ADC_GetCarTuneVal_r(const int meta_handle, unsigned int ms_timeout, int *val);
#endif // #ifdef _WIN32
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_CancelAllBlockingCall_r(const int meta_handle);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_SetBootStopFlag_r(const int meta_handle, int value);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_ForceCancelAllBlockingCall();
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_SetTargetFrameCompression_r(const int meta_handle, unsigned int ms_timeout, bool enabled, bool *isSuccess);
/********************************************
 *      Load Nvram file Platform API
 ********************************************/
#define DEFAULT_TARGET_FILEPATH "//system//etc//mddb"
#define PATTERN_FILENAME "MDDB.META_"
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_QueryModemDatabasePath(const int meta_handle, unsigned int ms_timeout, char *file_path);

typedef struct
{
    char          target_foldername[256];
    const char   *pattern_filename;
    const char   *MD_type;
    char          database_filepath[256];
} SP_GET_NVRAM_DB_REQ;

typedef struct
{
    char          target_foldername[256];
    const char   *pattern_filename;
    const char   *MD_type;
    wchar_t       database_filepath[256];
} SP_GET_NVRAM_DB_REQW;

typedef struct
{
    char          database_filename[256];
} SP_GET_NVRAM_DB_CNF;

typedef struct
{
    wchar_t       database_filename[256];
} SP_GET_NVRAM_DB_CNFW;
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_TravelTargetSideFile(const int meta_handle, unsigned int ms_timeout, char *pTargetfoldername, char *pattern_filename, unsigned int *file_count);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_GetTargetSideFileInfo(const int meta_handle, unsigned int ms_timeout, unsigned int file_idx, unsigned int *file_size, char *file_name);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_ReceiveTargetSideFile(const int meta_handle, unsigned int ms_timeout, const char *pSourceFileName, const char *pDestFileName);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_ReceiveTargetSideFileW(const int meta_handle, unsigned int ms_timeout, const char *pSourceFileName, const wchar_t *pDestFileName);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_IsSupportedLoadSPNvDb(const int meta_handle, unsigned int ms_timeout);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_LoadSPNvDatabase(const int meta_handle, unsigned int ms_timeout, SP_GET_NVRAM_DB_REQ *p_req, SP_GET_NVRAM_DB_CNF *p_cnf);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_LoadSPNvDatabaseW(const int meta_handle, unsigned int ms_timeout, SP_GET_NVRAM_DB_REQW *p_req, SP_GET_NVRAM_DB_CNFW *p_cnf);
/********************************************
 *      Reboot Platform to Normal mode API
 ********************************************/
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_RebootToNormalMode_r(const int meta_handle);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_RebootModemAndChangeMode_r(const int meta_handle, unsigned int ms_timeout, unsigned char md_idx, unsigned char meta_mode);

SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_RebootModemAndChangeModebyUSB_r(const int meta_handle, unsigned int ms_timeout, unsigned char md_idx, unsigned char meta_mode, int com_port);
#ifdef _WIN32
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_RebootModemAndChangeModebyUart_r(const int meta_handle, unsigned int ms_timeout, unsigned char md_idx, unsigned char meta_mode, int com_port);
#endif//#ifdef _WIN32
/********************************************
 *      Clear Meta_boot flag to Normal mode API
 ********************************************/
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_CleanMetaBootFlag_r(const int meta_handle, unsigned int ms_timeout, const char *APDB_pahtName);
/********************************************
 *      Control MeteMode MDlogger API
 ********************************************/
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_IsSupportedControlMdlogger_r(const int meta_handle, unsigned int ms_timeout);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_MdloggerUsbLoggingControl_r(const int meta_handle, unsigned int ms_timeout, bool enableLogging, unsigned int ms_waiting = 0);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_MdloggerSdcardLoggingControl_r(const int meta_handle, unsigned int ms_timeout, bool enableLogging, unsigned int ms_waiting = 0);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_IsSupportedMdloggerStatus_r(const int meta_handle, unsigned int ms_timeout);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_GetMdloggerStatus_r(const int meta_handle, unsigned int ms_timeout, unsigned int *mdStatus);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_LoadSDcardMdLog(const int meta_handle, unsigned int ms_timeout, char *target_foldername, char *filename, unsigned int LogByteUpperBound);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_LoadSDcardMdLogW(const int meta_handle, unsigned int ms_timeout, const char *target_foldername, const wchar_t *filename, char (&loadedList)[64][256], int *loadedCount, unsigned int LogByteUpperBound);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_GetMdLogPathWhenNormalStatus(const int meta_handle, unsigned int ms_timeout, char *mdLogPath);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_GetMdLogPathWhenExceptionStatus(const int meta_handle, unsigned int ms_timeout, char *mdLogPath);

/********************************************
 *      Control MeteMode APlogger API
 ********************************************/
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_IsSupportedControlApLogger_r(const int meta_handle, unsigned int ms_timeout);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_APlogControl_r(const int meta_handle, unsigned int ms_timeout, unsigned int storeLogMode, unsigned int loggingAction);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_GetAPLogPath_r(const int meta_handle, unsigned int ms_timeout, char *ApLogPath);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_LoadSDcardApLog(const int meta_handle, unsigned int ms_timeout, char *target_foldername, char *log_filepath);
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_LoadSDcardApLogW(const int meta_handle, unsigned int ms_timeout, char *target_foldername, const wchar_t *log_filepath);

typedef struct
{
    unsigned short year;
    unsigned short month;
    unsigned short day;
    unsigned short hour;
    unsigned short minute;
    unsigned short second;
    unsigned short milliseconds;
} SP_TARGET_CLOCK_SETTINGS;
SP_META_WRAPPER_RESULT APMETAAPI SP_META_Wrapper_SetTargetClock_r(const int meta_handle, unsigned int ms_timeout, SP_TARGET_CLOCK_SETTINGS time);


#ifdef __cplusplus
}
#endif // #ifdef __cplusplus
#endif // #ifndef __SP_META_WRAPPER_H__
