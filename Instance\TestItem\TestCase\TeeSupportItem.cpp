#include "TeeSupportItem.h"

TeeSupportItem::TeeSupportItem()
{
    m_params = {
        {"TEE Version", "Default"},
    };
}

QWidget* TeeSupportItem::createParameterWidget(QWidget* parent)
{
    QWidget* widget = new QWidget(parent);
    QFormLayout* layout = new QFormLayout(widget);
    widget->setStyleSheet(R"(font: 9pt "Arial";)");

    QComboBox* TeeCombo = new QComboBox(widget);
    TeeCombo->addItems({"Default", "v4.3.0.0", "v4.3.6.3", "v4.5.0.0", "v4.6.1.0", "v5.7.3.1"});
    TeeCombo->setCurrentText(m_params["TEE Version"].toString());
    connect(TeeCombo, &QComboBox::currentTextChanged, [this](const QString& text) {
        m_params["TEE Version"] = text;
        emit parametersChanged();
    });

    layout->addRow("TEE Ver: ", TeeCombo);

    return widget;
}
