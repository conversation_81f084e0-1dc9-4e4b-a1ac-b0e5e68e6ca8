#include "ConnectAgilent.h"

ConnectAgilent::ConnectAgilent(QObject *parent, UINT nTreadID)
    : QThread(parent)
    , m_nTreadID(nTreadID)
{
}

ConnectAgilent::~ConnectAgilent(void)
{
}

void ConnectAgilent::UpdateUIMsg(QString log, LOGCOLOR_TYPE textcolor)
{
    emit signal_VisaUpdateUILog(m_nTreadID, QString("[Visa]%1").arg(log), textcolor);
}

bool ConnectAgilent::ConnectByGPIB()
{
    // 打开GPIB设备
    status[m_nTreadID] = viOpenDefaultRM(&defaultRM[m_nTreadID]);
    if (status[m_nTreadID] != VI_SUCCESS) {
        qDebug() << "Failed to open the default resource manager.";
        UpdateUIMsg("Failed to open the default resource manager.", RED);
        return false;
    }

    // 打开GPIB设备的地址为1的设备
    status[m_nTreadID] = viOpen(defaultRM[m_nTreadID], Common::getInstance()->getDutGPIBAddress(m_nTreadID).toStdString().c_str(), VI_NULL, VI_NULL, &vi[m_nTreadID]);
    if (status[m_nTreadID] != VI_SUCCESS) {
        qDebug() << "Failed to open the GPIB device.";
        viClose(defaultRM[m_nTreadID]);
        UpdateUIMsg(QString("Failed to open the GPIB device with %1.").arg(Common::getInstance()->getDutGPIBAddress(m_nTreadID)), RED);
        return false;
    }

    // 发送命令并读取响应
    char command[256] = "*IDN?";
    char response[256] = "";
    UpdateUIMsg(QString("Command: %1").arg(command));
    status[m_nTreadID] = viWrite(vi[m_nTreadID], (ViBuf)command, strlen(command), NULL);
    if (status[m_nTreadID] == VI_SUCCESS) {
        status[m_nTreadID] = viRead(vi[m_nTreadID], (ViBuf)response, sizeof(response), NULL);
        if (status[m_nTreadID] == VI_SUCCESS) {
            qDebug() << "Response: " << response;
            UpdateUIMsg(QString("Response: %1").arg(response).chopped(1));
        }
    }

    return true;
}

void ConnectAgilent::DisConnectByGPIB()
{
    // 关闭GPIB设备
    if(vi[m_nTreadID] != NULL)
        viClose(vi[m_nTreadID]);

    if(defaultRM[m_nTreadID] != NULL)
        viClose(defaultRM[m_nTreadID]);
}

void ConnectAgilent::ReadErrorQueue()
{
    while(true){
        char command[256] = "SYST:ERR?";
        char response[256] = "";
        status[m_nTreadID] = viWrite(vi[m_nTreadID], (ViBuf)command, strlen(command), NULL);
        if (status[m_nTreadID] == VI_SUCCESS) {
            status[m_nTreadID] = viRead(vi[m_nTreadID], (ViBuf)response, sizeof(response), NULL);
            if (status[m_nTreadID] == VI_SUCCESS) {
                qDebug() << "Response: " << response;
                UpdateUIMsg(QString("Command: SYST:ERR? \r\nResponse: %1").arg(response));
            }
        }

        std::string rc = response;
        if(rc != "0,\"NO ERROR\""){
            error_queue[m_nTreadID].push_back(rc);
        }
        else{
            break;
        }
    }
}

void ConnectAgilent::ClearStatus()
{
    char Command[256] = "*CLS";
    status[m_nTreadID] = viWrite(vi[m_nTreadID], (ViBuf)Command, strlen(Command), NULL);
    if (status[m_nTreadID] == VI_SUCCESS) {
        qDebug() << "Clear Status Success!";
    }
}

void ConnectAgilent::ResetDevice()
{
    char Command[256] = "*RST";
    status[m_nTreadID] = viWrite(vi[m_nTreadID], (ViBuf)Command, strlen(Command), NULL);
    if (status[m_nTreadID] == VI_SUCCESS) {
        qDebug() << "Reset Status Success!";
    }
}

void ConnectAgilent::SetOutputCoupled(bool coupled)
{;
    char Command[256] = "*SAV 0";
    status[m_nTreadID] = viWrite(vi[m_nTreadID], (ViBuf)Command, strlen(Command), NULL);
    if (status[m_nTreadID] == VI_SUCCESS) {
        qDebug() << "*SAV 0 Success!";
    }

    sprintf_s(Command, "OUTP:PON:STAT RCL 0");
    status[m_nTreadID] = viWrite(vi[m_nTreadID], (ViBuf)Command, strlen(Command), NULL);
    if (status[m_nTreadID] == VI_SUCCESS) {
        qDebug() << "OUTP:PON:STAT RCL 0 Success!";
    }

    sprintf_s(Command, "INST:COUP:OUTP:STAT %s", (coupled ? "ALL" : "NONE"));
    status[m_nTreadID] = viWrite(vi[m_nTreadID], (ViBuf)Command, strlen(Command), NULL);
    if (status[m_nTreadID] == VI_SUCCESS) {
        qDebug() << Command << " Success!";
    }
}

void ConnectAgilent::SetOutputVoltageAndCurrentLevels(int channel, float voltage, float current)
{
    char Command[256] = {0};
    if (channel == 1) {
        sprintf_s(Command, "VOLT %f;:CURR %f", voltage, current);
    } else if (channel == 2) {
        sprintf_s(Command, "VOLT2 %f;:CURR2 %f", voltage, current);
    }

    status[m_nTreadID] = viWrite(vi[m_nTreadID], (ViBuf)Command, strlen(Command), NULL);
    if (status[m_nTreadID] == VI_SUCCESS) {
        qDebug() << "Set Output Voltage And Current Levels Success!" ;
    }
}

void ConnectAgilent::SetCurrentMeasurementRange(MEASURE_RANGE range)
{
    char Command[256] = {0};
    if (range == HIGH) {
        sprintf_s(Command, "SENS:CURR:RANG MAX");// 0 ~ 3A
    } else if (range == MIDDLE) {
        sprintf_s(Command, "SENS:CURR:RANG 1.0"); // 0 ~ 1A
    } else if (range == LOW) {
        sprintf_s(Command, "SENS:CURR:RANG MIN"); // 0 ~ 20mA
    }

    status[m_nTreadID] = viWrite(vi[m_nTreadID], (ViBuf)Command, strlen(Command), NULL);
    if (status[m_nTreadID] == VI_SUCCESS) {
        qDebug() << "Set Current Measurement Range Success!" ;
    }
}

void ConnectAgilent::EnableOutput(int channel, bool enable)
{
    char Command[256] = {0};
    sprintf_s(Command, "OUTP%d %s", channel, (enable ? "ON" : "OFF"));
    UpdateUIMsg(QString("Command: %1").arg(Command));
    status[m_nTreadID] = viWrite(vi[m_nTreadID], (ViBuf)Command, strlen(Command), NULL);
    if (status[m_nTreadID] == VI_SUCCESS) {
        qDebug() << "Enable Output Success!" ;
        UpdateUIMsg(QString("Enable Output Success!"));
    }
}

double ConnectAgilent::ReadMeasureCurrent(int channel)
{
    char Command[256] = {0};
    char response[256] = "";

    if (channel == 1) {
        sprintf_s(Command, "MEAS:CURR?");
    } else if (channel == 2) {
        sprintf_s(Command, "MEAS:CURR2?");
    }

    status[m_nTreadID] = viWrite(vi[m_nTreadID], (ViBuf)Command, strlen(Command), NULL);
    if (status[m_nTreadID] == VI_SUCCESS) {
        status[m_nTreadID] = viRead(vi[m_nTreadID], (ViBuf)response, sizeof(response), NULL);
        if (status[m_nTreadID] == VI_SUCCESS) {
            qDebug() << "Response: " << response;
            //UpdateUIMsg(QString("Command: %1 \r\nResponse: %2").arg(Command).arg(response));
        }
    }

    // DC电源返回的电流单位是A，转换为mA
    return atof(response) * 1000;
}
