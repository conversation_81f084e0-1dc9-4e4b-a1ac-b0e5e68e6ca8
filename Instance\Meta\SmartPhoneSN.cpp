#include "SmartPhoneSN.h"
#include "C2kAgent_api_datatype.h"
#include "LibKPHA.h"

////////////////////////////////////////////////////

const unsigned int PortEnumHelper::ms_iFilterLen = 1024;
const unsigned int PortEnumHelper::ms_iFilterNum = PortEnumHelper::ms_iFilterLen/20u;
const unsigned int PortEnumHelper::ms_iPortNum = 20;

PortEnumHelper::PortEnumHelper()
    : m_szFilter(NULL)
    , m_aiFlag(NULL)
    , m_astPort(NULL)
{
    memset(&m_stFiler, 0, sizeof(SP_COM_FILTER_LIST_S));
}
PortEnumHelper::~PortEnumHelper()
{
    delete [] m_szFilter;
    delete [] m_aiFlag;
    delete [] m_stFiler.m_ppFilterID;
    delete [] m_astPort;
}

void PortEnumHelper::SetFilter(SP_FILTER_TYPE_E type, const char * filter, int flag /*= 0*/, bool bAppend /*= false*/)
{
    char * szFilter = NULL;
    size_t nLen = 0u, nPos = 0u;

    if (m_szFilter == NULL)
        m_szFilter = new char[ms_iFilterLen];
    if (m_stFiler.m_ppFilterID == NULL)
    {
        m_stFiler.m_ppFilterID = new char * [ms_iFilterNum];
        m_aiFlag = new int [ms_iFilterNum];
    }

    if (filter == NULL || filter[0] == '\0')
        return;

    if (!bAppend)
    {
        memset(m_szFilter, 0, ms_iFilterLen);
        memset(m_stFiler.m_ppFilterID, 0, sizeof(char *)*ms_iFilterNum);
        m_stFiler.m_uCount = 0;
        szFilter = m_szFilter;
    }
    else if (m_stFiler.m_uCount == 0)
        szFilter = m_szFilter;
    else
    {
        nLen = strlen(m_stFiler.m_ppFilterID[m_stFiler.m_uCount - 1]);
        szFilter = m_stFiler.m_ppFilterID[m_stFiler.m_uCount - 1] + nLen + 1;
    }

    strcpy_s(szFilter, ms_iFilterLen - (szFilter - m_szFilter), filter);
    m_stFiler.m_eType = type;

    nLen = strlen(szFilter);
    while (nPos < nLen)
    {
        nPos += strspn(szFilter+nPos, " \t,;");
        if (nPos >= nLen)
            break;
        m_stFiler.m_ppFilterID[m_stFiler.m_uCount] = szFilter+nPos;
        m_aiFlag[m_stFiler.m_uCount] = flag;
        m_stFiler.m_uCount ++;

        nPos += strcspn(szFilter+nPos, " \t,;");
        if (nPos >= nLen)
            break;
        szFilter[nPos++] = '\0';
    }
}

SP_COM_FILTER_LIST_S * PortEnumHelper::GetFilter()
{
    return &m_stFiler;
}

int PortEnumHelper::GetFlag(int index)
{
    if (index >= (int)m_stFiler.m_uCount)
        return -1;
    return m_aiFlag[index];
}

SP_COM_PROPERTY_S * PortEnumHelper::GetPorts(bool bClear /*= false*/)
{
    if (m_astPort == NULL)
    {
        m_astPort = new SP_COM_PROPERTY_S[ms_iPortNum];
        memset(m_astPort, 0, sizeof(SP_COM_PROPERTY_S)*ms_iPortNum);
    }
    else if (bClear)
        memset(m_astPort, 0, sizeof(SP_COM_PROPERTY_S)*ms_iPortNum);

    return m_astPort;
}

////////////////////////////////////////////////////

static const GUID  GUID_PORT_CLASS_USB2SER = {0x4D36E978L, 0xE325, 0x11CE, {0xBF, 0xC1, 0x08, 0x00, 0x2B, 0xE1, 0x03, 0x18}};


int __stdcall MdTypeSwitchHandler(META_MDTYPE_Switch_Param_T mdtype_switch_param, void* MdTypeSwitch_CB_Arg)
{
    return 1;
}

void __stdcall CNF_ReadFromAPNvram(const AP_FT_NVRAM_READ_CNF *cnf, const short token, void *usrData)
{
    if (cnf->status == META_SUCCESS)
    {
        SetEvent(*((HANDLE*)usrData));
        //MTRACE(m_hDebugTrace[m_nTreadID]," Set Event to read AP Nvram.");
    }

}

void __stdcall CNF_WriteToAPNvram ( const AP_FT_NVRAM_WRITE_CNF *cnf, const short token, void *usrData)
{
    if (cnf->status == META_SUCCESS)
    {
        SetEvent(*((HANDLE*)usrData));
        //MTRACE(m_hDebugTrace[m_nTreadID]," set Event to write AP Nvram.");
    }
}

void __stdcall CNF_SPReadFromNVRAM(const AP_FT_NVRAM_READ_CNF *cnf, const short token, void *usrData)
{
    if (cnf->status == META_SUCCESS)
    {
        SetEvent(*((HANDLE*)usrData));
    }
}

void __stdcall CNF_SPWriteToNVRAM ( const AP_FT_NVRAM_WRITE_CNF *cnf, const short token, void *usrData)
{
    if (cnf->status == META_SUCCESS)
    {
        SetEvent(*((HANDLE*)usrData));
    }
}

SmartPhoneSN::SmartPhoneSN(QObject *parent, UINT nTreadID)
    : SNBase(parent, nTreadID)
    , m_nTreadID(nTreadID)
{
    memset(&m_stModeArg, 0, sizeof(SP_BOOT_ARG_S));
    m_nTreadID = nTreadID;

    m_hMauiMetaHandle = INVALID_META_HANDLE;
    m_eMetaMode = SP_NOTIN_META;
    m_nKernelComport = 0;
    m_pMetaStopFlag = &g_TestStopFlag[nTreadID];
    m_bTargetInMetaMode = false;

    m_bWorldPhone = false;
    m_bWithoutMD = false;
    m_bDualModem = false;
    m_bWriteProdInfo = false;
    m_iCurMDChanelIndex = 0;
    memset(m_iMDChannelIndex, 0, (sizeof(UINT)*MAX_MD_CHANNEL_NUM));

    m_bDSDAProject = false;
    m_iC2kProject = 0;
    m_bInitExtMDdb = false;

    MetaHandle_Init();
}


SmartPhoneSN::~SmartPhoneSN(void)
{
    MetaHandle_DeInit();
}

META_RESULT SmartPhoneSN::BootMetaMode()
{
    META_RESULT ret;
    Init();

    SetupMetaModeParameters();

    ret = EnterAPMetaMode();

    return ret;
}

META_RESULT SmartPhoneSN::InitAPMeta()
{
    META_RESULT ret;

    if(Common::getInstance()->getLoadApFromDutEnable() || Common::getInstance()->getLoadMdFromDutEnable())
    {
        UpdateUIMsg("Query DB From Dut...");
        ret = QueryDBFromDUT();
        if (ret != META_SUCCESS)
        {
            goto End;
        }
    }

    if (!Common::getInstance()->getAPDBInitFlag(m_nTreadID) && Common::getInstance()->getLoadApFromDutEnable())
    {
        UpdateUIMsg("Get AP DB From Dut...");
        ret = GetAPDBFromDUT();
        if (ret != META_SUCCESS)
        {
            goto End;
        }
    }

    UpdateUIMsg("Load AP DB And Init...");
    ret = LoadAPDatabase();
    if (ret != META_SUCCESS)
    {
        goto End;
    }

End:
    return ret;
}

META_RESULT SmartPhoneSN::InitMDMeta()
{
    META_RESULT ret = META_FAILED;
    int iRet = GetSPModemInfo_Ex();
    if (iRet != 0)
    {
        goto End;
    }

    if (!Common::getInstance()->getMD1DBInitFlag(m_nTreadID) && Common::getInstance()->getLoadMdFromDutEnable())
    {
        UpdateUIMsg("Get MD DB From Dut...");
        ret = GetMDDBFromDUT();
        if (ret != META_SUCCESS)
        {
            goto End;
        }
    }

    UpdateUIMsg("Enable Modem Meta...");
    ret = MDSLA_Connect();
    if (ret != META_SUCCESS)
    {
        goto End;
    }

    UpdateUIMsg("Init Modem DB...");
    ret = LoadModemDatabase(0);
    if (ret != META_SUCCESS)
    {
        goto End;
    }

End:
    return ret;
}

META_RESULT SmartPhoneSN::ExitMetaMode(bool bpass)
{
    if (m_eMetaMode == SP_AP_META || m_eMetaMode == SP_MODEM_META)
    {
        if(bpass)
        {
            REQ_BackupNvram2BinRegion_Start();
        }
        ExitAPMetaMode();
    }

    return META_SUCCESS;
}

META_RESULT SmartPhoneSN::ExitMetaModeToReboot(bool bpass)
{
    META_RESULT MetaResult = META_SUCCESS;
    if(bpass)
    {
        MetaResult = REQ_BackupNvram2BinRegion_Start();
    }

    if (MetaResult != META_SUCCESS)
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::ExitMetaModeToReboot(): REQ_BackupNvram2BinRegion_Start fail!");
        SP_META_DisconnectWithTarget_r (m_hMauiMetaHandle);
        return MetaResult;
    }
    else
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::ExitMetaModeToReboot(): REQ_BackupNvram2BinRegion_Start Success!");
        SP_META_CloseComPortReboot_r(m_hMauiMetaHandle);
        return MetaResult;
    }
}

void SmartPhoneSN::Init()
{
    memset(&m_stModeArg, 0, sizeof(SP_BOOT_ARG_S));
    memset(&m_sMdInfo, 0, sizeof(SP_MODEMInfo_s));
    m_nKernelComport = 0;
    m_eMetaMode = SP_NOTIN_META;
    m_bWorldPhone = false;
    m_bWithoutMD = false;
    m_bDualModem = false;
    m_bWriteProdInfo = false;

    m_iCurMDChanelIndex = 0;
    m_bDSDAProject = false;
    m_iC2kProject = 0;
    m_bInitExtMDdb = false;
    memset(m_iMDChannelIndex, 0, (sizeof(UINT)*MAX_MD_CHANNEL_NUM));

     m_bStopBeforeUSBInsert = true;

     SNThread_Init();
     testCaseObj = Common::getInstance()->getJsonParams(InputCodeItem::staticType());
     YDtestCaseObj = Common::getInstance()->getJsonParams(InputYDCodesItem::staticType());
}

META_RESULT SmartPhoneSN::MetaHandle_Init()
{
    META_RESULT result;

    if (m_hMauiMetaHandle == INVALID_META_HANDLE)
    {
        result = ModemMetaHandle_Init();
        if (result != META_SUCCESS)
            return result;

        result = APMetaHandle_Init();
        if (result != META_SUCCESS)
            return result;
    }

    return META_SUCCESS;
}

META_RESULT SmartPhoneSN::ModemMetaHandle_Init()
{
    META_RESULT meta_result = META_SUCCESS;
    meta_result = META_GetAvailableHandle_Ex(&m_hMauiMetaHandle, DATA_LIBRARY_MODE_EDB);
    if (meta_result != META_SUCCESS)
    {
        MTRACE_ERR (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::META_GetAvailableHandle(): Get available modem meta handle fail, MetaResult = %s", Common::getInstance()->ResultToString(meta_result));
        return meta_result;
    }
    MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::META_GetAvailableHandle(): Get available modem meta handle success!");

    meta_result = META_Init_Ex_2_r( m_hMauiMetaHandle, NULL, MdQueryHandler, (void*)&m_sMdInfo, NULL, NULL, MdTypeSwitchHandler, NULL);
    if (meta_result != META_SUCCESS)
    {
        MTRACE_ERR (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::META_Init_Ex_2_r(): Init modem meta handle fail, MetaResult = %s", Common::getInstance()->ResultToString(meta_result));
        return meta_result;
    }
    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::META_Init_Ex_2_r(): Init modem meta handle success!");

    return meta_result;
}

META_RESULT SmartPhoneSN::APMetaHandle_Init()
{
    META_RESULT spMetaResult = META_SUCCESS;

    m_hSPMetaHandle = m_hMauiMetaHandle;

    spMetaResult = SP_META_Init_r (m_hSPMetaHandle , NULL);
    if ( spMetaResult != META_SUCCESS)
    {
        MTRACE_ERR(m_hDebugTrace[m_nTreadID], "SP_META_Init_r(): Init AP handle fail, MetaResult = %s", Common::getInstance()->ResultToString_SP(spMetaResult));
        return spMetaResult;
    }
    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::SP_META_Init_r(): Init AP handle success");

    return META_SUCCESS;
}

void SmartPhoneSN::MetaHandle_DeInit()
{
    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::MetaHandle_DeInit() : DeInit meta handle start...");
    if (m_hMauiMetaHandle != INVALID_META_HANDLE)
    {
        ModemMetaHandle_DeInit();
        m_hMauiMetaHandle = INVALID_META_HANDLE;
    }

    if (m_hSPMetaHandle != INVALID_META_HANDLE)
    {
        APMetaHandle_DeInit();
        m_hSPMetaHandle = INVALID_META_HANDLE;
    }

    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::MetaHandle_DeInit() : DeInit meta handle end...");
}

void SmartPhoneSN::ModemMetaHandle_DeInit()
{
    META_Deinit_r(&m_hMauiMetaHandle);
}

void SmartPhoneSN::APMetaHandle_DeInit()
{
    SP_META_Deinit_r(&m_hSPMetaHandle);
}


void SmartPhoneSN::SetupMetaModeParameters()
{
    MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::SetupMetaModeParameters() start...");
    //For preloader handshake parameters
    memset (&m_stModeArg, 0x0, sizeof(SP_BOOT_ARG_S));
    memset(&m_tMetaReq_Ex, 0, sizeof(m_tMetaReq_Ex));
    memset(&m_tMetaConnReport_Ex, 0, sizeof(m_tMetaConnReport_Ex));

    m_nKernelComport = 0;//g_sMetaComm.iCOMPort

    //Old parameters
    m_stModeArg.m_bbchip_type = SP_AUTO_DETECT_BBCHIP;
    m_stModeArg.m_ext_clock = SP_AUTO_DETECT_EXT_CLOCK;
    m_stModeArg.m_ms_boot_timeout = SP_BOOT_INFINITE;
    m_stModeArg.m_max_start_cmd_retry_count = 1;
    //New parameters
    m_stModeArg.m_uTimeout = Common::getInstance()->getPLTimeout();
    m_stModeArg.m_uRetryTime = 2000;
    m_stModeArg.m_uInterval = 10;
    m_stModeArg.m_uBaudrate = CBR_115200;
    m_stModeArg.m_pStopFlag  = m_pMetaStopFlag;

    m_stModeArg.m_auth_handle = NULL;
    m_stModeArg.m_scert_handle = NULL;
    m_stModeArg.m_cb_sla_challenge = NULL;
    m_stModeArg.m_cb_sla_challenge_arg = NULL;
    m_stModeArg.m_cb_sla_challenge_end = NULL;
    m_stModeArg.m_cb_sla_challenge_end_arg = NULL;

    m_stModeArg.m_bIsUSBEnable = true;
    m_stModeArg.m_bIsSymbolicEnable = false;
    m_stModeArg.m_bIsCompositeDeviceEnable = Common::getInstance()->getADBServiceEnable();
    m_stModeArg.m_euBootMode = SP_META_BOOT;
    m_stModeArg.m_uPortNumber = 0;
    m_stModeArg.m_uMDMode = 0;

    //Common
    m_tMetaReq_Ex.com_port = m_nKernelComport;
    m_tMetaReq_Ex.ms_connect_timeout = Common::getInstance()->getKLTimeout();
    m_tMetaReq_Ex.close_com_port = 1;
    m_tMetaReq_Ex.InMetaMode = 1;
    m_tMetaReq_Ex.protocol = 1;          // DHL
    m_tMetaReq_Ex.channel_type = 1;      // Native channel
    m_tMetaReq_Ex.boot_meta_arg.m_bbchip_type = SP_AUTO_DETECT_BBCHIP;
    m_tMetaReq_Ex.boot_meta_arg.m_ext_clock = SP_AUTO_DETECT_EXT_CLOCK;
    //UART parameters
    m_tMetaReq_Ex.baudrate[0] = META_BAUD921600;
    m_tMetaReq_Ex.baudrate[1] = META_BAUD_END;
    m_tMetaReq_Ex.flowctrl = META_SW_FLOWCTRL;
    //USB
    MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::SetupMetaModeParameters(): Target enter meta mode by [USB].");
    m_tMetaReq_Ex.usb_enable = 1;
    m_tMetaReq_Ex.boot_meta_arg.m_usb_enable = 1;

    MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::SetupMetaModeParameters() end...");
}

int SmartPhoneSN::TryToOpenSPKernelComport(int KernelCom_Num)
{
    int ret_i = 1;
    DWORD dwError_win = 0u;
    char * sz_error = NULL;
    HANDLE Hcom;
    char tmp_com[30] = {0};
    int retryTime = 0;

    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::TryToOpenSPKernelComport(): Try to open kernel comport until it ready, KernelCom_Num = %d...", KernelCom_Num);

    sz_error = new char[1024];
    memset(sz_error, 0, 1024);
    sprintf_s(tmp_com, "\\\\.\\COM%d", KernelCom_Num);

    //try to open kernel comport until it ready
    do
    {
        retryTime += 1;
        Hcom = CreateFile(tmp_com, GENERIC_WRITE | GENERIC_READ, 0, NULL, OPEN_EXISTING, 0, NULL);
        if (INVALID_HANDLE_VALUE != Hcom)
        {
            CloseHandle(Hcom);
            MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::TryToOpenSPKernelComport(): success, retryTime = %d", retryTime);
            ret_i = 0;
            break;
        }
        dwError_win = GetLastError();
        if (sz_error != NULL)
        {
            Common::getInstance()->ResultToString_Win(dwError_win, sz_error, 1024u);
            MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::TryToOpenSPKernelComport(): fail(%u: %s), retryTime = %d",
                dwError_win, sz_error, retryTime);
        }
        else
            MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::TryToOpenSPKernelComport(): fail(%u), retryTime = %d",
            dwError_win, retryTime);

        Sleep(1000);
    } while (retryTime < 50);

    if (sz_error != NULL)
        delete [] sz_error;
    return ret_i;
}

META_RESULT SmartPhoneSN::EnterAPMetaMode()
{
    META_RESULT spMetaResult = META_SUCCESS;
    int bootResult = 0;

    MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::EnterAPMetaMode() : Enter ap meta start...");
    bootResult = ConnectWithPreloader();
    if (bootResult != 0)
    {
        UpdateUIMsg("Searching preloader com port and handshake Fail.", RED);
        spMetaResult = (META_RESULT)bootResult;
        return spMetaResult;
    }
    UpdateUIMsg("Searching preloader com port and handshake OK.");

    // Kernel Port apear at least take 6s, Wait 4s for Preloader Port disapear
    //  when Preloader and Kernel has the same port
    Sleep(4000);

    bootResult = ConnectWithKernelPort_Ex();
    if (bootResult == 0)
    {
        UpdateUIMsg("Searching kernel com port and handshake OK.");
    }
    else
    {
        UpdateUIMsg("Searching kernel com port and handshake Fail.", RED);
    }

    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::EnterAPMetaMode() : Enter ap meta end.");
    spMetaResult = (META_RESULT)bootResult;
    return spMetaResult;
}

META_RESULT SmartPhoneSN::ExitAPMetaMode()
{
    UpdateUIMsg("Exit Meta Mode...");
    META_RESULT spMetaResult;
    char *pFuncName = NULL;

    if (!m_bTargetInMetaMode)
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::ExitAPMetaMode(): not in meta mode.");
        return META_SUCCESS;
    }

    spMetaResult = SP_META_DisconnectWithTarget_r (m_hSPMetaHandle);
    pFuncName = "SP_META_DisconnectWithTarget_r()";

    if (spMetaResult == META_SUCCESS)
        m_bTargetInMetaMode = false;

    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::ExitAPMeta(): Exit meta mode by %s successfully! MetaResult = %s", pFuncName, Common::getInstance()->ResultToString_SP(spMetaResult));
    return spMetaResult;
}

META_RESULT SmartPhoneSN::EnableModemMeta()
{
    int iRet = 0;

    MTRACE(m_hDebugTrace[m_nTreadID], "META_DLL::META_ConnectWithMultiModeTarget_r(): Connect to MODEM meta mode...");
    m_tMetaReq_Ex.com_port = m_nKernelComport;
    iRet = META_ConnectWithMultiModeTarget_r(m_hMauiMetaHandle, &m_tMetaReq_Ex, sizeof(m_tMetaReq_Ex), m_pMetaStopFlag, &m_tMetaConnReport_Ex);
    if (iRet == META_SUCCESS)
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "META_DLL::META_ConnectWithMultiModeTarget_r(): ok.");
        iRet = META_SUCCESS;
        m_eMetaMode = SP_MODEM_META;
    }
    else if (iRet == META_MAUI_DB_INCONSISTENT)
    {
        m_eMetaMode = SP_MODEM_META;
        if (!Common::getInstance()->getMD1DBInitFlag(m_nTreadID))
        {
            MTRACE(m_hDebugTrace[m_nTreadID], "META_DLL::META_ConnectWithMultiModeTarget_r(): md db inconsistent, directly ignore.");
            iRet = META_SUCCESS;
        }
        else
        {
            MTRACE_ERR(m_hDebugTrace[m_nTreadID], "META_DLL::META_ConnectWithMultiModeTarget_r(): fail, md db inconsistent.");
        }
    }
    else
    {
        MTRACE_ERR(m_hDebugTrace[m_nTreadID], "META_DLL::META_ConnectWithMultiModeTarget_r(): fail, %s", Common::getInstance()->ResultToString(iRet));
    }

    return (META_RESULT)iRet;
}

int SmartPhoneSN::ConnectWithPreloader()
{
    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::ConnectWithPreloader(): Start to get dynamic preloader comport...");

    PortEnumHelper cPortHelper;
    SP_COM_PROPERTY_S sCOMProperty = {0};

    int iRet = 0;
    unsigned int eType;   //0:BoorROMUSB,1:PreloaderUSB

    cPortHelper.SetFilter(SP_WHITE_LIST, Common::getInstance()->getBromFilter().toStdString().c_str(), BootROMUSB);
    cPortHelper.SetFilter(SP_WHITE_LIST, Common::getInstance()->getPreloaderFilter().toStdString().c_str(), PreloaderUSB, true);

    if(Common::getInstance()->getDutPort(m_nTreadID, PRELOADER_COM) <= 0)
    {
        UpdateUIMsg("Start to get dynamic preloader comport...");
        int iTimeout = m_stModeArg.m_uTimeout / 1000;  //timeout unit: s, but m_stModeArg.m_uTimeout unit is ms
        iRet = SP_GetIncrementCOMPortWithFilter(cPortHelper.GetFilter(), &sCOMProperty, NULL, true, m_pMetaStopFlag, iTimeout);
        if (0 == iRet)
        {
            switch (cPortHelper.GetFlag(sCOMProperty.m_iFilterIndex))
            {
            case PreloaderUSB:
                eType = PreloaderUSB;
                break;
            case BootROMUSB:
                eType = BootROMUSB;
                break;
            default:
                //MessageBox (NULL, "Filter index error!", "fail", MB_OK);
                return META_FAILED;
            }

            m_stModeArg.m_uPortNumber = sCOMProperty.m_uNumber;
            Common::getInstance()->setDutPort(m_nTreadID, PRELOADER_COM, sCOMProperty.m_uNumber);
        }
        else
        {
            if (iRet == SP_S_TIMEOUT)
            {
                MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::ConnectWithPreloader(): Get dynamic preloader comport timeout...");
                UpdateUIMsg("Open Preloader Port timeout.", RED);
                //PopupMsgBox("Error", MB_OK | MB_ICONERROR, "SmartPhoneSN::ConnectWithPreloader(): Get dynamic preloader comport timeout...");
            }

            return iRet;
        }
    }
    else
    {
        eType = PreloaderUSB;
        m_stModeArg.m_uPortNumber = Common::getInstance()->getDutPort(m_nTreadID, PRELOADER_COM);
        UpdateUIMsg(QString("Try Open Preloader Port %1 ...").arg(m_stModeArg.m_uPortNumber));
        iRet = TryOpenComportUntilReady(m_stModeArg.m_uPortNumber);
        if(iRet != 0)
        {
            MTRACE_ERR(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::ConnectWithPreloader(): Try open COM[%d] fail!",  m_stModeArg.m_uPortNumber);
            UpdateUIMsg(QString("Try Open Preloader Port COM[%1] fail!").arg(m_stModeArg.m_uPortNumber), RED);
            return iRet;
        }
    }

    ::Sleep(10);
    if (BootROMUSB == eType)
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::ConnectWithPreloader(): Get preloader comport successfully, comport = %d", m_stModeArg.m_uPortNumber);
        iRet = SP_BootROM_BootMode(&m_stModeArg);
        if (iRet == 0)
        {
            MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::SP_BootROM_BootMode(): Preloader boot meta mode success!!");
        }
        else
        {
            MTRACE_ERR(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::SP_BootROM_BootMode(): Preloader boot meta mode Fail, Err = %d", iRet);
            UpdateUIMsg(QString("Preloader boot meta mode Fail, Err = %1").arg(Common::getInstance()->ResultToString_SP(iRet)), RED);
        }
    }
    else if (PreloaderUSB == eType)
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::ConnectWithPreloader(): Get preloader comport successfully, comport = %d", m_stModeArg.m_uPortNumber);
        iRet = SP_Preloader_BootMode(&m_stModeArg);
        if (iRet == 0)
        {
            MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::SP_Preloader_BootMode(): Preloader boot meta mode success!!");
        }
        else
        {
            MTRACE_ERR(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::SP_Preloader_BootMode(): Preloader boot meta mode Fail, Err = %d", iRet);
            UpdateUIMsg(QString("Preloader boot meta mode Fail, Err = %1").arg(Common::getInstance()->ResultToString_SP(iRet)), RED);
        }
    }

    return iRet;
}

int SmartPhoneSN::TryOpenComportUntilReady(const UINT nComport)
{
    DWORD dwError = 0u;

    int iRet = 0;
    HANDLE hCom = INVALID_HANDLE_VALUE;
    char strCom[30] = {0};
    sprintf_s(strCom, "\\\\.\\COM%d", nComport);

    int retryTime = 0;
    //try to open comport until it ready
    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::TryOpenComportUntilReady(): Try to open COM[%d] until it ready...", nComport);
    do
    {
        retryTime += 1;
        hCom = CreateFileA(strCom, GENERIC_WRITE | GENERIC_READ, 0, NULL, OPEN_EXISTING, 0, NULL);
        if (INVALID_HANDLE_VALUE != hCom)
        {
            int Ret = CloseHandle(hCom);
            MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::TryOpenComportUntilReady(): Open  COM[%d] success, retryTime = %d...", nComport, retryTime);
            return 0;
        }
        dwError = ::GetLastError();
        MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::TryOpenComportUntilReady(): fail(%u), retryTime = %d...", dwError, retryTime);
        Sleep(500);
    }while(retryTime <= 100 && *m_pMetaStopFlag != BOOT_STOP);

    char strErrMsg[100] = {0};
    sprintf(strErrMsg, "Try to open COM[%d] until it ready fail!!", nComport);
    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::TryOpenComportUntilReady(): %s", strErrMsg);

    return 1;
}

int SmartPhoneSN::ConnectWithKernelPort_Ex()
{
    int iRet = 0;

    PortEnumHelper cPortHelper;
    SP_COM_PROPERTY_S * psCOMProperty = NULL;
    META_ConnectByUSB_Req spUSBConnReq;
    META_ConnectByUSB_Report spUSBConnReport;

    // kernel comport filter white list
    cPortHelper.SetFilter(SP_WHITE_LIST, Common::getInstance()->getKernelFilter().toStdString().c_str());
    psCOMProperty = cPortHelper.GetPorts(true);
    memset(&spUSBConnReq, 0, sizeof(spUSBConnReq));
    memset(&spUSBConnReport, 0, sizeof(spUSBConnReport));

    // phase out SP_META_GetDynamicUSBComPort_r API, cause it have probabilistic can`t fetch kernel comport issue
    if(Common::getInstance()->getDutPort(m_nTreadID, KERNEL_COM) <= 0)
    {
        UpdateUIMsg("Try to open kernel comport...");
        // timeout unit: s, but m_tMetaReq_Ex.ms_connect_timeout unit is ms
        int iTimeout = m_tMetaReq_Ex.ms_connect_timeout / 1000;
        MTRACE(m_hDebugTrace[m_nTreadID], "SP_BROM::SP_GetUSBCOMPortWithFilter(): enum kernel comport...");
        while(1)
        {
            iRet = SP_GetUSBCOMPortWithFilter(cPortHelper.GetFilter(), psCOMProperty, false, m_pMetaStopFlag, iTimeout);
            if (iRet != 0)
            {
                MTRACE_ERR(m_hDebugTrace[m_nTreadID], "SP_BROM::SP_GetUSBCOMPortWithFilter(): enum kernel comport fail(%d).", iRet);
                iRet = 1;
                goto Err;
            }

            if(psCOMProperty->m_uNumber == 0)
            {
                MTRACE(m_hDebugTrace[m_nTreadID], "SP_BROM::SP_GetUSBCOMPortWithFilter(): kernel comport %u. retry get port!", psCOMProperty->m_uNumber);
                continue;
            }
            else
                break;
        }
        MTRACE(m_hDebugTrace[m_nTreadID], "SP_BROM::SP_GetUSBCOMPortWithFilter(): kernel comport %u.", psCOMProperty->m_uNumber);

        m_nKernelComport = psCOMProperty->m_uNumber;
        Common::getInstance()->setDutPort(m_nTreadID, KERNEL_COM, psCOMProperty->m_uNumber);
    }
    else
    {
        m_nKernelComport = Common::getInstance()->getDutPort(m_nTreadID, KERNEL_COM);
        UpdateUIMsg(QString("Try Open Kernel Port %1 ...").arg(m_nKernelComport));
    }

    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::TryToOpenSPKernelComport(): Try to open kernel comport...");
    spUSBConnReq.com_port = m_nKernelComport;
    spUSBConnReq.ms_connect_timeout = m_tMetaReq_Ex.ms_connect_timeout;
    iRet = TryToOpenSPKernelComport(m_nKernelComport);
    if (iRet != 0)
    {
        MTRACE_ERR(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::TryToOpenSPKernelComport(): Try to open kernel comport fail.");
        iRet = 2;
        goto Err;
    }
    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::TryToOpenSPKernelComport(): Try to open kernel com port ok.");

    MTRACE(m_hDebugTrace[m_nTreadID], "SP_META_ConnectInMetaModeByUSB_r(): Enter AP meta mode by comport %u...", m_nKernelComport);
    iRet = SP_META_ConnectInMetaModeByUSB_r (m_hSPMetaHandle, &spUSBConnReq, m_pMetaStopFlag, &spUSBConnReport);
    if (iRet == META_SUCCESS)
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "SP_META_ConnectInMetaModeByUSB_r(): ok.");
        m_eMetaMode = SP_AP_META;
        m_bTargetInMetaMode = true;
        m_bStopBeforeUSBInsert = false;
        iRet = 0;
    }
    else if (iRet == META_MAUI_DB_INCONSISTENT)
    {
        m_eMetaMode = SP_AP_META;
        m_bTargetInMetaMode = true;
        m_bStopBeforeUSBInsert = false;
        if (!Common::getInstance()->getAPDBInitFlag(m_nTreadID))
        {
            MTRACE(m_hDebugTrace[m_nTreadID], "SP_META_ConnectInMetaModeByUSB_r(): ap db inconsistent, directly ignore.");
            iRet = 0;
        }
        else if (/*g_sMetaComm.IgnoreDBInconsistent*/false)
        {
            MTRACE_WARN(m_hDebugTrace[m_nTreadID], "SP_META_ConnectInMetaModeByUSB_r(): ap db inconsistent, but ignore.");
            iRet = 0;
        }
        else
        {
            MTRACE_ERR(m_hDebugTrace[m_nTreadID], "SP_META_ConnectInMetaModeByUSB_r(): fail, ap db inconsistent.");
            iRet = 3;
        }
    }
    else
    {
        MTRACE_ERR(m_hDebugTrace[m_nTreadID], "SP_META_ConnectInMetaModeByUSB_r(): Enter meta fail, %s", Common::getInstance()->ResultToString_SP(iRet));
        m_eMetaMode = SP_NOTIN_META;
        m_bTargetInMetaMode = false;
        m_bStopBeforeUSBInsert = true;
        iRet = 3;
    }

Err:
    return iRet;
}

int SmartPhoneSN::GetSPModemInfo_Ex()
{
    int iRet = 0;

    //Init parameters for without world phone feature
    m_sMdInfo.number_of_md = 0;
    m_sMdInfo.active_md_idx = 1;
    m_sMdInfo.number_of_mdSwImg = 1;
    m_sMdInfo.activeMdTypeIdx = 0;

    MODEM_QUERY_INFO_REQ pReq;
    MODEM_QUERY_INFO_CNF pCnf;
    memset(&pReq, 0, sizeof(MODEM_QUERY_INFO_REQ));
    memset(&pCnf, 0, sizeof(MODEM_QUERY_INFO_CNF));

    if (SP_META_QueryIfFunctionSupportedByTarget_r(m_hSPMetaHandle, 5000, "SP_META_MODEM_Query_Info_r") == META_SUCCESS)
    {
        iRet = SP_META_MODEM_Query_Info_r(m_hSPMetaHandle, 10000, &pReq, &pCnf);
        if (iRet != META_SUCCESS)
        {
            MTRACE_ERR(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::SP_META_MODEM_Query_Info_r(): fail, %s.", Common::getInstance()->ResultToString_SP(iRet));
            return iRet;
        }
        MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::SP_META_MODEM_Query_Info_r(): ok. md_num = %d, active_md_idx = %d.",
            pCnf.modem_number, pCnf.modem_id);
        m_sMdInfo.number_of_md = pCnf.modem_number;
        m_sMdInfo.active_md_idx = pCnf.modem_id;
        m_bWithoutMD = (m_sMdInfo.number_of_md == 0) ? true : false;
        m_bDualModem = (m_sMdInfo.number_of_md == 2) ? true : false;
    }

    if (m_sMdInfo.number_of_md <= 0)
        return META_SUCCESS;

    MODEM_CAPABILITY_LIST_REQ pCapabilitiesReq;
    MODEM_CAPABILITY_LIST_CNF pCapabilitiesCnf;
    memset(&pCapabilitiesReq, 0, sizeof(pCapabilitiesReq));
    memset(&pCapabilitiesCnf, 0, sizeof(pCapabilitiesCnf));

    if (SP_META_QueryIfFunctionSupportedByTarget_r(m_hSPMetaHandle, 5000, "SP_META_MODEM_Capability_r") == META_SUCCESS)
    {
        iRet = SP_META_MODEM_Capability_r(m_hSPMetaHandle, 10000, &pCapabilitiesReq, &pCapabilitiesCnf);
        if (iRet != META_SUCCESS)
        {
            m_sMdInfo.multi_md_capability_support = 0;
            MTRACE_ERR(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::SP_META_MODEM_Capability_r(): Get modem capability from target Fail!!");

            m_tMetaReq_Ex.protocol = 1;
            m_tMetaReq_Ex.channel_type = 1;
            iRet = META_SUCCESS;
        }
        else
        {
            m_sMdInfo.multi_md_capability_support = 1;
            memcpy(&m_SpMdCapList, &pCapabilitiesCnf, sizeof(pCapabilitiesCnf));
            m_tMetaReq_Ex.protocol = m_SpMdCapList.modem_cap[m_sMdInfo.active_md_idx - 1].md_service;
            m_tMetaReq_Ex.channel_type =  m_SpMdCapList.modem_cap[m_sMdInfo.active_md_idx - 1].ch_type;

            if (m_sMdInfo.number_of_md >= 2)
            {
                m_bDSDAProject = true;
                m_iC2kProject = 0;
                int mdIndex = 0;

                for (int i = 0; i < MAX_MD_CHANNEL_NUM; i++)
                {
                    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN: Modem capability, protocol = %d, channel_type = %d!",
                        m_SpMdCapList.modem_cap[i].md_service, m_SpMdCapList.modem_cap[i].ch_type);

                    if (m_SpMdCapList.modem_cap[i].md_service > 0 )
                    {
                        m_iMDChannelIndex[mdIndex] = i;
                        mdIndex += 1;
                    }

                    if (m_sMdInfo.number_of_md == 2 && m_SpMdCapList.modem_cap[i].md_service == 3)
                    {
                        m_iC2kProject = 1;
                        m_bDSDAProject = false;
                    }
                }
            }
        }
        MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::SP_META_MODEM_Capability_r(): Get modem capability, protocol = %d, channel_type = %d!",
            m_tMetaReq_Ex.protocol, m_tMetaReq_Ex.channel_type);
    }


    MODEM_GET_CURRENTMODEMTYPE_REQ pCurMDTypeReq;
    MODEM_GET_CURENTMODEMTYPE_CNF pCurMDTypeCnf;
    memset(&pCurMDTypeReq, 0, sizeof(pCurMDTypeReq));
    memset(&pCurMDTypeCnf, 0, sizeof(pCurMDTypeCnf));

    if (SP_META_QueryIfFunctionSupportedByTarget_r(m_hSPMetaHandle, 5000, "SP_META_MODEM_Get_CurrentModemType_r") == META_SUCCESS)
    {
        iRet = SP_META_MODEM_Get_CurrentModemType_r(m_hSPMetaHandle, 10000, &pCurMDTypeReq, &pCurMDTypeCnf);
        if (iRet != META_SUCCESS)
        {
            iRet = META_SUCCESS;
            MTRACE_ERR(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::SP_META_MODEM_Get_CurrentModemType_r(): Get modem type from target Fail, mean that handset not support worldphone feature!!");
        }
        else
        {
            m_sMdInfo.current_mdtype = pCurMDTypeCnf.current_modem_type;
            MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::SP_META_MODEM_Get_CurrentModemType_r(): Get MD Image info from target success, MdType = %d.", m_sMdInfo.current_mdtype);
        }
    }


    MODEM_QUERY_MDIMGTYPE_REQ pMDImgTypeReq;
    MODEM_QUERY_MDIMGTYPE_CNF pMDImgTypeCnf;
    memset(&pMDImgTypeReq, 0, sizeof(pMDImgTypeReq));
    memset(&pMDImgTypeCnf, 0, sizeof(pMDImgTypeCnf));

    if (SP_META_QueryIfFunctionSupportedByTarget_r(m_hSPMetaHandle, 5000, "SP_META_MODEM_Query_MDIMGType_r") == META_SUCCESS)
    {
        iRet = SP_META_MODEM_Query_MDIMGType_r(m_hSPMetaHandle, 10000, &pMDImgTypeReq, &pMDImgTypeCnf);
        if (iRet != META_SUCCESS)
        {
            iRet = META_SUCCESS;
            MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::SP_META_MODEM_Query_MDIMGType_r(): Get MD Image info from target fail, mean that handset not support worldphone feature!!");
        }
        else
        {
            memcpy(m_sMdInfo.mdimg_type, pMDImgTypeCnf.mdimg_type, 16);
            bool bAllZero = true;
            m_bWorldPhone = true;
            m_sMdInfo.number_of_mdSwImg = 0;
            //Get current active mdtype index
            for (int i = 0; i < 16; i++)
            {
                if (m_sMdInfo.mdimg_type[i] != 0 )
                {
                    m_sMdInfo.number_of_mdSwImg += 1;
                    bAllZero = false;
                }

                if (m_sMdInfo.current_mdtype != 0 && m_sMdInfo.mdimg_type[i] == m_sMdInfo.current_mdtype)
                {
                    m_sMdInfo.activeMdTypeIdx = i;
                }
            }

            //For before MT6577(include MT6577)old chip project
            //Old chip project call this api will return success, but mdimg_type array data all zero
            if (bAllZero)
            {
                m_sMdInfo.number_of_mdSwImg = 1;
                m_sMdInfo.activeMdTypeIdx = 0;
                m_bWorldPhone = false;
                MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::SP_META_MODEM_Query_MDIMGType_r(): Get MD Image info from target success but all zero, mean that handset not support worldphone feature!!");
            }
            else
            {
                MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::SP_META_MODEM_Query_MDIMGType_r(): Get MD Image info from target success, activeMdTypeIdx = %d, mean that handset support worldphone feature!!", m_sMdInfo.activeMdTypeIdx);
            }

            if (m_sMdInfo.number_of_md >= 2)
            {
                //extern modem have one SwImg
                m_sMdInfo.number_of_mdSwImg += (m_sMdInfo.number_of_md - 1);
            }
        }
    }

    if (m_bDSDAProject)
    {
        MODEM_QUERY_DOWNLOAD_STATUS_REQ pDLReq;
        MODEM_QUERY_DOWNLOAD_STATUS_CNF pDLCnf;

        memset(&pDLReq, 0, sizeof(MODEM_QUERY_DOWNLOAD_STATUS_REQ));
        memset(&pDLCnf, 0, sizeof(MODEM_QUERY_DOWNLOAD_STATUS_CNF));

        iRet = SP_META_MODEM_Query_Download_Status_r (m_hSPMetaHandle, 80000, &pDLReq, &pDLCnf);
        if (iRet != META_SUCCESS)
        {
            MTRACE_ERR(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::SP_META_MODEM_Query_Download_Status_r(): Query extern modem download process fail, MetaResult = %s", Common::getInstance()->ResultToString_SP(iRet));
            iRet = 0;
        }
    }

    return iRet;
}

META_RESULT SmartPhoneSN::QueryDBFromDUT()
{
    META_RESULT SPMetaResult = META_SUCCESS;

    MTRACE(m_hDebugTrace[m_nTreadID], "SP_META_QueryIfFunctionSupportedByTarget_r(): Query is suppport load db from DUT...");
    SPMetaResult = SP_META_QueryIfFunctionSupportedByTarget_r(m_hSPMetaHandle, 3000, "SP_META_File_Operation_Parse_r");
    if (SPMetaResult != META_SUCCESS)
        MTRACE_ERR(m_hDebugTrace[m_nTreadID], "SP_META_QueryIfFunctionSupportedByTarget_r(): Query fail, %s.", Common::getInstance()->ResultToString_SP(SPMetaResult));
    else
        MTRACE(m_hDebugTrace[m_nTreadID], "SP_META_QueryIfFunctionSupportedByTarget_r(): Suppport load db from DUT.");

    return SPMetaResult;
}

META_RESULT SmartPhoneSN::GetAPDBFromDUT()
{
    META_RESULT SPMetaResult = META_SUCCESS;
    unsigned int tmp_m = 0;

    // init data for Travel
    char db_folder_dut[128] = "/system/etc/apdb";
    static const char * db_pattern = "APDB_";
    static const char * db_pattern_n = "_ENUM";
    char db_fullname_dut[256] = "";
    char db_fullname_pc[256] = "";
    FILE_OPERATION_PARSE_REQ fop_req;
    FILE_OPERATION_PARSE_CNF fop_cnf;
    FILE_OPERATION_GETFILEINFO_REQ fog_req;
    FILE_OPERATION_GETFILEINFO_CNF fog_cnf;

    // query ap db path in dut
    SPMetaResult = SP_META_QueryIfFunctionSupportedByTarget_r(m_hSPMetaHandle, 3000, "SP_META_Query_APDBPath_r");
    if (SPMetaResult != META_SUCCESS)
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "Don't support SP_META_Query_APDBPath_r(%d: %s), use default path %s. ",
            SPMetaResult, Common::getInstance()->ResultToString_SP(SPMetaResult), db_folder_dut);
    }
    else
    {
        const int dbpathQueryCount = 30;
        QUERY_APDBPATH_REQ dbpath_req;
        QUERY_APDBPATH_CNF dbpath_cnf;

        memset(&dbpath_req, 0, sizeof(dbpath_req));
        memset(&dbpath_cnf, 0, sizeof(dbpath_cnf));
        for (tmp_m = 0; tmp_m < dbpathQueryCount; tmp_m++)
        {
            SPMetaResult = SP_META_Query_APDBPath_r(m_hSPMetaHandle, 3000, &dbpath_req, &dbpath_cnf);
            if (SPMetaResult == META_SUCCESS)
                break;
            MTRACE_WARN(m_hDebugTrace[m_nTreadID], "Query AP DB path fail(%d, %s), wait 1s and retry.", SPMetaResult, Common::getInstance()->ResultToString_SP(SPMetaResult));
            ::Sleep(1000);
        }
        if (tmp_m >= dbpathQueryCount)
        {
            MTRACE_ERR(m_hDebugTrace[m_nTreadID], "Query AP DB path fail, stop query.");
            return SPMetaResult;
        }
        strcpy_s(db_folder_dut, 128, (char *)dbpath_cnf.apdb_path);
        MTRACE(m_hDebugTrace[m_nTreadID], "Get AP DB path %s.", db_folder_dut);
    }

    // get file count
    memset(&fop_req, 0, sizeof(fop_req));
    strcpy_s((char*)fop_req.path_name, 256, db_folder_dut);
    strcpy_s((char*)fop_req.filename_substr, 256, db_pattern);
    memset(&fop_cnf, 0, sizeof(fop_cnf));
    MTRACE(m_hDebugTrace[m_nTreadID], "SP_META_File_Operation_Parse_r(): Find AP DB files from DUT...");
    SPMetaResult = SP_META_File_Operation_Parse_r(m_hSPMetaHandle, 3000, &fop_req, &fop_cnf);
    if (SPMetaResult != META_SUCCESS)
    {
        MTRACE_ERR(m_hDebugTrace[m_nTreadID], "SP_META_File_Operation_Parse_r(): Find fail, %s.", Common::getInstance()->ResultToString_SP(SPMetaResult));
        return SPMetaResult;
    }
    MTRACE(m_hDebugTrace[m_nTreadID], "SP_META_File_Operation_Parse_r(): Find %u AP DB file(s) from DUT.", fop_cnf.file_count);
    if (fop_cnf.file_count <= 0u)
        return META_FAILED;

    memset(&fog_req, 0, sizeof(fog_req));
    memset(&fog_cnf, 0, sizeof(fog_cnf));
    memset(db_fullname_dut, 0, 256);
    memset(db_fullname_pc, 0, 256);
    for (tmp_m = 0; tmp_m < fop_cnf.file_count; tmp_m++)
    {
        // get DB file info: type, size, full name
        fog_req.index = tmp_m;
        MTRACE(m_hDebugTrace[m_nTreadID], "SP_META_File_Operation_GetFileInfo_r(): Get %dth AP DB file info...", tmp_m, Common::getInstance()->ResultToString_SP(SPMetaResult));
        SPMetaResult = SP_META_File_Operation_GetFileInfo_r(m_hSPMetaHandle, 3000, &fog_req, &fog_cnf);
        if (SPMetaResult != META_SUCCESS)
        {
            MTRACE_ERR(m_hDebugTrace[m_nTreadID], "SP_META_File_Operation_GetFileInfo_r(): Get %dth AP DB file info fail, %s.", tmp_m, Common::getInstance()->ResultToString_SP(SPMetaResult));
            continue;
        }
        MTRACE(m_hDebugTrace[m_nTreadID], "SP_META_File_Operation_GetFileInfo_r(): %dth AP DB: type(%u) size(%u) name(%s).",
            tmp_m, fog_cnf.file_info.file_type, fog_cnf.file_info.file_size, (char*)fog_cnf.file_info.file_name);
        if (fog_cnf.file_info.file_size <= 0u)
            continue;

        // ap db file name don't has prefix "_APDB" and postfix "_ENUM"
        if (fog_cnf.file_info.file_name[0] == '_')
            continue;
        if (strstr((char *)fog_cnf.file_info.file_name, db_pattern_n) != NULL)
            continue;

        // trans file to pc
        sprintf_s(db_fullname_dut, "%s/%s", db_folder_dut, (char*)fog_cnf.file_info.file_name);
        sprintf_s(db_fullname_pc, "%s%s", Common::getInstance()->getSubLogDirPath(m_nTreadID), (char*)fog_cnf.file_info.file_name);
        MTRACE(m_hDebugTrace[m_nTreadID], "SP_META_File_Operation_ReceiveFile_r(): Save %dth AP DB file to %s...", tmp_m, db_fullname_pc, Common::getInstance()->ResultToString_SP(SPMetaResult));
        SPMetaResult = SP_META_File_Operation_ReceiveFile_r(m_hSPMetaHandle, 8000, db_fullname_dut, db_fullname_pc);
        if (SPMetaResult != META_SUCCESS)
        {
            MTRACE_ERR(m_hDebugTrace[m_nTreadID], "SP_META_File_Operation_ReceiveFile_r(): Save AP DB file fail, %s.", tmp_m, db_fullname_pc, Common::getInstance()->ResultToString_SP(SPMetaResult));
            continue;
        }
        MTRACE(m_hDebugTrace[m_nTreadID], "SP_META_File_Operation_ReceiveFile_r(): Save %dth AP DB file ok.", tmp_m, db_fullname_pc, Common::getInstance()->ResultToString_SP(SPMetaResult));

        // get the db path on pc
        Common::getInstance()->setAPDbPath_DUT(QString("%1").arg(db_fullname_pc));

        // current: only support ubin, only one modem db
        break;
    }

    return META_SUCCESS;
}

META_RESULT SmartPhoneSN::GetMDDBFromDUT()
{
    META_RESULT SPMetaResult = META_SUCCESS;
    unsigned int tmp_m = 0;

    // init data for Travel
    char db_folder_dut[64] = "/system/etc/mddb";
    static const char * db_pattern = "MDDB.META_";
    const char * db_pattern_odb = "MDDB.META.O";//load odb,add 2020/4/28
    char db_fullname_dut[256] = "";
    char db_fullname_pc[256] = "";
    FILE_OPERATION_PARSE_REQ fop_req;
    FILE_OPERATION_PARSE_CNF fop_cnf;
    FILE_OPERATION_GETFILEINFO_REQ fog_req;
    FILE_OPERATION_GETFILEINFO_CNF fog_cnf;

    // query ap db path in dut
    SPMetaResult = SP_META_QueryIfFunctionSupportedByTarget_r(m_hSPMetaHandle, 3000, "SP_META_MODEM_Query_MDDBPath_r");
    if (SPMetaResult != META_SUCCESS)
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "Don't support SP_META_MODEM_Query_MDDBPath_r(%d: %s), use default path %s. ",
            SPMetaResult, Common::getInstance()->ResultToString_SP(SPMetaResult), db_folder_dut);
    }
    else
    {
        const int dbpathQueryCount = 30;
        MODEM_QUERY_MDDBPATH_REQ dbpath_req;
        MODEM_QUERY_MDDBPATH_CNF dbpath_cnf;

        memset(&dbpath_req, 0, sizeof(dbpath_req));
        memset(&dbpath_cnf, 0, sizeof(dbpath_cnf));
        for (tmp_m = 0; tmp_m < dbpathQueryCount; tmp_m++)
        {
            SPMetaResult = SP_META_MODEM_Query_MDDBPath_r(m_hSPMetaHandle, 3000, &dbpath_req, &dbpath_cnf);
            if (SPMetaResult == META_SUCCESS)
                break;
            MTRACE_WARN(m_hDebugTrace[m_nTreadID], "Query MD DB path fail(%d, %s), wait 1s and retry.", SPMetaResult, Common::getInstance()->ResultToString_SP(SPMetaResult));
            ::Sleep(1000);
        }
        if (tmp_m >= dbpathQueryCount)
        {
            MTRACE_ERR(m_hDebugTrace[m_nTreadID], "Query MD DB path fail, stop query.");
            return SPMetaResult;
        }
        strcpy_s(db_folder_dut, 64, (char *)dbpath_cnf.mddb_path);
        MTRACE(m_hDebugTrace[m_nTreadID], "Get MD DB path %s.", db_folder_dut);
    }

    // get file count
    memset(&fop_req, 0, sizeof(fop_req));
    strcpy_s((char*)fop_req.path_name, 256, db_folder_dut);
    //strcpy_s((char*)fop_req.filename_substr, 256, db_pattern);
    strcpy_s((char*)fop_req.filename_substr, sizeof(fop_req.filename_substr), db_pattern_odb);//load odb,add 2020/4/28
    memset(&fop_cnf, 0, sizeof(fop_cnf));
    MTRACE(m_hDebugTrace[m_nTreadID], "SP_META_File_Operation_Parse_r(): Find MD DB files from DUT...");
    SPMetaResult = SP_META_File_Operation_Parse_r(m_hSPMetaHandle, 3000, &fop_req, &fop_cnf);
    if (SPMetaResult != META_SUCCESS)
    {
        MTRACE_ERR(m_hDebugTrace[m_nTreadID], "SP_META_File_Operation_Parse_r(): Find fail, %s.", Common::getInstance()->ResultToString_SP(SPMetaResult));
        return SPMetaResult;
    }
    MTRACE(m_hDebugTrace[m_nTreadID], "SP_META_File_Operation_Parse_r(): Find %u MD DB file(s) from DUT.", fop_cnf.file_count);
    if (fop_cnf.file_count <= 0u)
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "Md ODb Path from DUT is zero, find edb next");
        Sleep(100);
        memset(&fop_req, 0, sizeof(fop_req));
        strcpy_s((char*)fop_req.path_name, sizeof(fop_req.path_name), db_folder_dut);
        strcpy_s((char*)fop_req.filename_substr, sizeof(fop_req.filename_substr), db_pattern);
        memset(&fop_cnf, 0, sizeof(fop_cnf));
        SPMetaResult = SP_META_File_Operation_Parse_r(m_hSPMetaHandle, 3000, &fop_req, &fop_cnf);
        if(SPMetaResult != META_SUCCESS)
        {
            return SPMetaResult;
        }
        if(fop_cnf.file_count <= 0u)
        {
            return META_FAILED;
        }
    }
    memset(&fog_req, 0, sizeof(fog_req));
    memset(&fog_cnf, 0, sizeof(fog_cnf));
    memset(db_fullname_dut, 0, 256);
    memset(db_fullname_pc, 0, 256);
    for (tmp_m = 0; tmp_m < fop_cnf.file_count; tmp_m++)
    {
        // get DB file info: type, size, full name
        fog_req.index = tmp_m;
        MTRACE(m_hDebugTrace[m_nTreadID], "SP_META_File_Operation_GetFileInfo_r(): Get %dth MD DB file info...", tmp_m, Common::getInstance()->ResultToString_SP(SPMetaResult));
        SPMetaResult = SP_META_File_Operation_GetFileInfo_r(m_hSPMetaHandle, 3000, &fog_req, &fog_cnf);
        if (SPMetaResult != META_SUCCESS)
        {
            MTRACE_ERR(m_hDebugTrace[m_nTreadID], "SP_META_File_Operation_GetFileInfo_r(): Get %dth MD DB file info fail, %s.", tmp_m, Common::getInstance()->ResultToString_SP(SPMetaResult));
            continue;
        }
        MTRACE(m_hDebugTrace[m_nTreadID], "SP_META_File_Operation_GetFileInfo_r(): %dth MD DB: type(%u) size(%u) name(%s).",
            tmp_m, fog_cnf.file_info.file_type, fog_cnf.file_info.file_size, (char*)fog_cnf.file_info.file_name);
        if (fog_cnf.file_info.file_size <= 0u)
            continue;

        // trans file to pc
        sprintf_s(db_fullname_dut, "%s/%s", db_folder_dut, (char*)fog_cnf.file_info.file_name);
        sprintf_s(db_fullname_pc, "%s%s", Common::getInstance()->getSubLogDirPath(m_nTreadID), (char*)fog_cnf.file_info.file_name);
        MTRACE(m_hDebugTrace[m_nTreadID], "SP_META_File_Operation_ReceiveFile_r(): Save %dth MD DB file to %s...", tmp_m, db_fullname_pc, Common::getInstance()->ResultToString_SP(SPMetaResult));
        SPMetaResult = SP_META_File_Operation_ReceiveFile_r(m_hSPMetaHandle, 8000, db_fullname_dut, db_fullname_pc);
        if (SPMetaResult != META_SUCCESS)
        {
            MTRACE_ERR(m_hDebugTrace[m_nTreadID], "SP_META_File_Operation_ReceiveFile_r(): Save MD DB file fail, %s.", tmp_m, db_fullname_pc, Common::getInstance()->ResultToString_SP(SPMetaResult));
            continue;
        }
        MTRACE(m_hDebugTrace[m_nTreadID], "SP_META_File_Operation_ReceiveFile_r(): Save %dth MD DB file ok.", tmp_m, db_fullname_pc, Common::getInstance()->ResultToString_SP(SPMetaResult));

        // get the db path on pc
        Common::getInstance()->setMd1DbPath_DUT(QString("%1").arg(db_fullname_pc));

        // current: only support ubin, only one modem db
        break;
    }

    return META_SUCCESS;
}

META_RESULT SmartPhoneSN::LoadModemDatabase(int MDindex)
{
    META_RESULT meta_result = META_SUCCESS;
    char *pFuncName = NULL;

    bool pbInitDBDone = NULL;
    const char *pStrMDDbpath = NULL;
    unsigned long db = 0;
    char temppath[MAX_PATH] = {0};

    switch (MDindex)
    {
    case 0:
        pbInitDBDone = Common::getInstance()->getMD1DBInitFlag(m_nTreadID);
        if (Common::getInstance()->getLoadMdFromDutEnable() && (Common::getInstance()->getMd1DbPath_DUT() != ""))
            memcpy(temppath, Common::getInstance()->getMd1DbPath_DUT().toStdString().c_str(), sizeof(temppath));
        else
            memcpy(temppath, Common::getInstance()->getMdDbPath().toStdString().c_str(), sizeof(temppath));
        break;

    case 1:
        pbInitDBDone = Common::getInstance()->getMD2DBInitFlag(m_nTreadID);
        memcpy(temppath, Common::getInstance()->getMd2DbPath().toStdString().c_str(), sizeof(temppath));
        break;

    default:
        MTRACE_ERR (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::LoadModemDatabase(): Incorrect MDIndex = %d!", MDindex);
        return META_INVALID_ARGUMENTS;
        break;
    }

    pStrMDDbpath = temppath;
    if (pStrMDDbpath == NULL || pStrMDDbpath[0] == '\0')
    {
        MTRACE_ERR(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::LoadModemDatabase(): Incorrect database file path!");
        return META_INVALID_ARGUMENTS;
    }

    if (pbInitDBDone)
    {
        // For MT6595 DSDA project
        if (m_bDSDAProject)
        {
            MDindex = m_iCurMDChanelIndex;
        }
        MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::META_NVRAM_Init_r(): MD[%d] database already init done!",  MDindex);
        return META_SUCCESS;
    }

    if (m_bDualModem == true && m_bWorldPhone == false)
    {
        MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::META_NVRAM_Init_Ex_r(): start to init MD[%d] database, DB path = \"%s\"...",
            MDindex, pStrMDDbpath);
        meta_result = META_NVRAM_Init_Ex_r(m_hMauiMetaHandle, MDindex, pStrMDDbpath, &db);
        pFuncName = "META_NVRAM_Init_Ex_r";
    }
    else
    {
        MDindex = m_sMdInfo.active_md_idx - 1;
        //For MT6595 DSDA project
        if (m_bDSDAProject)
        {
            MDindex = m_iCurMDChanelIndex;
            if (m_bInitExtMDdb)
            {
                m_sMdInfo.activeMdTypeIdx = 0;
            }
        }

        MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::META_NVRAM_Init_Ex_Mdtype_r(): start to init MD[%d] database, DB path = \"%s\"...",
            MDindex, pStrMDDbpath);
        meta_result = META_NVRAM_Init_Ex_Mdtype_r(m_hMauiMetaHandle, MDindex, m_sMdInfo.activeMdTypeIdx, pStrMDDbpath, &db);
        pFuncName = "META_NVRAM_Init_Ex_Mdtype_r";
    }

    if (meta_result == META_SUCCESS)
    {
        if(MDindex==1)
            Common::getInstance()->setMD2DBInitFlag(m_nTreadID, true);
        else
            Common::getInstance()->setMD1DBInitFlag(m_nTreadID, true);
        MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::%s(): Init MD[%d] database successfully!", pFuncName, MDindex);
    }
    else if (meta_result == META_MAUI_DB_INCONSISTENT /*&& g_sMetaComm.IgnoreDBInconsistent*/)
    {
        if(MDindex==1)
            Common::getInstance()->setMD2DBInitFlag(m_nTreadID, true);
        else
            Common::getInstance()->setMD1DBInitFlag(m_nTreadID, true);
        MTRACE_WARN(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::%s(): md db inconsistent, but ignore.", pFuncName);
        meta_result = META_SUCCESS;
    }
    else
    {
        if(MDindex==1)
            Common::getInstance()->setMD2DBInitFlag(m_nTreadID, false);
        else
            Common::getInstance()->setMD1DBInitFlag(m_nTreadID, false);
        MTRACE_ERR(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::%s(): Init MD[%d] database Fail, MetaResult = %s",
            pFuncName, MDindex, Common::getInstance()->ResultToString(meta_result));
    }

    return meta_result;
}

META_RESULT SmartPhoneSN::LoadAPDatabase()
{
    MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::LoadAPDatabase() start...");

    META_RESULT MetaResult = META_SUCCESS;
    const char * dbpath = NULL;
    char temppath[MAX_PATH] = {0};
    unsigned long db;

    if (Common::getInstance()->getLoadApFromDutEnable() && (Common::getInstance()->getAPDbPath_DUT() != ""))
        memcpy(temppath, Common::getInstance()->getAPDbPath_DUT().toStdString().c_str(), sizeof(temppath));
    else
        memcpy(temppath, Common::getInstance()->getAPDbPath().toStdString().c_str(), sizeof(temppath));

    dbpath = temppath;
    if (Common::getInstance()->getAPDBInitFlag(m_nTreadID))
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::LoadAPDatabase(): Already init AP database done, DB path = %s", dbpath);
        MetaResult = META_SUCCESS;
        goto _end;
    }

    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::LoadAPDatabase(): Start to init AP database, DB path = %s", dbpath);
    MetaResult = SP_META_NVRAM_Init_r(m_hSPMetaHandle, dbpath, &db);
    if ( MetaResult == META_SUCCESS)
    {
        Common::getInstance()->setAPDBInitFlag(m_nTreadID, true);
        MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::LoadAPDatabase(): ok.");
    }
    else if (MetaResult == META_MAUI_DB_INCONSISTENT /*&& g_sMetaComm.IgnoreDBInconsistent*/)
    {
        Common::getInstance()->setAPDBInitFlag(m_nTreadID, true);
        MTRACE_WARN(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::LoadAPDatabase(): ap db inconsistent, but ignore.");
        MetaResult = META_SUCCESS;
    }
    else
    {
        Common::getInstance()->setAPDBInitFlag(m_nTreadID, false);
        MTRACE_ERR(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::LoadAPDatabase(): fail, %s", Common::getInstance()->ResultToString_SP(MetaResult));
    }

_end:
    MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::LoadAPDatabase() end.");
    return MetaResult;
}

META_RESULT SmartPhoneSN::MDSLA_Connect()
{
    META_RESULT meta_result=EnableModemMeta();
    if(meta_result!=META_SUCCESS)
    {
        MTRACE_ERR(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::MDSLA_Connect(): EnableModemMeta failed");
        return meta_result;
    }

    return META_SUCCESS;
}

META_RESULT SmartPhoneSN::REQ_ReadFromModemNVRAM(FT_NVRAM_READ_REQ *psNVRAM_ReadReq, FT_NVRAM_4BYTES_LID_READ_CNF *psNVRAM_ReadCnf)
{
    META_RESULT meta_result;
    meta_result = META_NVRAM_4Bytes_LID_Read_Ex_r(m_hMauiMetaHandle,
        15000,
        psNVRAM_ReadReq,
        psNVRAM_ReadCnf);

    if (meta_result != META_SUCCESS)
    {
        MTRACE_ERR (m_hDebugTrace[m_nTreadID], "SNBase::META_NVRAM_4Bytes_LID_Read_Ex_r(): Read nvram data fail! MetaResult = %s",
            Common::getInstance()->ResultToString(meta_result));
    }

    return meta_result;
}

META_RESULT SmartPhoneSN::REQ_ReadModem_NVRAM_Start(WriteData_Type_e dataType, char *pOutData, unsigned short iRID)
{

    if (!pOutData)
    {
        return META_INVALID_ARGUMENTS;
    }
    m_bWriteNvram = false;

    int iBufLen = 0;
    char *pLID = NULL;
    pConductDataFunc pDataFunc = NULL;
    char *pFuncName = NULL;
    char *pDataType = NULL;

    switch (dataType)
    {
    case WRITE_BARCODE:
        iBufLen = 64;
        pDataType = "Barcode";
        pLID = "NVRAM_EF_BARCODE_NUM_LID";
        pDataFunc = &SmartPhoneSN::ConductBarcodeData;
        pFuncName = "SmartPhoneSN::ConductBarcodeData()";
        break;

    case WRITE_IMEI:
        iBufLen = 10;
        pDataType = "IMEI";
        pLID = "NVRAM_EF_IMEI_IMEISV_LID";
        pDataFunc = &SmartPhoneSN::ConductIMEIData;
        pFuncName = "SmartPhoneSN::ConductIMEIData()";
        break;

    case WRITE_MEID:
        iBufLen = 16;
        pDataType = "MEID";
        pLID = "NVRAM_EF_C2K_MOBILE_ID_LID";
        pDataFunc = &SmartPhoneSN::ConductMEIDData;
        pFuncName = "SmartPhoneSN::ConductMEIDData()";
        break;
    default:
        return META_INVALID_ARGUMENTS;
    }

    META_RESULT MetaResult = META_SUCCESS;

    int iMetaTimeout  = 5000;
    int iReadBufSize = 0;
    FT_NVRAM_READ_REQ  sNVRAM_ReadReq;
    FT_NVRAM_4BYTES_LID_READ_CNF  sNVRAM_ReadCnf;


    memset(&sNVRAM_ReadReq, 0, sizeof(FT_NVRAM_READ_REQ));
    memset(&sNVRAM_ReadCnf, 0, sizeof(FT_NVRAM_4BYTES_LID_READ_CNF));

    MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::META_NVRAM_GetRecLen_r(): Start to get nvram struct size via LID = \"%s\"...", pLID);
    MetaResult = META_NVRAM_GetRecLen_r(m_hMauiMetaHandle, pLID, &iReadBufSize);
    if ( META_SUCCESS != MetaResult)
    {
        MTRACE_ERR (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::META_NVRAM_GetRecLen_r(): Get nvram struct size fail, MetaResult = %s", Common::getInstance()->ResultToString(MetaResult));
        return MetaResult;
    }
    else
    {
        MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::META_NVRAM_GetRecLen_r(): Get nvram struct size = %d successfully!", iReadBufSize);

        if ( NULL != sNVRAM_ReadCnf.buf )
        {
            free ( sNVRAM_ReadCnf.buf );
            sNVRAM_ReadCnf.buf = NULL;
        }

        sNVRAM_ReadReq.LID = pLID;
        sNVRAM_ReadReq.RID = iRID;
        sNVRAM_ReadCnf.len = iReadBufSize;
        sNVRAM_ReadCnf.buf = (unsigned char*) malloc (iReadBufSize * sizeof(unsigned char));
        if (NULL == sNVRAM_ReadCnf.buf)
        {
            MTRACE_ERR (m_hDebugTrace[m_nTreadID], "Malloc heap memory cause fail!");
            return  META_FAILED;
        }
    }

    MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_ReadFromModemNVRAM(): Start to read nvram data...");
    MetaResult =  REQ_ReadFromModemNVRAM (&sNVRAM_ReadReq, &sNVRAM_ReadCnf);
    if (MetaResult != META_SUCCESS )
    {
        pFuncName = "SmartPhoneSN::REQ_ReadFromModemNVRAM()";
        goto Err;
    }
    MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_ReadFromModemNVRAM(): Read nvram data successfully!");

    memcpy(pOutData, sNVRAM_ReadCnf.buf, iReadBufSize);

    if (sNVRAM_ReadCnf.buf != NULL)
    {
        free (sNVRAM_ReadCnf.buf);
        sNVRAM_ReadCnf.buf = NULL;
    }

    return MetaResult;

Err:
    if (sNVRAM_ReadCnf.buf != NULL)
    {
        free (sNVRAM_ReadCnf.buf);
        sNVRAM_ReadCnf.buf = NULL;
    }

    MTRACE_ERR (m_hDebugTrace[m_nTreadID], "%s: fail! MetaResult = %s", pFuncName, Common::getInstance()->ResultToString(MetaResult));

    return MetaResult;

}

META_RESULT SmartPhoneSN::REQ_BackupNvram2BinRegion_Start()
{
    META_RESULT spMetaResult = META_SUCCESS;
    SetCleanBootFlag_REQ req;
    SetCleanBootFlag_CNF cnf;

    req.Notused = 0;

    spMetaResult =  SP_META_SetCleanBootFlag_r ( m_hMauiMetaHandle, 15000, &req, &cnf );

    return spMetaResult;
}

void SmartPhoneSN::WaitKernelPortDisappear()
{
    DWORD dwError = 0u;
    int retryTime = 0;
    HANDLE hCom = INVALID_HANDLE_VALUE;
    char strCom[30] = {0};
    sprintf_s(strCom, "\\\\.\\COM%d", Common::getInstance()->getDutPort(m_nTreadID, KERNEL_COM));

    // try to open comport until it ready
    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::WaitKernelPortDisappear(): Wait kernel port[%u] disappear...", Common::getInstance()->getDutPort(m_nTreadID, KERNEL_COM));
    do
    {
        retryTime ++;
        hCom = ::CreateFileA(strCom, FILE_READ_DATA, 0, NULL, OPEN_EXISTING, 0, NULL);
        if (hCom == INVALID_HANDLE_VALUE)
        {
            dwError = ::GetLastError();
            MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::WaitKernelPortDisappear(): Kernel port disappear(%d).", dwError);
            break;
        }
        ::CloseHandle(hCom);
        MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::WaitKernelPortDisappear(): Kernel port not disappear, times %d.", retryTime);
        Sleep(100);
    }while(retryTime <= 100 && *m_pMetaStopFlag != BOOT_STOP);
}

void SmartPhoneSN::UpdateUIMsg(QString log, LOGCOLOR_TYPE textcolor)
{
    emit signal_MetaUpdateUILog(m_nTreadID, QString("[Meta]%1").arg(log), textcolor);
}

META_RESULT SmartPhoneSN::ReadBarcodeFromAp(char* barcode)
{
    META_RESULT ret = META_SUCCESS;
    UpdateUIMsg("Read Barcode From AP Nvram...");
    PRODUCT_INFO CheckProductInfo;
    char *pLID = "AP_CFG_REEB_PRODUCT_INFO_LID";
    int iLoop = 0;

    while(iLoop < 3)
    {
        ret = REQ_ReadFromAPNvram(pLID, 1, (unsigned char *)(&CheckProductInfo), sizeof(PRODUCT_INFO));
        if(ret != META_SUCCESS)
        {
            Sleep(1000);
            iLoop++;
        }
        else
        {
            break;
        }
    }

    if(ret == META_SUCCESS)
    {
        strcpy_s(barcode, 64, (char*)CheckProductInfo.barcode);
        MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::ReadBarcodeFromAp()  Write barcode = \"%s\"", barcode);
    }

    return ret;

}

META_RESULT SmartPhoneSN::REQ_ReadFromAPNvram(const char* pLID,  unsigned short iRID, unsigned char *pOutDataBuf, const unsigned int nBufLen)
{
    if(!pLID || !pOutDataBuf)
    {
        return META_INVALID_ARGUMENTS;
    }
    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_ReadFromAPNvram(): Star read data from ap nvram, lid = %s, rid = %d, nBufLen = %d",
            pLID, iRID, nBufLen);

    META_RESULT iRet = META_SUCCESS;
    int nReadBufLen = 0;
    AP_FT_NVRAM_READ_REQ sNVRAM_ReadReq;
    AP_FT_NVRAM_READ_CNF sNVRAM_ReadCnf;
    memset(&sNVRAM_ReadReq, 0, sizeof(AP_FT_NVRAM_READ_REQ));
    memset(&sNVRAM_ReadCnf, 0, sizeof(AP_FT_NVRAM_READ_CNF));

    MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_ReadFromAPNvram(): Start to get nvram struct size...");
    iRet = SP_META_NVRAM_GetRecLen(pLID, &nReadBufLen);
    if (META_SUCCESS !=  iRet)
    {
        MTRACE_ERR (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::SP_META_NVRAM_GetRecLen(): Get nvram struct size fail, MetaResult = %s", Common::getInstance()->ResultToString_SP(iRet));
        return iRet;
    }
    MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::SP_META_NVRAM_GetRecLen(): Get nvram struct size = %d successfully!", nReadBufLen);
    if (nReadBufLen > nBufLen)
    {
        MTRACE_ERR (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_ReadFromAPNvram(): Nvram LID %s record size %d > buffer size %d, need confirm.", pLID, nReadBufLen, nBufLen);
        return META_BUFFER_LEN;
    }

    if ( NULL != sNVRAM_ReadCnf.buf )
    {
        free ( sNVRAM_ReadCnf.buf );
        sNVRAM_ReadCnf.buf = NULL;
    }
    sNVRAM_ReadReq.LID = pLID;
    sNVRAM_ReadReq.RID = iRID;
    sNVRAM_ReadCnf.len = nReadBufLen;
    sNVRAM_ReadCnf.buf = (unsigned char*) malloc (nReadBufLen*sizeof(unsigned char));
    if (NULL == sNVRAM_ReadCnf.buf)
    {
        MTRACE_ERR (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_ReadFromAPNvram(): Malloc heap memory cause fail!");
        return	META_FAILED;
    }

    memset(pOutDataBuf, 0, nBufLen);
    MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_ReadFromAPNvram(): Start to read ap nvram data...");
    iRet =  ReadFromAPNvram (&sNVRAM_ReadReq, &sNVRAM_ReadCnf);
    if (iRet != META_SUCCESS )
    {
        MTRACE_ERR (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_ReadFromAPNvram(): Read ap nvram data fail, MetaResult = %s", Common::getInstance()->ResultToString_SP(iRet));
    }
    else
    {
        memcpy_s(pOutDataBuf, nBufLen, sNVRAM_ReadCnf.buf, nReadBufLen);
        MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_ReadFromAPNvram(): Read nvram data successfully!");
    }

    if (sNVRAM_ReadCnf.buf != NULL)
    {
        free (sNVRAM_ReadCnf.buf);
        sNVRAM_ReadCnf.buf = NULL;
    }

    return iRet;
}

META_RESULT SmartPhoneSN::ReadFromAPNvram(AP_FT_NVRAM_READ_REQ *psNVRAM_ReadReq, AP_FT_NVRAM_READ_CNF *psNVRAM_ReadCnf)
{
    DWORD wait_result;
    short iNVRAM_OPID;
    META_RESULT iRet = META_SUCCESS;
    MTRACE(m_hDebugTrace[m_nTreadID]," create Event to read AP Nvram.");
    m_hReadFromNVRAMEvent = CreateEvent( NULL, TRUE, FALSE, NULL );
    ResetEvent ( m_hReadFromNVRAMEvent );
    MTRACE(m_hDebugTrace[m_nTreadID]," Reset Event to read AP Nvram.");
    iRet = SP_META_NVRAM_Read_r ( m_hSPMetaHandle,
                                psNVRAM_ReadReq,
                                psNVRAM_ReadCnf,
                                CNF_ReadFromAPNvram,
                                &iNVRAM_OPID,
                                (void*)&m_hReadFromNVRAMEvent );

    wait_result = WaitForSingleObject ( m_hReadFromNVRAMEvent, 15000 );
    if ( WAIT_TIMEOUT == wait_result )
    {
        CloseHandle (m_hReadFromNVRAMEvent);
        return META_TIMEOUT;
    }
    else if(WAIT_OBJECT_0)
    {
        CloseHandle (m_hReadFromNVRAMEvent);
    }

    return iRet;
}

//------------------------------------------------

META_MD_Query_Result_T __stdcall MdQueryHandler(void* MdQuery_CB_Arg)
{
    SP_MODEMInfo_s *sMdInfo = (SP_MODEMInfo_s*)MdQuery_CB_Arg;
    META_MD_Query_Result_T result;

    result.number_of_md = sMdInfo->number_of_md;
    result.active_md_idx = sMdInfo->active_md_idx - 1;
    result.number_of_mdSwImg = sMdInfo->number_of_mdSwImg;
    result.active_mdtype_idx = sMdInfo->activeMdTypeIdx;
    result.multi_talk = (result.active_md_idx != 0 || result.number_of_md >= 2) ? TRUE : FALSE;
    result.multi_frame_type = 1;

    result.multi_mdtype = (result.number_of_mdSwImg > result.number_of_md) ? true : false;//zishuo modify
    result.multi_md_capability_support = sMdInfo->multi_md_capability_support;
    result.reserved = 5;

    return result;
}

META_RESULT SmartPhoneSN::REQ_WriteModem_NVRAM_Start(WriteData_Type_e dataType, char *pInData, unsigned short iRID)
{
    if (!pInData || strlen(pInData) == 0)
    {
        return META_INVALID_ARGUMENTS;
    }
    m_bWriteNvram = true;

    int iBufLen = 0;
    char *pLID = NULL;
    pConductDataFunc pDataFunc = NULL;
    char *pFuncName = NULL;
    char pWriteData[64] = {0};
    char pReadData[64] = {0};
    char *pDataType = NULL;

    switch (dataType)
    {
    case WRITE_BARCODE:
        iBufLen = 64;
        pDataType = "Barcode";
        pLID = "NVRAM_EF_BARCODE_NUM_LID";
        pDataFunc = &SmartPhoneSN::ConductBarcodeData;
        pFuncName = "SmartPhoneSN::ConductBarcodeData()";
        break;

    case WRITE_IMEI:
        iBufLen = 10;
        pDataType = "IMEI";
        pLID = "NVRAM_EF_IMEI_IMEISV_LID";
        pDataFunc = &SmartPhoneSN::ConductIMEIData;
        pFuncName = "SmartPhoneSN::ConductIMEIData()";
        break;

    case WRITE_MEID:
        //iBufLen = 14;
        iBufLen = 16;
        pDataType = "MEID";
        pLID = "NVRAM_EF_C2K_MOBILE_ID_LID";
        pDataFunc = &SmartPhoneSN::ConductMEIDData;
        pFuncName = "SmartPhoneSN::ConductMEIDData()";
        break;

    default:
        return META_INVALID_ARGUMENTS;
    }


    int iMetaTimeout  = 5000;
    int iWriteBufSize = 0;
    FT_NVRAM_WRITE_REQ sNVRAM_WriteReq;
    FT_NVRAM_4BYTES_LID_WRITE_CNF sNVRAM_WriteCnf;
    FT_NVRAM_READ_REQ  sNVRAM_ReadReq;
    FT_NVRAM_4BYTES_LID_READ_CNF  sNVRAM_ReadCnf;
    META_RESULT meta_result = META_SUCCESS;

    memset(&sNVRAM_WriteReq, 0, sizeof(FT_NVRAM_WRITE_REQ));
    memset(&sNVRAM_WriteCnf, 0, sizeof(FT_NVRAM_4BYTES_LID_WRITE_CNF));
    memset(&sNVRAM_ReadReq, 0, sizeof(FT_NVRAM_READ_REQ));
    memset(&sNVRAM_ReadCnf, 0, sizeof(FT_NVRAM_4BYTES_LID_READ_CNF));

    MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::META_NVRAM_GetRecLen_r(): Start to get nvram struct size via LID = \"%s\"...", pLID);
    meta_result = META_NVRAM_GetRecLen_r(m_hMauiMetaHandle, pLID, &iWriteBufSize);
    if ( META_SUCCESS != meta_result)
    {
        MTRACE_ERR (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::META_NVRAM_GetRecLen_r(): Get nvram struct size fail, MetaResult = %s", Common::getInstance()->ResultToString(meta_result));
        return meta_result;
    }
    else
    {
        MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::META_NVRAM_GetRecLen_r(): Get nvram struct size = %d successfully!", iWriteBufSize);

        if ( NULL != sNVRAM_ReadCnf.buf )
        {
            free ( sNVRAM_ReadCnf.buf );
            sNVRAM_ReadCnf.buf = NULL;
        }

        sNVRAM_ReadReq.LID = pLID;
        sNVRAM_ReadReq.RID = iRID;
        sNVRAM_ReadCnf.len = iWriteBufSize;
        sNVRAM_ReadCnf.buf = (unsigned char*) malloc (iWriteBufSize * sizeof(unsigned char));
        if (NULL == sNVRAM_ReadCnf.buf)
        {
            MTRACE_ERR (m_hDebugTrace[m_nTreadID], "Malloc heap memory cause fail!");
            return  META_FAILED;
        }
    }

    MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_ReadFromModemNVRAM(): Start to read nvram data...");
    meta_result =  REQ_ReadFromModemNVRAM (&sNVRAM_ReadReq, &sNVRAM_ReadCnf);
    if (meta_result != META_SUCCESS )
    {
        pFuncName = "SmartPhoneSN::REQ_ReadFromModemNVRAM()";
        goto Err;
    }
    MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_ReadFromModemNVRAM(): Read nvram data successfully!");

    meta_result = (this->*pDataFunc)((char*)sNVRAM_ReadCnf.buf, iRID, pInData, iBufLen);
    if (meta_result !=  META_SUCCESS)
    {
        goto Err;
    }
    else
    {
        /*
        for(int i = 0; i < iBufLen; i++)
        {
        writeData[i] = sNVRAM_ReadCnf.buf[i];
        }
        */
        memcpy(pWriteData, sNVRAM_ReadCnf.buf, iBufLen);
        MTRACE (m_hDebugTrace[m_nTreadID], "%s: Conduct nvram data successfully!", pFuncName);
    }

    sNVRAM_WriteReq.LID = pLID;
    sNVRAM_WriteReq.RID = iRID;
    sNVRAM_WriteReq.len = iWriteBufSize;
    sNVRAM_WriteReq.buf = sNVRAM_ReadCnf.buf;
    m_sNVRAM_OPID = iRID;

    //sNVRAM_WriteCnf.status = 0;
    //sNVRAM_WriteCnf.LID = pLID;
    sNVRAM_WriteCnf.RID = iRID;

    MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_WriteToModemNVRAM(): Start to write nvram data...");
    meta_result =  REQ_WriteToModemNVRAM(&sNVRAM_WriteReq, &sNVRAM_WriteCnf);
    if (meta_result != META_SUCCESS )
    {
        pFuncName = "SmartPhoneSN::REQ_WriteToModemNVRAM()";
        goto Err;
    }
    MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_WriteToModemNVRAM(): Write nvram data successfully!");

    MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_WriteToModemNVRAM(): Read nvram data for check start...");
    memset(sNVRAM_ReadCnf.buf, 0 , sNVRAM_ReadCnf.len);
    meta_result = REQ_ReadFromModemNVRAM (&sNVRAM_ReadReq, &sNVRAM_ReadCnf);
    if (meta_result != META_SUCCESS )
    {
        pFuncName = "SmartPhoneSN::REQ_WriteToModemNVRAM()";
        goto Err;
    }
    else
    {
        char tmpReadData[64] = {0};
        m_bWriteNvram = false;
        memcpy(pReadData, sNVRAM_ReadCnf.buf, iBufLen);
        (this->*pDataFunc)(tmpReadData, iRID, pReadData, iBufLen);
        if (strncmp(pWriteData, pReadData, iBufLen) != 0)
        {
            MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN: Write_%s[%s]", pDataType, pInData);
            MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN: Read_%s[%s]", pDataType, tmpReadData);
            MTRACE_ERR (m_hDebugTrace[m_nTreadID], "SmartPhoneSN: Check nvram data FAIL!!");
            meta_result = META_FAILED;
        }
        else
        {

            MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN: Write_%s[%s]", pDataType, pInData);
            MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN: Read_%s[%s]", pDataType, tmpReadData);
            MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN: Check nvram data PASS!!");
        }
        if (dataType == WRITE_IMEI)
        {
            strncpy_s(pInData, IMEI_ARRAY_LEN, tmpReadData, IMEI_ARRAY_LEN);
        }
    }
    MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_WriteToModemNVRAM(): Read nvram data for check end...");

    if (sNVRAM_ReadCnf.buf != NULL)
    {
        free (sNVRAM_ReadCnf.buf);
        sNVRAM_ReadCnf.buf = NULL;
    }

    return meta_result;

Err:
    if (sNVRAM_ReadCnf.buf != NULL)
    {
        free (sNVRAM_ReadCnf.buf);
        sNVRAM_ReadCnf.buf = NULL;
    }

    MTRACE_ERR (m_hDebugTrace[m_nTreadID], "%s: fail! MetaResult = %s", pFuncName, Common::getInstance()->ResultToString(meta_result));

    return meta_result;
}

META_RESULT SmartPhoneSN::REQ_ReadAP_NVRAM_Start(WriteData_Type_e dataType, char *pOutData, unsigned short iRID)
{
    MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_ReadAP_NVRAM_Start()...");
    MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::Read DataType is = %d", dataType);

    META_RESULT MetaResult = META_SUCCESS;
    int iBufLen = 0;
    m_bWriteNvram = false;
    char *pLID = NULL;
    pConductDataFunc pDataFunc = NULL;
    switch (dataType)
    {
    case WRITE_BARCODE:
    case WRITE_IMEI:
    case WRITE_IMEI2:
        pLID = "AP_CFG_REEB_PRODUCT_INFO_LID";
        break;
    case WRITE_BT:
        iBufLen = 6;
        pLID = "AP_CFG_RDEB_FILE_BT_ADDR_LID";
        pDataFunc = &SmartPhoneSN::ConductBTAddrData;
        break;

    case WRITE_WIFI:
        iBufLen = 6;
        pLID = "AP_CFG_RDEB_FILE_WIFI_LID";
        pDataFunc = &SmartPhoneSN::ConductWifiAddrData;
        break;

    /*case WRITE_ETHERNET_MAC:
        iBufLen = 6;
        pLID = "AP_CFG_RDEB_FILE_ETHERNET_LID";
        pDataFunc = &SmartPhoneSN::ConductEthernetMacAddrData;
        break;*/

    default:
        return META_INVALID_ARGUMENTS;
    }

    AP_FT_NVRAM_READ_REQ sNVRAM_ReadReq;
    AP_FT_NVRAM_READ_CNF sNVRAM_ReadCnf;
    META_RESULT meta_result;
    int iWriteBufSize = 0;
    memset(&sNVRAM_ReadReq, 0, sizeof(AP_FT_NVRAM_READ_REQ));
    memset(&sNVRAM_ReadCnf, 0, sizeof(AP_FT_NVRAM_READ_CNF));

    MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::SP_META_NVRAM_GetRecLen(): Start to get nvram struct size via LID = \"%s\"...", pLID);
    meta_result = SP_META_NVRAM_GetRecLen(pLID, &iWriteBufSize);
    if (  META_SUCCESS != meta_result)
    {
        MTRACE_ERR (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::SP_META_NVRAM_GetRecLen(): Get nvram struct size fail, MetaResult = %s", Common::getInstance()->ResultToString_SP(meta_result));
        return meta_result;
    }
    else
    {
        MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::SP_META_NVRAM_GetRecLen(): Get nvram struct size = %d successfully!", iWriteBufSize);
        if ( NULL != sNVRAM_ReadCnf.buf )
        {
            free ( sNVRAM_ReadCnf.buf );
            sNVRAM_ReadCnf.buf = NULL;
        }

        sNVRAM_ReadReq.LID = pLID;
        sNVRAM_ReadReq.RID = iRID;
        sNVRAM_ReadCnf.len = iWriteBufSize;
        sNVRAM_ReadCnf.buf = (unsigned char*) malloc (iWriteBufSize * sizeof(unsigned char));
        if (NULL == sNVRAM_ReadCnf.buf)
        {
            MTRACE_ERR (m_hDebugTrace[m_nTreadID], "Malloc heap memory cause fail!");
            return  META_FAILED;
        }
    }

    MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_ReadFromAPNVRAM(): Start to read nvram data...");
    meta_result =  REQ_ReadFromAPNVRAM (&sNVRAM_ReadReq, &sNVRAM_ReadCnf);

    if (meta_result != META_SUCCESS )
    {
        MTRACE_ERR (m_hDebugTrace[m_nTreadID], "REQ_ReadFromAPNVRAM: fail! MetaResult = %s", Common::getInstance()->ResultToString_SP(meta_result));
        return MetaResult;
    }

    if (dataType == WRITE_BARCODE)
    {
        // if(!strcmp(sNVRAM_ReadCnf.buf,"000000000000"))
        {
            int i = 0;
            for (i = 0; i < 64; i++)
            {
                if (!((sNVRAM_ReadCnf.buf[i] >= 'a' && sNVRAM_ReadCnf.buf[i] <= 'z')
                      || (sNVRAM_ReadCnf.buf[i] > 'A' && sNVRAM_ReadCnf.buf[i] < 'Z')
                      || (sNVRAM_ReadCnf.buf[i] >= '0' && sNVRAM_ReadCnf.buf[i] <= '9')))
                {
                    break;
                }
                else
                {
                    pOutData[i] = sNVRAM_ReadCnf.buf[i];
                }
            }

            pOutData[i] = '\0';
        }
    }

    if (dataType == WRITE_IMEI)
    {
        int i = 0;
        for (; i < 8; i++)
        {
            if ((sNVRAM_ReadCnf.buf[64 + i] & 15) >= '0' &&
                (sNVRAM_ReadCnf.buf[64 + i] & 15) <= '9' &&
                ((sNVRAM_ReadCnf.buf[64 + i] >> 4) & 15) >= '0' &&
                ((sNVRAM_ReadCnf.buf[64 + i] >> 4) & 240) <= '9')
            {
                break;
            }
            else
            {
                pOutData[2 * i] = (sNVRAM_ReadCnf.buf[64 + i] & 15 ) + '0';
            }

            pOutData[2 * i + 1] = (sNVRAM_ReadCnf.buf[64 + i] >> 4 & 15) + '0';
        }

        pOutData[2 * i - 1] = '\0';
    }
    if (dataType == WRITE_IMEI2)
    {
        int i = 0;
        for (; i < 8; i++)
        {
            if ((sNVRAM_ReadCnf.buf[74 + i] & 15) >= '0' &&
                (sNVRAM_ReadCnf.buf[74 + i] & 15) <= '9' &&
                ((sNVRAM_ReadCnf.buf[74 + i] >> 4) & 15) >= '0' &&
                ((sNVRAM_ReadCnf.buf[74 + i] >> 4) & 240) <= '9')
            {
                break;
            }
            else
            {
                pOutData[2 * i] = (sNVRAM_ReadCnf.buf[74 + i] & 15 ) + '0';
            }

            pOutData[2 * i + 1] = (sNVRAM_ReadCnf.buf[74 + i] >> 4 & 15) + '0';
        }

        pOutData[2 * i - 1] = '\0';
    }
    if (dataType == WRITE_BT)
    {
        //pDataType = "BT";
        for (int i = 0; i < iBufLen; i++)
        {
            pOutData[i] = sNVRAM_ReadCnf.buf[i];
        }
        char tmpReadData[13] = {0};
        (this->*pDataFunc)(tmpReadData, iRID, pOutData, iBufLen);
        strncpy(pOutData, tmpReadData, sizeof(tmpReadData));
        MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_ReadAP_NVRAM_Start() BT:%s...",pOutData);
    }
    if (dataType == WRITE_WIFI)
    {
        //pDataType = "Wifi";
        //Wifi address offset is 0x4
        for (int i = 0; i < iBufLen; i++)
        {
            pOutData[i] = sNVRAM_ReadCnf.buf[0x4 + i];
        }
        char tmpReadData[13] = {0};
        (this->*pDataFunc)(tmpReadData, iRID, pOutData, iBufLen);
        strncpy(pOutData, tmpReadData, sizeof(tmpReadData));
        MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_ReadAP_NVRAM_Start() WIFI:%s...",pOutData);
    }
    /*if (dataType == WRITE_ETHERNET_MAC)
    {
        //pDataType = "Ethernet_Mac";
        for (int i = 0; i < iBufLen; i++)
        {
            pOutData[i] = sNVRAM_ReadCnf.buf[i];
        }
        char tmpReadData[13] = {0};
        (this->*pDataFunc)(tmpReadData, iRID, pOutData, iBufLen);
        strncpy(pOutData, tmpReadData, sizeof(tmpReadData));
        MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_ReadAP_NVRAM_Start() Ethernet Mac:%s...",pOutData);
    }*/

    MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_ReadAP_NVRAM_Start() Success...");
    return MetaResult;
}

META_RESULT SmartPhoneSN::REQ_ReadFromAPNVRAM(AP_FT_NVRAM_READ_REQ *psNVRAM_ReadReq, AP_FT_NVRAM_READ_CNF *psNVRAM_ReadCnf)
{
    DWORD wait_result;
    short iNVRAM_OPID;
    META_RESULT MetaResult;

    m_hReadFromNVRAMEvent = ::CreateEvent( NULL, TRUE, FALSE, NULL );
    ::ResetEvent(m_hReadFromNVRAMEvent);

    MetaResult = SP_META_NVRAM_Read_r(m_hSPMetaHandle,
                                      psNVRAM_ReadReq,
                                      psNVRAM_ReadCnf,
                                      CNF_SPReadFromNVRAM,
                                      &iNVRAM_OPID,
                                      (void*)&m_hReadFromNVRAMEvent);

    wait_result = ::WaitForSingleObject(m_hReadFromNVRAMEvent, 15000);
    ::CloseHandle (m_hReadFromNVRAMEvent);
    if (wait_result == WAIT_TIMEOUT)
        return META_TIMEOUT;
    if (wait_result != WAIT_OBJECT_0)
        return META_FAILED;

    return MetaResult;
}

META_RESULT SmartPhoneSN::ReadBarcodefromNVRAM(char * SN, int len)
{
    int iBufLen = 0;
    char *pLID = NULL;
    pConductDataFunc pDataFunc = NULL;
    char *pFuncName = NULL;
    char pWriteData[64] = {0};
    char pReadData[64] = {0};
    char *pDataType = NULL;

    iBufLen = 64;
    pDataType = "Barcode";
    pLID = "NVRAM_EF_BARCODE_NUM_LID";
    pDataFunc = &SmartPhoneSN::ConductBarcodeData;
    pFuncName = "SmartPhoneSN::ConductBarcodeData()";

    int iMetaTimeout  = 5000;
    int iWriteBufSize = 0;
    FT_NVRAM_READ_REQ  sNVRAM_ReadReq;
    FT_NVRAM_4BYTES_LID_READ_CNF  sNVRAM_ReadCnf;
    META_RESULT meta_result = META_SUCCESS;

    memset(&sNVRAM_ReadReq, 0, sizeof(FT_NVRAM_READ_REQ));
    memset(&sNVRAM_ReadCnf, 0, sizeof(FT_NVRAM_4BYTES_LID_READ_CNF));

    MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::META_NVRAM_GetRecLen(): Start to get nvram struct size via LID = \"%s\"...", pLID);
    meta_result = META_NVRAM_GetRecLen(pLID,&iWriteBufSize);
    if ( META_SUCCESS != meta_result)
    {
        MTRACE_ERR (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::META_NVRAM_GetRecLen(): Get nvram struct size fail, MetaResult = %s", Common::getInstance()->ResultToString(meta_result));
        return meta_result;
    }
    else
    {
        MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::META_NVRAM_GetRecLen(): Get nvram struct size = %d successfully!", iWriteBufSize);

        if ( NULL != sNVRAM_ReadCnf.buf )
        {
            free ( sNVRAM_ReadCnf.buf );
            sNVRAM_ReadCnf.buf = NULL;
        }

        sNVRAM_ReadReq.LID = pLID;
        sNVRAM_ReadReq.RID = 1;
        sNVRAM_ReadCnf.len = iWriteBufSize;
        sNVRAM_ReadCnf.buf = (unsigned char*) malloc (iWriteBufSize*sizeof(unsigned char));
        if (NULL == sNVRAM_ReadCnf.buf)
        {
            MTRACE_ERR (m_hDebugTrace[m_nTreadID], "Malloc heap memory cause fail!");
            return  META_FAILED;
        }
    }

    MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_ReadFromModemNVRAM(): Start to read nvram data...");
    meta_result =  REQ_ReadFromModemNVRAM (&sNVRAM_ReadReq, &sNVRAM_ReadCnf);
    if (meta_result != META_SUCCESS )
    {
        pFuncName = "SmartPhoneSN::REQ_ReadFromModemNVRAM()";
        goto Err;
    }
    MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_ReadFromModemNVRAM(): Read nvram data successfully!");

    memcpy(SN, sNVRAM_ReadCnf.buf, iBufLen);
    MTRACE (m_hDebugTrace[m_nTreadID], "%s: Conduct nvram data successfully!", pFuncName);

    if (sNVRAM_ReadCnf.buf != NULL)
    {
        free (sNVRAM_ReadCnf.buf);
        sNVRAM_ReadCnf.buf = NULL;
    }

    return meta_result;

Err:
    if (sNVRAM_ReadCnf.buf != NULL)
    {
        free (sNVRAM_ReadCnf.buf);
        sNVRAM_ReadCnf.buf = NULL;
    }

    MTRACE_ERR (m_hDebugTrace[m_nTreadID], "%s: fail! MetaResult = %s", pFuncName, Common::getInstance()->ResultToString(meta_result));

    return meta_result;
}

META_RESULT SmartPhoneSN::ReadBarcodeFromMd(char* barcode)
{
    META_RESULT MetaResult;
    memset(barcode, 0, BARCODE_MAX_LENGTH);
    UpdateUIMsg("Read Barcode From Modem Nvram...");
    MetaResult = ReadBarcodefromNVRAM(barcode, BARCODE_MAX_LENGTH-1);
    if(MetaResult!=META_SUCCESS)
    {
        goto End;
    }

End:
    return MetaResult;
}

// C2K
META_RESULT SmartPhoneSN::EnterC2KGen90(bool WriteFlag, char* m_szReadBack)
{
    META_RESULT ret_last = META_SUCCESS;
    META_RESULT MetaResult = META_SUCCESS;

    bool bInited = false;
    bool bSwitchMd = false;
    bool bConnected = false;
    char szLogFilePath[MAX_PATH] = "";
    C2K_LIBCONFIGPARMS c2kConfig;
    PHONEATTRIBUTE_PARMS phoneAttr;
    C2K_CONNECT_PARAMS connectReq;
    char szPath[MAX_PATH] = {0};

    int preMdChannelIdx = m_sMdInfo.active_md_idx;
    int c2kMdChannelIdx = -1;
    unsigned int c2k_protocol = META_MODEM_SRV_ETS;
    unsigned int c2k_ch_type = META_MODEM_CH_TUNNELING;

    UpdateUIMsg("Start init C2K Meta...");


    // get c2k modem index and channel protocol + type
    for (int i = 0; i < (int)m_sMdInfo.number_of_md + 2; i++)
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "[C2K] i= %d, protocol = %d, channel_type=%d",
               i, m_SpMdCapList.modem_cap[i].md_service, m_SpMdCapList.modem_cap[i].ch_type);

        if (m_SpMdCapList.modem_cap[i].md_service == META_MODEM_SRV_ETS)
        {
            c2kMdChannelIdx = i;
            c2k_protocol = m_SpMdCapList.modem_cap[i].md_service;
            c2k_ch_type = m_SpMdCapList.modem_cap[i].ch_type;
            break;
        }
    }
    if (c2kMdChannelIdx == -1)
    {
        MTRACE_ERR(m_hDebugTrace[m_nTreadID], "[C2K] can not find C2K modem.");
        return META_FAILED;
    }

    // init c2k
    sprintf_s(szLogFilePath, "%sETS_LOG.txt", Common::getInstance()->getSubLogDirPath(m_nTreadID));
    c2kConfig.bLog = true;
    c2kConfig.bScript = false;
    c2kConfig.szLogPath = szLogFilePath;
    c2kConfig.szScriptPath = "";
    memset(&phoneAttr, 0, sizeof(PHONEATTRIBUTE_PARMS));
    phoneAttr.eAfcControlMode = C2K_AFC_TCXO;
    phoneAttr.eRfMode = C2K_RF_MODE_EVDO;
    phoneAttr.eRxCalibrationType = C2K_RX_CAL_MAIN;
    phoneAttr.eSidbAccessMode = C2K_SIDB_FSI;
    phoneAttr.bWriteSIDBFlag = false;       //if write calibration flag?
    phoneAttr.uPllSettleTime = 100;
    phoneAttr.uRxAGCGainTableSelectSettleTime = 200;
    phoneAttr.uRxCtrlSettleTime = 200;
    phoneAttr.uRxGainStateSettleTime = 200;
    phoneAttr.uTxAGCConfigSettleTime = 50;
    phoneAttr.uTxCtrlSettleTime = 50;
    phoneAttr.uTxTestRateSettleTime = 100;
    phoneAttr.uEnableRxTxSpySettleTime = 0;
    phoneAttr.bAFCSlopeStepPerPpmWithQ6 = 0;

    MTRACE (m_hDebugTrace[m_nTreadID], "[C2K] META_C2K_Init() start...");
    MetaResult = META_C2K_Init(m_hMauiMetaHandle, &c2kConfig, &phoneAttr);
    if (MetaResult != META_SUCCESS)
    {
        MTRACE_ERR (m_hDebugTrace[m_nTreadID], "[C2K] META_C2K_Init() fail");
        UpdateUIMsg("META_C2K_Init() fail", RED);
        return MetaResult;
    }
    bInited = true; // inited
    MTRACE (m_hDebugTrace[m_nTreadID], "[C2K] META_C2K_Init() end...");

    // connect to c2k
    MTRACE(m_hDebugTrace[m_nTreadID], "[C2K] Connect C2K USB port.");
    if (c2k_ch_type == META_MODEM_CH_TUNNELING)
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "[C2K] Switch to C2K modem start...");
        if (c2kMdChannelIdx != preMdChannelIdx)
        {
            MTRACE(m_hDebugTrace[m_nTreadID], "[C2K] META_DLL::META_SwitchCurrentModemEx_r() : Switch to C2K modem start...");
            MetaResult = META_SwitchCurrentModemEx_r(m_hMauiMetaHandle, 10000, c2kMdChannelIdx, c2k_protocol, c2k_ch_type, NULL, NULL);
            if (MetaResult != META_SUCCESS)
            {
                MTRACE_ERR(m_hDebugTrace[m_nTreadID], "[C2K] META_DLL::META_SwitchCurrentModemEx_r() : fail, %s", Common::getInstance()->ResultToString(MetaResult));
                goto end;
            }
            bSwitchMd = true;
            MTRACE(m_hDebugTrace[m_nTreadID], "[C2K] META_DLL::META_SwitchCurrentModemEx_r() : ok.");
        }
        MTRACE(m_hDebugTrace[m_nTreadID], "[C2K] Switch to C2K modem end.");

        MTRACE(m_hDebugTrace[m_nTreadID], "[C2K] us_com_port = %d", c2kMdChannelIdx);
        connectReq.eComType = C2K_COM_VC;
        connectReq.uComNum = c2kMdChannelIdx;
    }
    else
    {
        connectReq.eComType = C2K_COM_USB;
        connectReq.uComNum = 0;

        GetModuleFileName(NULL, szPath, MAX_PATH);
        (strchr(szPath, '\\'))[1] = 0;
        strcat_s(szPath, "EtsMsg.txt");

        MTRACE(m_hDebugTrace[m_nTreadID], "[C2K] META_C2K_LoadMessage_r() : start...");
        MetaResult = META_C2K_LoadMessage_r(m_hMauiMetaHandle, szPath);
        if (MetaResult != META_SUCCESS)
        {
            UpdateUIMsg(QString("META_C2K_LoadMessage_r() : fail, %1").arg(META_C2K_GetErrorString(m_hMauiMetaHandle)), RED);
            MTRACE_ERR(m_hDebugTrace[m_nTreadID], "[C2K] META_C2K_LoadMessage_r() : fail, %s", META_C2K_GetErrorString(m_hMauiMetaHandle));
            goto end;
        }
        UpdateUIMsg("META_C2K_LoadMessage_r() : successful!");
        MTRACE (m_hDebugTrace[m_nTreadID], "[C2K] META_C2K_LoadMessage_r() : successful!");
    }

    UpdateUIMsg("Start connect with C2K Meta...");

    MTRACE(m_hDebugTrace[m_nTreadID], "[C2K] META_C2K_ConnectWithTarget_r() start...");
    for (int i = 0; i < 20; i++)
    {
        MetaResult = META_C2K_ConnectWithTarget_r(m_hMauiMetaHandle, 2000, &connectReq);
        if (MetaResult == META_SUCCESS)
            break;
    }
    if (MetaResult != META_SUCCESS)
    {
        UpdateUIMsg(QString("META_C2K_ConnectWithTarget_r() : fail, %1").arg(META_C2K_GetErrorString(m_hMauiMetaHandle)), RED);
        MTRACE_ERR(m_hDebugTrace[m_nTreadID], "[C2K] META_C2K_ConnectWithTarget_r() : fail, %s", META_C2K_GetErrorString(m_hMauiMetaHandle));
        goto end;
    }
    bConnected = true; // connected
    UpdateUIMsg("META_C2K_ConnectWithTarget_r() : successful!");
    MTRACE(m_hDebugTrace[m_nTreadID], "[C2K] META_C2K_ConnectWithTarget_r() : successful!");


    // handshake with c2k
    for (int i = 0; i < 10; i++)
    {
        MetaResult = META_C2K_LoopbackTest_r(m_hMauiMetaHandle, 2000, i + 1);
        if (MetaResult == META_SUCCESS)
            break;
    }
    if (MetaResult != META_SUCCESS )
    {
        UpdateUIMsg(QString("META_C2K_LoopbackTest_r() : fail, %1").arg(META_C2K_GetErrorString(m_hMauiMetaHandle)), RED);
        MTRACE_ERR(m_hDebugTrace[m_nTreadID], "[C2K] META_C2K_LoopbackTest_r() : fail, %s", META_C2K_GetErrorString(m_hMauiMetaHandle));
        goto end;
    }

    // task
    if (testCaseObj[ECodeStr[WRITE_MEID]].toBool())
        ret_last = (META_RESULT)WriteMEID90(WriteFlag, m_szReadBack);
    else if (0/*g_sMetaComm.sWriteOption.bWriteEsn*/)
        ret_last = (META_RESULT)WriteESN90(WriteFlag, m_szReadBack);


end:
    // disconnect
    if (bConnected)
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "[C2K] META_C2K_DisconnectWithTarget_r start...");
        MetaResult = META_C2K_DisconnectWithTarget_r(m_hMauiMetaHandle);
        if (MetaResult != META_SUCCESS)
        {
            MTRACE_ERR(m_hDebugTrace[m_nTreadID], "[C2K] META_C2K_DisconnectWithTarget_r : MetaResult = %s", META_C2K_GetErrorString(m_hMauiMetaHandle));
            UpdateUIMsg(QString("META_C2K_DisconnectWithTarget_r : MetaResult = %1").arg(META_C2K_GetErrorString(m_hMauiMetaHandle)), RED);
            return MetaResult;
        }
        MTRACE(m_hDebugTrace[m_nTreadID], "[C2K] META_C2K_DisconnectWithTarget_r end.");
    }

    // deinit
    if (bInited)
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "[C2K] META_C2K_DeInit start...");
        MetaResult = META_C2K_DeInit(m_hMauiMetaHandle);
        if (MetaResult != META_SUCCESS)
        {
            MTRACE_ERR(m_hDebugTrace[m_nTreadID], "[C2K] META_C2K_DeInit : MetaResult = %s", META_C2K_GetErrorString(m_hMauiMetaHandle));
            UpdateUIMsg(QString("META_C2K_DeInit : MetaResult = %1").arg(META_C2K_GetErrorString(m_hMauiMetaHandle)), RED);
            return MetaResult;
        }
        MTRACE(m_hDebugTrace[m_nTreadID], "[C2K] META_C2K_DeInit end!");
    }

    if (bSwitchMd)
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "[C2K] switch to 234G modem start!");
        MetaResult = META_SwitchCurrentModemEx_r(m_hMauiMetaHandle, 10000, 0, m_SpMdCapList.modem_cap[0].md_service,
                                                 m_SpMdCapList.modem_cap[0].ch_type, NULL, NULL);
        if (MetaResult == META_SUCCESS)
        {
            MTRACE(m_hDebugTrace[m_nTreadID], "[C2K] META_SwitchCurrentModemEx_r(): [C2K] switch to 234G modem successful!");
            UpdateUIMsg("META_SwitchCurrentModemEx_r(): [C2K] switch to 234G modem successful!");
        }
        else if (MetaResult == META_MAUI_DB_INCONSISTENT)
        {
            MetaResult = META_SUCCESS;
            MTRACE(m_hDebugTrace[m_nTreadID], "[C2K] META_SwitchCurrentModemEx_r(): [C2K] switch successful, md db inconsistent, directly ignore.");
        }
        else
        {
            MTRACE_ERR(m_hDebugTrace[m_nTreadID], "[C2K] META_SwitchCurrentModemEx_r(): [C2K] switch to 234G modem fail!");
            UpdateUIMsg("META_SwitchCurrentModemEx_r(): [C2K] switch to 234G modem fail!", RED);
            return MetaResult;
        }
        MTRACE(m_hDebugTrace[m_nTreadID], "[C2K] switch to 234G modem end!");
    }

    if (ret_last != META_SUCCESS)
    {
        if (testCaseObj[ECodeStr[WRITE_MEID]].toBool())
            UpdateUIMsg("[C2K] Write MEID fail.", RED);
        else if (0/*g_sMetaComm.sWriteOption.bWriteEsn*/)
            UpdateUIMsg("[C2K] Write ESN fail.", RED);
    }

    return ret_last;
}

int SmartPhoneSN::EnterC2KGen93(bool WriteFlag, char* m_szReadBack)
{
    int ret_i = 0;
    META_RESULT ret_meta = META_SUCCESS;

    MMRfTestCmdRfCapabilityReq mreq_rfcap;
    MMRfTestCmdRfCapabilityCnf mcnf_rfcap;

    UpdateUIMsg("Start enter C2K...");

    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::EnterC2KGen93(): [C2K] Query if support query c2k...");
    ret_meta = META_QueryIfFunctionSupportedByTarget_r(m_hMauiMetaHandle, 30000, "META_MMRf_GetRfCapability_r");
    if (ret_meta != META_SUCCESS)
    {
        UpdateUIMsg("Don't support query C2K, may be not support C2K.", RED);
        MTRACE_ERR(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::EnterC2KGen93(): [C2K] Query if support api META_MMRf_GetRfCapability_r fail. %s.", Common::getInstance()->ResultToString(ret_meta));
        return 1;
    }

    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::EnterC2KGen93(): [C2K] Query if support c2k...");
    memset(&mreq_rfcap, 0, sizeof(mreq_rfcap));
    mreq_rfcap.capabilityItemsSize = sizeof(MMRfCapabilityItemSet);
    mreq_rfcap.calibrationItemsSize = sizeof(MMRfCalibrationItemSet);
    memset(&mcnf_rfcap, 0, sizeof(mcnf_rfcap));
    ret_meta = META_MMRf_GetRfCapability_r(m_hMauiMetaHandle, 30000, &mreq_rfcap, sizeof(mreq_rfcap), &mcnf_rfcap, sizeof(mcnf_rfcap));
    if (ret_meta != META_SUCCESS)
    {
        UpdateUIMsg("Query C2K info fail, may be not support C2K.");
        MTRACE_ERR(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::EnterC2KGen93(): [C2K] META_MMRf_GetRfCapability_r fail. %s.", Common::getInstance()->ResultToString(ret_meta));
        return 2;
    }

    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::EnterC2KGen93(): [C2K] calibrationItems.ratmap_support_WM: is_capable(%d), parameter(%d).",
           mcnf_rfcap.calibrationItems.ratmap_support_WM.is_capable,
           mcnf_rfcap.calibrationItems.ratmap_support_WM.parameter);

    if (!mcnf_rfcap.calibrationItems.ratmap_support_WM.is_capable ||
        (mcnf_rfcap.calibrationItems.ratmap_support_WM.parameter & 0x04) == 0)
    {
        UpdateUIMsg("Don't suport C2K.", RED);
        MTRACE_ERR(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::EnterC2KGen93(): [C2K] Don't support c2k.", Common::getInstance()->ResultToString(ret_meta));
        return -1;
    }

    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::EnterC2KGen93(): [C2K] META_MMC2K_RegisterHandler_r start...");
    ret_meta = META_MMC2K_RegisterHandler_r(m_hMauiMetaHandle);
    if (ret_meta != META_SUCCESS)
    {
        UpdateUIMsg("Register C2K fail.", RED);
        MTRACE_ERR(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::EnterC2KGen93(): [C2K] META_MMC2K_RegisterHandler_r fail. %s.", Common::getInstance()->ResultToString(ret_meta));
        return 3;
    }

    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::EnterC2KGen93(): [C2K] META_MMC2K_SetTargetGenType_r start...");
    ret_meta = META_MMC2K_SetTargetGenType_r(m_hMauiMetaHandle, 1);
    if (ret_meta != META_SUCCESS)
    {
        UpdateUIMsg("Set C2K info fail.", RED);
        MTRACE_ERR(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::EnterC2KGen93(): [C2K] META_MMC2K_SetTargetGenType_r fail. %s.", Common::getInstance()->ResultToString(ret_meta));
        return 4;
    }
    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::EnterC2KGen93(): [C2K] META_MMC2K_SetTargetGenType_r end.");

    C2kMsCapability capabilityV3;
    memset(&capabilityV3, 0, sizeof(capabilityV3));
    ret_meta = META_MMC2K_QueryTargetCapability_r(m_hMauiMetaHandle, 50000, &capabilityV3);
    if (ret_meta != META_SUCCESS)
    {
        UpdateUIMsg("Query C2K fail.", RED);
        MTRACE_ERR(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::EnterC2KGen93(): [C2K] META_MMC2K_QueryTargetCapability_r fail. %s.", Common::getInstance()->ResultToString(ret_meta));
        return 3;
    }
    UpdateUIMsg("Enter C2K success, start to write mied/esn...");

    // task
    if (testCaseObj[ECodeStr[WRITE_MEID]].toBool())
    {
        if((capabilityV3.rxCalCW & 0x20) == 0x20)
        {
            //ret_i = REQ_WriteModem_NVRAM_Start(WRITE_MEID, m_sScanData.strMeid, 1);
            ret_i = WriteMEIDToFTNVRAM(WriteFlag, m_szReadBack);
        }
        else
        {
            ret_i = WriteMEID93(WriteFlag, m_szReadBack);
        }
    }
    else if (0/*g_sMetaComm.sWriteOption.bWriteEsn*/)
    {
        MTRACE_ERR(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::EnterC2KGen93(): [C2K] MT6293 don't support ESN writing.");
        UpdateUIMsg("[C2K] MT6293 don't support ESN writing!", RED);
        ret_i = META_FAILED;
    }
    if (ret_i != 0)
        return 5;

    return 0;
}

int SmartPhoneSN::WriteMEID90(bool WriteFlag, char* m_szReadBack)
{
    META_RESULT ret_meta = META_SUCCESS;
    MEID_TABLE data_meid;

    memset(&data_meid, 0, sizeof(data_meid));
    data_meid.idType = 0;
    memcpy(data_meid.meid, m_sScanData.strMeid, MEID_ARRAY_LEN);

    if(WriteFlag)
    {
        UpdateUIMsg("Start to Write MEID (>=MT6290)...");
        MTRACE(m_hDebugTrace[m_nTreadID], "[C2K] META_C2K_WriteNvram_r() : Write MEID start...");
        for (int i = 0; i < 20; i++)
        {
            ret_meta = META_C2K_WriteNvram_r(m_hMauiMetaHandle, 3000, 0, C2K_DB_MEID, &data_meid, sizeof(data_meid));
            if (ret_meta == META_SUCCESS)
                break;
        }
        if (ret_meta != META_SUCCESS)
        {
            UpdateUIMsg(QString("META_C2K_WriteNvram_r() : MetaResult = %1").arg(META_C2K_GetErrorString(m_hMauiMetaHandle)), RED);
            MTRACE_ERR(m_hDebugTrace[m_nTreadID], "[C2K] META_C2K_WriteNvram_r() : Write MEID fail, %s", META_C2K_GetErrorString(m_hMauiMetaHandle));
            return ret_meta;
        }
        MTRACE(m_hDebugTrace[m_nTreadID], "[C2K] META_C2K_WriteNvram_r() : Write MEID end.");
    }

    UpdateUIMsg("Read MEID for check start...");
    MTRACE(m_hDebugTrace[m_nTreadID], "[C2K] Read MEID for check start...");
    memset(&data_meid, 0, sizeof(data_meid));
    data_meid.idType = 0;
    ret_meta = META_C2K_ReadNvram_r(m_hMauiMetaHandle, 3000, 0, C2K_DB_MEID, &data_meid, sizeof(data_meid));
    if (ret_meta != META_SUCCESS)
    {
        UpdateUIMsg(QString("META_C2K_ReadNvram_r() : MetaResult = %1").arg(META_C2K_GetErrorString(m_hMauiMetaHandle)), RED);
        MTRACE_ERR(m_hDebugTrace[m_nTreadID], "[C2K] META_C2K_ReadNvram_r(): Read MEID fail, %s", META_C2K_GetErrorString(m_hMauiMetaHandle));
        return ret_meta;
    }

    MTRACE(m_hDebugTrace[m_nTreadID], "[C2K] Read MEID :%s", data_meid.meid);

    if(WriteFlag)
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "[C2K] Write MEID :%s", m_sScanData.strMeid);
        if (_stricmp(data_meid.meid, m_sScanData.strMeid) != 0)
        {
            UpdateUIMsg("[C2K] Checking MEID fail!", RED);
            MTRACE_ERR(m_hDebugTrace[m_nTreadID], "[C2K] Checking MEID fail!");
            return META_FAILED;
        }
        UpdateUIMsg("Checking MEID succesfully!");
        MTRACE(m_hDebugTrace[m_nTreadID], "[C2K] Checking MEID succesfully!");
    }
    else
    {
        memcpy(m_szReadBack, data_meid.meid, MEID_ARRAY_LEN);
    }

    return META_SUCCESS;
}

int SmartPhoneSN::WriteESN90(bool WriteFlag, char* m_szReadBack)
{
    META_RESULT ret_meta = META_SUCCESS;
    MEID_TABLE data_meid;

    memset(&data_meid, 0, sizeof(data_meid));
    data_meid.idType = 1;
    memcpy(data_meid.esn, m_sScanData.strEsn, ESN_ARRAY_LEN);

    if(WriteFlag)
    {
        UpdateUIMsg("Start to Write ESN (>=MT6290)...");
        MTRACE(m_hDebugTrace[m_nTreadID], "[C2K] META_C2K_WriteNvram_r() : Write ESN start...");
        for (int i = 0; i < 20; i++)
        {
            ret_meta = META_C2K_WriteNvram_r(m_hMauiMetaHandle, 3000, 0, C2K_DB_MEID, &data_meid, sizeof(data_meid));
            if (ret_meta == META_SUCCESS)
                break;
        }
        if (ret_meta != META_SUCCESS)
        {
            UpdateUIMsg(QString("META_C2K_WriteNvram_r() : Write ESN fail, %1").arg(META_C2K_GetErrorString(m_hMauiMetaHandle)), RED);
            MTRACE_ERR(m_hDebugTrace[m_nTreadID], "[C2K] META_C2K_WriteNvram_r() : Write ESN fail, %s", META_C2K_GetErrorString(m_hMauiMetaHandle));
            return ret_meta;
        }
        UpdateUIMsg("META_C2K_WriteNvram_r() : Write ESN succesful!");
        MTRACE(m_hDebugTrace[m_nTreadID], "[C2K] META_C2K_WriteNvram_r() : Write ESN succesful!");
    }

    UpdateUIMsg("Read ESN for check start...");
    MTRACE(m_hDebugTrace[m_nTreadID], "[C2K] Read ESN for check start...");
    memset(&data_meid, 0, sizeof(data_meid));
    data_meid.idType = 1;
    ret_meta = META_C2K_ReadNvram_r(m_hMauiMetaHandle, 3000, 0, C2K_DB_MEID, &data_meid, sizeof(data_meid));
    if (ret_meta != META_SUCCESS)
    {
        UpdateUIMsg(QString("META_C2K_ReadNvram_r() : Read ESN fail, %1").arg(META_C2K_GetErrorString(m_hMauiMetaHandle)), RED);
        MTRACE_ERR(m_hDebugTrace[m_nTreadID], "[C2K] META_C2K_ReadNvram_r() : Read ESN fail, %s", META_C2K_GetErrorString(m_hMauiMetaHandle));
        return ret_meta;
    }

    MTRACE(m_hDebugTrace[m_nTreadID], "[C2K] Read ESN :%s", data_meid.esn);

    if(WriteFlag)
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "[C2K] Write ESN :%s", m_sScanData.strMeid);
        if (_stricmp(data_meid.esn, m_sScanData.strEsn) != 0)
        {
            UpdateUIMsg("Checking ESN Fail!", RED);
            MTRACE_ERR(m_hDebugTrace[m_nTreadID], "[C2K] Checking ESN Fail!");
            return ret_meta;
        }
        UpdateUIMsg("Checking Esn Successfully!");
        MTRACE(m_hDebugTrace[m_nTreadID], "[C2K] Checking ESN successfully!");
    }
    else
    {
        memcpy(m_szReadBack, data_meid.esn, ESN_ARRAY_LEN);
    }

    return META_SUCCESS;
}

int SmartPhoneSN::WriteMEID93(bool WriteFlag, char* m_szReadBack)
{
    META_RESULT ret_meta = META_SUCCESS;
    C2kTestCmdGetSetMeid data_meid;

    memset(&data_meid, 0, sizeof(data_meid));
    memcpy(data_meid.meid, m_sScanData.strMeid, MEID_ARRAY_LEN);

    if(WriteFlag)
    {
        UpdateUIMsg("Start to Write MEID (>=MT6293)...");
        MTRACE(m_hDebugTrace[m_nTreadID], "[C2K] META_MMC2K_SetMeid_r() : Write MEID start...");
        for (int i = 0; i < 20; i++)
        {
            ret_meta = META_MMC2K_SetMeid_r(m_hMauiMetaHandle, 3000, &data_meid);
            if (ret_meta == META_SUCCESS)
                break;
        }
        if (ret_meta != META_SUCCESS)
        {
            UpdateUIMsg(QString("META_MMC2K_SetMeid_r() : MetaResult = %1").arg(META_C2K_GetErrorString(m_hMauiMetaHandle)), RED);
            MTRACE_ERR(m_hDebugTrace[m_nTreadID], "[C2K] META_MMC2K_SetMeid_r() : Write MEID fail, %s", META_C2K_GetErrorString(m_hMauiMetaHandle));
            return ret_meta;
        }
        MTRACE(m_hDebugTrace[m_nTreadID], "[C2K] META_MMC2K_SetMeid_r() : Write MEID end.");
    }

    UpdateUIMsg("Read MEID for check start...");
    MTRACE(m_hDebugTrace[m_nTreadID], "[C2K] Read MEID for check start...");
    memset(&data_meid, 0, sizeof(data_meid));
    ret_meta = META_MMC2K_GetMeid_r(m_hMauiMetaHandle, 3000, &data_meid);
    if (ret_meta != META_SUCCESS)
    {
        UpdateUIMsg(QString("META_MMC2K_GetMeid_r() : MetaResult = %1").arg(META_C2K_GetErrorString(m_hMauiMetaHandle)), RED);
        MTRACE_ERR(m_hDebugTrace[m_nTreadID], "[C2K] META_MMC2K_GetMeid_r(): Read MEID fail, %s", META_C2K_GetErrorString(m_hMauiMetaHandle));
        return ret_meta;
    }

    MTRACE(m_hDebugTrace[m_nTreadID], "[C2K] Read MEID :%s", data_meid.meid);

    if(WriteFlag)
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "[C2K] Write MEID :%s", m_sScanData.strMeid);
        if (_stricmp(data_meid.meid, m_sScanData.strMeid) != 0)
        {
            UpdateUIMsg("[C2K] Checking MEID fail!", RED);
            MTRACE_ERR(m_hDebugTrace[m_nTreadID], "[C2K] Checking MEID fail!");
            return META_FAILED;
        }
        UpdateUIMsg("Checking MEID succesfully!");
        MTRACE(m_hDebugTrace[m_nTreadID], "[C2K] Checking MEID succesfully!");
    }
    else
    {
        memcpy(m_szReadBack, data_meid.meid, MEID_ARRAY_LEN);
    }

    return META_SUCCESS;
}

int SmartPhoneSN::WriteMEIDToFTNVRAM(bool WriteFlag, char* m_szReadBack)
{
    META_RESULT meta_result = META_SUCCESS;

    UpdateUIMsg("Start to Write MEID (>=MT6293)...");
    MTRACE(m_hDebugTrace[m_nTreadID], "UI Input MEID = \"%s\"", m_sScanData.strMeid);
    MTRACE(m_hDebugTrace[m_nTreadID], "[C2K] WriteMEIDToFTNVRAM() : Write MEID start...");

    int C2K_META_TIMEOUT  = 5000;
    int m_meidBufSizeForNv = 0;
    char *pLID = NULL;
    FT_NVRAM_WRITE_REQ sNVRAM_WriteReq;
    FT_NVRAM_4BYTES_LID_WRITE_CNF sNVRAM_WriteCnf;
    FT_NVRAM_READ_REQ  sNVRAM_ReadReq;
    FT_NVRAM_4BYTES_LID_READ_CNF  sNVRAM_ReadCnf;
    char *m_meidBufForNv;
    MEID_struct_T m_WriterMEID;
    MEID_struct_T m_ReadMEID;

    pLID = "NVRAM_EF_C2K_MOBILE_ID_LID";
    memset(&m_WriterMEID, 0, sizeof(m_WriterMEID));
    memset(&m_ReadMEID, 0, sizeof(m_ReadMEID));
    memset(&sNVRAM_WriteReq, 0, sizeof(FT_NVRAM_WRITE_REQ));
    memset(&sNVRAM_WriteCnf, 0, sizeof(FT_NVRAM_4BYTES_LID_WRITE_CNF));
    memset(&sNVRAM_ReadReq, 0, sizeof(FT_NVRAM_READ_REQ));
    memset(&sNVRAM_ReadCnf, 0, sizeof(FT_NVRAM_4BYTES_LID_READ_CNF));

    memcpy(m_WriterMEID.meid, m_sScanData.strMeid, MEID_ARRAY_LEN);

    MTRACE (m_hDebugTrace[m_nTreadID], "META_NVRAM_GetRecLen_r(): Start to get nvram struct size via LID = \"%s\"...", pLID);
    meta_result = META_NVRAM_GetRecLen_r(m_hMauiMetaHandle, pLID, &m_meidBufSizeForNv);
    if (meta_result != META_SUCCESS)
    {
        UpdateUIMsg(QString("META_NVRAM_GetRecLen_r() : MetaResult = %1").arg(META_C2K_GetErrorString(m_hMauiMetaHandle)), RED);
        MTRACE_ERR(m_hDebugTrace[m_nTreadID], "[C2K] META_NVRAM_GetRecLen_r() : Write MEID fail, %s", META_C2K_GetErrorString(m_hMauiMetaHandle));
        goto Err;
    }

    m_meidBufForNv = new char[m_meidBufSizeForNv];
    memset(m_meidBufForNv, 0, m_meidBufSizeForNv);

    if(WriteFlag)
    {
        m_WriterMEID.idType = 2;
        meta_result = META_NVRAM_Compose_MEID_r(m_hMauiMetaHandle, &m_WriterMEID, m_meidBufForNv, m_meidBufSizeForNv);

        sNVRAM_WriteReq.LID = pLID;
        sNVRAM_WriteReq.RID = 1;
        sNVRAM_WriteReq.len = m_meidBufSizeForNv;
        sNVRAM_WriteReq.buf = (unsigned char *)m_meidBufForNv;

        meta_result = META_NVRAM_4Bytes_LID_Write_Ex_r(m_hMauiMetaHandle, C2K_META_TIMEOUT, &sNVRAM_WriteReq, &sNVRAM_WriteCnf);
        if (meta_result != META_SUCCESS)
        {
            UpdateUIMsg(QString("META_NVRAM_4Bytes_LID_Write_Ex_r() : MetaResult = %1").arg(META_C2K_GetErrorString(m_hMauiMetaHandle)), RED);
            MTRACE_ERR(m_hDebugTrace[m_nTreadID], "[C2K] META_NVRAM_4Bytes_LID_Write_Ex_r(): Read MEID fail, %s", META_C2K_GetErrorString(m_hMauiMetaHandle));
            goto Err;
        }
        MTRACE(m_hDebugTrace[m_nTreadID], "[C2K] META_NVRAM_4Bytes_LID_Write_Ex_r() : Write MEID end.");
    }

    UpdateUIMsg("Read MEID for check start...");
    MTRACE(m_hDebugTrace[m_nTreadID], "[C2K] Read MEID for check start...");

    memset(m_meidBufForNv, 0, m_meidBufSizeForNv);

    sNVRAM_ReadReq.LID = pLID;
    sNVRAM_ReadReq.RID = 1;

    sNVRAM_ReadCnf.len = m_meidBufSizeForNv;
    sNVRAM_ReadCnf.buf = (unsigned char *)m_meidBufForNv;

    meta_result = META_NVRAM_4Bytes_LID_Read_Ex_r(m_hMauiMetaHandle, C2K_META_TIMEOUT, &sNVRAM_ReadReq, &sNVRAM_ReadCnf);
    if (meta_result != META_SUCCESS)
    {
        UpdateUIMsg(QString("META_NVRAM_4Bytes_LID_Read_Ex_r() : MetaResult = %1").arg(META_C2K_GetErrorString(m_hMauiMetaHandle)), RED);
        MTRACE_ERR(m_hDebugTrace[m_nTreadID], "[C2K] META_NVRAM_4Bytes_LID_Read_Ex_r(): Read MEID fail, %s", META_C2K_GetErrorString(m_hMauiMetaHandle));
        goto Err;
    }
    MTRACE(m_hDebugTrace[m_nTreadID], "[C2K] META_NVRAM_4Bytes_LID_Read_Ex_r() : Read MEID end.");

    meta_result = META_NVRAM_Decompose_MEID_r(m_hMauiMetaHandle, &m_ReadMEID, m_meidBufForNv, m_meidBufSizeForNv);

    if(WriteFlag)
        MTRACE(m_hDebugTrace[m_nTreadID], "[C2K] Write MEID :%s", m_sScanData.strMeid);

    MTRACE(m_hDebugTrace[m_nTreadID], "[C2K] Read MEID :%s", m_ReadMEID.meid);
    if (_stricmp(m_ReadMEID.meid, m_sScanData.strMeid) != 0)
    {
        UpdateUIMsg("[C2K] Checking MEID fail!", RED);
        MTRACE_ERR(m_hDebugTrace[m_nTreadID], "[C2K] Checking MEID fail!");
        return META_FAILED;
    }

    if(!WriteFlag)
        memcpy(m_szReadBack, m_ReadMEID.meid, MEID_ARRAY_LEN);

    UpdateUIMsg("Checking MEID succesfully!");
    MTRACE(m_hDebugTrace[m_nTreadID], "[C2K] Checking MEID succesfully!");

    if (m_meidBufForNv != NULL)
    {
        delete[] m_meidBufForNv;
        m_meidBufForNv = NULL;
    }

    return META_SUCCESS;

Err:
    if (m_meidBufForNv != NULL)
    {
        delete[] m_meidBufForNv;
        m_meidBufForNv = NULL;
    }
    MTRACE_ERR (m_hDebugTrace[m_nTreadID], " Write MEID Fail");

    return meta_result;

}

META_RESULT SmartPhoneSN::REQ_WriteAP_NVRAM_Start(WriteData_Type_e dataType, char *pInData, unsigned short iRID)
{
    if (!pInData)
    {
        return META_INVALID_ARGUMENTS;
    }
    m_bWriteNvram = true;

    int iBufLen = 0;
    char *pLID = NULL;
    pConductDataFunc pDataFunc = NULL;

    switch (dataType)
    {
    case WRITE_BT:
        iBufLen = 6;
        pLID = "AP_CFG_RDEB_FILE_BT_ADDR_LID";
        pDataFunc = &SmartPhoneSN::ConductBTAddrData;
        break;

    case WRITE_WIFI:
        iBufLen = 6;
        pLID = "AP_CFG_RDEB_FILE_WIFI_LID";
        pDataFunc = &SmartPhoneSN::ConductWifiAddrData;
        break;

    case WRITE_ETHERNET_MAC:
        iBufLen = 6;
        pLID = "AP_CFG_RDEB_FILE_ETHERNET_LID";
        pDataFunc = &SmartPhoneSN::ConductEthernetMacAddrData;
        break;

    default:
        return META_INVALID_ARGUMENTS;
    }

    const int MAX_DATA_LENGTH = 6;
    int iMetaTimeout = 5000;

    AP_FT_NVRAM_WRITE_REQ sNVRAM_WriteReq;
    AP_FT_NVRAM_READ_REQ sNVRAM_ReadReq;
    AP_FT_NVRAM_READ_CNF sNVRAM_ReadCnf;
    META_RESULT meta_result;
    char pWriteData[MAX_DATA_LENGTH] = {0};
    char pReadData[MAX_DATA_LENGTH] = {0};
    int iWriteBufSize = 0;
    unsigned long wifiChipVersion = 0;
    char *pFuncName = NULL;
    int rs = 0;

    memset(&sNVRAM_WriteReq, 0, sizeof(AP_FT_NVRAM_WRITE_REQ));
    memset(&sNVRAM_ReadReq, 0, sizeof(AP_FT_NVRAM_READ_REQ));
    memset(&sNVRAM_ReadCnf, 0, sizeof(AP_FT_NVRAM_READ_CNF));

    MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::SP_META_NVRAM_GetRecLen(): Start to get nvram struct size via LID = \"%s\"...", pLID);
    meta_result = SP_META_NVRAM_GetRecLen(pLID, &iWriteBufSize);
    if (  META_SUCCESS != meta_result)
    {
        MTRACE_ERR (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::SP_META_NVRAM_GetRecLen(): Get nvram struct size fail, MetaResult = %s", Common::getInstance()->ResultToString_SP(meta_result));
        return meta_result;
    }
    else
    {
        MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::SP_META_NVRAM_GetRecLen(): Get nvram struct size = %d successfully!", iWriteBufSize);
        if ( NULL != sNVRAM_ReadCnf.buf )
        {
            free ( sNVRAM_ReadCnf.buf );
            sNVRAM_ReadCnf.buf = NULL;
        }

        sNVRAM_ReadReq.LID = pLID;
        sNVRAM_ReadReq.RID = iRID;
        sNVRAM_ReadCnf.len = iWriteBufSize;
        sNVRAM_ReadCnf.buf = (unsigned char*) malloc (iWriteBufSize * sizeof(unsigned char));
        if (NULL == sNVRAM_ReadCnf.buf)
        {
            MTRACE_ERR (m_hDebugTrace[m_nTreadID], "Malloc heap memory cause fail!");
            return  META_FAILED;
        }
    }


    MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_ReadFromAPNVRAM(): Start to read nvram data...");
    meta_result =  REQ_ReadFromAPNVRAM (&sNVRAM_ReadReq, &sNVRAM_ReadCnf);
    if (meta_result != META_SUCCESS )
    {
        pFuncName = "SmartPhoneSN::REQ_ReadFromAPNVRAM()";
        goto Err;
    }


    if (strcmp(pLID, "AP_CFG_RDEB_FILE_WIFI_LID") == 0)
    {
        MTRACE (m_hDebugTrace[m_nTreadID], "SNBase::ConductWifiAddrData(): Start Conduct wifi nvram data...!");
        pFuncName = "SNBase::ConductWifiAddrData()";
        rs = (this->*pDataFunc)((char*)sNVRAM_ReadCnf.buf + 0x4, iRID, pInData, iBufLen);
        for (int i = 0; i < iBufLen; i++)
        {
            pWriteData[i] = sNVRAM_ReadCnf.buf[0x4 + i];
        }
    }
    else if (strcmp(pLID, "AP_CFG_RDEB_FILE_BT_ADDR_LID") == 0)
    {
        MTRACE (m_hDebugTrace[m_nTreadID], "SNBase::ConductBTAddrData(): Start Conduct BT nvram data...!");
        pFuncName = "SNBase::ConductBTAddrData()";
        rs = (this->*pDataFunc)((char*)sNVRAM_ReadCnf.buf, iRID, pInData, iBufLen);
        for (int i = 0; i < iBufLen; i++)
        {
            pWriteData[i] = sNVRAM_ReadCnf.buf[i];
        }
    }
    else if (strcmp(pLID, "AP_CFG_RDEB_FILE_ETHERNET_LID") == 0)
    {
        MTRACE (m_hDebugTrace[m_nTreadID], "SNBase::ConductEthernetMacAddrData(): Start Conduct Ethernet Mac nvram data...!");
        pFuncName = "SNBase::ConductEthernetMacAddrData()";
        rs = (this->*pDataFunc)((char*)sNVRAM_ReadCnf.buf, iRID, pInData, iBufLen);
        for (int i = 0; i < iBufLen; i++)
        {
            pWriteData[i] = sNVRAM_ReadCnf.buf[i];
        }
    }

    meta_result = (META_RESULT)rs;
    if (meta_result !=  META_SUCCESS)
    {
        goto Err;
    }
    MTRACE (m_hDebugTrace[m_nTreadID], "%s: Conduct nvram data successfully!", pFuncName);

    sNVRAM_WriteReq.LID = pLID;
    sNVRAM_WriteReq.RID = iRID;
    sNVRAM_WriteReq.len = iWriteBufSize;
    sNVRAM_WriteReq.buf = sNVRAM_ReadCnf.buf;
    m_sNVRAM_OPID = iRID;

    MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_WriteToAPNVRAM(): Start to write nvram data...");
    meta_result =  REQ_WriteToAPNVRAM(sNVRAM_WriteReq);
    if (meta_result != META_SUCCESS )
    {
        pFuncName = "SmartPhoneSN::REQ_WriteToAPNVRAM()";
        goto Err;
    }
    MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_WriteToAPNVRAM(): Write nvram data successfully!");

    MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_WriteToAPNVRAM(): Read nvram data for check start...");
    memset(sNVRAM_ReadCnf.buf, 0 , sNVRAM_ReadCnf.len);
    meta_result = REQ_ReadFromAPNVRAM (&sNVRAM_ReadReq, &sNVRAM_ReadCnf);
    if (meta_result != META_SUCCESS )
    {
        pFuncName = "SmartPhoneSN::REQ_ReadFromAPNVRAM()";
        goto Err;
    }
    else
    {
        char *pDataType = NULL;
        memset(pReadData, 0, sizeof(char)*MAX_DATA_LENGTH);
        if (strcmp(pLID, "AP_CFG_RDEB_FILE_WIFI_LID") == 0)
        {
            pDataType = "Wifi";
            //Wifi address offset is 0x4
            for (int i = 0; i < iBufLen; i++)
            {
                pReadData[i] = sNVRAM_ReadCnf.buf[0x4 + i];
            }
        }
        else if (strcmp(pLID, "AP_CFG_RDEB_FILE_BT_ADDR_LID") == 0)
        {
            pDataType = "BT";
            for (int i = 0; i < iBufLen; i++)
            {
                pReadData[i] = sNVRAM_ReadCnf.buf[i];
            }
        }
        else if (strcmp(pLID, "AP_CFG_RDEB_FILE_ETHERNET_LID") == 0)
        {
            pDataType = "Ethernet_Mac";
            for (int i = 0; i < iBufLen; i++)
            {
                pReadData[i] = sNVRAM_ReadCnf.buf[i];
            }
        }

        char tmpReadData[13] = {0};
        m_bWriteNvram = false;
        (this->*pDataFunc)(tmpReadData, iRID, pReadData, iBufLen);

        if (strncmp(pWriteData, pReadData, iBufLen) != 0)
        {
            MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN: Write_%s[%s]", pDataType, pInData);
            MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN: Read_%s[%s]", pDataType, tmpReadData);
            MTRACE_ERR (m_hDebugTrace[m_nTreadID], "SmartPhoneSN: Check nvram data FAIL!!");
            meta_result = META_FAILED;
        }
        else
        {
            MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN: Write_%s[%s]", pDataType, pInData);
            MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN: Read_%s[%s]", pDataType, tmpReadData);
            MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN: Check nvram data PASS!!");
        }
    }
    MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_WriteToAPNVRAM(): Read nvram data for check end...");

    if (sNVRAM_ReadCnf.buf != NULL)
    {
        free (sNVRAM_ReadCnf.buf);
        sNVRAM_ReadCnf.buf = NULL;
    }

    return meta_result;

Err:
    if (sNVRAM_ReadCnf.buf != NULL)
    {
        free (sNVRAM_ReadCnf.buf);
        sNVRAM_ReadCnf.buf = NULL;
    }

    MTRACE_ERR (m_hDebugTrace[m_nTreadID], "%s: fail! MetaResult = %s", pFuncName, Common::getInstance()->ResultToString_SP(meta_result));
    return meta_result;
}

META_RESULT SmartPhoneSN::REQ_WriteToAPNVRAM(AP_FT_NVRAM_WRITE_REQ sNVRAM_WriteReq)
{
    META_RESULT MetaResult;
    DWORD wait_result;
    short iNVRAM_OPID;

    m_hWriteToNVRAMEvent = CreateEvent(NULL, true, false, NULL);
    ResetEvent(m_hWriteToNVRAMEvent);

    MetaResult = SP_META_NVRAM_Write_r(m_hSPMetaHandle,
                                       &sNVRAM_WriteReq,
                                       CNF_SPWriteToNVRAM,
                                       &iNVRAM_OPID,
                                       (void*)&m_hWriteToNVRAMEvent);

    wait_result = WaitForSingleObject(m_hWriteToNVRAMEvent, 15000);

    if (WAIT_TIMEOUT == wait_result)
    {
        return META_TIMEOUT;
    }
    else if (WAIT_OBJECT_0)
    {
        CloseHandle (m_hWriteToNVRAMEvent);
    }

    if ( MetaResult != META_SUCCESS )
    {
        return MetaResult;
    }

    return META_SUCCESS;
}

META_RESULT SmartPhoneSN::REQ_WriteAP_PRODINFO_Start()
{
    META_RESULT meta_result = META_SUCCESS;
    m_bWriteNvram = true;
    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_WriteAP_PRODINFO_Start(): Write Barcode/Imei/Bt/Wifi/Serial.No/NetCode to prod_info start...");

    int iIMEINums = Common::getInstance()->getIMEINum();
    int iMetaTimeout = 5000;
    int iWriteBufSize = 0;
    char *pLID = "AP_CFG_REEB_PRODUCT_INFO_LID";
    char *pFuncName = NULL;
    unsigned char *pWriteData = NULL;

    AP_FT_NVRAM_WRITE_REQ sNVRAM_WriteReq;
    AP_FT_NVRAM_READ_REQ sNVRAM_ReadReq;
    AP_FT_NVRAM_READ_CNF sNVRAM_ReadCnf;

    // Check
    if (testCaseObj[ECodeStr[WRITE_BARCODE]].toBool() && Common::getInstance()->getItemEnable(InputCodeItem::staticType()))
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_WriteAP_PRODINFO_Start(): Write Barcode = %s", m_sScanData.strBarcode);
        if (m_sScanData.strBarcode == NULL || strlen(m_sScanData.strBarcode) == 0)
        {
            return META_INVALID_ARGUMENTS;
        }
    }
    if (testCaseObj[ECodeStr[WRITE_IMEI]].toBool() && Common::getInstance()->getItemEnable(InputCodeItem::staticType()))
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_WriteAP_PRODINFO_Start(): Write IMEI1 = %s", m_sScanData.strIMEI[0]);
        if (m_sScanData.strIMEI[0] == NULL || strlen(m_sScanData.strIMEI[0]) == 0)
        {
            return META_INVALID_ARGUMENTS;
        }
    }
    if (testCaseObj[ECodeStr[WRITE_IMEI2]].toBool() && Common::getInstance()->getItemEnable(InputCodeItem::staticType()))
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_WriteAP_PRODINFO_Start(): Write IMEI2 = %s", m_sScanData.strIMEI[1]);
        if (m_sScanData.strIMEI[1] == NULL || strlen(m_sScanData.strIMEI[1]) == 0)
        {
            return META_INVALID_ARGUMENTS;
        }
    }
    if (testCaseObj[ECodeStr[WRITE_BT]].toBool() && Common::getInstance()->getItemEnable(InputCodeItem::staticType()))
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_WriteAP_PRODINFO_Start(): Write BTAddr = %s", m_sScanData.strBTAddr);
        if (m_sScanData.strBTAddr == NULL || strlen(m_sScanData.strBTAddr) == 0)
        {
            return META_INVALID_ARGUMENTS;
        }
    }
    if (testCaseObj[ECodeStr[WRITE_WIFI]].toBool() && Common::getInstance()->getItemEnable(InputCodeItem::staticType()))
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_WriteAP_PRODINFO_Start(): Write WifiAddr = %s", m_sScanData.strWifiAddr);
        if (m_sScanData.strWifiAddr == NULL || strlen(m_sScanData.strWifiAddr) == 0)
        {
            return META_INVALID_ARGUMENTS;
        }
    }
    if (testCaseObj[ECodeStr[WRITE_SERIALNO]].toBool() && Common::getInstance()->getItemEnable(InputCodeItem::staticType()))
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_WriteAP_PRODINFO_Start(): Write SerialNo = %s", m_sScanData.strSerialNo);
        if (m_sScanData.strSerialNo == NULL || strlen(m_sScanData.strSerialNo) == 0)
        {
            return META_INVALID_ARGUMENTS;
        }
    }
    if (testCaseObj[ECodeStr[WRITE_NETCODE]].toBool() && Common::getInstance()->getItemEnable(InputCodeItem::staticType()))
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_WriteAP_PRODINFO_Start(): Write NetCode = %s", m_sScanData.strNetCode);
        if (m_sScanData.strNetCode == NULL || strlen(m_sScanData.strNetCode) == 0)
        {
            return META_INVALID_ARGUMENTS;
        }
    }
    if (YDtestCaseObj[EYDCodeStr[WRITE_SCRILSF]].toBool() && Common::getInstance()->getItemEnable(InputYDCodesItem::staticType()))
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_WriteAP_PRODINFO_Start(): Write ScrilSf = %s", m_sScanData.strScrilSf);
        if (m_sScanData.strScrilSf == NULL || strlen(m_sScanData.strScrilSf) == 0)
        {
            return META_INVALID_ARGUMENTS;
        }
    }
    /*if (g_sMetaComm.sWriteOption.bWriteDJSN1)
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_WriteAP_PRODINFO_Start(): Write DJSN1 = %s", m_sScanData.strDJSN1);
        if (m_sScanData.strDJSN1 == NULL || strlen(m_sScanData.strDJSN1) == 0)
        {
            return META_INVALID_ARGUMENTS;
        }
    }
    if (g_sMetaComm.sWriteOption.bWriteDJSN2)
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_WriteAP_PRODINFO_Start(): Write DJSN2 = %s", m_sScanData.strDJSN2);
        if (m_sScanData.strDJSN2 == NULL || strlen(m_sScanData.strDJSN2) == 0)
        {
            return META_INVALID_ARGUMENTS;
        }
    }
    if (g_sMetaComm.sWriteOption.bWriteProductSn)
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_WriteAP_PRODINFO_Start(): Write LDSN = %s", m_sScanData.strLDSN);
        if (m_sScanData.strLDSN == NULL || strlen(m_sScanData.strLDSN) == 0)
        {
            return META_INVALID_ARGUMENTS;
        }
    }

    if(g_sMetaComm.sWriteAI.bWriteASn)
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_WriteAP_PRODINFO_Start(): Write ACERSN = %s", m_sScanData.strACERSN);
        if (m_sScanData.strACERSN == NULL || strlen(m_sScanData.strACERSN) == 0)
        {
            return META_INVALID_ARGUMENTS;
        }
    }

    if(g_sMetaComm.sWriteAI.bWriteASku)
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_WriteAP_PRODINFO_Start(): Write ACERSKU = %s", g_sMetaComm.sWriteAI.strASku);
        if (g_sMetaComm.sWriteAI.strASku == NULL || strlen(g_sMetaComm.sWriteAI.strASku) == 0)
        {
            return META_INVALID_ARGUMENTS;
        }
    }

    if(g_sMetaComm.sWriteAI.bWriteAModel)
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_WriteAP_PRODINFO_Start(): Write ACERMODEL = %s", g_sMetaComm.sWriteAI.strAModel);
        if (g_sMetaComm.sWriteAI.strAModel == NULL || strlen(g_sMetaComm.sWriteAI.strAModel) == 0)
        {
            return META_INVALID_ARGUMENTS;
        }
    }

    if(g_sMetaComm.sWriteAI.bWriteADN)
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_WriteAP_PRODINFO_Start(): Write ACERDEVICENAME = %s", g_sMetaComm.sWriteAI.strADN);
        if (g_sMetaComm.sWriteAI.strADN == NULL || strlen(g_sMetaComm.sWriteAI.strADN) == 0)
        {
            return META_INVALID_ARGUMENTS;
        }
    }*/


    memset(&sNVRAM_WriteReq, 0, sizeof(AP_FT_NVRAM_WRITE_REQ));
    memset(&sNVRAM_ReadReq, 0, sizeof(AP_FT_NVRAM_READ_REQ));
    memset(&sNVRAM_ReadCnf, 0, sizeof(AP_FT_NVRAM_READ_CNF));

    MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::SP_META_NVRAM_GetRecLen(): Start to get nvram struct size via LID = \"%s\"...", pLID);
    meta_result = SP_META_NVRAM_GetRecLen(pLID, &iWriteBufSize);
    if (  META_SUCCESS != meta_result)
    {
        MTRACE_ERR (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::SP_META_NVRAM_GetRecLen(): Get nvram struct size fail, MetaResult = %s", Common::getInstance()->ResultToString_SP(meta_result));
        return meta_result;
    }
    MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::SP_META_NVRAM_GetRecLen(): Get nvram struct size = %d successfully!", iWriteBufSize);
    if ( NULL != sNVRAM_ReadCnf.buf )
    {
        free(sNVRAM_ReadCnf.buf);
        sNVRAM_ReadCnf.buf = NULL;
    }

    if (NULL != pWriteData)
    {
        free(pWriteData);
        pWriteData = NULL;
    }

    sNVRAM_ReadReq.LID = pLID;
    sNVRAM_ReadReq.RID = 1;
    sNVRAM_ReadCnf.len = iWriteBufSize;
    sNVRAM_ReadCnf.buf = (unsigned char*) malloc(iWriteBufSize * sizeof(unsigned char));
    pWriteData = (unsigned char*) malloc(iWriteBufSize * sizeof(unsigned char));
    if (NULL == sNVRAM_ReadCnf.buf || NULL == pWriteData)
    {
        MTRACE_ERR (m_hDebugTrace[m_nTreadID], "Malloc heap memory cause fail!");
        return  META_FAILED;
    }

    MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_ReadFromAPNVRAM(): Start to read nvram data...");
    meta_result =  REQ_ReadFromAPNVRAM (&sNVRAM_ReadReq, &sNVRAM_ReadCnf);
    if (meta_result != META_SUCCESS )
    {
        pFuncName = "SmartPhoneSN::REQ_ReadFromAPNVRAM()";
        goto Err;
    }

    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::ConductProdInfoData(): Conduct Prod_Info nvram data start...");
    meta_result = ConductProdInfoData(sNVRAM_ReadCnf.buf, iWriteBufSize);
    if (meta_result != META_SUCCESS)
    {
        pFuncName = "SmartPhoneSN::ConductProdInfoData()";
        goto Err;
    }
    else
    {
        memcpy(pWriteData, sNVRAM_ReadCnf.buf, iWriteBufSize);
        MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::ConductProdInfoData(): Conduct Prod_Info nvram data successfully!!");
    }

    sNVRAM_WriteReq.LID = pLID;
    sNVRAM_WriteReq.RID = 1;
    sNVRAM_WriteReq.len = iWriteBufSize;
    sNVRAM_WriteReq.buf = pWriteData;
    m_sNVRAM_OPID = 1;

    MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_WriteToAPNVRAM(): Start to write nvram data...");
    meta_result =  REQ_WriteToAPNVRAM(sNVRAM_WriteReq);
    if (meta_result != META_SUCCESS )
    {
        pFuncName = "SmartPhoneSN::REQ_WriteToAPNVRAM()";
        goto Err;
    }
    MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_WriteToAPNVRAM(): Write nvram data successfully!");

    MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_WriteToAPNVRAM(): Read nvram data for check start...");
    memset(sNVRAM_ReadCnf.buf, 0 , sNVRAM_ReadCnf.len);
    meta_result = REQ_ReadFromAPNVRAM (&sNVRAM_ReadReq, &sNVRAM_ReadCnf);
    if (meta_result != META_SUCCESS )
    {
        pFuncName = "SmartPhoneSN::REQ_ReadFromAPNVRAM()";
        goto Err;
    }
    else
    {
        if (memcmp(sNVRAM_ReadCnf.buf, pWriteData, sNVRAM_ReadCnf.len) != 0)
        {
            MTRACE_ERR (m_hDebugTrace[m_nTreadID], "SmartPhoneSN: Check prod_info data FAIL!!");
            meta_result = META_FAILED;
        }
        else
        {
            MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN: Check prod_info data PASS!!");
        }
    }
    MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_WriteToAPNVRAM(): Read nvram data for check end...");

    if (sNVRAM_ReadCnf.buf != NULL)
    {
        free (sNVRAM_ReadCnf.buf);
        sNVRAM_ReadCnf.buf = NULL;
    }

    if (pWriteData != NULL)
    {
        free (pWriteData);
        pWriteData = NULL;
    }

    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_WriteAP_PRODINFO_Start(): Write Barcode/Imei/Bt/Wifi/Serial.No to prod_info end...");
    return meta_result;

Err:
    if (sNVRAM_ReadCnf.buf != NULL)
    {
        free (sNVRAM_ReadCnf.buf);
        sNVRAM_ReadCnf.buf = NULL;
    }

    if (pWriteData != NULL)
    {
        free (pWriteData);
        pWriteData = NULL;
    }

    MTRACE_ERR (m_hDebugTrace[m_nTreadID], "%s: fail! MetaResult = %s", pFuncName, Common::getInstance()->ResultToString_SP(meta_result));
    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::REQ_WriteAP_PRODINFO_Start(): Write Barcode/Imei/Bt/Wifi/Serial.No to prod_info end...");
    return meta_result;
}

META_RESULT SmartPhoneSN::ConductProdInfoData(unsigned char *pBuf, int nBufLen)
{
    typedef struct _sn_write_flag
    {
        unsigned char     total_pass: 1;
        unsigned char     barcode: 1;
        unsigned char     IMEI: 1;
        unsigned char     bt: 1;
        unsigned char     wifi: 1;
        unsigned char     MEID: 1;
        unsigned int      reserved: 10;
        //unsigned char       sn_write_flag[2];
    } sn_write_flag;

    PRODUCT_INFO * pstProInfo = (PRODUCT_INFO *)pBuf;
    sn_write_flag * pstSnFlag = (sn_write_flag *) & (pstProInfo->mtk_test_flag.sn_write_flag);
    memset(pstSnFlag, 0, sizeof(sn_write_flag));

    if (testCaseObj[ECodeStr[WRITE_BARCODE]].toBool() && Common::getInstance()->getItemEnable(InputCodeItem::staticType()))
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "Prod_Info->Barcode = [%s]", m_sScanData.strBarcode);
        memcpy(&pstProInfo->barcode, m_sScanData.strBarcode, BARCODE_MAX_LENGTH);
        pstSnFlag->barcode = 1;
    }

    if (testCaseObj[ECodeStr[WRITE_IMEI]].toBool() && Common::getInstance()->getItemEnable(InputCodeItem::staticType()))
    {
        int idIMEI = 0, idByte = 0;
        unsigned char tmpCh;

        MTRACE(m_hDebugTrace[m_nTreadID], "Prod_Info->IMEI[%d] = [%s]", idIMEI, m_sScanData.strIMEI[idIMEI]);
        for (int idByte = 0; idByte < 7; idByte++)
        {
            // High 4Bit
            tmpCh = m_sScanData.strIMEI[idIMEI][idByte * 2 + 1] - '0';
            tmpCh = ((tmpCh << 4) & 0xf0u);

            // Low 4Bit
            tmpCh += m_sScanData.strIMEI[idIMEI][idByte * 2] - '0';
            pstProInfo->IMEI[idIMEI].imei[idByte] = tmpCh;
        }

        // Checksum Byte : 0xf-
        tmpCh = m_sScanData.strIMEI[idIMEI][14] - '0';
        tmpCh |= 0xf0u;
        pstProInfo->IMEI[idIMEI].imei[7] = tmpCh;

        pstSnFlag->IMEI = 1;
    }

    if (testCaseObj[ECodeStr[WRITE_IMEI2]].toBool() && Common::getInstance()->getItemEnable(InputCodeItem::staticType()))
    {
        int idIMEI = 1, idByte = 0;
        unsigned char tmpCh;
        MTRACE(m_hDebugTrace[m_nTreadID], "Prod_Info->IMEI[%d] = [%s]", idIMEI, m_sScanData.strIMEI[idIMEI]);
        for (int idByte = 0; idByte < 7; idByte++)
        {
            // High 4Bit
            tmpCh = m_sScanData.strIMEI[idIMEI][idByte * 2 + 1] - '0';
            tmpCh = ((tmpCh << 4) & 0xf0u);

            // Low 4Bit
            tmpCh += m_sScanData.strIMEI[idIMEI][idByte * 2] - '0';
            pstProInfo->IMEI[idIMEI].imei[idByte] = tmpCh;
        }

        // Checksum Byte : 0xf-
        tmpCh = m_sScanData.strIMEI[idIMEI][14] - '0';
        tmpCh |= 0xf0u;
        pstProInfo->IMEI[idIMEI].imei[7] = tmpCh;

        pstSnFlag->IMEI = 1;
    }


    if (testCaseObj[ECodeStr[WRITE_BT]].toBool() && Common::getInstance()->getItemEnable(InputCodeItem::staticType()))
    {
        char * pBtAddr = m_sScanData.strBTAddr;
        unsigned char tmpCh;

        MTRACE(m_hDebugTrace[m_nTreadID], "Prod_Info->BtAddress = [%s]", m_sScanData.strBTAddr);
        for (int i = 0; i < 6; i++)
        {
            // High 4Bit
            if ('0' <= pBtAddr[i * 2] && pBtAddr[i * 2] <= '9')
            {
                tmpCh = pBtAddr[i * 2] - '0';
                tmpCh = ((tmpCh << 4) & 0xf0u);
            }
            else if ('A' <= pBtAddr[i * 2] && pBtAddr[i * 2] <= 'F')
            {
                tmpCh = pBtAddr[i * 2] - 'A' + 10;
                tmpCh = ((tmpCh << 4) & 0xf0u);
            }

            // Low 4Bit
            if ('0' <= pBtAddr[(i * 2 + 1)] && pBtAddr[(i * 2 + 1)] <= '9')
            {
                tmpCh += pBtAddr[(i * 2 + 1)] - '0';
            }
            else if ('A' <= pBtAddr[(i * 2 + 1)] && pBtAddr[(i * 2 + 1)] <= 'F')
            {
                tmpCh += pBtAddr[(i * 2 + 1)] - 'A' + 10;
            }

            pstProInfo->target_info.BTAddr[i] = tmpCh;
        }

        pstSnFlag->bt = 1;
    }

    if (testCaseObj[ECodeStr[WRITE_WIFI]].toBool() && Common::getInstance()->getItemEnable(InputCodeItem::staticType()))
    {
        char * pWiFiAddr = m_sScanData.strWifiAddr;
        unsigned char tmpCh;

        MTRACE(m_hDebugTrace[m_nTreadID], "Prod_Info->WiFiAddress = [%s]", m_sScanData.strWifiAddr);
        for (int i = 0; i < 6; i++)
        {
            // High 4Bit
            if ('0' <= pWiFiAddr[i * 2] && pWiFiAddr[i * 2] <= '9')
            {
                tmpCh = pWiFiAddr[i * 2] - '0';
                tmpCh = ((tmpCh << 4) & 0xf0u);
            }
            else if ('A' <= pWiFiAddr[i * 2] && pWiFiAddr[i * 2] <= 'F')
            {
                tmpCh = pWiFiAddr[i * 2] - 'A' + 10;
                tmpCh = ((tmpCh << 4) & 0xf0u);
            }

            // Low 4Bit
            if ('0' <= pWiFiAddr[(i * 2 + 1)] && pWiFiAddr[(i * 2 + 1)] <= '9')
            {
                tmpCh += pWiFiAddr[(i * 2 + 1)] - '0';
            }
            else if ('A' <= pWiFiAddr[(i * 2 + 1)] && pWiFiAddr[(i * 2 + 1)] <= 'F')
            {
                tmpCh += pWiFiAddr[(i * 2 + 1)] - 'A' + 10;
            }

            pstProInfo->target_info.WifiAddr[i] = tmpCh;
        }

        pstSnFlag->wifi = 1;
    }

    if (testCaseObj[ECodeStr[WRITE_SERIALNO]].toBool() && Common::getInstance()->getItemEnable(InputCodeItem::staticType()))
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "Prod_Info->ADBSeriaNo = [%s]", m_sScanData.strSerialNo);
        memcpy((char *)pstProInfo->target_info.ADBSeriaNo, m_sScanData.strSerialNo, SERIAL_NO_BUF_LEN);
        pstProInfo->target_info.ADBSeriaNo[SERIAL_NO_LEN] = '\0';
    }

    if (testCaseObj[ECodeStr[WRITE_NETCODE]].toBool() && Common::getInstance()->getItemEnable(InputCodeItem::staticType()))
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "Prod_Info->reserved[65~96] = [%s]", m_sScanData.strNetCode);
        char temp[1024] = {0};
        memcpy(temp, (char *)pstProInfo, sizeof(PRODUCT_INFO));
        for(int i = 0; i< strlen(m_sScanData.strNetCode); i++)
        {
            temp[531+i] = m_sScanData.strNetCode[i];
        }
        memcpy((char *)pstProInfo, temp, sizeof(PRODUCT_INFO));
    }

    if (YDtestCaseObj[EYDCodeStr[WRITE_SCRILSF]].toBool() && Common::getInstance()->getItemEnable(InputYDCodesItem::staticType()))
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "Prod_Info->reserved[0~11] = [%s]", m_sScanData.strScrilSf);
        char temp[1024] = {0};
        memcpy(temp, (char *)pstProInfo, sizeof(PRODUCT_INFO));
        memcpy(&temp[466], m_sScanData.strScrilSf, SCRILSF_LENGTH);
        memcpy((char *)pstProInfo, temp, sizeof(PRODUCT_INFO));
    }

    /*if (g_sMetaComm.sWriteOption.bWriteProductSn)
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "Prod_Info->reserved[97~129] = [%s]", m_sScanData.strLDSN);
        char temp[1024] = {0};
        memcpy(temp, (char *)pstProInfo, sizeof(PRODUCT_INFO));
        for(int i = 0; i< strlen(m_sScanData.strLDSN); i++)
        {
            temp[564+i] = m_sScanData.strLDSN[i];
        }
        memcpy((char *)pstProInfo, temp, sizeof(PRODUCT_INFO));
    }

    if (g_sMetaComm.sWriteAI.bWriteASn)
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "Prod_Info->reserved[466~487] = [%s]", m_sScanData.strACERSN);

        // 创建临时缓冲区并初始化清零
        char temp[sizeof(PRODUCT_INFO)] = {0};
        memcpy(temp, (char *)pstProInfo, sizeof(PRODUCT_INFO));

        const int SN_REQUIRED_LENGTH = 22;
        const int SN_SRC_MAX_LEN = sizeof(m_sScanData.strACERSN) - 1; // 预留结束符

        // 计算实际有效数据长度（含结束符则减1）
        int srcValidLen = strnlen(m_sScanData.strACERSN, SN_SRC_MAX_LEN);

        // 安全拷贝长度（确保不超过目标长度）
        int safeCopyLen = (srcValidLen > SN_REQUIRED_LENGTH) ? SN_REQUIRED_LENGTH : srcValidLen;

        // 拷贝有效数据（严格限定长度）
        memcpy(&temp[466], m_sScanData.strACERSN, safeCopyLen);

        // 填充剩余空间为 \0（精确计算填充范围）
        if (safeCopyLen < SN_REQUIRED_LENGTH)
        {
            MTRACE(m_hDebugTrace[m_nTreadID], "警告：SN 码长度不足 22 位，剩余位填充 0");
            memset(&temp[466 + safeCopyLen], 0, SN_REQUIRED_LENGTH - safeCopyLen);
        }

        // 回写数据
        memcpy((char *)pstProInfo, temp, sizeof(PRODUCT_INFO));
    }


    if (g_sMetaComm.sWriteAI.bWriteADN)
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "Prod_Info->reserved[488~507] = [%s]", g_sMetaComm.sWriteAI.strADN);

        // 创建临时缓冲区并拷贝原始数据
        char temp[sizeof(PRODUCT_INFO)] = {0};
        memcpy(temp, (char *)pstProInfo, sizeof(PRODUCT_INFO));

        const int DEST_LEN = 20;
        const int SRC_LEN = sizeof(g_sMetaComm.sWriteAI.strADN) - 1;

        int srcValidLen = strnlen(g_sMetaComm.sWriteAI.strADN, SRC_LEN);
        // 计算安全拷贝长度（取目标长度和源数据有效长度的最小值）
        int copyLen = (srcValidLen >= DEST_LEN) ?  DEST_LEN:srcValidLen ;
        memcpy(&temp[488], g_sMetaComm.sWriteAI.strADN, copyLen);

        // 若源数据不足，填充空字符
        if (copyLen < DEST_LEN)
        {
            memset(&temp[488 + copyLen],'\0', DEST_LEN - copyLen);
            MTRACE(m_hDebugTrace[m_nTreadID], "ADN 数据不足 20 字节，填充 %d 个空字符", DEST_LEN - copyLen);
        }
        // 回写数据
        memcpy((char *)pstProInfo, temp, sizeof(PRODUCT_INFO));
    }


    if (g_sMetaComm.sWriteAI.bWriteASku)
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "Prod_Info->reserved[508~517] = [%s]", g_sMetaComm.sWriteAI.strASku);

        // 创建临时缓冲区并拷贝原始数据
        char temp[sizeof(PRODUCT_INFO)] = {0};
        memcpy(temp, (char *)pstProInfo, sizeof(PRODUCT_INFO));
        const int DEST_LEN = 10;
        const int SRC_LEN = sizeof(g_sMetaComm.sWriteAI.strASku) - 1 ;

        int srcValidLen = strnlen(g_sMetaComm.sWriteAI.strASku, SRC_LEN);
        // 计算安全拷贝长度（取目标长度和源数据有效长度的最小值）
        int copyLen = (srcValidLen >= DEST_LEN) ?  DEST_LEN:srcValidLen ;
        memcpy(&temp[508], g_sMetaComm.sWriteAI.strASku, copyLen);

        if (copyLen < DEST_LEN)
        {
            memset(&temp[508 + copyLen], '\0', DEST_LEN - copyLen);
            MTRACE(m_hDebugTrace[m_nTreadID], "SKU 数据不足 10 字节，填充 %d 个空字符", DEST_LEN - copyLen);
        }
        // 回写数据
        memcpy((char *)pstProInfo, temp, sizeof(PRODUCT_INFO));
    }


    if (g_sMetaComm.sWriteAI.bWriteAModel)
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "Prod_Info->reserved[518~527] = [%s]", g_sMetaComm.sWriteAI.strAModel);

        // 创建临时缓冲区并拷贝原始数据
        char temp[sizeof(PRODUCT_INFO)] = {0};
        memcpy(temp, (char *)pstProInfo, sizeof(PRODUCT_INFO));
        const int DEST_LEN = 10;
        const int SRC_LEN = sizeof(g_sMetaComm.sWriteAI.strAModel) - 1 ;

        int srcValidLen = strnlen(g_sMetaComm.sWriteAI.strAModel, SRC_LEN);
        // 计算安全拷贝长度（取目标长度和源数据有效长度的最小值）
        int copyLen = (srcValidLen >= DEST_LEN) ?  DEST_LEN:srcValidLen ;
        memcpy(&temp[518], g_sMetaComm.sWriteAI.strAModel, copyLen);

        if (copyLen < DEST_LEN)
        {
            memset(&temp[518 + copyLen], '\0', DEST_LEN - copyLen);
            MTRACE(m_hDebugTrace[m_nTreadID], "Model 数据不足 10 字节，填充 %d 个空字符", DEST_LEN - copyLen);
        }
        // 回写数据
        memcpy((char *)pstProInfo, temp, sizeof(PRODUCT_INFO));
    }


    if (g_sMetaComm.sWriteOption.bWriteDJSN1)
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "DJSN1 Prod_Info[735-766] = [%s]", m_sScanData.strDJSN1);
        char temp[1024] = {0};
        memcpy(temp, (char *)pstProInfo, sizeof(PRODUCT_INFO));
        for(int i = 0; i< DJSN_LEN; i++)
        {
            temp[735+i] = m_sScanData.strDJSN1[i];
        }
        memcpy((char *)pstProInfo, temp, sizeof(PRODUCT_INFO));
    }

    if (g_sMetaComm.sWriteOption.bWriteDJSN2)
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "DJSN2 Prod_Info[768-799] = [%s]", m_sScanData.strDJSN2);
        char temp[1024] = {0};
        memcpy(temp, (char *)pstProInfo, sizeof(PRODUCT_INFO));
        for(int i = 0; i< DJSN_LEN; i++)
        {
            temp[768+i] = m_sScanData.strDJSN2[i];
        }
        memcpy((char *)pstProInfo, temp, sizeof(PRODUCT_INFO));
    }*/

    if (testCaseObj[ECodeStr[WRITE_MEID]].toBool())
        pstSnFlag->MEID = 1;

    pstSnFlag->total_pass = 1;
    MTRACE(m_hDebugTrace[m_nTreadID], "Prod_Info->sn_flag = [0x%02x%02x] Bitmap: (15~0) Reserve|MEID|Wifi|Bt|IMEI|Barcode|Pass",
           pstProInfo->mtk_test_flag.sn_write_flag.sn_write_flag[1],
           pstProInfo->mtk_test_flag.sn_write_flag.sn_write_flag[0]);

    if (1/*g_sMetaComm.bClearMetaBootFlag*/)
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "Clear MetaBootFlag: mode %u, type %u, id %u",
               pstProInfo->mtk_reserved_flag.mtk_boot_mode_flag.boot_mode,
               pstProInfo->mtk_reserved_flag.mtk_boot_mode_flag.com_type,
               pstProInfo->mtk_reserved_flag.mtk_boot_mode_flag.com_id);
        pstProInfo->mtk_reserved_flag.mtk_boot_mode_flag.boot_mode = 0;
        pstProInfo->mtk_reserved_flag.mtk_boot_mode_flag.com_type = 0;
        pstProInfo->mtk_reserved_flag.mtk_boot_mode_flag.com_id = 0;
    }

    return META_SUCCESS;
}

bool SmartPhoneSN::CheckMdBarcode(char *szReadBackSN,int iReadBackSNLen)
{
    META_RESULT MetaResult;
    memset((void*)szReadBackSN, 0, iReadBackSNLen);
    int istrCam = -1;

    MetaResult = ReadBarcodefromNVRAM(szReadBackSN, iReadBackSNLen-1);
    if(MetaResult!=META_SUCCESS)
    {
        UpdateUIMsg("ReadBack Barcode From Md Fail !", RED);
        return false;
    }
    char * p = strchr(szReadBackSN,' ');
    if (p)
    {
        *p = 0;
    }

    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::CheckMdBarcode()  Phone barcode = \"%s\"", szReadBackSN);
    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::CheckMdBarcode()  Scan barcode = \"%s\"", m_sScanData.strBarcode);

    UpdateUIMsg(QString("ReadBack Md Barcode = %1").arg(szReadBackSN));
    istrCam = strcmp(szReadBackSN, m_sScanData.strBarcode);

    if(istrCam == 0)
    {
        return true;
    }

    return false;
}

bool SmartPhoneSN::CheckAPBarcode(char *szReadBackSN,int iReadBackSNLen)
{
    int istrCam = -1;
    AP_FT_NVRAM_READ_REQ sNVRAM_ReadReq;
    AP_FT_NVRAM_READ_CNF sNVRAM_ReadCnf;
    META_RESULT meta_result;
    PRODUCT_INFO* product_info;

    memset(&sNVRAM_ReadReq, 0, sizeof(AP_FT_NVRAM_READ_REQ));
    memset(&sNVRAM_ReadCnf, 0, sizeof(AP_FT_NVRAM_READ_CNF));

    sNVRAM_ReadReq.LID = "AP_CFG_REEB_PRODUCT_INFO_LID";
    sNVRAM_ReadReq.RID = 1;
    sNVRAM_ReadCnf.len = sizeof(PRODUCT_INFO);
    sNVRAM_ReadCnf.buf = (unsigned char*) malloc (sizeof(PRODUCT_INFO));


    meta_result =  REQ_ReadFromAPNVRAM (&sNVRAM_ReadReq, &sNVRAM_ReadCnf);
    if (meta_result != META_SUCCESS )
    {
        UpdateUIMsg("Read ProInfo Fail!", RED);
        return false;
    }

    product_info = (PRODUCT_INFO*)sNVRAM_ReadCnf.buf;

    memset((void*)szReadBackSN, 0, iReadBackSNLen);
    _snprintf_s(szReadBackSN, iReadBackSNLen, iReadBackSNLen-1, (char *)product_info->barcode);
    char * p = strchr(szReadBackSN,' ');
    if (p)
    {
        *p = 0;
    }

    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::CheckAPBarcode()  Phone barcode = \"%s\"", szReadBackSN);
    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::CheckAPBarcode()  Scan barcode = \"%s\"", m_sScanData.strBarcode);

    UpdateUIMsg(QString("ReadBack AP Barcode = %1").arg(szReadBackSN));
    istrCam = strcmp(szReadBackSN, m_sScanData.strBarcode);
    if(istrCam == 0)
    {
        return true;
    }

    return false;
}

bool SmartPhoneSN::CheckIMEI1(char *szReadBackImei)
{
    META_RESULT MetaResult;
    memset((void*)szReadBackImei, 0, IMEI_ARRAY_LEN);
    int istrCam = -1;

    MetaResult = REQ_ReadAP_NVRAM_Start(WRITE_IMEI, szReadBackImei,1);
    if(MetaResult!=META_SUCCESS)
    {
        UpdateUIMsg("ReadBack IMEI1 Fail !", RED);
        return false;
    }

    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::CheckIMEI1()  Phone IMEI1 = \"%s\"", szReadBackImei);
    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::CheckIMEI1()  Scan IMEI1 = \"%s\"", m_sScanData.strIMEI[0]);

    UpdateUIMsg(QString("ReadBack IMEI1 = %1").arg(szReadBackImei));
    istrCam = strcmp(szReadBackImei,m_sScanData.strIMEI[0]);

    if(istrCam == 0)
    {
        return true;
    }

    return false;
}

bool SmartPhoneSN::CheckIMEI2(char *szReadBackImei)
{
    META_RESULT MetaResult;
    memset((void*)szReadBackImei, 0, IMEI_ARRAY_LEN);
    int istrCam = -1;

    MetaResult = REQ_ReadAP_NVRAM_Start(WRITE_IMEI2, szReadBackImei,1);
    if(MetaResult!=META_SUCCESS)
    {
        UpdateUIMsg("ReadBack IMEI2 Fail !", RED);
        return false;
    }

    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::CheckIMEI2()  Phone IMEI2 = \"%s\"", szReadBackImei);
    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::CheckIMEI2()  Scan IMEI2 = \"%s\"", m_sScanData.strIMEI[1]);

    UpdateUIMsg(QString("ReadBack IMEI2 = %1").arg(szReadBackImei));
    istrCam = strcmp(szReadBackImei,m_sScanData.strIMEI[1]);

    if(istrCam == 0)
    {
        return true;
    }

    return false;
}

bool SmartPhoneSN::CheckMEID(char *szReadBackMeid)
{
    if (META_SUCCESS != EnableModemMeta())
        return false;

    if (m_eMetaMode == SP_MODEM_META)
    {
        int iRet = 0;
        if (m_iC2kProject != 0)
            iRet = EnterC2KGen90(false, szReadBackMeid);
        else
            iRet = EnterC2KGen93(false, szReadBackMeid);

        if((iRet == -1))
        {
            UpdateUIMsg("The Phone is not Support C2K", RED);
            return false;
        }

        UpdateUIMsg(QString("ReadBack MEID = %1").arg(szReadBackMeid));
        if(stricmp(szReadBackMeid, m_sScanData.strMeid) == 0)
            return true;
        else
            return false;
    }

    return false;
}

bool SmartPhoneSN::CheckBtAddr(char *szReadBackBT)
{
    META_RESULT MetaResult;
    memset((void*)szReadBackBT, 0, BT_ARRAY_LEN);
    int istrCam = -1;

    MetaResult = REQ_ReadAP_NVRAM_Start(WRITE_BT, szReadBackBT, 1);
    if(MetaResult!=META_SUCCESS)
    {
        UpdateUIMsg("ReadBack BtAddr Fail !", RED);
        return false;
    }

    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::CheckBtAddr()  Phone BtAddr = \"%s\"", szReadBackBT);
    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::CheckBtAddr()  Scan BtAddr = \"%s\"", m_sScanData.strBTAddr);

    UpdateUIMsg(QString("ReadBack BtAddr = %1").arg(szReadBackBT));
    istrCam = strcmp(szReadBackBT, m_sScanData.strBTAddr);

    if(istrCam == 0)
    {
        return true;
    }

    return false;
}

bool SmartPhoneSN::CheckWifiMac(char *szReadBackWifi)
{
    META_RESULT MetaResult;
    memset((void*)szReadBackWifi, 0, WIFI_ARRAY_LEN);
    int istrCam = -1;

    MetaResult = REQ_ReadAP_NVRAM_Start(WRITE_WIFI, szReadBackWifi, 1);
    if(MetaResult!=META_SUCCESS)
    {
        UpdateUIMsg("ReadBack WifiMac Fail !", RED);
        return false;
    }

    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::CheckWifiMac()  Phone WifiMac = \"%s\"", szReadBackWifi);
    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::CheckWifiMac()  Scan WifiMac = \"%s\"", m_sScanData.strWifiAddr);

    UpdateUIMsg(QString("ReadBack WifiMac = %1").arg(szReadBackWifi));
    istrCam = strcmp(szReadBackWifi, m_sScanData.strWifiAddr);

    if(istrCam == 0)
    {
        return true;
    }

    return false;
}

bool SmartPhoneSN::FactoryReset()
{
    EMMC_CLEAR_CNF_S cnf;
    memset(&cnf,0,sizeof(EMMC_CLEAR_CNF_S));
    META_RESULT iRet = SP_META_ClearValueEx_r(m_hSPMetaHandle, 15000, &cnf);
    if (iRet != META_SUCCESS)
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "SP_META_ClearValueEx_r  \"%s\"", Common::getInstance()->ResultToString(iRet));
        UpdateUIMsg(QString("FactoryReset Fail = %1").arg(Common::getInstance()->ResultToString(iRet)), RED);
        return false;
    }
    else
    {
        return true;
    }
}

bool SmartPhoneSN::CheckSIM(int num)// 0->sim1; 1->sim2
{
    META_RESULT mr = META_FAILED;
    unsigned char simModuleId = num;
    int status = -1;

    mr = META_MISC_SimHwTest_r(m_hMauiMetaHandle, 3000, simModuleId, &status);
    if (mr == META_SUCCESS && status == 0)
    {
        return true;
    }
    else
    {
        return false;
    }
}

bool SmartPhoneSN::CheckTCard()
{
    FT_SDCARD_REQ req;
    FT_SDCARD_CNF cnf;
    req.dwSDHCIndex = 1;

    return META_SUCCESS == SP_META_SDCARD_Query_r(m_hSPMetaHandle, 3000, &req, &cnf);
}

META_RESULT SmartPhoneSN::ReadSwVer(char* Prop, char* m_szReadVer)
{
    META_RESULT MetaResult;
    BUILD_PROP_REQ_S build_prop_key;
    BUILD_PROP_CNF_S build_prop_value;

    memset(&build_prop_key, 0, sizeof(BUILD_PROP_REQ_S));
    memset(&build_prop_value, 0, sizeof(BUILD_PROP_CNF_S));
    sprintf_s((char*)(build_prop_key.tag), sizeof(BUILD_PROP_REQ_S)-1, Prop);

    MetaResult = SP_META_GetTargetBuildProp_r(m_hSPMetaHandle, &build_prop_key, &build_prop_value);
    if(MetaResult == META_SUCCESS)
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::ReadSwVer()  Phone SWVersion = \"%s\"", (char*)(build_prop_value.content));
        sprintf_s(m_szReadVer, PROP_VALUE_MAX-1, "%s", (char*)(build_prop_value.content));
        UpdateUIMsg(QString("ReadBack SwVer : %1").arg(m_szReadVer));
    }

    return MetaResult;
}

META_RESULT SmartPhoneSN::GetBatLevel(int *Level)
{
    META_RESULT MetaResult = META_SUCCESS;
    unsigned int  ms_timeout = 5000;

    //  解决偶发读取电量为-1的bug
    int count = 0;
    while (count<3)
    {
        count++;
        MetaResult = SP_META_ADC_GetBatCapacity_r(m_hSPMetaHandle, ms_timeout, Level);
        if(MetaResult == META_SUCCESS && *Level >= 0 && *Level <= 100)
        {
            break;
        }
        else
        {
            Sleep(1000);
        }
    }

    return MetaResult;
}

bool SmartPhoneSN::CheckBarcodeFlag()
{
    META_RESULT MetaResult;
    char szReadBackSN[BARCODE_ARRAY_LEN] = { 0 };

    if(Common::getInstance()->getWifionlyEnable())
    {
        AP_FT_NVRAM_READ_REQ sNVRAM_ReadReq;
        AP_FT_NVRAM_READ_CNF sNVRAM_ReadCnf;
        PRODUCT_INFO* product_info;

        memset(&sNVRAM_ReadReq, 0, sizeof(AP_FT_NVRAM_READ_REQ));
        memset(&sNVRAM_ReadCnf, 0, sizeof(AP_FT_NVRAM_READ_CNF));

        sNVRAM_ReadReq.LID = "AP_CFG_REEB_PRODUCT_INFO_LID";
        sNVRAM_ReadReq.RID = 1;
        sNVRAM_ReadCnf.len = sizeof(PRODUCT_INFO);
        sNVRAM_ReadCnf.buf = (unsigned char*) malloc (sizeof(PRODUCT_INFO));


        MetaResult =  REQ_ReadFromAPNVRAM (&sNVRAM_ReadReq, &sNVRAM_ReadCnf);
        if (MetaResult != META_SUCCESS )
        {
            UpdateUIMsg("Read ProInfo Fail!", RED);
            return false;
        }

        product_info = (PRODUCT_INFO*)sNVRAM_ReadCnf.buf;
        _snprintf_s(szReadBackSN, BARCODE_MAX_LENGTH, BARCODE_MAX_LENGTH-1, (char *)product_info->barcode);
    }
    else
    {
        MetaResult = ReadBarcodefromNVRAM(szReadBackSN, BARCODE_ARRAY_LEN-1);
        if(MetaResult!=META_SUCCESS)
        {
            UpdateUIMsg("ReadBack Barcode From Md Fail !", RED);
            return false;
        }
    }

    QJsonObject CheckFlagObj = Common::getInstance()->getJsonParams(CheckBarcodeFlagItem::staticType());
    if(CheckFlagObj.isEmpty())
    {
        UpdateUIMsg("Flag Info is Empty !", RED);
        return false;
    }

    bool testflag = true;
    QString AllFlagInfo = CheckFlagObj["Flag"].toString();
    QStringList FlagInfo_list = AllFlagInfo.split(";");
    for(int num=0; num < FlagInfo_list.count(); num++)
    {
        QStringList FlagInfo = FlagInfo_list[num].split(":");
        if(FlagInfo.count() !=2 || FlagInfo[0].toInt() > 62 || FlagInfo[0].toInt() < 0)
        {
            UpdateUIMsg("Flag Info is Illegal !", RED);
            return false;
        }

        int key = FlagInfo[0].toInt();
        QString value = FlagInfo[1];
        if(QString(szReadBackSN[key]) != value)
        {
            UpdateUIMsg(QString("ReadBackSN[%1] != %2").arg(key).arg(value), RED);
            testflag = false;
        }
    }

    return testflag;
}

META_RESULT SmartPhoneSN::TEE_Authorize()
{
    //metaHandle 为一个合法的META会话id
    char kph_base_path[256];
    char module_path[260];
    int r = -1;

    //获取 kph_base_path-START
    GetModuleFileNameA(NULL, module_path, 260);
    std::string::size_type pos =
        std::string(module_path).find_last_of("/\\");
    sprintf(kph_base_path, "%s\\tee_stuff",
            std::string(module_path).substr(0, pos).c_str());
    /*配置文件 cfg.ini，kph_env.ini 等应放置在产线工具目录下的
    tee_stuff/kph_in/文件夹中*/
    //kph.log 会被保存至产线工具目录下的 tee_stuff/kph_log/文件夹中
    //获取 kph_base_path-END

    if ((r = KPHA_InitEnv(kph_base_path)) < 0)
    {
        //kph_base_path 为产线工具目录下 tee_stuff 文件夹的路径
        return META_FAILED;
    }

    if ((r = KPHA_SetupDevice(m_hMauiMetaHandle)) < 0)
    {
        return META_FAILED;
    }
    return META_SUCCESS;
}

META_RESULT SmartPhoneSN::TEE_Check()
{
    int r = -1;
    bool bUnknown = true;
    std::string sFailCause;

    //metaHandle 为一个合法的 META 会话 id
    char kph_base_path[256];
    char module_path[260];

    //获取 kph_base_path-START
    /* 配置文件 cfg.ini，kph_env.ini 等应放置在产线工具目录下的
    tee_stuff/kph_in/文件夹中 */
    //kph.log 会被保存至产线工具目录下的 tee_stuff/kph_log/文件夹中
    GetModuleFileNameA(NULL, module_path, 260);
    std::string::size_type pos =
        std::string(module_path).find_last_of("/\\");
    sprintf(kph_base_path, "%s\\tee_stuff",
            std::string(module_path).substr(0, pos).c_str());
    //获取 kph_base_path-END

    //初始化
    //kph_base_path 为产线工具目录下 tee_stuff 文件夹的路径
    r = KPHA_InitEnv(kph_base_path);
    if (r < 0)
        return META_FAILED;

    //TEE check
    r = KPHA_Check_TEE_State(m_hMauiMetaHandle);
    if (r == 0)
        return META_SUCCESS;
    if (r < 0)
    {
        // 调用 KPHA_Check_TEE_State 出错
        // 可能原因：未集成 TEE 功能，或者初始化失败
        sFailCause = "TEE key not support or init failed";
        return META_FAILED;
    }

    if (r & (1 << 0))
    {
        sFailCause = "TEE key not generated";
        bUnknown = false;
    }

    if (r & (1 << 1))
    {
        if (bUnknown == false)
            sFailCause += ", ";

        sFailCause += "TEE not authorized";
        bUnknown = false;
    }

    if (1/*g_sMetaComm.sWriteOption.bTeeCheckKey*/)
    {
        if (r & (1 << 3))
        {
            if (bUnknown == false)
                sFailCause += ", ";

            sFailCause += "Keybox not imported";
            bUnknown = false;
        }
    }

    if (0/*g_sMetaComm.sWriteOption.bTeeCheckDrm*/)
    {
        if (r & (1 << 4))
        {
            if (bUnknown == false)
                sFailCause += ", ";

            sFailCause += "DRM_Key not imported";
            bUnknown = false;
        }
    }

    if (bUnknown)
        sFailCause = "Unknown";

    if (!((0/*g_sMetaComm.sWriteOption.bTeeCheckDrm*/ && (r & (1 << 4)))
          || (1/*g_sMetaComm.sWriteOption.bTeeCheckKey*/ && (r & (1 << 3)))) && bUnknown)
    {
        return META_SUCCESS;
    }

    UpdateUIMsg(QString("TEE_Check Fail! ErrInfo: %1").arg(sFailCause.c_str()), RED);
    return META_FAILED;
}

bool SmartPhoneSN::CheckMdSN(char *szReadBackSN,int iReadBackSNLen, int codetype)
{
    META_RESULT MetaResult;
    memset((void*)szReadBackSN, 0, iReadBackSNLen);
    int istrCam = -1;

    MetaResult = ReadBarcodefromNVRAM(szReadBackSN, iReadBackSNLen-1);
    if(MetaResult!=META_SUCCESS)
    {
        UpdateUIMsg("ReadBack Barcode From Md Fail !", RED);
        return false;
    }
    char * p = strchr(szReadBackSN,' ');
    if (p)
    {
        *p = 0;
    }

    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::CheckMdSN()  Phone barcode = \"%s\"", szReadBackSN);
    UpdateUIMsg(QString("ReadBack Md Barcode = %1").arg(szReadBackSN));

    if(codetype==0)
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::CheckMdSN()  Scan OBSN = \"%s\"", m_sScanData.strOBSN);
        istrCam = strcmp(szReadBackSN, m_sScanData.strOBSN);
    }
    else
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::CheckMdSN()  Scan IWSN = \"%s\"", m_sScanData.strIWSN);
        istrCam = strcmp(szReadBackSN, m_sScanData.strIWSN);
    }

    if(istrCam == 0)
    {
        return true;
    }

    return false;
}

bool SmartPhoneSN::CheckAPSN(char *szReadBackSN,int iReadBackSNLen, int codetype)
{
    int istrCam = -1;
    AP_FT_NVRAM_READ_REQ sNVRAM_ReadReq;
    AP_FT_NVRAM_READ_CNF sNVRAM_ReadCnf;
    META_RESULT meta_result;
    PRODUCT_INFO* product_info;

    memset(&sNVRAM_ReadReq, 0, sizeof(AP_FT_NVRAM_READ_REQ));
    memset(&sNVRAM_ReadCnf, 0, sizeof(AP_FT_NVRAM_READ_CNF));

    sNVRAM_ReadReq.LID = "AP_CFG_REEB_PRODUCT_INFO_LID";
    sNVRAM_ReadReq.RID = 1;
    sNVRAM_ReadCnf.len = sizeof(PRODUCT_INFO);
    sNVRAM_ReadCnf.buf = (unsigned char*) malloc (sizeof(PRODUCT_INFO));


    meta_result =  REQ_ReadFromAPNVRAM (&sNVRAM_ReadReq, &sNVRAM_ReadCnf);
    if (meta_result != META_SUCCESS )
    {
        UpdateUIMsg("Read ProInfo Fail!", RED);
        return false;
    }

    product_info = (PRODUCT_INFO*)sNVRAM_ReadCnf.buf;

    memset((void*)szReadBackSN, 0, iReadBackSNLen);
    _snprintf_s(szReadBackSN, iReadBackSNLen, iReadBackSNLen-1, (char *)product_info->barcode);
    char * p = strchr(szReadBackSN,' ');
    if (p)
    {
        *p = 0;
    }

    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::CheckAPSN()  Phone barcode = \"%s\"", szReadBackSN);
    UpdateUIMsg(QString("ReadBack AP Barcode = %1").arg(szReadBackSN));

    if(codetype==0)
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::CheckAPSN()  Scan barcode = \"%s\"", m_sScanData.strOBSN);
        istrCam = strcmp(szReadBackSN, m_sScanData.strOBSN);
    }
    else
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::CheckAPSN()  Scan IWSN = \"%s\"", m_sScanData.strIWSN);
        istrCam = strcmp(szReadBackSN, m_sScanData.strIWSN);
    }

    if(istrCam == 0)
    {
        return true;
    }

    return false;
}

bool SmartPhoneSN::CheckSerialNo()
{
    char serialno[SERIAL_NO_BUF_LEN] = { 0 };
    AP_FT_NVRAM_READ_REQ sNVRAM_ReadReq;
    AP_FT_NVRAM_READ_CNF sNVRAM_ReadCnf;
    META_RESULT meta_result;
    PRODUCT_INFO* product_info;

    memset(&sNVRAM_ReadReq, 0, sizeof(AP_FT_NVRAM_READ_REQ));
    memset(&sNVRAM_ReadCnf, 0, sizeof(AP_FT_NVRAM_READ_CNF));

    sNVRAM_ReadReq.LID = "AP_CFG_REEB_PRODUCT_INFO_LID";
    sNVRAM_ReadReq.RID = 1;
    sNVRAM_ReadCnf.len = sizeof(PRODUCT_INFO);
    sNVRAM_ReadCnf.buf = (unsigned char*) malloc (sizeof(PRODUCT_INFO));

    meta_result =  REQ_ReadFromAPNVRAM (&sNVRAM_ReadReq, &sNVRAM_ReadCnf);
    if (meta_result != META_SUCCESS )
    {
        UpdateUIMsg("Read ProInfo Fail!", RED);
        return false;
    }

    product_info = (PRODUCT_INFO*)sNVRAM_ReadCnf.buf;

    for (int i = 0; i < SERIAL_NO_BUF_LEN; i++)
    {
        serialno[i] = product_info->target_info.ADBSeriaNo[i];
    }

    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::CheckSerialNo()  Phone serialno = \"%s\"", serialno);
    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::CheckSerialNo()  Scan serialno = \"%s\"", m_sScanData.strSerialNo);
    if(strcmp(serialno, m_sScanData.strSerialNo)==0)
    {
        UpdateUIMsg(QString("ReadBack SerialNo = %1").arg(serialno));
    }
    else
    {
        UpdateUIMsg(QString("ReadBack SerialNo = %1").arg(serialno), RED);
        return false;
    }

    return true;
}

bool SmartPhoneSN::GetScrilSf(char* ScrilSf)
{
    AP_FT_NVRAM_READ_REQ sNVRAM_ReadReq;
    AP_FT_NVRAM_READ_CNF sNVRAM_ReadCnf;
    META_RESULT meta_result;
    PRODUCT_INFO* product_info;

    memset(&sNVRAM_ReadReq, 0, sizeof(AP_FT_NVRAM_READ_REQ));
    memset(&sNVRAM_ReadCnf, 0, sizeof(AP_FT_NVRAM_READ_CNF));

    sNVRAM_ReadReq.LID = "AP_CFG_REEB_PRODUCT_INFO_LID";
    sNVRAM_ReadReq.RID = 1;
    sNVRAM_ReadCnf.len = sizeof(PRODUCT_INFO);
    sNVRAM_ReadCnf.buf = (unsigned char*) malloc (sizeof(PRODUCT_INFO));

    meta_result =  REQ_ReadFromAPNVRAM (&sNVRAM_ReadReq, &sNVRAM_ReadCnf);
    if (meta_result != META_SUCCESS )
    {
        UpdateUIMsg("Read ProInfo Fail!", RED);
        return false;
    }

    product_info = (PRODUCT_INFO*)sNVRAM_ReadCnf.buf;
    for (int i = 0; i < SCRILSF_LENGTH; i++)
    {
        ScrilSf[i] = product_info->reserved[i];
    }
    return true;
}

bool SmartPhoneSN::CheckEthernetMac()
{
    char szReadBackMac[ETHERNET_MAC_ARRAY_LEN] = { 0 };
    META_RESULT MetaResult;
    int istrCam = -1;

    MetaResult = REQ_ReadAP_NVRAM_Start(WRITE_ETHERNET_MAC, szReadBackMac, 1);
    if(MetaResult!=META_SUCCESS)
    {
        UpdateUIMsg("ReadBack EthernetMac Fail !", RED);
        return false;
    }

    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::CheckEthernetMac()  Phone EthernetMac = \"%s\"", szReadBackMac);
    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::CheckEthernetMac()  Scan EthernetMac = \"%s\"", m_sScanData.strEthernetMac);

    UpdateUIMsg(QString("ReadBack EthernetMac = %1").arg(szReadBackMac));
    istrCam = strcmp(szReadBackMac, m_sScanData.strEthernetMac);

    if(istrCam == 0)
    {
        return true;
    }

    return false;
}

bool SmartPhoneSN::CheckNetCode()
{
    char NetCode[NETCODE_BUF_LEN] = { 0 };
    AP_FT_NVRAM_READ_REQ sNVRAM_ReadReq;
    AP_FT_NVRAM_READ_CNF sNVRAM_ReadCnf;
    META_RESULT meta_result;
    PRODUCT_INFO* product_info;
    int iWriteBufSize = 0;

    memset(&sNVRAM_ReadReq, 0, sizeof(AP_FT_NVRAM_READ_REQ));
    memset(&sNVRAM_ReadCnf, 0, sizeof(AP_FT_NVRAM_READ_CNF));

    if(Common::getInstance()->getNetCodeType() == 2)
    {
        sNVRAM_ReadReq.LID = "AP_CFG_RDEB_PRIZE_FACTORY_INFO_LID";
        sNVRAM_ReadReq.RID = 1;

        MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::hwtest_GetNetCode(): Start to get nvram struct size via LID = \"%s\"...", sNVRAM_ReadReq.LID);
        meta_result = SP_META_NVRAM_GetRecLen(sNVRAM_ReadReq.LID, &iWriteBufSize);
        if (  META_SUCCESS != meta_result)
        {
            MTRACE_ERR (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::hwtest_GetNetCode(): Get nvram struct size fail, MetaResult = %s", Common::getInstance()->ResultToString_SP(meta_result));
            return false;
        }
        else
        {
            MTRACE (m_hDebugTrace[m_nTreadID], "SmartPhoneSN::SP_META_NVRAM_GetRecLen(): Get nvram struct size = %d successfully!", iWriteBufSize);
            if ( NULL != sNVRAM_ReadCnf.buf )
            {
                free ( sNVRAM_ReadCnf.buf );
                sNVRAM_ReadCnf.buf = NULL;
            }

            sNVRAM_ReadCnf.len = iWriteBufSize;
            sNVRAM_ReadCnf.buf = (unsigned char*) malloc (iWriteBufSize * sizeof(unsigned char));
            if (NULL == sNVRAM_ReadCnf.buf)
            {
                MTRACE_ERR (m_hDebugTrace[m_nTreadID], "Malloc heap memory cause fail!");
                return false;
            }

            meta_result =  REQ_ReadFromAPNVRAM (&sNVRAM_ReadReq, &sNVRAM_ReadCnf);
            if (meta_result != META_SUCCESS )
            {
                UpdateUIMsg("Read FACTORY_INFO Fail!", RED);
                return false;
            }

            for (int i = 0; i < 21; i++)
            {
                NetCode[i] = sNVRAM_ReadCnf.buf[i];
            }
        }
    }
    else
    {
        sNVRAM_ReadReq.LID = "AP_CFG_REEB_PRODUCT_INFO_LID";
        sNVRAM_ReadReq.RID = 1;
        sNVRAM_ReadCnf.len = sizeof(PRODUCT_INFO);
        sNVRAM_ReadCnf.buf = (unsigned char*) malloc (sizeof(PRODUCT_INFO));

        meta_result =  REQ_ReadFromAPNVRAM (&sNVRAM_ReadReq, &sNVRAM_ReadCnf);
        if (meta_result != META_SUCCESS )
        {
            UpdateUIMsg("Read ProInfo Fail!", RED);
            return false;
        }

        product_info = (PRODUCT_INFO*)sNVRAM_ReadCnf.buf;

        for (int i = 0; i < 31; i++)
        {
            NetCode[i] = sNVRAM_ReadCnf.buf[531+i];
        }
    }

    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::CheckNetCode()  Phone NetCode = \"%s\"", NetCode);
    MTRACE(m_hDebugTrace[m_nTreadID], "SmartPhoneSN::CheckNetCode()  Scan NetCode = \"%s\"", m_sScanData.strNetCode);
    if(strcmp(NetCode, m_sScanData.strNetCode) != 0)
    {
        UpdateUIMsg(QString("Scandata[%1] not equal readback[%2] data!").arg(m_sScanData.strNetCode).arg(NetCode), RED);
        return false;
    }

    return true;
}

bool SmartPhoneSN::CheckScrilSf(int ScrilSfType)
{
    char ScrilSf[SCRILSF_ARRAY_LEN] = {0};
    bool chkret = GetScrilSf(ScrilSf);
    if(chkret)
    {
        if(ScrilSfType == 0)
        {
            int result = strcmp(m_sScanData.strScrilSf, ScrilSf);//SFMB
            if(result == 0)
                return true;
            else
            {
                MTRACE (m_hDebugTrace[m_nTreadID], "The ScrilSf_MachineBody  is [%s] not equal to Inside the machine of ScrilSf[%s]---FAIL!", m_sScanData.strScrilSf, ScrilSf);
                UpdateUIMsg(QString("Inside the machine of ScrilSf=[%1], But Scan ScrilSf =[%2]").arg(ScrilSf).arg(m_sScanData.strScrilSf), RED);
                return false;
            }
        }
        else
        {
            int result = strcmp(m_sScanData.strScrilSf, ScrilSf);//SFOB
            if(result == 0)
                return true;

            MTRACE (m_hDebugTrace[m_nTreadID], "The ScrilSf_OuterBox  is [%s] not equal to Inside the machine of ScrilSf[%s]---FAIL!", m_sScanData.strScrilSf, ScrilSf);
            UpdateUIMsg(QString("Inside the machine of ScrilSf=[%1], But Scan ScrilSf =[%2]").arg(ScrilSf).arg(m_sScanData.strScrilSf), RED);
            return false;
        }
    }
    else
        return false;
}
