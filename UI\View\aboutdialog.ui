<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CAboutDialog</class>
 <widget class="QDialog" name="CAboutDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>351</width>
    <height>235</height>
   </rect>
  </property>
  <property name="font">
   <font>
    <family>Arial</family>
   </font>
  </property>
  <property name="windowTitle">
   <string>About</string>
  </property>
  <layout class="QGridLayout" name="gridLayout" rowstretch="4,0,0,0,0">
   <item row="0" column="0">
    <widget class="QWidget" name="widget" native="true">
     <property name="styleSheet">
      <string notr="true">image: url(:/images/agn-logo.png);</string>
     </property>
    </widget>
   </item>
   <item row="4" column="0">
    <widget class="QLabel" name="m_lbCopyRight">
     <property name="text">
      <string>(C) Copyright 2025 - 2035 Agenew Inc. All rights reserved.</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
   </item>
   <item row="1" column="0">
    <widget class="QLabel" name="label">
     <property name="font">
      <font>
       <family>Arial</family>
       <pointsize>20</pointsize>
      </font>
     </property>
     <property name="text">
      <string>Agenew MultiCodes Tool</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
   </item>
   <item row="2" column="0">
    <widget class="QLabel" name="label_2">
     <property name="text">
      <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; font-size:11pt; font-weight:600; text-decoration: underline;&quot;&gt;Support List:&lt;/span&gt;&lt;/p&gt;&lt;p&gt;MTK&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
   </item>
   <item row="3" column="0">
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>10</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
