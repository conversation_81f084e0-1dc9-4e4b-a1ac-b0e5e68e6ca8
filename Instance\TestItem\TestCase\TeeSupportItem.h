#ifndef TEESUPPORTITEM_H
#define TEESUPPORTITEM_H

#include "../testcasebase.h"
#include <QCheckBox>
#include <QComboBox>
#include <QLineEdit>
#include <QFormLayout>

class TeeSupportItem : public TestCaseBase
{
    Q_OBJECT
public:
    TeeSupportItem();

    QString type() const override { return "TeeSupport"; }
    static QString staticType() { return "TeeSupport"; }
    QString displayName() const override { return "Tee Support"; }

    QWidget* createParameterWidget(QWidget* parent) override;

    QVariantMap parameters() const override { return m_params; }
    void setParameters(const QVariantMap& params) override { m_params = params; }
};

#endif // TEESUPPORTITEM_H
