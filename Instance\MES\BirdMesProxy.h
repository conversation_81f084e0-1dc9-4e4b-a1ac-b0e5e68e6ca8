#pragma once

#include "MesProxyBase.h"

class BirdMesProxy : public MesProxyBase
{
public:
	BirdMesProxy(void);
	~BirdMesProxy(void);

	bool PreWork(MesDataUnit_s *unit, MesErrChar *errInfo);
	bool CheckSN(MesDataUnit_s *unit, MesErrChar *errInfo);
	bool QuerySN(MesDataUnit_s *unit, MesErrChar *errInfo);
	bool MarkWork(MesDataUnit_s *unit, MesErrChar *errInfo);

private:
	void Shell(LPSTR args, char* result, int size);
	bool CheckRoutePassed(string iSN, string Po,string station,string usercode, char *oErrMessage);
	bool SetMobileData(string iSN,string Po,string iResCode, string iOperator, string iResult, string iErrCode, char *oErrMessage);
	bool GetSnRelate(string SN, string RelString, char *oErrMessage);
	string GetMEIOrNetCodeRange(string iSN, string RelString);
	string GetIMobileAllinfo(string Code, string RelString);
};

