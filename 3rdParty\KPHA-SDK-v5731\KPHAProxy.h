#pragma once

#ifdef _WIN32
#include <windows.h>
#endif

class KPHAProxy
{
public:
	K<PERSON><PERSON>Proxy(void);
	~KPHAProxy(void);

	static KPHAProxy * inst();
    bool init(HWND parent, bool flag = false);
    bool DBHelper_Library_DynamicLoad(HWND parent);

	int Dll_KPHA_InitEnv(char *base_path);

	int Dll_KPHA_ClearEnv(void);

	int Dll_KPHA_GetLastError(char *err_msg, unsigned int capacity);

	int Dll_KPHA_SetLogPath(const char *log_path);

	int Dll_KPHA_ShutdownDevice(int handle);

	int Dll_KPHA_GetLicenseCount(void);

	int Dll_KPHA_Init(int handle, char *log_path, char *infile_dir, char *outfile_dir);

	int Dll_KPHA_SetupDevice(int handle);

	int Dll_KPHA_Init2(int handle, const char *base_path);

	int Dll_KPHA_Is_TEE_Supported(int handle);

	int Dll_KPHA_Check_TEE_State(int handle);

	unsigned long  Dll_Get_KPHA_Version(void);

	int Dll_KPHA_Check_RPMB(int handle, char *buf, int len);

	int Dll_KPHA_Write_Daccess_Lock_File(int handle, const char *infile_dir);

	int Dll_KPHA_CSRCheck(int handle);

	bool oldversion;

};

