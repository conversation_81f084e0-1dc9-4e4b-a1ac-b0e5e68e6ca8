#ifndef POWERONCURRENTITEM_H
#define POWERONCURRENTITEM_H

#include "../testcasebase.h"
#include <QCheckBox>
#include <QComboBox>
#include <QLineEdit>
#include <QFormLayout>

class PowerOnCurrentItem : public TestCaseBase
{
    Q_OBJECT
public:
    PowerOnCurrentItem();

    QString type() const override { return "PowerOnCurrent"; }
    static QString staticType() { return "PowerOnCurrent"; }
    QString displayName() const override { return "PowerOn Current"; }

    QWidget* createParameterWidget(QWidget* parent) override;

    QVariantMap parameters() const override { return m_params; }
    void setParameters(const QVariantMap& params) override { m_params = params; }
};

#endif // POWERONCURRENTITEM_H
