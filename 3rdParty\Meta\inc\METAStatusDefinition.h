/*
 * Copyright (c) 2020, MediaTek Inc. All rights reserved.
 *
 * This software/firmware and related documentation ("MediaTek Software") are
 * protected under relevant copyright laws.
 * The information contained herein is confidential and proprietary to
 * MediaTek Inc. and/or its licensors.
 * Except as otherwise provided in the applicable licensing terms with
 * MediaTek Inc. and/or its licensors, any reproduction, modification, use or
 * disclosure of MediaTek Software, and information contained herein, in whole
 * or in part, shall be strictly prohibited.
*/
#ifndef __META_STATUS_DEFINITION_H__
#define __META_STATUS_DEFINITION_H__

#define META_INFO_ENUM_BASE (0)
#define META_ERROR_ENUM_BASE (999)
#define META_ERROR_CFG_ENUM_BASE (1000)
#define META_ERROR_INI_ENUM_BASE (2000)
#define META_ERROR_TOOL_ENUM_BASE (3000)
#define META_ERROR_DUT_ENUM_BASE (4000)
#define META_ERROR_INST_ENUM_BASE (5000)
#define META_ERROR_USER_ENUM_BASE (6000)

/**
 * \ingroup General
 * \details The enumeration for META status code
 */
typedef enum
{
    /* Information code */
    META_INFO_UNDEFINED = META_INFO_ENUM_BASE,
    META_INFO_START,
    META_INFO_SUCCESS,

    META_ERROR_UNDEFINED = META_ERROR_ENUM_BASE,

    /* CFG Error */
    META_ERROR_CONFIG_MISSING_SECTION = META_ERROR_CFG_ENUM_BASE,
    META_ERROR_CONFIG_MISSING_KEYWORD,
    META_ERROR_CONFIG_INVALID_VALUE,
    META_ERROR_CONFIG_DEPRECATED_KEYWORD,

    /* INI Error */
    META_ERROR_INI_MISSING_SECTION = META_ERROR_INI_ENUM_BASE,
    META_ERROR_INI_MISSING_KEYWORD,
    META_ERROR_INI_INVALID_VALUE,

    /* Tool Flow Error */
    META_ERROR_TOOL_FLOW_EXCEPTION = META_ERROR_TOOL_ENUM_BASE,
    META_ERROR_TOOL_IMPORT_DATA_FAILED,
    META_ERROR_TOOL_EXPORT_DATA_FAILED,
    META_ERROR_TOOL_NOT_YET_INIT,
    META_ERROR_TOOL_INIT_FAIL,
    META_ERROR_TOOL_FLOW_NOT_SUPPORT,
    META_ERROR_TOOL_LOAD_LIBRARY_FAILED,

    /* DUT Error */
    META_ERROR_DUT_RF_PERFORMANCE_CHECK_FAILED = META_ERROR_DUT_ENUM_BASE,
    META_ERROR_DUT_COMMAND_FAIL,
    META_ERROR_DUT_READ_CAL_DATA_FROM_TARGET_FAILED,
    META_ERROR_DUT_WRITE_CAL_DATA_TO_TARGET_FAILED,
    META_ERROR_DUT_CAPABILITY_NOT_SUPPORT,
    META_ERROR_DUT_INVALID_INFO,
    META_ERROR_DUT_EXECUTE_LIST_MODE_COMMAND_FAILED,
    META_ERROR_DUT_FETCH_LIST_MODE_DONWLINK_RESULT_FAILED,
    META_ERROR_DUT_SYNC_FAILED_BEFORE_UPLINK_MEASUREMENT,
    META_ERROR_DUT_SYNC_FAILED_BEFORE_DOWNLINK_MEASUREMENT,
    META_ERROR_DUT_RESET_DOWNLINK_BLER_RSSI_COUNT_FAILED,
    META_ERROR_DUT_FETCH_DOWNLINK_BLER_RSSI_REPORT_FAILED,
    META_ERROR_DUT_MEASURE_DOWNLINK_BLER_RSSI_FAILED,
    META_ERROR_DUT_MEASURE_DOWNLINK_SENSITIVITY_FAILED,

    /* Instrument Error */
    META_ERROR_INST_CONNECT_INSTRUEMNT_FAILED = META_ERROR_INST_ENUM_BASE,
    META_ERROR_INST_INIT_FAILED,
    META_ERROR_INST_FETCH_UPLINK_SIGNAL_FAILED,
    META_ERROR_INST_EXECUTE_LIST_MODE_FAILED,
    META_ERROR_INST_FETCH_LIST_MODE_UPLINK_RESULT_FAILED,
    META_ERROR_INST_SWITCH_RF_PORT_FAILED,
    META_ERROR_INST_CHANGE_DOWNLINK_SIGNAL_POWER_FAILED,
    META_ERROR_INST_DOWNLINK_WAVEFORM_NOT_FOUND,
    META_ERROR_INST_DOWNLINK_WAVEFORM_NOT_SUPPORTED,
    META_ERROR_INST_PRESETTING_FAILED,
    META_ERROR_INST_RESET_FAILED,
    META_ERROR_INST_UNDEFINED_ERROR,
    META_ERROR_INST_CABLE_LOSS_INVALID,
    META_ERROR_INST_GET_CAPABILITY_FAILED,
    META_ERROR_INST_CONFIG_LIST_MODE_FAILED,
    META_ERROR_INST_POSTSETTING_FAILED,

    /* User Operation Error*/
    META_ERROR_USER_OPERATION_INVALID_SEQUENCE = META_ERROR_USER_ENUM_BASE,
    META_ERROR_USER_OPERATION_INVALID_ITEM,
    META_ERROR_USER_OPERATION_ABORT_BY_USER
} E_META_STATUS_CODE;

/**
 * \ingroup General
 * \details The enumeration for status type
 */
typedef enum
{
    META_STATUS_TYPE_UNDEFINED = 0,
    META_STATUS_TYPE_INFO,
    META_STATUS_TYPE_ERROR,
    META_STATUS_TYPE_NUM,
} E_META_STATUS_TYPE;

/**
 * \ingroup General
 * \details The status code, status type and status description for calibration or tuning runtime flow
 */
struct S_META_STATUS_T
{
    void *object;                    /**< the upper layer instance (user-defined) */
    char itemName[128];              /**< the item name */
    E_META_STATUS_CODE statusCode;   /**< the status code */
    E_META_STATUS_TYPE statusType;   /**< the status type, information or error */
    char statusMsgbuf[512];          /**< the brief description of status code */
    char extraMsgbuf[2048];          /**< the detail message for status code */
};

/**
 * \ingroup TriCalFlowStruct
 * \details the callback function for calibration runtime status
 */
typedef void (__stdcall *META_Status_CallBack)(const S_META_STATUS_T *status);

#endif //  #ifdef __META_STATUS_DEFINITION_H__
