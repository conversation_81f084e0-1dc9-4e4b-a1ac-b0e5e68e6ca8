#pragma once

//------------------- Data structure for test tools
#define MES_SIZE_BASE   40    // All kinds of code, such as SN, ORDER
#define MES_SIZE_INFO   128*2     // Such as err msg, err items
#define MES_SIZE_LONG   256*4     // Extended information set, such as IMEI1+IMEI2+....

#define MES_PASS 0
#define MES_FAIL 1

typedef char    MesErrChar[MES_SIZE_LONG];

typedef struct
{
    char    ItemString[MES_SIZE_INFO];      // Test Items Not Passed, "TP,CAM".
    char    LongString[MES_SIZE_LONG];      // "IMEI:3574111122223333,ICCID:83605337981288888888"
} MesDetailDataUnit_s;

typedef struct
{
	char    Sn[MES_SIZE_BASE];
	char    OrderNo[MES_SIZE_BASE];
	char    StationCode[MES_SIZE_BASE];
	char    OperatorCode[MES_SIZE_BASE];
    int     TestFlag;
    //AGN_MES
    int     QType;
	int     ElapsedTime;
	char    FailMsg[MES_SIZE_INFO];
	MesDetailDataUnit_s  TestItems;
    //SD_MES
    char    InParams[MES_SIZE_LONG];
    //YD_MES
    char    IPAddr[MES_SIZE_BASE];
    char    ResName[MES_SIZE_BASE];
    char    Machine[MES_SIZE_BASE];
    char    Fixture[MES_SIZE_BASE];
    char    Phenomenon[MES_SIZE_BASE];
    char    CSSN[MES_SIZE_BASE];
    char    BT[MES_SIZE_BASE];
    char    Wifi[MES_SIZE_BASE];
    char    MEID[MES_SIZE_BASE];
    char    IMEI1[MES_SIZE_BASE];
    char    IMEI2[MES_SIZE_BASE];
    char    NetCode[MES_SIZE_BASE];
    char    BatLabel[MES_SIZE_BASE];
    bool    bUpdateStation;
} MesDataUnit_s;
