#include "ServerApiProxy.h"

ServerApiProxy::ServerApiProxy(QObject *parent,
                               HttpClient *httpclient,
                               UINT nTreadID)
    : QThread(parent)
    , m_httpclient(httpclient)
    , m_nTreadID(nTreadID)
{

}

ServerApiProxy::~ServerApiProxy(void)
{
}

QString ServerApiProxy::GetMacFromZentao(QString &Order, QString &SN)
{
    QString address = QString(HTTP_IP_ADDRESS) + QString(GET_MAC_ADDRESS) + "-" + Order + "-" + SN;

    qDebug() << "[Thread " << m_nTreadID+1 << "]" << "GetMacFromZentao address: " << address;
    return m_httpclient->HttpGet(address);
}

QString ServerApiProxy::UploadMacToZentao(QString &Order, QString &SN, QString &BtMac, QString &WifiMac)
{
    QString address = QString(HTTP_IP_ADDRESS) + QString(UPLOAD_MAC_ADDRESS) + "-" + Order + "-" + SN + "-" + BtMac + "-" + WifiMac;

    qDebug() << "[Thread " << m_nTreadID+1 << "]" << "UploadMacToZentao address: " << address;
    return m_httpclient->HttpGet(address);
}

QString ServerApiProxy::CheckMacFromZentao(QString &Order, QString &BtMac, QString &WifiMac)
{
    QString address = QString(HTTP_IP_ADDRESS) + QString(CHECK_MAC_ADDRESS) + "-" + Order + "-" + BtMac + "-" + WifiMac;

    qDebug() << "[Thread " << m_nTreadID+1 << "]" << "CheckMacFromZentao address: " << address;
    return m_httpclient->HttpGet(address);
}
