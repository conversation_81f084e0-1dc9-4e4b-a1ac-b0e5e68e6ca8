#pragma once
#include <QSharedPointer>
#include <QString>
#include <QTextCodec>
#include <QFile>
#include <QDir>
#include <QMessageBox>
#include <QDebug>
#include <QElapsedTimer>
#include <QTimer>
#include <QBitArray>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QtNetwork/QSslSocket>
#include <QNetworkAccessManager>
#include <QSerialPort>
#include <QSerialPortInfo>
#include <QDateTime>
#include <Windows.h>
#include <iostream>
#include <shlwapi.h>
#include "Instance/HttpClient/HttpClient.h"
#include "Instance/HttpClient/ServerApiProxy.h"
#include "../Utility/IniItem.h"
#include "../Utility/FileUtils.h"
#include "../Utility/Utils.h"
//META
#include "sp_brom.h"
#include "meta.h"
#include "xboot_debug.h"
#include "Mdebug.h"
//TestItem
#include "Instance/TestItem/TestItem_HeaderFile.h"
//Device Setting
#include "3rdPartyDeviceCmd.h"
#include "3rdParty/BLUAPI/inc/BLUAPI.h"

#define g_strToolVersion "Agenew_MultiCodes_Tool_v1.0_202500704"

#define  PreloaderUSB  1
#define  BootROMUSB   0
#define  MAX_MD_CHANNEL_NUM 8

#define INVALID_META_HANDLE -1
#define MAX_SUPPORT_COMPORT_NUM 4

#define MAX_RANDOM_PIN_LENGTH 6

#define CONTROL_GUN_TIME    100

typedef enum{
	FAIL = 0,
	PASS = 1,
    RUNNING = 3,
    IDLE = 4
}TestResult_Status;

typedef enum
{
	START_STAGE = 0,
    CHECK_AUTO_TEST_STATUS,
    INIT_UI_DISPALY,
    CONNECT_PWR,
    TEST_BOTTOM_CURRENT,
    ENTER_META_MODE,
    INIT_APDB_STAGE,
    INIT_MDDB_STAGE,
    READBACK_DUT_INFO,
    READ_SW_VER_STAGE,
    CHECK_BARCODE_FLAG_STAGE,
    WRITE_CODES_STAGE,
    SP_PRODINFO_STAGE,
    TEE_SUPPORT_STAGE,
    CHECK_CDOES_STAGE,
    CHECK_CARD_STAGE,
    CHECK_BATLEVEL_STAGE,
    TEE_CHECK_STAGE,
    SP_BACKUP_NVRAM_STAGE,
    FACTORY_RESET_STAGE,
    EXIT_META_MODE,
    REBOOT_STAGE,
	TEST_PWRON_CURRENT,
    TEST_DEELSLEEP_CURRENT,
    TEST_CHANGE_CURRENT,
    TEST_PWROFF_CURRENT,
    DISCONNECT_PWR_STAGE,
    END_STAGE = 256
}TEST_STAGE_e;

typedef enum{
    STANDRAD_MSGBOX = 0,
    INFORMATION_MSGBOX = 1,
    WARNING_MSGBOX = 2,
    CRITICAL_MSGBOX = 3,
    QUESTION_MSGBOX = 4
}MessageBox_Type;

typedef enum{
    PRELOADER_COM = 0,
    KERNEL_COM = 1,
    CONTROLBOX_COM = 2,
    SCANGUN_COM = 3,
}PORT_TYPE;

typedef enum{
    POWEROFF_ITEM = 0,
    POWERON_ITEM = 1,
    DEEPSLEEP_ITEM = 2,
    CHARGE_ITEM = 3,
    BOTTOM_ITEM = 4,
}CURRENTITEM_TYPE;

typedef enum
{
    OUTPUT_CHANNEL_1 = 1,
    OUTPUT_CHANNEL_2 = 2
}Output_Channel_e;

typedef enum {
    HIGH,
    MIDDLE,
    LOW
}MEASURE_RANGE;

enum PowerType
{
    Battery,
    Dual,
    Single,
    BlueBird,
};

enum UsbControlType
{
    PowerSupply,
    ControlBox,
    Manual
};

typedef enum{
    BLACK = 0,
    RED = 1,
    GREEN = 2,
}LOGCOLOR_TYPE;

typedef struct
{
    UINT nPreLoaderComNum;
    UINT nKernelComNum;
    UINT nControlBoxComNum;
    UINT nScanGunComNum;
}ComportArg_s;

typedef struct
{
    QString GPIBAddress;
    float Output1Voltage;
    float Output1Current;
    float Output2Voltage;
    float Output2Current;
    UINT nBluComNum;
    float BluOutputVoltage;
}PowerSupply_struct;

typedef struct
{
    bool bAPDBFromDUT;
    bool bMDDBFromDUT;
    bool bDBInitModem_1[MAX_SUPPORT_COMPORT_NUM];
    bool bDBInitModem_2[MAX_SUPPORT_COMPORT_NUM];
    bool bDBInitAP[MAX_SUPPORT_COMPORT_NUM];
    char strMD1Dbpath[MAX_PATH];
    char strMD1DbPath_DUT[MAX_PATH];
    char strMD2Dbpath[MAX_PATH];
    char strAPDbpath[MAX_PATH];
    char strAPDbPath_DUT[MAX_PATH];
}DBFileOption_struct;

typedef struct
{
    char strBromFilter[MAX_PATH];
    char strPreloaderFilter[MAX_PATH];
    char strKernelFilter[MAX_PATH];
}PortFilter_struct;

typedef struct
{
    unsigned int number_of_md;
    unsigned int active_md_idx;
    unsigned int current_mdtype;
    unsigned int mdimg_type[16];
    unsigned int number_of_mdSwImg;
    int          activeMdTypeIdx;
    unsigned int multi_md_capability_support;
}SP_MODEMInfo_s;

typedef enum
{
    SP_MODEM_META = 0,
    SP_AP_META,
    SP_NOTIN_META = 256
}SP_METAMODE_e;

typedef struct {
    UINT iCOMPort;
    UINT iPre_Connect_Timeout;
    UINT iKernel_Connect_Timeout;
    DBFileOption_struct sDBFileOption;
    PortFilter_struct   sPortFilter;
    ScanData_struct m_sScanData[MAX_SUPPORT_COMPORT_NUM];
    IMEIOption_struct sIMEIOption;
    bool TeeOldVer;
} META_Common_struct;

typedef struct
{
    int		PassNums;
    int		FailNums;
    int 	CountNums;
}TestCount_struct;

typedef struct
{
    QString Order;
    QString Station;
    QString UserCode;
}SDMesConfig_struct;

typedef struct
{
    QString Server;
    QString Line;
    QString Order;
    QString Station;
    QString UserCode;
}AGNMesConfig_struct;

typedef struct
{
    QString IP;
    QString Order;
    QString Station;
    QString ResName;
}YDMesConfig_struct;

typedef struct
{
    bool    MesEnable;
    bool    NotCheckStationEnbale;
    bool    NotUpdateEnbale;
    QString MesType;
    SDMesConfig_struct  SDConfig;
    AGNMesConfig_struct AGNConfig;
    YDMesConfig_struct YDConfig;
}MesConfig_struct;

typedef struct
{
    bool                WifiOnlyEnable;
    int                 NetCodeType;
}CfgFile_struct;

typedef struct
{
    bool                SignalRet[MAX_SUPPORT_COMPORT_NUM];
    QString             PortRequest[MAX_SUPPORT_COMPORT_NUM];
    bool                ScanGunSignal[MAX_SUPPORT_COMPORT_NUM];
    QString             ScanGunRequest[MAX_SUPPORT_COMPORT_NUM];
    bool                CtrBoxFlag[MAX_SUPPORT_COMPORT_NUM];
    int                 needCtrBox[MAX_SUPPORT_COMPORT_NUM];
    bool                CtrScanGunFlag[MAX_SUPPORT_COMPORT_NUM];
    int                 needScanGun[MAX_SUPPORT_COMPORT_NUM];
	bool 				Sleepflag;
}Request_struct;

typedef struct
{
    bool    BoxLastStatus[MAX_SUPPORT_COMPORT_NUM];
}BoxStatus_struct;

typedef struct {
    QString             CfgFilePath;
    QString             JsonFilePath;
    QString             CodesFilePath;
    QJsonDocument       m_ItemJsonDoc;
    bool                AdbServiceEnable;
    char                m_LogDir[MAX_PATH];
    char                m_strLogDir_Sub[MAX_SUPPORT_COMPORT_NUM][MAX_PATH];
    bool                DutActive[MAX_SUPPORT_COMPORT_NUM];
    bool                UseRelayEnbale;
    bool                UseScanGunEnable;
    META_Common_struct  g_sMetaComm;
    ComportArg_s        m_sComArg[MAX_SUPPORT_COMPORT_NUM];
    TestCount_struct    m_TestCount[MAX_SUPPORT_COMPORT_NUM];
    MesConfig_struct    m_MesConfig;
    CfgFile_struct      m_CfgFilePara;
	Request_struct      m_RequestArg;
	PowerType           iPowerType;
	PowerSupply_struct  m_sPSArg[4];
	UsbControlType      iUsbControlType;
}CommonSettings_struct;

class Common
{
public:
    virtual ~Common();
    static QSharedPointer<Common> getInstance();
    void saveOptionFile();

    //cfg
    void loadCfgFile(QString cfgpath);
    void saveCfgFile(QString cfgpath);

    //json
    void loadJsonFile(QString jsonpath);
    QJsonDocument getItemJsonDoc();
    QJsonObject getJsonParams(QString testcase);
    void setJsonParams(QString testcase, QJsonObject params);
    bool getItemEnable(QString testcase);

    //Common
    QString getCfgFilePath() const;
    void setCfgFilePath(QString str);
    QString getJsonFilePath() const;
    void setJsonFilePath(QString str);
    QString getCodesFilePath() const;
    void setCodesFilePath(QString str);

    bool getWifionlyEnable() const;
    void setWifionlyEnable(bool Enable);
    bool getADBServiceEnable() const;
    void setADBServiceEnable(bool Enable);
    bool getLoadApFromDutEnable() const;
    void setLoadApFromDutEnable(bool Enable);
    bool getLoadMdFromDutEnable() const;
    void setLoadMdFromDutEnable(bool Enable);
    QString getAPDbPath() const;
    void setAPDbPath(QString str);
    QString getMdDbPath() const;
    void setMdDbPath(QString str);
    QString getLogPath() const;
    void setLogPath(QString str);

    //TestCfg
    int getNetCodeType() const;
    void setNetCodeType(int type);
    bool getUseRelayEnable() const;
    void setUseRelayEnable(bool Enable);
    bool getUseScanGunEnable() const;
    void setUseScanGunEnable(bool Enable);
	
	//Power Supply
    PowerType getPowerType();
    void setPowerType(PowerType type);
    UsbControlType getUsbControlType() const;
    void setUsbControlType(UsbControlType type);

    //Dut Config
    bool getActiveEnable(int nThreadID) const;
    void setActiveEnable(int nThreadID, bool Enable);
    int getDutPort(int nThreadID, PORT_TYPE porttype) const;
    void setDutPort(int nThreadID, PORT_TYPE porttype, int port);

    QString getDutGPIBAddress(int nThreadID) const;
    void setDutGPIBAddress(int nThreadID, QString str);
    float getDutOutput1Vol(int nThreadID) const;
    void setDutOutput1Vol(int nThreadID, float value);
    float getDutOutput1Current(int nThreadID) const;
    void setDutOutput1Current(int nThreadID, float value);
    float getDutOutput2Vol(int nThreadID) const;
    void setDutOutput2Vol(int nThreadID, float value);
    float getDutOutput2Current(int nThreadID) const;
    void setDutOutput2Current(int nThreadID, float value);
    UINT getDutBluPort(int nThreadID) const;
    void setDutBluPort(int nThreadID, UINT port);
    float getDutBluOutputVol(int nThreadID) const;
    void setDutBluOutputVol(int nThreadID, float value);

    //Meta Function
    QString getBromFilter() const;
    QString getPreloaderFilter() const;
    QString getKernelFilter() const;
    UINT getPLTimeout() const;
    UINT getKLTimeout() const;

    bool getAPDBInitFlag(int nThreadID) const;
    void setAPDBInitFlag(int nThreadID, bool Enable);
    bool getMD1DBInitFlag(int nThreadID) const;
    void setMD1DBInitFlag(int nThreadID, bool Enable);
    bool getMD2DBInitFlag(int nThreadID) const;
    void setMD2DBInitFlag(int nThreadID, bool Enable);
    QString getAPDbPath_DUT() const;
    void setAPDbPath_DUT(QString str);
    QString getMd1DbPath_DUT() const;
    void setMd1DbPath_DUT(QString str);
    QString getMd2DbPath() const;
    void setMd2DbPath(QString str);

    bool ResultToString_Win(DWORD ED, char* lpBuffer, DWORD nSize);
    const char * ResultToString_SP(int errCode);
    const char * ResultToString(int errCode);

    void DebugOnOff(int nThreadID, bool bOn);
    const char* getSubLogDirPath(UINT nThreadID) const;

    //Test Count
    int getTestCount(int nThreadID, TestResult_Status m_status = IDLE) const;
    void setTestCount(int nThreadID, TestResult_Status m_status = IDLE);
    void ClearTestCount();

    QString ConvertStdString(const char* str);
    const char * ConvertQString( const QString& str);
    QString convertUtf8ToGbk(const QString &utf8String);
    QString ConvertANSIToUTF8(const std::string& ansiString);

    //Mes
    bool getMesOfflineEnable() const;
    void setMesOfflineEnable(bool Enable);
    void setMesNotCheckStationEnable(bool enbale);
    bool getMesNotCheckStationEnable() const;
    void setMesNotUpdateEnable(bool enbale);
    bool getMesNotUpdateEnable() const;
    void setMesType(QString str);
    QString getMesType() const;
    void setSDMesOrder(QString str);
    QString getSDMesOrder() const;
    void setSDMesStation(QString str);
    QString getSDMesStation() const;
    void setSDMesUserCode(QString str);
    QString getSDMesUserCode() const;
    void setAGNMesLine(QString str);
    QString getAGNMesLine() const;
    void setAGNMesOrder(QString str);
    QString getAGNMesOrder() const;
    void setAGNMesStation(QString str);
    QString getAGNMesStation() const;
    void setAGNMesServer(QString str);
    QString getAGNMesServer() const;
    void setAGNMesUserCode(QString str);
    QString getAGNMesUserCode() const;
    void setYDMesIP(QString str);
    QString getYDMesIP() const;
    void setYDMesOrder(QString str);
    QString getYDMesOrder() const;
    void setYDMesStation(QString str);
    QString getYDMesStation() const;
    void setYDMesResName(QString str);
    QString getYDMesResName() const;

    //MetaCode
    ScanData_struct getScanData(int nThreadID=0) const;
    void setScanData(int nThreadID, ScanData_struct data);

    int getIMEINum() const;
    void setIMEINum(int num);

    QStringList getTestItemOrder() const;
    void setTestItemOrder(QStringList &order);

	//Testing Process Para
    bool getSignalRet(int nThreadID) const;
    void setSignalRet(int nThreadID, bool Enable);

    void setPortRequest(int nThreadID, QString str);
    QString getPortRequest(int nThreadID) const;

    void setneedCtrBox(int nThreadID, int flag);
    int getneedCtrBox(int nThreadID) const;

    bool getCtrBoxFlag(int nThreadID) const;
    void setCtrBoxFlag(int nThreadID, bool Enable);

    void setneedScanGun(int nThreadID, int flag);
    int getneedScanGun(int nThreadID) const;

    bool getScanGunFlag(int nThreadID) const;
    void setScanGunFlag(int nThreadID, bool Enable);
	
	bool getTestBoxLastStatus(int nThreadID) const;
    void setTestBoxLastStatus(int nThreadID, bool Enable);

    //避免控制同一继电器时命令冲突
	bool getSleepFlag() const;
    void setSleepFlag(bool Enable);

    bool getScanGunSignal(int nThreadID) const;
    void setScanGunSignal(int nThreadID, bool Enable);

    void setScanGunRequest(int nThreadID, QString str);
    QString getScanGunRequest(int nThreadID) const;
	
	//Current Arg
    float getCurrentMaxValue(CURRENTITEM_TYPE item) const;
    void setCurrentMaxValue(CURRENTITEM_TYPE item, float value);
    float getCurrentMinValue(CURRENTITEM_TYPE item) const;
    void setCurrentMinValue(CURRENTITEM_TYPE item, float value);
    float getCurrentTriggerValue(CURRENTITEM_TYPE item) const;
    void setCurrentTriggerValue(CURRENTITEM_TYPE item, float value);
    int getCurrentTestTime(CURRENTITEM_TYPE item) const;
    void setCurrentTestTime(CURRENTITEM_TYPE item, int value);
    int getCurrentTimeout(CURRENTITEM_TYPE item) const;
    void setCurrentTimeout(CURRENTITEM_TYPE item, int value);
    int getCurrentSleepTime(CURRENTITEM_TYPE item) const;
    void setCurrentSleepTime(CURRENTITEM_TYPE item, int value);

    //Tee Version
    bool IsTeeOldVer() const;
    void SetTeeOldVer(bool Enable);

    // TEE Version Management
    QString getCurrentTeeVersion() const;
    void setCurrentTeeVersion(const QString& version);
    void initializeTeeVersion(const QString& version);  // For initial setup
    bool hasTeeVersionChanged();

private:
    void loadOptionFile();
    void SettingInitPara();

private:
    Common();
    Common(const Common &) = delete;
    Common &operator=(const Common &) = delete;
    Common(const Common &&) = delete;
    Common &operator=(const Common &&) = delete;

private:
    static QSharedPointer<Common> m_instance;

    CommonSettings_struct   CommonSetting;
    bool bDebugOn[MAX_SUPPORT_COMPORT_NUM];
    MetaTrace_Handle_Sentry* m_phDebugSentry[MAX_SUPPORT_COMPORT_NUM];
    QStringList m_testItemOrder;
	BoxStatus_struct m_BoxStatus;
    QString m_currentTeeVersion;

    QStringList defaultOrderList = {
        InputCodeItem::staticType(),
        InputYDCodesItem::staticType(),
        BottomCurrentItem::staticType(),
        EnterModeItem::staticType(),
        LoadSnItem::staticType(),
        ReadSwVerItem::staticType(),
        CheckBarcodeFlagItem::staticType(),
        WriteCodesItem::staticType(),
        TeeSupportItem::staticType(),
        CheckCodesItem::staticType(),
        CheckCardItem::staticType(),
        CheckBatLevelItem::staticType(),
        TeeCheckItem::staticType(),
        FactoryResetItem::staticType(),
        RebootItem::staticType(),
        PowerOnCurrentItem::staticType(),
        DeepSleepCurrentItem::staticType(),
        ChargeCurrentItem::staticType(),
        PowerOffItem::staticType(),
        PowerOffCurrentItem::staticType(),
    };

};

extern int g_TestStopFlag[MAX_SUPPORT_COMPORT_NUM];
extern hMTRACE  m_hDebugTrace[MAX_SUPPORT_COMPORT_NUM];
