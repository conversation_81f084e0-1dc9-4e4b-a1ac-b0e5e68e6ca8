#ifndef SERVERAPIPROXY_H
#define SERVERAPIPROXY_H

#include <QObject>
#include <QThread>
#include "Common/Common.h"
#include "Instance/HttpClient/JsonHandler.h"

#define HTTP_IP_ADDRESS     "http://**************"
#define GET_MAC_ADDRESS     "/macmanagement-getmacfromzentao"
#define UPLOAD_MAC_ADDRESS  "/macmanagement-uploadmactozentao"
#define CHECK_MAC_ADDRESS   "/macmanagement-checkmactozentao"

class ServerApiProxy : QThread
{
    Q_OBJECT
public:
    ServerApiProxy(QObject *parent = nullptr, HttpClient* httpclient = nullptr, UINT nTreadID = 0);
    ~ServerApiProxy(void);

    QString GetMacFromZentao(QString &Order, QString &SN);

    QString UploadMacToZentao(QString &Order, QString &SN, QString &BtMac, QString &WifiMac);

    QString CheckMacFromZentao(QString &Order, QString &BtMac, QString &WifiMac);

private:
    HttpClient*     m_httpclient;
    UINT            m_nTreadID;
};

#endif // SERVERAPIPROXY_H
