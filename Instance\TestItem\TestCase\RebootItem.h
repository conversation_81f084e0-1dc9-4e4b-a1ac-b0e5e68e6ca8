#ifndef REBOOTITEM_H
#define REBOOTITEM_H

#include "../testcasebase.h"
#include <QCheckBox>
#include <QComboBox>
#include <QLineEdit>
#include <QFormLayout>

class RebootItem : public TestCaseBase
{
    Q_OBJECT
public:
    RebootItem();

    QString type() const override { return "Reboot"; }
    static QString staticType() { return "Reboot"; }
    QString displayName() const override { return "Reboot"; }

    QWidget* createParameterWidget(QWidget* parent) override;

    QVariantMap parameters() const override { return m_params; }
    void setParameters(const QVariantMap& params) override { m_params = params; }
};

#endif // REBOOTITEM_H
