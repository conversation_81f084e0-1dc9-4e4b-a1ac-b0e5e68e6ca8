#ifndef INPUTCODEITEM_H
#define INPUTCODEITEM_H

#include "../testcasebase.h"
#include <QCheckBox>
#include <QComboBox>
#include <QLineEdit>
#include <QFormLayout>
#include <QPushButton>
#include <QGroupBox>

class InputCodeItem : public TestCaseBase
{
    Q_OBJECT
public:
    InputCodeItem();

    QString type() const override { return "InputCode"; }
    static QString staticType() { return "InputCode"; }
    QString displayName() const override { return "Input Code"; }

    QWidget* createParameterWidget(QWidget* parent) override;

    QVariantMap parameters() const override { return m_params; }
    void setParameters(const QVariantMap& params) override { m_params = params; }

signals:
    void xlsxFileSelected(const QString& filePath);

private slots:
    void selectXlsxFile();

private:
    QRegExpValidator* barcodeValidator = new QRegExpValidator(QRegExp("[A-Za-z0-9]{0,22}"), this);
    QRegExpValidator* imeiValidator = new QRegExpValidator(QRegExp("[0-9]{0,15}"), this);
    QRegExpValidator* btValidator = new QRegExpValidator(QRegExp("[A-Fa-f0-9]{0,12}"), this);
    QRegExpValidator* wifiValidator = new QRegExpValidator(QRegExp("[A-Fa-f0-9]{0,12}"), this);
    QRegExpValidator* meidValidator = new QRegExpValidator(QRegExp("[A-Za-z0-9]{0,14}"), this);
    QRegExpValidator* serialNoValidator = new QRegExpValidator(QRegExp("[A-Za-z0-9]{0,20}"), this);
    QRegExpValidator* netCodeValidator = new QRegExpValidator(QRegExp("[A-Za-z0-9]{0,21}"), this);
    QRegExpValidator* ethernetMacValidator = new QRegExpValidator(QRegExp("[A-Fa-f0-9]{0,12}"), this);
    QRegExpValidator* ScrilSfValidator = new QRegExpValidator(QRegExp("[A-Za-z0-9]{0,12}"), this);

    QLineEdit* m_xlsxPathLineEdit = nullptr;
    QString styleSheet = R"(
        QGroupBox {
            border: 0.5px solid;
            border-radius: 3px;
            padding: 3px;
            margin-top: 2.2ex;
        }
        QGroupBox::title {
            subcontrol-position: top left;
            left: 12px;
            top: -8px;
        }
    )";
};

#endif // INPUTCODEITEM_H
