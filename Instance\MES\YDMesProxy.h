#pragma once

#include "MesProxyBase.h"

class YDMesProxy : public MesProxyBase
{
public:
	YDMesProxy(void);
	~YDMesProxy(void);

    bool PreWork(MesDataUnit_s *unit, MesErrChar *errInfo);
    bool CheckSN(MesDataUnit_s *unit, MesErrChar *errInfo);
    bool QuerySN(MesDataUnit_s *unit, MesErrChar *errInfo);
    bool MarkWork(MesDataUnit_s *unit, MesErrChar *errInfo);

private:
    HINSTANCE m_hDLL;

	bool Dll_CheckSNStatus(char* IPadress, char* StationNo, char* SN, char* StationName, char* ResName, 
						char* Machine, char* Fixture, char* ErrorInfo);
	bool Dll_UpdateSNStatus(char* IPadress, char* StationNo, char* SN, char* StationName, char* ResName, 
						char* Machine, char* Fixture, char* result, char* Phenomenon, char* ErrorInfo);
	bool Dll_GetSNInfo(char* IPadress, char* SN, char* CSSN, char* BT, char* Wifi, 
						char* MEID, char* IMEI1, char* IMEI2, char* NetCode, char* ErrorInfo);
	bool Dll_UpdateMacStatus(char* IPadress, char* StationNo, char* SN, char* StationName, char* ResName, 
						char* Machine, char* Fixture, char* Mac, char* ErrorInfo);
};
