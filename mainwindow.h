#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QAction>
#include <QMenu>
#include <QFileDialog>
#include <QToolTip>
#include <QTreeWidget>
#include <QSplitter>
#include <QSettings>
#include <QMessageBox>
#include <QScrollArea>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QInputDialog>
#include <QLabel>
#include <QPushButton>
#include <QComboBox>
#include <QDialog>
#include <QFont>
#include <QListWidgetItem>
#include <QGroupBox>
#include <QListWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QSettings>
#include <QMenu>
#include <QInputDialog>
#include <QFile>
#include <QTableWidget>
#include <QTextEdit>
#include "UI/View/showlogdialog.h"
#include "UI/View/messettingdialog.h"
#include "UI/View/aboutdialog.h"
#include "UI/View/settingclass.h"
#include "UI/View/additemdialog.h"
#include "Cmd/MainWindowCallback.h"
#include "Common/Common.h"
#include "Instance/LoadXlsx/QxlsxInstance.h"
#include "Instance/TestItem/TestCase/InputCodeItem.h"
#include "3rdParty/KPHA-SDK-v5731/KPHAProxy.h"

QT_BEGIN_NAMESPACE
namespace Ui {
class MainWindow;
}
class ShowLogDialog;
class MesSettingDialog;
class CAboutDialog;
class SettingClass;
class AddItemDialog;
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

    void InitToolBar();
    void UpdateUIDisplay();
    void UpdateTestListItem(UINT nTreadID, QString m_ItemName, TestResult_Status m_Status, QString value="");
    void UpdateStartButton(UINT nTreadID);
    void InitItemListView(UINT nTreadID);
    void UpdateOperateResult(UINT nTreadID, TestResult_Status Status);
    void InitTestRate();
    void UpdateTestRate(UINT nTreadID, TestResult_Status Status);
    void SetTimeToLog(UINT nTreadID);
    void ResetTestTime();

    void EnableUIItem(UINT nTreadID);
    void DisableUIItem(UINT nTreadID);
    void ClearLogs(UINT nTreadID);
    void AppendLogs(UINT nTreadID, const QString log, LOGCOLOR_TYPE textcolor = BLACK);
    void UpdateBarcodeToUI(UINT nTreadID, QString m_Barcode);
    void UpdateUIPortInfo(UINT nTreadID);

    void SetLogFilePath(UINT nTreadID);
    void WriteLog(UINT nTreadID, QString log);
    void JudgeMesStatus(UINT nTreadID);

    void slot_start_timer(UINT nTreadID);

    void InitTestItemCaseUI();

    void loadTestCases();

    void InitTestItemParams();

    void setItemInfoParams();

    bool CheckScanData(const char *pStrHeader, bool bCheckHeader, const char *pInData, WriteData_Type_e dataType, UINT nThreadID);

    bool CheckYDScanData(const char *pStrHeader, bool bCheckHeader, const char *pInData, YDData_Type_e dataType, UINT nThreadID);

    bool WriteUIChange(UINT nThreadID);

    QListWidget* getListWidget() const;
	
	bool CheckSNFromMes(UINT nTreadID, QString barcode);

    bool GetCodesFromMes(UINT nTreadID, QString barcode);

    bool UpdateResultToMes(UINT nThreadID, TestResult_Status Status, QString barcode, UINT TestTime);

    void showAllWidgetsInGroupBox(QGroupBox* groupBox);

    void RefreshMesType();

    void CheckMouseFocus(UINT nTreadID);

    void bringToFront();

    void BringWindowToTop();

    void CheckCtrlBoxStatus(UINT m_nTreadID=0, bool bCheckStatus = true);

    QString stringOfBits(const QString &decimalString);

    QString removeSpecialCharacters(const QString& input);

    quint64 hexStringToUInt64(const QString &hexStr);

    QString uInt64ToHexString(quint64 value);

    QString addHexStrings(const QString &a, const QString &b);

    QString subtractHexStrings(const QString &a, const QString &b);

    int compareHexStrings(const QString &a, const QString &b);


private slots:
    void on_Dut1_Start_Button_clicked();

    void on_Dut2_Start_Button_clicked();

    void on_Dut3_Start_Button_clicked();

    void on_Dut4_Start_Button_clicked();

    void on_Log_Button_1_clicked();

    void on_Log_Button_2_clicked();

    void on_Log_Button_3_clicked();

    void on_Log_Button_4_clicked();

    void slot_UpdateTestTime1();

    void slot_UpdateTestTime2();

    void slot_UpdateTestTime3();

    void slot_UpdateTestTime4();

    void ShowTooltip(const QModelIndex &index);

    void Slot_actionAboutTriggered();

    void Slot_actionClearRecordTriggered();

    void checkMousePosition();

    void on_Mes_btn_clicked();

    void on_Home_btn_clicked();

    void on_Setting_btn_clicked();

    void on_CfgFile_btn_clicked();

    void on_LogPath_btn_clicked();

    void on_Save_btn_clicked();

    void on_Cancel_btn_clicked();

    void on_m_btnApDbBrowse_clicked();

    void on_m_btnMdDbBrowse_clicked();

    void updateParameterPanel();

    void showContextMenu(const QPoint& pos);

    void addTestCase();

    void removeTestCase();

    void on_JsonFile_btn_clicked();
	
    void onDut1EditReturnPressed();

    void onDut2EditReturnPressed();

    void onDut3EditReturnPressed();

    void onDut4EditReturnPressed();
    
    void onXlsxFileSelected(const QString& filePath);
    
    void onParametersChanged();
    
    bool checkUnsavedChanges();

    void onListItemChanged(QListWidgetItem* item);

    void Dut1CheckFixtureStatus();

    void Dut2CheckFixtureStatus();

    void Dut3CheckFixtureStatus();

    void Dut4CheckFixtureStatus();

    void on_UseRelay_checkBox_clicked();

    void on_UseScanGun_checkBox_clicked();

    void on_PowerType_comboBox_currentIndexChanged(int index);

    void on_UsbControl_comboBox_currentIndexChanged(int index);

private:
    void InitUIControlVariable();

    void InitDlgUIProp();

    void InitEditReturnFun();

    void RegTestItem();

    void saveTestCases();

    void createTestCase(const QString& type, const QVariantMap& params = {});

    void CheckMainUIInputDisplay();

    void showstatusbarInfo(QString Info, LOGCOLOR_TYPE color = BLACK);

    void initCodesXlsx();

    void InitScanDataFromXlsx(UINT nThreadID);

    void InitScanDataFromDafault(UINT nThreadID);

    bool InitScanDataFromSection(UINT nThreadID);

    bool InitScanDataFromYnSystem(UINT nThreadID);

    void InitRelayPort(UINT nTreadID);

    void InitScanGunPort(UINT nTreadID);

    void CheckTeeVersion();

    bool copyTeeDllFiles(const QString& version);
    QString createDllReplacementScript(const QString& version);
    void restartApplicationWithDllReplacement(const QString& version);

    //UI Control
    QVector<QLabel *> PreLoaderPortlable;
    QVector<QLabel *> KernelPortlable;
    QVector<QPushButton*> TestBotton;
    QVector<QGroupBox*> CodeGroups;
    QVector<QLineEdit*> barcodeEdits;
    QVector<QLineEdit*> imei1Edits;
    QVector<QLineEdit*> imei2Edits;
    QVector<QLineEdit*> wifiEdits;
    QVector<QLineEdit*> btEdits;
    QVector<QLineEdit*> meidEdits;
    QVector<QLineEdit*> EtherMacEdits;
    QVector<QLineEdit*> SerialNoEdits;
    QVector<QLineEdit*> NetCodeEdits;
    QVector<QLineEdit*> OBSNEdits;
    QVector<QLineEdit*> IWSNEdits;
    QVector<QLineEdit*> BatLabelEdits;
    QVector<QLineEdit*> ScrilSfEdits;
    QVector<QLabel*> barcodelabel;
    QVector<QLabel*> imei1label;
    QVector<QLabel*> imei2label;
    QVector<QLabel*> wifilabel;
    QVector<QLabel*> btlabel;
    QVector<QLabel*> meidlabel;
    QVector<QLabel*> EtherMaclabel;
    QVector<QLabel*> SerialNolabel;
    QVector<QLabel*> NetCodelabel;
    QVector<QLabel*> OBSNlabel;
    QVector<QLabel*> IWSNlabel;
    QVector<QLabel*> BatLabellabel;
    QVector<QLabel*> ScrilSflabel;
    QVector<QTableWidget *> ItemTableWidget;
    QVector<QTextEdit *> test_message;

    //Param
    bool settingflag[MAX_SUPPORT_COMPORT_NUM];
    QElapsedTimer m_start_clock[MAX_SUPPORT_COMPORT_NUM];
    QTimer *m_time_label_timer[MAX_SUPPORT_COMPORT_NUM];
    QString LogFilePath[MAX_SUPPORT_COMPORT_NUM];
    bool m_frameVisible;
    QWidget* m_paramPanel;
    QSplitter* m_splitter;
    QMap<QString, TestCaseBase*> m_testCases;
    char m_pstrScanItem[260] = { 0 };
    bool LoadXlsxFlag = false;
    bool LoadMesFlag = false;
    bool LoadDefaultFlag = false;
    bool LoadSection = false;
    bool LoadYnSystem = false;
    bool m_hasUnsavedChanges = false;

    QTimer *timer[MAX_SUPPORT_COMPORT_NUM];
    bool BoxStatus[MAX_SUPPORT_COMPORT_NUM] = {false, false, false, false};

private:
    Ui::MainWindow          *ui;
    ShowLogDialog           *m_showlogdialog;
    MainWindowCallback      *m_mainwindowcallback;
    MesSettingDialog        *m_messettingdialog;
    CAboutDialog            *m_aboutDialog;
    SettingClass            *m_settingclass;
    AddItemDialog           *m_additemdialog;
    QAction                 *m_pAboutAction;
    QAction                 *m_pClearRecordAction;
    QxlsxInstance           *m_xlsxInstance;
};
#endif // MAINWINDOW_H
