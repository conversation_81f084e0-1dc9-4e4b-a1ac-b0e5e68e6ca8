#ifndef CHARGECURRENTITEM_H
#define CHARGECURRENTITEM_H

#include "../testcasebase.h"
#include <QCheckBox>
#include <QComboBox>
#include <QLineEdit>
#include <QFormLayout>

class ChargeCurrentItem : public TestCaseBase
{
    Q_OBJECT
public:
    ChargeCurrentItem();

    QString type() const override { return "ChargeCurrent"; }
    static QString staticType() { return "ChargeCurrent"; }
    QString displayName() const override { return "Charge Current"; }

    QWidget* createParameterWidget(QWidget* parent) override;

    QVariantMap parameters() const override { return m_params; }
    void setParameters(const QVariantMap& params) override { m_params = params; }
};

#endif // CHARGECURRENTITEM_H
