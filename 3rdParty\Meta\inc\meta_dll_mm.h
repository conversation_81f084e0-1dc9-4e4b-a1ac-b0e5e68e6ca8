/*****************************************************************************
*  Copyright Statement:
*  --------------------
*  This software is protected by Copyright and the information contained
*  herein is confidential. The software may not be copied and the information
*  contained herein may not be used or disclosed except with the written
*  permission of MediaTek Inc. (C) 2001
*
*****************************************************************************/
/*******************************************************************************
 * Filename:
 * ---------
 * meta_mm.h
 *
 * Project:
 * --------
 *   META
 *
 * Description:
 * ------------
 *   This module contains the definitions for using META_DLL.dll.
 *
 * Author:
 * -------
 *  YH Sung (mtk02607)
 *
 *==============================================================================
 *           HISTORY
 * Below this line, this part is controlled by PVCS VM. DO NOT MODIFY!!
 *------------------------------------------------------------------------------
 *
 *------------------------------------------------------------------------------
 * Upper this line, this part is controlled by PVCS VM. DO NOT MODIFY!!
 *==============================================================================
 *******************************************************************************/
#ifndef __META_MM_H__
#define __META_MM_H__
#if !defined(__GNUC__) || !defined(WIN32)

// CCT
typedef struct
{
    char sensor_prefix_name[64];
} CCT_Dual_Dev_ISP_camera_sensor_prefix_name_struct;

typedef struct
{
    char lens_prefix_name[64];
} CCT_Dual_Dev_ISP_camera_lens_prefix_name_struct;
#endif // #if !defined(__GNUC__) || !defined(WIN32)
#endif // #ifndef __META_MM_H__
