#ifndef QXLSXINSTANCE_H
#define QXLSXINSTANCE_H

#include "Common/Common.h"
#include "xlsxdocument.h"
#include <QByteArray>

typedef struct {
    int i_barcode;
    int i_imei1;
    int i_imei2;
    int i_meid;
    int i_btmac;
    int i_wifimac;
    int i_ethernetmac;
    int i_serialno;
    int i_netcode;
} DataColumn_struct;

struct CodesInfo
{
    QString size;
    QString value;
};

struct Codes
{
    CodesInfo szBarcode;
    CodesInfo szIMEI1;
    CodesInfo szIMEI2;
    CodesInfo szMEID;
    CodesInfo szBtMac;
    CodesInfo szWifiMac;
    CodesInfo szEthernetMac;
    CodesInfo szSerialNo;
    CodesInfo szNetCode;
};

class QxlsxInstance
{
public:
    QxlsxInstance();
    virtual ~QxlsxInstance();
    static QSharedPointer<QxlsxInstance> getInstance();

    bool Init(QString datacache_path_, QString& ErrInfo);
    void InitData();

    Codes GetDataByBarcode(QString Barcode);

private:
    bool readTitle(QXlsx::Document *xlsx, QString& ErrInfo);

private:
    static QSharedPointer<QxlsxInstance> m_instance;

    QXlsx::Document *xlsx;
    DataColumn_struct _column;
    int rowCount;
    int columnCount;
    QMap<QString, Codes> Codedata;

private:

};

#endif // QXLSXINSTANCE_H
