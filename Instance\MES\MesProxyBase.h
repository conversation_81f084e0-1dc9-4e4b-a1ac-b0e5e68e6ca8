#pragma once

#include "MesDataUnit.h"
#include "Common/Common.h"
#include <Windows.h>
#include <shlwapi.h>
#include <string>
using namespace std;

class MesProxyBase
{
public:
	MesProxyBase(void);
	virtual ~MesProxyBase(void);

	virtual bool PreWork(MesDataUnit_s *unit, MesErrChar *errInfo);
	virtual bool CheckSN(MesDataUnit_s *unit, MesErrChar *errInfo);
	virtual bool QuerySN(MesDataUnit_s *unit, MesErrChar *errInfo);
	virtual bool MarkWork(MesDataUnit_s *unit, MesErrChar *errInfo);
};

