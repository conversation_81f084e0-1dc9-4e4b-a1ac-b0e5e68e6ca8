#ifndef TESTCASEFACTORY_H
#define TESTCASEFACTORY_H

#include <QMap>
#include <QStringList>
#include <functional>
#include "testcasebase.h"

class TestCaseFactory {
public:
    template<typename T>
    static void registerTestCase() {
        m_creators[T::staticType()] = [](){ return new T; };
    }

    static TestCaseBase* createTestCase(const QString& type) {
        if(m_creators.contains(type))
            return m_creators[type]();
        return nullptr;
    }
    
    static QStringList availableTestCaseTypes() {
        return m_creators.keys();
    }

private:
    static QMap<QString, std::function<TestCaseBase*()>> m_creators;
};

#endif // TESTCASEFACTORY_H
