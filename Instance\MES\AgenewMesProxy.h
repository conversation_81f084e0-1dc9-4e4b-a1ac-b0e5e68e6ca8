#pragma once

#include "MesProxyBase.h"

//------------------- Data structure for test tools
#define STR_SIZE_BASE   40    // All kinds of code, such as SN, ORDER
#define STR_SIZE_INFO   128*2     // Such as err msg, err items
#define STR_SIZE_LONG   256*4     // Extended information set, such as IMEI1+IMEI2+....


typedef char    ErrChar[STR_SIZE_LONG];

typedef struct
{
    char    ItemString[STR_SIZE_INFO];      // Test Items Not Passed, "TP,CAM".
    char    LongString[STR_SIZE_LONG];      // "IMEI:3574111122223333,ICCID:83605337981288888888"
} TooLTestDetail;

typedef struct
{
	// Order & SN
	char    Sn[STR_SIZE_BASE];
	char    OrderNo[STR_SIZE_BASE];
	char    StationCode[STR_SIZE_BASE];
	char    OperatorCode[STR_SIZE_BASE];
	int     QType; // If SN be wrote with IMEIs by TestTool or be banded with IMEIs by [MES Order Creating], set QType "0" or "1" to query back.
	// Update Station pass
	int     ElapsedTime;
	int     TestFlag;
	char    FailCode[STR_SIZE_BASE];
	char    FailMsg[STR_SIZE_INFO];
	TooLTestDetail  TestItems;
	//
	char    Ver[STR_SIZE_BASE];
	char    ProductName[STR_SIZE_BASE];
} TooLCallParam;

typedef bool (__stdcall *pABPREWORK)(TooLCallParam*, ErrChar*);
typedef bool (__stdcall *pABQUERYSN)(TooLCallParam*, ErrChar*);
typedef bool (__stdcall *pABCHECKSN)(TooLCallParam*, ErrChar*);
typedef bool (__stdcall *pABMARKWORK)(TooLCallParam*, ErrChar*);

class AgenewMesProxy : public MesProxyBase
{
public:
	AgenewMesProxy();
	~AgenewMesProxy();

    bool PreWork(MesDataUnit_s *unit, MesErrChar *errInfo);
    bool CheckSN(MesDataUnit_s *unit, MesErrChar *errInfo);
    bool QuerySN(MesDataUnit_s *unit, MesErrChar *errInfo);
    bool MarkWork(MesDataUnit_s *unit, MesErrChar *errInfo);

private:
    HINSTANCE m_hDLL;

	bool AbPreWork(TooLCallParam* param, ErrChar* errInfo);
	bool AbCheckSN(TooLCallParam* param, ErrChar* errInfo);
	bool AbQuerySN(TooLCallParam* param, ErrChar* errInfo);
	bool AbMarkWork(TooLCallParam* param, ErrChar* errInfo);
};
