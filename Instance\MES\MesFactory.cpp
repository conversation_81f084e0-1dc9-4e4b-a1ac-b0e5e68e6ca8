#include "MesFactory.h"
#include "AgenewMesProxy.h"
#include "BirdMesProxy.h"
#include "YDMesProxy.h"

#include <stdlib.h>
#include <Shlwapi.h>
#include <WinBase.h>

MesProvider_e MesFactory::m_MesProvider = AGENEW;

MesProxyBase* MesFactory::GetMesProxy()
{
	MesProxyBase *proxy = nullptr;

    QString type = Common::getInstance()->getMesType();
    if(type == "SD_MES")
    {
        m_MesProvider = BIRD;
    }
    else if(type == "AGN_MES")
    {
        m_MesProvider = AGENEW;
    }
    else if(type == "YD_MES")
    {
        m_MesProvider = YD;
    }

	switch(m_MesProvider)
	{
	case BIRD:
		proxy = new BirdMesProxy();
		break;
	case AGENEW:
		proxy = new AgenewMesProxy();
		break;
    case YD:
        proxy = new YDMesProxy();
        break;
	default:
		proxy = new MesProxyBase();
		break;
	}

	return proxy;
}

MesProvider_e MesFactory::GetMesProvider()
{
	return m_MesProvider;
}
