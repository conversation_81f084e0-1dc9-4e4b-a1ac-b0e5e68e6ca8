#ifndef SNBASE_H
#define SNBASE_H

#include <QObject>
#include <QThread>
#include "Common/Common.h"

class SNBase : public QThread
{
    Q_OBJECT
public:
    SNBase(QObject *parent = nullptr, UINT nThreadID = 0);

    void SNThread_Init();

    META_RESULT ConductBarcodeData(char *pOutData, unsigned short RID_para, char *pInDatabuf, int outBufSize);

    META_RESULT ConductIMEIData(char *pOutData, unsigned short RID_para, char *pInDatabuf, int outBufSize);

    META_RESULT ConductBTAddrData(char *pOutData, unsigned short RID_para, char *pInDatabuf, int outBufSize);

    META_RESULT ConductWifiAddrData(char *pOutData, unsigned short RID_para, char *pInDatabuf, int outBufSize);

    META_RESULT ConductEthernetMacAddrData(char *pOutData, unsigned short RID_para, char *pInDatabuf, int outBufSize);

    META_RESULT ConductMEIDData(char *pOutData, unsigned short RID_para, char *pInDatabuf, int outBufSize);

    META_RESULT Decompose_IMEI(char *pOutData, unsigned short RID_para,  char *pInDatabuf, int bufSize);

    META_RESULT Compose_IMEI(char *pOutData, unsigned short RID_para, char *pInDatabuf, int bufSize, bool bChecksum);

    META_RESULT REQ_ReadFromModemNVRAM(FT_NVRAM_READ_REQ *psNVRAM_ReadReq, FT_NVRAM_4BYTES_LID_READ_CNF *psNVRAM_ReadCnf);

    META_RESULT REQ_WriteToModemNVRAM(FT_NVRAM_WRITE_REQ *psNVRAM_WriteReq, FT_NVRAM_4BYTES_LID_WRITE_CNF *psNVRAM_WriteCnf);

    ScanData_struct m_sScanData;

protected:
    IMEISV_struct_T m_sIMEISV[MAX_IMEI_NUMS];

    int             m_hMauiMetaHandle;
    short           m_sNVRAM_OPID;
    bool            m_bWriteNvram;

    HANDLE  m_hWriteToNVRAMEvent;

private:
    UINT            m_nThreadID;

};

#endif // SNBASE_H
