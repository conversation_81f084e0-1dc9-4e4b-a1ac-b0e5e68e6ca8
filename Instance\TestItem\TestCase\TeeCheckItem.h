#ifndef TEECHECKITEM_H
#define TEECHECKITEM_H

#include "../testcasebase.h"
#include <QCheckBox>
#include <QComboBox>
#include <QLineEdit>
#include <QFormLayout>

class TeeCheckItem : public TestCaseBase
{
    Q_OBJECT
public:
    TeeCheckItem();

    QString type() const override { return "TeeCheck"; }
    static QString staticType() { return "TeeCheck"; }
    QString displayName() const override { return "Tee Check"; }

    QWidget* createParameterWidget(QWidget* parent) override;

    QVariantMap parameters() const override { return m_params; }
    void setParameters(const QVariantMap& params) override { m_params = params; }
};

#endif // TEECHECKITEM_H
