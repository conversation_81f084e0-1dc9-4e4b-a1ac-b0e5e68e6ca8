#ifndef POWEROFFCURRENTITEM_H
#define POWEROFFCURRENTITEM_H

#include "../testcasebase.h"
#include <QCheckBox>
#include <QComboBox>
#include <QLineEdit>
#include <QFormLayout>

class PowerOffCurrentItem : public TestCaseBase
{
    Q_OBJECT
public:
    PowerOffCurrentItem();

    QString type() const override { return "PowerOffCurrent"; }
    static QString staticType() { return "PowerOffCurrent"; }
    QString displayName() const override { return "PowerOff Current"; }

    QWidget* createParameterWidget(QWidget* parent) override;

    QVariantMap parameters() const override { return m_params; }
    void setParameters(const QVariantMap& params) override { m_params = params; }
};

#endif // POWEROFFCURRENTITEM_H
