#include "MainWindow.h"
#include "settingclass.h"
#include "ui_mainwindow.h"
#include "./Common/Common.h"

SettingClass::SettingClass(MainWindow *mainwindow, Ui::MainWindow *MainUI)
    : m_mainwindow(mainwindow)
    , m_ui(MainUI)
{

}

void SettingClass::InitDlgUIProp()
{
    QIntValidator *IntValidator = new QIntValidator(0, 10000, this);
    m_ui->GPIBID_lineEdit_Dut1->setValidator(IntValidator);
    m_ui->Address_lineEdit_Dut1->setValidator(IntValidator);
    m_ui->GPIBID_lineEdit_Dut2->setValidator(IntValidator);
    m_ui->Address_lineEdit_Dut2->setValidator(IntValidator);
    m_ui->GPIBID_lineEdit_Dut3->setValidator(IntValidator);
    m_ui->Address_lineEdit_Dut3->setValidator(IntValidator);
    m_ui->GPIBID_lineEdit_Dut4->setValidator(IntValidator);
    m_ui->Address_lineEdit_Dut4->setValidator(IntValidator);
    m_ui->Dut1_BluPort_lineEdit->setValidator(IntValidator);
    m_ui->Dut2_BluPort_lineEdit->setValidator(IntValidator);
    m_ui->Dut3_BluPort_lineEdit->setValidator(IntValidator);
    m_ui->Dut4_BluPort_lineEdit->setValidator(IntValidator);

    QDoubleValidator *DoubleValidator = new QDoubleValidator(0.00, 10000.00, 2, this);
    m_ui->Output1_Vol_lineEdit_Dut1->setValidator(DoubleValidator);
    m_ui->Output1_Cur_lineEdit_Dut1->setValidator(DoubleValidator);
    m_ui->Output2_Vol_lineEdit_Dut1->setValidator(DoubleValidator);
    m_ui->Output2_Cur_lineEdit_Dut1->setValidator(DoubleValidator);
    m_ui->Output1_Vol_lineEdit_Dut2->setValidator(DoubleValidator);
    m_ui->Output1_Cur_lineEdit_Dut2->setValidator(DoubleValidator);
    m_ui->Output2_Vol_lineEdit_Dut2->setValidator(DoubleValidator);
    m_ui->Output2_Cur_lineEdit_Dut2->setValidator(DoubleValidator);
    m_ui->Output1_Vol_lineEdit_Dut3->setValidator(DoubleValidator);
    m_ui->Output1_Cur_lineEdit_Dut3->setValidator(DoubleValidator);
    m_ui->Output2_Vol_lineEdit_Dut3->setValidator(DoubleValidator);
    m_ui->Output2_Cur_lineEdit_Dut3->setValidator(DoubleValidator);
    m_ui->Output1_Vol_lineEdit_Dut4->setValidator(DoubleValidator);
    m_ui->Output1_Cur_lineEdit_Dut4->setValidator(DoubleValidator);
    m_ui->Output2_Vol_lineEdit_Dut4->setValidator(DoubleValidator);
    m_ui->Output2_Cur_lineEdit_Dut4->setValidator(DoubleValidator);
    m_ui->Dut1_BluVol_lineEdit->setValidator(DoubleValidator);
    m_ui->Dut2_BluVol_lineEdit->setValidator(DoubleValidator);
    m_ui->Dut3_BluVol_lineEdit->setValidator(DoubleValidator);
    m_ui->Dut4_BluVol_lineEdit->setValidator(DoubleValidator);
}

void SettingClass::InitSettings()
{
    InitDlgUIProp();
    Common::getInstance()->loadCfgFile(Common::getInstance()->getCfgFilePath());
    m_ui->WifiOnly_checkBox->setChecked(Common::getInstance()->getWifionlyEnable());
    m_ui->EnableAdb_checkBox->setChecked(Common::getInstance()->getADBServiceEnable());
    m_ui->LoadAPDBFormDut_checkbox->setChecked(Common::getInstance()->getLoadApFromDutEnable());
    m_ui->LoadMDDBFormDut_checkbox->setChecked(Common::getInstance()->getLoadMdFromDutEnable());
    m_ui->m_edtApDbFile->setText(Common::getInstance()->getAPDbPath());
    m_ui->m_edtMdDbFile->setText(Common::getInstance()->getMdDbPath());

    m_ui->Dut1_checkBox->setChecked(Common::getInstance()->getActiveEnable(0));
    m_ui->Dut2_checkBox->setChecked(Common::getInstance()->getActiveEnable(1));
    m_ui->Dut3_checkBox->setChecked(Common::getInstance()->getActiveEnable(2));
    m_ui->Dut4_checkBox->setChecked(Common::getInstance()->getActiveEnable(3));

	QString TempAddress = Common::getInstance()->getDutGPIBAddress(0);
    m_ui->GPIBID_lineEdit_Dut1->setText(TempAddress.left(TempAddress.indexOf("::")).mid(4));
    m_ui->Address_lineEdit_Dut1->setText(TempAddress.mid(TempAddress.indexOf("::")+2).left(TempAddress.mid(TempAddress.indexOf("::")+2).indexOf("::")));
    m_ui->Output1_Vol_lineEdit_Dut1->setText(QString::number(Common::getInstance()->getDutOutput1Vol(0)));
    m_ui->Output2_Vol_lineEdit_Dut1->setText(QString::number(Common::getInstance()->getDutOutput2Vol(0)));
    m_ui->Output1_Cur_lineEdit_Dut1->setText(QString::number(Common::getInstance()->getDutOutput1Current(0)));
    m_ui->Output2_Cur_lineEdit_Dut1->setText(QString::number(Common::getInstance()->getDutOutput2Current(0)));
    m_ui->Dut1_BluPort_lineEdit->setText(QString::number(Common::getInstance()->getDutBluPort(0)));
    m_ui->Dut1_BluVol_lineEdit->setText(QString::number(Common::getInstance()->getDutBluOutputVol(0)));

    TempAddress = Common::getInstance()->getDutGPIBAddress(1);
    m_ui->GPIBID_lineEdit_Dut2->setText(TempAddress.left(TempAddress.indexOf("::")).mid(4));
    m_ui->Address_lineEdit_Dut2->setText(TempAddress.mid(TempAddress.indexOf("::")+2).left(TempAddress.mid(TempAddress.indexOf("::")+2).indexOf("::")));
    m_ui->Output1_Vol_lineEdit_Dut2->setText(QString::number(Common::getInstance()->getDutOutput1Vol(1)));
    m_ui->Output2_Vol_lineEdit_Dut2->setText(QString::number(Common::getInstance()->getDutOutput2Vol(1)));
    m_ui->Output1_Cur_lineEdit_Dut2->setText(QString::number(Common::getInstance()->getDutOutput1Current(1)));
    m_ui->Output2_Cur_lineEdit_Dut2->setText(QString::number(Common::getInstance()->getDutOutput2Current(1)));
    m_ui->Dut2_BluPort_lineEdit->setText(QString::number(Common::getInstance()->getDutBluPort(1)));
    m_ui->Dut2_BluVol_lineEdit->setText(QString::number(Common::getInstance()->getDutBluOutputVol(1)));

    TempAddress = Common::getInstance()->getDutGPIBAddress(2);
    m_ui->GPIBID_lineEdit_Dut3->setText(TempAddress.left(TempAddress.indexOf("::")).mid(4));
    m_ui->Address_lineEdit_Dut3->setText(TempAddress.mid(TempAddress.indexOf("::")+2).left(TempAddress.mid(TempAddress.indexOf("::")+2).indexOf("::")));
    m_ui->Output1_Vol_lineEdit_Dut3->setText(QString::number(Common::getInstance()->getDutOutput1Vol(2)));
    m_ui->Output2_Vol_lineEdit_Dut3->setText(QString::number(Common::getInstance()->getDutOutput2Vol(2)));
    m_ui->Output1_Cur_lineEdit_Dut3->setText(QString::number(Common::getInstance()->getDutOutput1Current(2)));
    m_ui->Output2_Cur_lineEdit_Dut3->setText(QString::number(Common::getInstance()->getDutOutput2Current(2)));
    m_ui->Dut3_BluPort_lineEdit->setText(QString::number(Common::getInstance()->getDutBluPort(2)));
    m_ui->Dut3_BluVol_lineEdit->setText(QString::number(Common::getInstance()->getDutBluOutputVol(2)));

    TempAddress = Common::getInstance()->getDutGPIBAddress(3);
    m_ui->GPIBID_lineEdit_Dut4->setText(TempAddress.left(TempAddress.indexOf("::")).mid(4));
    m_ui->Address_lineEdit_Dut4->setText(TempAddress.mid(TempAddress.indexOf("::")+2).left(TempAddress.mid(TempAddress.indexOf("::")+2).indexOf("::")));
    m_ui->Output1_Vol_lineEdit_Dut4->setText(QString::number(Common::getInstance()->getDutOutput1Vol(3)));
    m_ui->Output2_Vol_lineEdit_Dut4->setText(QString::number(Common::getInstance()->getDutOutput2Vol(3)));
    m_ui->Output1_Cur_lineEdit_Dut4->setText(QString::number(Common::getInstance()->getDutOutput1Current(3)));
    m_ui->Output2_Cur_lineEdit_Dut4->setText(QString::number(Common::getInstance()->getDutOutput2Current(3)));
    m_ui->Dut4_BluPort_lineEdit->setText(QString::number(Common::getInstance()->getDutBluPort(3)));
    m_ui->Dut4_BluVol_lineEdit->setText(QString::number(Common::getInstance()->getDutBluOutputVol(3)));
	
    m_ui->CfgFile_lineEdit->setText(Common::getInstance()->getCfgFilePath());
    m_ui->JsonFile_lineEdit->setText(Common::getInstance()->getJsonFilePath());
    m_ui->LogPath_lineEdit->setText(Common::getInstance()->getLogPath());

    m_ui->UseRelay_checkBox->setChecked(Common::getInstance()->getUseRelayEnable());
    m_ui->UseScanGun_checkBox->setChecked(Common::getInstance()->getUseScanGunEnable());

    m_ui->UsbControl_comboBox->setCurrentIndex((int)Common::getInstance()->getUsbControlType());
    m_ui->PowerType_comboBox->setCurrentIndex((int)Common::getInstance()->getPowerType());
    m_ui->Ouput2_groupBox_1->setVisible(Common::getInstance()->getPowerType() == PowerType::Dual);
    m_ui->Ouput2_groupBox_2->setVisible(Common::getInstance()->getPowerType() == PowerType::Dual);
    m_ui->Ouput2_groupBox_3->setVisible(Common::getInstance()->getPowerType() == PowerType::Dual);
    m_ui->Ouput2_groupBox_4->setVisible(Common::getInstance()->getPowerType() == PowerType::Dual);
	
    InitPortListView();
}

void SettingClass::SaveSettings()
{
    Common::getInstance()->setCfgFilePath(m_ui->CfgFile_lineEdit->text());
    Common::getInstance()->setJsonFilePath(m_ui->JsonFile_lineEdit->text());
    Common::getInstance()->setLogPath(m_ui->LogPath_lineEdit->text());
    Common::getInstance()->setWifionlyEnable(m_ui->WifiOnly_checkBox->isChecked());
    Common::getInstance()->setADBServiceEnable(m_ui->EnableAdb_checkBox->isChecked());
    Common::getInstance()->setLoadApFromDutEnable(m_ui->LoadAPDBFormDut_checkbox->isChecked());
    Common::getInstance()->setLoadMdFromDutEnable(m_ui->LoadMDDBFormDut_checkbox->isChecked());
    Common::getInstance()->setAPDbPath(m_ui->m_edtApDbFile->text());
    qDebug() << "SettingDialog::SaveSettings: set AP Db Path is " << m_ui->m_edtApDbFile->text();
    Common::getInstance()->setMdDbPath(m_ui->m_edtMdDbFile->text());
    qDebug() << "SettingDialog::SaveSettings: set Md Db Path is " << m_ui->m_edtMdDbFile->text();

    Common::getInstance()->setActiveEnable(0, m_ui->Dut1_checkBox->isChecked());
    Common::getInstance()->setActiveEnable(1, m_ui->Dut2_checkBox->isChecked());
    Common::getInstance()->setActiveEnable(2, m_ui->Dut3_checkBox->isChecked());
    Common::getInstance()->setActiveEnable(3, m_ui->Dut4_checkBox->isChecked());

    Common::getInstance()->setDutPort(0, PRELOADER_COM,  m_ui->PortInfo_tableView->model()->index(0, 0).data().toString().toInt());
    Common::getInstance()->setDutPort(0, KERNEL_COM,  m_ui->PortInfo_tableView->model()->index(0, 1).data().toString().toInt());
    Common::getInstance()->setDutPort(0, CONTROLBOX_COM,  m_ui->PortInfo_tableView->model()->index(0, 2).data().toString().toInt());
    Common::getInstance()->setDutPort(0, SCANGUN_COM, m_ui->PortInfo_tableView->model()->index(0, 3).data().toString().toInt());
    Common::getInstance()->setDutPort(1, PRELOADER_COM,  m_ui->PortInfo_tableView->model()->index(1, 0).data().toString().toInt());
    Common::getInstance()->setDutPort(1, KERNEL_COM,  m_ui->PortInfo_tableView->model()->index(1, 1).data().toString().toInt());
    Common::getInstance()->setDutPort(1, CONTROLBOX_COM,  m_ui->PortInfo_tableView->model()->index(1, 2).data().toString().toInt());
    Common::getInstance()->setDutPort(1, SCANGUN_COM, m_ui->PortInfo_tableView->model()->index(1, 3).data().toString().toInt());
    Common::getInstance()->setDutPort(2, PRELOADER_COM,  m_ui->PortInfo_tableView->model()->index(2, 0).data().toString().toInt());
    Common::getInstance()->setDutPort(2, KERNEL_COM,  m_ui->PortInfo_tableView->model()->index(2, 1).data().toString().toInt());
    Common::getInstance()->setDutPort(2, CONTROLBOX_COM,  m_ui->PortInfo_tableView->model()->index(2, 2).data().toString().toInt());
    Common::getInstance()->setDutPort(2, SCANGUN_COM, m_ui->PortInfo_tableView->model()->index(2, 3).data().toString().toInt());
    Common::getInstance()->setDutPort(3, PRELOADER_COM,  m_ui->PortInfo_tableView->model()->index(3, 0).data().toString().toInt());
    Common::getInstance()->setDutPort(3, KERNEL_COM,  m_ui->PortInfo_tableView->model()->index(3, 1).data().toString().toInt());
    Common::getInstance()->setDutPort(3, CONTROLBOX_COM,  m_ui->PortInfo_tableView->model()->index(3, 2).data().toString().toInt());
    Common::getInstance()->setDutPort(3, SCANGUN_COM, m_ui->PortInfo_tableView->model()->index(3, 3).data().toString().toInt());
    Common::getInstance()->setDutBluPort(0, m_ui->Dut1_BluPort_lineEdit->text().toInt());
    Common::getInstance()->setDutBluPort(1, m_ui->Dut2_BluPort_lineEdit->text().toInt());
    Common::getInstance()->setDutBluPort(2, m_ui->Dut3_BluPort_lineEdit->text().toInt());
    Common::getInstance()->setDutBluPort(3, m_ui->Dut4_BluPort_lineEdit->text().toInt());

    Common::getInstance()->setDutGPIBAddress(0, QString("GPIB%1::%2::INSTR").arg(m_ui->GPIBID_lineEdit_Dut1->text()).arg(m_ui->Address_lineEdit_Dut1->text()));
    Common::getInstance()->setDutGPIBAddress(1, QString("GPIB%1::%2::INSTR").arg(m_ui->GPIBID_lineEdit_Dut2->text()).arg(m_ui->Address_lineEdit_Dut2->text()));
    Common::getInstance()->setDutGPIBAddress(2, QString("GPIB%1::%2::INSTR").arg(m_ui->GPIBID_lineEdit_Dut3->text()).arg(m_ui->Address_lineEdit_Dut3->text()));
    Common::getInstance()->setDutGPIBAddress(3, QString("GPIB%1::%2::INSTR").arg(m_ui->GPIBID_lineEdit_Dut4->text()).arg(m_ui->Address_lineEdit_Dut4->text()));
    Common::getInstance()->setDutOutput1Vol(0, m_ui->Output1_Vol_lineEdit_Dut1->text().toFloat());
    Common::getInstance()->setDutOutput1Vol(1, m_ui->Output1_Vol_lineEdit_Dut2->text().toFloat());
    Common::getInstance()->setDutOutput1Vol(2, m_ui->Output1_Vol_lineEdit_Dut3->text().toFloat());
    Common::getInstance()->setDutOutput1Vol(3, m_ui->Output1_Vol_lineEdit_Dut4->text().toFloat());
    Common::getInstance()->setDutOutput2Vol(0, m_ui->Output2_Vol_lineEdit_Dut1->text().toFloat());
    Common::getInstance()->setDutOutput2Vol(1, m_ui->Output2_Vol_lineEdit_Dut2->text().toFloat());
    Common::getInstance()->setDutOutput2Vol(2, m_ui->Output2_Vol_lineEdit_Dut3->text().toFloat());
    Common::getInstance()->setDutOutput2Vol(3, m_ui->Output2_Vol_lineEdit_Dut4->text().toFloat());
    Common::getInstance()->setDutOutput1Current(0, m_ui->Output1_Cur_lineEdit_Dut1->text().toFloat());
    Common::getInstance()->setDutOutput1Current(1, m_ui->Output1_Cur_lineEdit_Dut2->text().toFloat());
    Common::getInstance()->setDutOutput1Current(2, m_ui->Output1_Cur_lineEdit_Dut3->text().toFloat());
    Common::getInstance()->setDutOutput1Current(3, m_ui->Output1_Cur_lineEdit_Dut4->text().toFloat());
    Common::getInstance()->setDutOutput2Current(0, m_ui->Output2_Cur_lineEdit_Dut1->text().toFloat());
    Common::getInstance()->setDutOutput2Current(1, m_ui->Output2_Cur_lineEdit_Dut2->text().toFloat());
    Common::getInstance()->setDutOutput2Current(2, m_ui->Output2_Cur_lineEdit_Dut3->text().toFloat());
    Common::getInstance()->setDutOutput2Current(3, m_ui->Output2_Cur_lineEdit_Dut4->text().toFloat());
    Common::getInstance()->setDutBluOutputVol(0, m_ui->Dut1_BluVol_lineEdit->text().toFloat());
    Common::getInstance()->setDutBluOutputVol(1, m_ui->Dut2_BluVol_lineEdit->text().toFloat());
    Common::getInstance()->setDutBluOutputVol(2, m_ui->Dut3_BluVol_lineEdit->text().toFloat());
    Common::getInstance()->setDutBluOutputVol(3, m_ui->Dut4_BluVol_lineEdit->text().toFloat());

    Common::getInstance()->setUseRelayEnable(m_ui->UseRelay_checkBox->isChecked());
    Common::getInstance()->setUseScanGunEnable(m_ui->UseScanGun_checkBox->isChecked());

    Common::getInstance()->setPowerType((PowerType)m_ui->PowerType_comboBox->currentIndex());
    Common::getInstance()->setUsbControlType((UsbControlType)m_ui->UsbControl_comboBox->currentIndex());
}

void SettingClass::InitPortListView()
{
    QStandardItemModel *model = new QStandardItemModel(m_ui->PortInfo_tableView);

    // 设置默认对齐方式为居中
    m_ui->PortInfo_tableView->horizontalHeader()->setDefaultAlignment(Qt::AlignCenter);
    // 设置表格视图为可编辑
    m_ui->PortInfo_tableView->setEditTriggers(QAbstractItemView::DoubleClicked | QAbstractItemView::SelectedClicked);
    m_ui->PortInfo_tableView->horizontalHeader()->setDefaultSectionSize(150);

    model->setVerticalHeaderItem(0, new QStandardItem("  Dut1  "));
    model->setVerticalHeaderItem(1, new QStandardItem("  Dut2  "));
    model->setVerticalHeaderItem(2, new QStandardItem("  Dut3  "));
    model->setVerticalHeaderItem(3, new QStandardItem("  Dut4  "));
    model->setHorizontalHeaderItem(0, new QStandardItem("Preloader"));
    model->setHorizontalHeaderItem(1, new QStandardItem("Kernel"));
    model->setHorizontalHeaderItem(2, new QStandardItem("ControlBox"));
    model->setHorizontalHeaderItem(3, new QStandardItem("ScanGun"));

    model->setItem(0, 0, new QStandardItem(QString::number(Common::getInstance()->getDutPort(0, PRELOADER_COM))));
    model->setItem(0, 1, new QStandardItem(QString::number(Common::getInstance()->getDutPort(0, KERNEL_COM))));
    model->setItem(0, 2, new QStandardItem(QString::number(Common::getInstance()->getDutPort(0, CONTROLBOX_COM))));
    model->setItem(0, 3, new QStandardItem(QString::number(Common::getInstance()->getDutPort(0, SCANGUN_COM))));
    model->setItem(1, 0, new QStandardItem(QString::number(Common::getInstance()->getDutPort(1, PRELOADER_COM))));
    model->setItem(1, 1, new QStandardItem(QString::number(Common::getInstance()->getDutPort(1, KERNEL_COM))));
    model->setItem(1, 2, new QStandardItem(QString::number(Common::getInstance()->getDutPort(1, CONTROLBOX_COM))));
    model->setItem(1, 3, new QStandardItem(QString::number(Common::getInstance()->getDutPort(1, SCANGUN_COM))));
    model->setItem(2, 0, new QStandardItem(QString::number(Common::getInstance()->getDutPort(2, PRELOADER_COM))));
    model->setItem(2, 1, new QStandardItem(QString::number(Common::getInstance()->getDutPort(2, KERNEL_COM))));
    model->setItem(2, 2, new QStandardItem(QString::number(Common::getInstance()->getDutPort(2, CONTROLBOX_COM))));
    model->setItem(2, 3, new QStandardItem(QString::number(Common::getInstance()->getDutPort(2, SCANGUN_COM))));
    model->setItem(3, 0, new QStandardItem(QString::number(Common::getInstance()->getDutPort(3, PRELOADER_COM))));
    model->setItem(3, 1, new QStandardItem(QString::number(Common::getInstance()->getDutPort(3, KERNEL_COM))));
    model->setItem(3, 2, new QStandardItem(QString::number(Common::getInstance()->getDutPort(3, CONTROLBOX_COM))));
    model->setItem(3, 3, new QStandardItem(QString::number(Common::getInstance()->getDutPort(3, SCANGUN_COM))));

    for(int row=0; row < MAX_SUPPORT_COMPORT_NUM; row++)
    {
        for(int col=0; col < 4; col++)
        {
            model->item(row, col)->setTextAlignment(Qt::AlignCenter);//居中
        }
    }

    m_ui->PortInfo_tableView->setModel(model);
}
