#include "ReadSwVerItem.h"

ReadSwVerItem::ReadSwVerItem()
{
    m_params = {
        {"System Property", "ro.build.display.id"},
        {"verify", ""}
    };
}

QWidget* ReadSwVerItem::createParameterWidget(QWidget* parent)
{
    QWidget* widget = new QWidget(parent);
    QFormLayout* layout = new QFormLayout(widget);
    widget->setStyleSheet(R"(font: 9pt "Arial";)");

    QLineEdit* systemPropEdit = new QLineEdit(widget);
    systemPropEdit->setText(m_params["System Property"].toString());
    connect(systemPropEdit, &QLineEdit::textChanged, [this](const QString& text) {
        m_params["System Property"] = text;
        emit parametersChanged();
    });
    layout->addRow("System Property:", systemPropEdit);

    QLineEdit* verifyEdit = new QLineEdit(widget);
    verifyEdit->setText(m_params["verify"].toString());
    connect(verifyEdit, &QLineEdit::textChanged, [this](const QString& text) {
        m_params["verify"] = text;
        emit parametersChanged();
    });
    layout->addRow("verify:", verifyEdit);

    return widget;
}
