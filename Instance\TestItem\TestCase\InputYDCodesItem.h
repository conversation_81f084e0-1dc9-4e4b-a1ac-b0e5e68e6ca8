#ifndef INPUTYDCODESITEM_H
#define INPUTYDCODESITEM_H

#include "../testcasebase.h"
#include <QCheckBox>
#include <QComboBox>
#include <QLineEdit>
#include <QFormLayout>
#include <QPushButton>
#include <QGroupBox>

class InputYDCodesItem : public TestCaseBase
{
    Q_OBJECT
public:
    InputYDCodesItem();

    QString type() const override { return "InputYDCodes"; }
    static QString staticType() { return "InputYDCodes"; }
    QString displayName() const override { return "Input YD Codes"; }

    QWidget* createParameterWidget(QWidget* parent) override;

    QVariantMap parameters() const override { return m_params; }
    void setParameters(const QVariantMap& params) override { m_params = params; }

private:
    QRegExpValidator* UINTValidator = new QRegExpValidator(QRegExp("[0-9]{0,2}"), this);
    QRegExpValidator* STRINGValidator = new QRegExpValidator(QRegExp("[A-Za-z0-9]{0,64}"), this);

    QString styleSheet = R"(
        QGroupBox {
            border: 0.5px solid;
            border-radius: 3px;
            padding: 3px;
            margin-top: 2.2ex;
        }
        QGroupBox::title {
            subcontrol-position: top left;
            left: 12px;
            top: -8px;
        }
    )";

};

#endif // INPUTYDCODESITEM_H
