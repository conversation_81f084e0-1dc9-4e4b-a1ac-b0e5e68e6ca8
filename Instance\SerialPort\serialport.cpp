#include "serialport.h"

SerialPort::SerialPort(QObject *parent, UINT nTreadID, QSerialPort* PortServer)
	: QThread(parent)
    , m_nTreadID(nTreadID)
{
    global_port = PortServer;
}

SerialPort::~SerialPort(void)
{
}

void SerialPort::UpdateUIMsg(QString log, LOGCOLOR_TYPE textcolor)
{
    emit signal_UpdateUILog(m_nTreadID, QString("[SerialPort] %1").arg(log), textcolor);
}

bool SerialPort::Init(UINT port, QSerialPort::BaudRate BaudRate)
{
    global_port->close();
    //port name 设置端口
    global_port->setPortName(QString("\\\\.\\COM%1").arg(port));
    //baud rate 设置波特率
    global_port->setBaudRate(BaudRate);
    //parity 设置校验位
    global_port->setParity(QSerialPort::NoParity);
    //data bits 设置数据位
    global_port->setDataBits(QSerialPort::Data8);
    //stop bits 设置停止位
    global_port->setStopBits(QSerialPort::OneStop);
    //Flow Control 设置流控
    global_port->setFlowControl(QSerialPort::NoFlowControl);

    //port open 打开选择端口
    bool ret = global_port->open(QIODevice::ReadWrite);

    if(ret){
        QString sm = "%1 OPENED, %2, 8, NONE, 1";
        QString status = sm.arg(global_port->portName()).arg(global_port->baudRate());
        qDebug() << __FUNCTION__ << ":" << status;
        UpdateUIMsg(status, GREEN);
    }
    else
    {
        QString sm = "%1 CONNECT FAIL! ErrorInfo: " + global_port->errorString();
        QString status = sm.arg(global_port->portName());
        qDebug() << __FUNCTION__ << ":" << status;
        UpdateUIMsg(status, RED);
        return false;
    }

    return true;
}

bool SerialPort::DeInit()
{
    global_port->close();

    QString sm = "%1 CLOSED";
    QString status = sm.arg(global_port->portName());
    UpdateUIMsg(status, RED);

    qDebug() << __FUNCTION__ << ":" << status;
    return true;
}

QString SerialPort::CheckStatus(QString Cmd)
{
    qDebug() << __FUNCTION__ << " Write: " << Cmd;
    //UpdateUIMsg("Write: " + Cmd);

    QByteArray data;
    int retry = 10;
    global_port->clear(QSerialPort::Input);
    global_port->write(QByteArray::fromHex(Cmd.toLatin1()));
    while (global_port->waitForReadyRead(100) && retry > 0)
    {
        data = data + global_port->readAll();
        retry--;

        if(data.length() == 6)
            break;
    }

    qDebug() << __FUNCTION__ << " Read: " << QString(data.toHex()).toUpper();
    //UpdateUIMsg("Write: " + Cmd);
    return QString(data.toHex()).toUpper();
}

void SerialPort::ControlScanGun()
{
    QString Cmd = ECOMMANDStr[E_START_SCAN];
    qDebug() << __FUNCTION__ << " Write: " << Cmd;

    connect(&timeoutTimer, &QTimer::timeout, this, &SerialPort::slot_GetScanGunData);
    timeoutTimer.start(200);
    global_port->write(Cmd.toLatin1());

    return ;
}

void SerialPort::slot_GetScanGunData()
{
    QByteArray data;
    QString request;

    data = data + global_port->readAll();
    if(data.isEmpty())
        return;

    request = QString(data).replace("\r\n", "");

    global_port->clear(QSerialPort::Input);
    Common::getInstance()->setScanGunRequest(m_nTreadID, request);
    Common::getInstance()->setScanGunSignal(m_nTreadID, true);
    disconnect(&timeoutTimer, &QTimer::timeout, this, &SerialPort::slot_GetScanGunData);
}

QString SerialPort::WriteCommand(QString command)
{
    QByteArray data;

    int retry = 10;
    global_port->write(QByteArray::fromHex(command.toLatin1()));
    while (global_port->waitForReadyRead(100) && retry > 0)
    {
        data = data + global_port->readAll();
        retry--;

        if(data.length() == 6)
            break;
    }

    return QString(data.toHex()).toUpper();
}

