#ifndef CONNECTAGILENT_H
#define CONNECTAGILENT_H

#include <QObject>
#include <QThread>
#include "Common/Common.h"
#include "visa.h"
#include "visatype.h"

class ConnectAgilent : public QThread
{
    Q_OBJECT
public:
    ConnectAgilent(QObject *parent = nullptr, UINT nTreadID = 0);
    ~ConnectAgilent(void);

    bool ConnectByGPIB();
    void DisConnectByGPIB();
    void ReadErrorQueue();
    bool ErrorOccurred();
    void ClearStatus();
    void ResetDevice();
    void SetOutputCoupled(bool coupled);
    void SetOutputVoltageAndCurrentLevels(int channel, float voltage, float current);
    void SetCurrentMeasurementRange(MEASURE_RANGE range);
    void EnableOutput(int channel, bool enable);
    double ReadMeasureCurrent(int channel);

signals:
    void signal_VisaUpdateUILog(UINT nTreadID, QString log, LOGCOLOR_TYPE textcolor = BLACK);

private:
    void UpdateUIMsg(QString log, LOGCOLOR_TYPE textcolor = BLACK);

    UINT m_nTreadID;
    ViSession defaultRM[4], vi[4];
    ViStatus status[4];
    std::list<std::string> error_queue[4];
    bool check_errors[4];

};

#endif // CONNECTAGILENT_H
