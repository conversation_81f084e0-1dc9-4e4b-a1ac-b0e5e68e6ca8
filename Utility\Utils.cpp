﻿#include "Utils.h"

#include <QSettings>
#include <QFile>
#include <QMessageBox>
#include <QApplication>
#include <QAbstractButton>
#include <QDir>
#include <QFileInfoList>
#include <QTextCodec>
#include <QScreen>

#include "FileUtils.h"
#include "version.h"

#ifdef _WIN32
#include <Windows.h>
#include <MAPI.h>
#include <TlHelp32.h>
#include <wchar.h>
#endif

QString Utils::getSystemOS()
{
    QString system_os("Unknown");
#ifdef Q_OS_WIN
    system_os = "WINDOWS";
#elif defined(Q_OS_LINUX)
    system_os = "LINUX";
#endif
    return system_os;
}

void Utils::setTextCodec()
{
#ifdef _WIN32
    QTextCodec *coder = QTextCodec::codecForName("System");
#else
    QTextCodec *coder = QTextCodec::codecForName("UTF-8");
#endif
    QTextCodec::setCodecForLocale(coder);
}

QString Utils::transBoolToYESNO(bool bVal)
{
    return bVal ? QStringLiteral("YES") : QStringLiteral("NO");
}

QString Utils::transBoolToOnOff(bool bVal)
{
    return bVal ? QStringLiteral("ON") : QStringLiteral("OFF");
}

bool Utils::transOnOffToBool(const QString &sVal)
{
    return sVal.trimmed().toLower() == "on";
}
