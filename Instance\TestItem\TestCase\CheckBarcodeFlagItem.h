#ifndef CHECKBARCODEFLAGITEM_H
#define CHECKBARCODEFLAGITEM_H

#include "../testcasebase.h"
#include <QCheckBox>
#include <QComboBox>
#include <QLineEdit>
#include <QFormLayout>

class CheckBarcodeFlagItem : public TestCaseBase
{
    Q_OBJECT
public:
    CheckBarcodeFlagItem();

    QString type() const override { return "CheckBarcodeFlag"; }
    static QString staticType() { return "CheckBarcodeFlag"; }
    QString displayName() const override { return "Check Barcode Flag"; }

    QWidget* createParameterWidget(QWidget* parent) override;

    QVariantMap parameters() const override { return m_params; }
    void setParameters(const QVariantMap& params) override { m_params = params; }
};

#endif // CHECKBARCODEFLAGITEM_H
