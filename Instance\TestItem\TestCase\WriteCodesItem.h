#ifndef WRITECODESITEM_H
#define WRITECODESITEM_H

#include "../testcasebase.h"
#include <QCheckBox>
#include <QComboBox>
#include <QLineEdit>
#include <QFormLayout>

class WriteCodesItem : public TestCaseBase
{
    Q_OBJECT
public:
    WriteCodesItem();

    QString type() const override { return "WriteCodes"; }
    static QString staticType() { return "WriteCodes"; }
    QString displayName() const override { return "Write Codes"; }

    QWidget* createParameterWidget(QWidget* parent) override;

    QVariantMap parameters() const override { return m_params; }
    void setParameters(const QVariantMap& params) override { m_params = params; }
};

#endif // WRITECODESITEM_H
