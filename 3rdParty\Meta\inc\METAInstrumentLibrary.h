/*
 * Copyright (c) 2020, MediaTek Inc. All rights reserved.
 *
 * This software/firmware and related documentation ("MediaTek Software") are
 * protected under relevant copyright laws.
 * The information contained herein is confidential and proprietary to
 * MediaTek Inc. and/or its licensors.
 * Except as otherwise provided in the applicable licensing terms with
 * MediaTek Inc. and/or its licensors, any reproduction, modification, use or
 * disclosure of MediaTek Software, and information contained herein, in whole
 * or in part, shall be strictly prohibited.
*/
#ifndef _META_INSTRUMENT_LIBRARY_H_
#define _META_INSTRUMENT_LIBRARY_H_

#ifdef __cplusplus
extern "C" {
#endif
#if defined(__CHECK_REENTRANCY__)
#if defined(_WIN32)
#define NON_REENTRANT_FUNC __declspec(deprecated("this is not a reentrant function"))
#else
#define NON_REENTRANT_FUNC
#endif // #if defined(_WIN32)
#else
#define NON_REENTRANT_FUNC
#endif // #if !defined(__BORLANDC__)
/*****************************************************************
 * This header MUST contain only common definitions and functions
 * DO NOT include ANY RCT-dependent definitions
 *****************************************************************/
#if !defined(PRIVATE_UNLESS_UNITTEST) && !defined(PROTECTED_UNLESS_UNITTEST)

#if defined(_UNITTEST)
#define PRIVATE_UNLESS_UNITTEST public
#define PROTECTED_UNLESS_UNITTEST public
#else
#define PRIVATE_UNLESS_UNITTEST private
#define PROTECTED_UNLESS_UNITTEST private
#endif

#endif // !defined(PRIVATE_UNLESS_UNITTEST) && !defined(PROTECTED_UNLESS_UNITTEST)

#define METAInstrumentLibrary_MAX_CONCURRENT_THREADS        32  // the same as  META_MAX_CONCURRENT_THREADS of META DLL

/// RCT return status code successful
const int RCTLIB_SUCCESS =  0;
/// RCT return status code failed or error
const int RCTLIB_ERROR   = -1;
/// RCT return status code successful
const int RCTLIB_ABORTED =  1;
/// RCT retrun status code not yet implemented
const int RCTLIB_NOT_YET_IMPLEMENTED = 2;

/// RCT operating mode enum value (Active Cell)
const unsigned int RCTLIB_OPERATING_MODE_ACTIVE              = 0;
/// RCT operating mode enum value (GSM BCH slot:0 BCCH, other: dummy)
const unsigned int RCTLIB_OPERATING_MODE_GSM_BCH             = 1;
/// RCT operating mode enum value (GSM BCH slot:0 BCCH, TCH and dummy)
const unsigned int RCTLIB_OPERATING_MODE_GSM_BCH_TCH         = 2;
/// RCT operating mode enum value (continuous wave)
const unsigned int RCTLIB_OPERATING_MODE_GSM_CW              = 3;
/// RCT operating mode enum value (GPRS BCH slot:0 BCCH other:dummy)
const unsigned int RCTLIB_OPERATING_MODE_GPRS_BCH            = 4;
/// RCT operating mode enum value (GPRS BCH slot:0 BCCH, PDTCH and dummy)
const unsigned int RCTLIB_OPERATING_MODE_GPRS_BCH_PDTCH      = 5;
/// RCT operating mode enum value (EGPRS BCH slot:0 other:dummy)
const unsigned int RCTLIB_OPERATING_MODE_EGPRS_BCH           = 6;
/// RCT operating mode enum value (GPRS BCH slot:0 BCCH, PDTCH and dummy)
const unsigned int RCTLIB_OPERATING_MODE_EGPRS_BCH_PDTCH     = 7;
/// RCT operating mode enum value (GSM fast handeset calibration mode)
const unsigned int RCTLIB_OPERATING_MODE_FDT                 = 8;
/// RCT operating mode enum value (WCDMA FDD test mode)
const unsigned int RCTLIB_OPERATING_MODE_WCDMA_FDD           = 9;
/// RCT operating mode enum value (cell off)
const unsigned int RCTLIB_OPERATING_MODE_CELL_OFF            = 10;
/// RCT operating mode enum value (TD test mode)
const unsigned int RCTLIB_OPERATING_MODE_TD_TEST             = 11;
const unsigned int RCTLIB_OPERATING_MODE_SIGNALING           = 12;
const unsigned int RCTLIB_OPERATING_MODE_NONSIGNALING        = 13;
#ifdef __META_C2K__
const unsigned int RCTLIB_OPERATING_MODE_C2K_TEST            = 14;
const unsigned int RCTLIB_OPERATING_MODE_C2K_CW              = 15;
const unsigned int RCTLIB_OPERATING_MODE_EVDO_TEST           = 16;
#endif //#ifdef __META_C2K__

typedef enum
{
    LTE_GEN_NULL    =  0,
    LTE_GEN_V1      =  1,
    LTE_GEN_V2      =  2,
    LTE_GEN_V3      =  3,
    LTE_GEN_V5      =  4,
    LTE_GEN_V7      =  5,
    LTE_GEN_V8      =  6,
    LTE_GEN_END     =  0xFFFF
} LTE_MODEM_GENERATION_VERSION_E;

typedef enum
{
    NR_GEN_NULL      =  0,
    NR_GEN_V7        =  1,
    NR_GEN_V8        =  2,
    NR_GEN_END       =  0x7FFFFFFF
} NR_MODEM_GENERATION_VERSION_E;

typedef enum
{
    MM_RAT_LTE = 0,
    MM_RAT_NR,
    MM_RAT_MAX
} MM_RAT_V7_E;

typedef enum
{
    TX,
    RX,
    RXD,
    ANTENNA_PORT_TYPE_NUM
} AntennaPortType;

typedef enum
{
    RF_Carkit_Null = -1,
    RF_Carkit0 = 0,
    RF_Carkit1,
    RF_Carkit2,
    RF_Carkit3,
    RF_Carkit4,
    RF_Carkit5,
    RF_Carkit6,
    RF_Carkit7,
    RF_Carkit8,
    RF_Carkit9,
    RF_Carkit10,
    RF_Carkit11,
    RF_Carkit12,
    RF_Carkit13,
    RF_Carkit14,
    RF_Carkit15,
    RF_Carkit16,
    RF_Carkit17,
    RF_Carkit18,
    RF_Carkit19,
    RF_Carkit20,
    RF_Carkit_NUM
} RF_Carkit_Index_E;

typedef enum
{
    RCTLIB_DEVICE_UNDEF = -1,
    RCTLIB_DEVICE_AGILENT_8960 = 0,
    RCTLIB_DEVICE_CMU_200 = 1,
    RCTLIB_DEVICE_CMW_500 = 2,
    RCTLIB_DEVICE_StarPoint_6010 = 3,
    RCTLIB_DEVICE_MT_8820 = 4,
    RCTLIB_DEVICE_MT_8870 = 5,
    RCTLIB_DEVICE_CTP_3110 = 6,
    RCTLIB_DEVICE_Transcom_6280 = 7,
    RCTLIB_DEVICE_AGILENT_EXT = 8,
    RCTLIB_DEVICE_LP_IQXSTREAM = 9,
    RCTLIB_DEVICE_PXI_3000 = 10, /* Phase out */
    RCTLIB_DEVICE_Welzek_6290 = 11,
    RCTLIB_DEVICE_LP_IQXSTREAM_M = 12,
    RCTLIB_DEVICE_StarPoint_9010 = 13,
    RCTLIB_DEVICE_CTP_3308_E = 14,
    RCTLIB_DEVICE_IQGIG_5G = 15,
    RCTLIB_DEVICE_CMP200 = 16,
    RCTLIB_DEVICE_DT_3308F = 17,
    RCTLIB_DEVICE_MT8000 = 18,
    RCTLIB_DEVICE_EXM6681A = 19,
    RCTLIB_DEVICE_IQGIG_IF = 20,
    RCTLIB_DEVICE_CMP180 = 21,
    RCTLIB_DEVICE_EXM6680A = 22,
    RCTLIB_DEVICE_CMW_100 = 23,
    RCTLIB_DEVICE_LP_IQXSTREAM_5G = 24,
    RCTLIB_DEVICE_EXM6640A = 25,
    RCTLIB_DEVICE_StarPoint_9100 = 26,
    /*************************
     * Add RCT support above
     ************************/
    RCTLIB_DEVICE_CALLBACK,
    RCTLIB_DEVICE_DUMMY,
    RCTLIB_DEVICE_NUM,

} E_RCTLIB_DEVICE_TYPE;

typedef enum
{
    PSULIB_DEVICE_UNDEF = -1,
    PSULIB_DEVICE_NONE = 0,
    PSULIB_DEVICE_AGILENT_E3631A = 1,
    PSULIB_DEVICE_RS_NGM202 = 2,
    PSULIB_DEVICE_NUM,

} E_PSULIB_DEVICE_TYPE;

typedef enum
{
    SWLIB_DEVICE_UNDEF  = -1,
    SWLIB_DEVICE_NONE   = 0,
    SWLIB_DEVICE_TC5535 = 1,
    SWLIB_DEVICE_CMQ300 = 2,
    SWLIB_DEVICE_KEYSIGHT_F9681A = 3,
    SWLIB_DEVICE_KEYSIGHT_Z2160A = 4,
    SWLIB_DEVICE_NUM,
} E_SWLIB_DEVICE_TYPE;

/* Appliaction format */
typedef enum
{
    E_INSTRMENT_APP_FORMAT_GSM_GPRS = 0,
    E_INSTRMENT_APP_FORMAT_WCDMA = 1,
    E_INSTRMENT_APP_FORMAT_TDSCDMA = 2,
#ifdef __META_LTE__
    E_INSTRMENT_APP_FORMAT_LTE = 3,
#endif //#ifdef __META_LTE__
#ifdef __META_C2K__
    E_INSTRMENT_APP_FORMAT_C2K = 4,
#endif //#ifdef __META_C2K__
    E_INSTRMENT_APP_FORMAT_NR = 5,
    E_INSTRMENT_APP_FORMAT_NR_FR2 = 6,
    E_INSTRMENT_APP_FORMAT_IOTNTN = 7,
    E_INSTRMENT_APP_FORMAT_COUNT
} E_INSTRMENT_APP_FORMAT;

/* Instument port enum */
typedef enum
{
    RCTLIB_RF_MAIN_INOUT_PORT = 0,
    RCTLIB_RF_RXD_OUTPUT_PORT = 1,
    RCTLIB_RF_INOUT_PORT1 = 2,
    RCTLIB_RF_INOUT_PORT2 = 3,
    RCTLIB_RF_INOUT_PORT3 = 4,
    RCTLIB_RF_INOUT_PORT4 = 5,
    RCTLIB_RF_PORT_TYPE_END = 6,
    RCTLIB_RF_MAIN_INOUT_PORT2 = 7,
    RCTLIB_RF_RXD_OUTPUT_PORT2 = 8
} E_RCTLIB_PORT_TYPE;

#ifdef _WIN32
typedef int (__cdecl *ApplicationLoggingCallback_T)(void *object, const char *fmt, void *arg);
#else
typedef int (__cdecl *ApplicationLoggingCallback_T)(void *object, const char *fmt, va_list arg);
#endif //#ifdef _WIN32
typedef int (__cdecl *ApplicationStopCallback_T)(void *object);


//add by yanxuqian on 20150910
typedef enum
{
    RCTLIB_MULTI_ATE_TEST_THREAD_NULL = -1,
    RCTLIB_MULTI_ATE_TEST_THREAD_1 = 0,
    RCTLIB_MULTI_ATE_TEST_THREAD_2 = 1,
    RCTLIB_MULTI_ATE_TEST_THREAD_3 = 2,
    RCTLIB_MULTI_ATE_TEST_THREAD_4 = 3,
    RCTLIB_MULTI_ATE_TEST_THREAD_5 = 4,
    RCTLIB_MULTI_ATE_TEST_THREAD_6 = 5,
    RCTLIB_MULTI_ATE_TEST_THREAD_7 = 6,
    RCTLIB_MULTI_ATE_TEST_THREAD_8 = 7,
    RCTLIB_MULTI_ATE_TEST_THREAD_9 = 8,
    RCTLIB_MULTI_ATE_TEST_THREAD_10 = 9,
    RCTLIB_MULTI_ATE_TEST_THREAD_11 = 10,
    RCTLIB_MULTI_ATE_TEST_THREAD_12 = 11,
    RCTLIB_MULTI_ATE_TEST_THREAD_13 = 12,
    RCTLIB_MULTI_ATE_TEST_THREAD_14 = 13,
    RCTLIB_MULTI_ATE_TEST_THREAD_15 = 14,
    RCTLIB_MULTI_ATE_TEST_THREAD_16 = 15,
    RCTLIB_MULTI_ATE_TEST_THREAD_MAX
} E_RCTLIB_MULTI_ATE_THREAD_NUMBER;

typedef enum
{
    RCTLIB_RF_PORT_NULL = 0,
    RCTLIB_RF_PORT_RF1 = 1,
    RCTLIB_RF_PORT_RF2 = 2,
    RCTLIB_RF_PORT_RF3 = 3,
    RCTLIB_RF_PORT_RF4 = 4,
    RCTLIB_RF_PORT_RF5 = 5,
    RCTLIB_RF_PORT_RF6 = 6,
    RCTLIB_RF_PORT_RF7 = 7,
    RCTLIB_RF_PORT_RF8 = 8,
    RCTLIB_RF_PORT_RF9 = 9,
    RCTLIB_RF_PORT_RF10 = 10,
    RCTLIB_RF_PORT_RF11 = 11,
    RCTLIB_RF_PORT_RF12 = 12,
    RCTLIB_RF_PORT_RF13 = 13,
    RCTLIB_RF_PORT_RF14 = 14,
    RCTLIB_RF_PORT_RF15 = 15,
    RCTLIB_RF_PORT_RF16 = 16,
    RCTLIB_RF_PORT_RF17 = 17,
    RCTLIB_RF_PORT_RF18 = 18,
    RCTLIB_RF_PORT_RF19 = 19,
    RCTLIB_RF_PORT_RF20 = 20,
    RCTLIB_RF_PORT_RF21 = 21,
    RCTLIB_RF_PORT_RF22 = 22,
    RCTLIB_RF_PORT_RF23 = 23,
    RCTLIB_RF_PORT_RF24 = 24,
    RCTLIB_RF_PORT_RF25 = 25,
    RCTLIB_RF_PORT_RF26 = 26,
    RCTLIB_RF_PORT_RF27 = 27,
    RCTLIB_RF_PORT_RF28 = 28,
    RCTLIB_RF_PORT_RF29 = 29,
    RCTLIB_RF_PORT_RF30 = 30,
    RCTLIB_RF_PORT_RF31 = 31,
    RCTLIB_RF_PORT_RF32 = 32,
    RCTLIB_RF_PORT_RF33 = 33,
    RCTLIB_RF_PORT_RF34 = 34,
    RCTLIB_RF_PORT_RF35 = 35,
    RCTLIB_RF_PORT_RF36 = 36,
    RCTLIB_RF_PORT_RF37 = 37,
    RCTLIB_RF_PORT_RF38 = 38,
    RCTLIB_RF_PORT_COUNT
} E_RCTLIB_RF_PORT_TYPE;

#if defined(LONG_MAX) && (LONG_MAX > 0x7FFFFFFFL)
typedef unsigned int        ViUInt32;
#else
typedef unsigned long       ViUInt32;
#endif

typedef struct
{
    ///add by yanxuqian on 20150910
    E_RCTLIB_MULTI_ATE_THREAD_NUMBER eThreadNumber;
    /// full file path to the CFG file
    char *cfg_file_path;
    /// the instrument type
    int i_device_type;
    /// init GGE tester
    bool b_init_GGE;
    /// the instrument type for WCDMA calibration
    int i_device_type_wcdma;
    /// init WCDMA tester
    bool b_init_WCDMA;
    /// the instrument type for TD calibration
    int i_device_type_tdscdma;
    /// init TDSCDMA tester
    bool b_init_TDSCDMA;
#ifdef __META_LTE__
    /// the instrument type for LTE calibration
    int i_device_type_lte;
    /// init LTE tester
    bool b_init_LTE;
#endif //#ifdef __META_LTE__
    /// the instrument type for NR calibration
    int i_device_type_nr;
    /// init NR tester
    bool b_init_NR;
    /// the instrument type for NR FR2 calibration
    int i_device_type_nr_fr2;
    /// init NR FR2 tester
    bool b_init_NR_FR2;
#ifdef __META_C2K__
    /// the instrument type for C2K calibration
    int i_device_type_c2k;
    /// init C2K tester
    bool b_init_C2K;
#endif //#ifdef __META_C2K__
    int i_device_type_iot_ntn;
    bool b_init_IOTNTN;
#if !defined(__NO_WCN_TEST__)
    bool b_init_BT;
    bool b_init_WIFI;
    bool b_init_GPS;
    bool b_init_WIFI_CAL;
    int   i_device_type_wcn_bt;
    int   i_device_type_wcn_wifi;
    int   i_device_type_wcn_gps;
    int   i_device_type_wcn_wifi_cal;
#endif
    ViUInt32 vi_handle;
    /// application handle
    void *applicationHandle;
    /// callback function pointer for application;
    ApplicationLoggingCallback_T LogFunction;
    /// callback function to check the user termination
    ApplicationStopCallback_T CheckStopFunction;
} S_RCTLIB_INIT_CFG_T;

typedef struct
{
    ///add by yanxuqian on 20150910
    E_RCTLIB_MULTI_ATE_THREAD_NUMBER eThreadNumber;
    /// full file path to the CFG file
    const wchar_t *cfg_file_path;
    /// the instrument type
    int i_device_type;
    /// init GGE tester
    bool b_init_GGE;
    /// the instrument type for WCDMA calibration
    int i_device_type_wcdma;
    /// init WCDMA tester
    bool b_init_WCDMA;
    /// the instrument type for TD calibration
    int i_device_type_tdscdma;
    /// init TDSCDMA tester
    bool b_init_TDSCDMA;
#ifdef __META_LTE__
    /// the instrument type for LTE calibration
    int i_device_type_lte;
    /// init LTE tester
    bool b_init_LTE;
    /// modem generation (gotten by NR capability)
    unsigned char modemGenerationLte;
#endif //#ifdef __META_LTE__
    /// the instrument type for NR calibration
    int i_device_type_nr;
    /// init NR tester
    bool b_init_NR;
    /// modem generation (gotten by NR capability)
    unsigned char modemGenerationNr;
    /// the instrument type for NR FR2 calibration
    int i_device_type_nr_fr2;
    /// init NR FR2 tester
    bool b_init_NR_FR2;
#ifdef __META_C2K__
    /// the instrument type for C2K calibration
    int i_device_type_c2k;
    /// init C2K tester
    bool b_init_C2K;
#endif //#ifdef __META_C2K__
    int i_device_type_iot_ntn;
    bool b_init_IOTNTN;
#if !defined(__NO_WCN_TEST__)
    bool b_init_BT;
    bool b_init_WIFI;
    bool b_init_GPS;
    bool b_init_WIFI_CAL;
    int   i_device_type_wcn_bt;
    int   i_device_type_wcn_wifi;
    int   i_device_type_wcn_gps;
    int   i_device_type_wcn_wifi_cal;
#endif
    bool isNeedReset;
    ViUInt32 vi_handle;
    /// application handle
    void *applicationHandle;
    /// callback function pointer for application;
    ApplicationLoggingCallback_T LogFunction;
    /// callback function to check the user termination
    ApplicationStopCallback_T CheckStopFunction;
} S_RCTLIB_INIT_CFG_TW;
/*****************************
 * PSU
 *****************************/
typedef struct
{
    /// full file path to the CFG file
    char *cfg_file_path;
    /// the instrument type
    int i_device_type;
    /// application handle
    void *applicationHandle;
    /// callback function pointer for application;
    ApplicationLoggingCallback_T LogFunction;
    /// callback function to check the user termination
    ApplicationStopCallback_T CheckStopFunction;
} S_PSULIB_INIT_CFG_T;

typedef struct
{
    /// full file path to the CFG file
    const wchar_t *cfg_file_path;
    /// the instrument type
    int i_device_type;
    /// application handle
    void *applicationHandle;
    /// callback function pointer for application;
    ApplicationLoggingCallback_T LogFunction;
    /// callback function to check the user termination
    ApplicationStopCallback_T CheckStopFunction;
} S_PSULIB_INIT_CFG_TW;

typedef struct
{
    double voltage;
    double current;
} S_PSULIB_RESULT_T;

NON_REENTRANT_FUNC int __stdcall PSULIB_Common_Connect(void);
NON_REENTRANT_FUNC int __stdcall PSULIB_Common_Disconnect(void);
NON_REENTRANT_FUNC int __stdcall PSULIB_Common_Reset(void);
NON_REENTRANT_FUNC int __stdcall PSULIB_FetchResult(S_PSULIB_RESULT_T *pResult);
NON_REENTRANT_FUNC int __stdcall PSULIB_OutputVoltCurrOn(double voltage, double current);
NON_REENTRANT_FUNC int __stdcall PSULIB_OutputVoltCurrOff(void);
int __stdcall PSULIB_Common_Initialize_r(const int meta_handle, const S_PSULIB_INIT_CFG_T *arg, unsigned int arg_size);
int __stdcall PSULIB_Common_Initialize_rW(const int meta_handle, const S_PSULIB_INIT_CFG_TW *arg, unsigned int arg_size);
int __stdcall PSULIB_Common_Connect_r(const int meta_handle);
int __stdcall PSULIB_Common_Disconnect_r(const int meta_handle);
int __stdcall PSULIB_FetchResult_r(const int meta_handle, S_PSULIB_RESULT_T *pResult);
int __stdcall PSULIB_OutputVoltCurrOn_r(const int meta_handle, double voltage, double current);
int __stdcall PSULIB_OutputVoltCurrOff_r(const int meta_handle);
int __stdcall PSULIB_Common_Reset_r(const int meta_handle);

/*****************************
 * Temperature Chamber
 *****************************/
typedef enum
{
    TCLIB_DEVICE_UNDEF       = -1,
    TCLIB_DEVICE_THERMOTRON  = 0,
    TCLIB_DEVICE_WGD5008     = 1,
    TCLIB_DEVICE_TE107       = 2,
    TCLIB_DEVICE_ESPEC_SU642 = 3,
    TCLIB_DEVICE_ESPEC_SU242 = 4,
    /*************************
     * Add TC support above
     ************************/
    TCLIB_DEVICE_DUMMY,
    TCLIB_DEVICE_NUM,
} E_TCLIB_DEVICE_TYPE;

typedef struct
{
    /// full file path to the CFG file
    const wchar_t *cfg_file_path;
    /// the instrument type
    int i_device_type;
    /// application handle
    void *applicationHandle;
    /// callback function pointer for application;
    ApplicationLoggingCallback_T LogFunction;
    /// callback function to check the user termination
    ApplicationStopCallback_T CheckStopFunction;
} S_TCLIB_INIT_CFG_TW;

int __stdcall TCLIB_Common_Initialize_rW(const int meta_handle, const S_TCLIB_INIT_CFG_TW *arg, unsigned int arg_size);
int __stdcall TCLIB_Common_Connect_r(const int meta_handle);
int __stdcall TCLIB_Common_Disconnect_r(const int meta_handle);
int __stdcall TCLIB_Common_Reset_r(const int meta_handle);
int __stdcall TCLIB_SetTemperature_r(const int meta_handle, float temp);
int __stdcall TCLIB_GetTemperature_r(const int meta_handle, float *temp);
int __stdcall TCLIB_Stop_r(const int meta_handle);

/*****************************
 * common
 *****************************/
NON_REENTRANT_FUNC int __stdcall RCTLIB_Common_Initialize(const S_RCTLIB_INIT_CFG_T *arg, unsigned int arg_size);
NON_REENTRANT_FUNC int __stdcall RCTLIB_Common_InitializeW(const S_RCTLIB_INIT_CFG_TW *arg, unsigned int arg_size);
NON_REENTRANT_FUNC int __stdcall RCTLIB_Common_Connect(void);
NON_REENTRANT_FUNC int __stdcall RCTLIB_Common_Disconnect(void);
NON_REENTRANT_FUNC int __stdcall RCTLIB_Common_Reset(void);
NON_REENTRANT_FUNC int __stdcall RCTLIB_Common_SetApplicationFormat(unsigned int application_format);
NON_REENTRANT_FUNC int __stdcall RCTLIB_Common_SetOperatingMode(unsigned int operating_mode);
NON_REENTRANT_FUNC int __stdcall RCTLIB_Common_Deinitialize(void);
NON_REENTRANT_FUNC int __stdcall RCTLIB_Common_SwitchPort(E_INSTRMENT_APP_FORMAT eRctToChange, E_RCTLIB_PORT_TYPE ePort);

// reentrant functions
struct S_META_STATUS_T;
int __stdcall RCTLIB_Get_Error_Status_r(const int metaHandle, S_META_STATUS_T *returnStatus);
int __stdcall RCTLIB_Common_Initialize_r(const int meta_handle, const S_RCTLIB_INIT_CFG_T *arg, unsigned int arg_size);
int __stdcall RCTLIB_Common_Initialize_rW(const int meta_handle, const S_RCTLIB_INIT_CFG_TW *arg, unsigned int arg_size);
int __stdcall RCTLIB_Common_Connect_r(const int meta_handle);
int __stdcall RCTLIB_Common_Disconnect_r(const int meta_handle);
int __stdcall RCTLIB_Common_Reset_r(const int meta_handle);
int __stdcall RCTLIB_Common_BaseReset_r(const int meta_handle, char *cfg_path);
int __stdcall RCTLIB_Common_BaseReset_rW(const int meta_handle, const wchar_t *cfg_path);
int __stdcall RCTLIB_Common_SetApplicationFormat_r(const int meta_handle, unsigned int application_format);
int __stdcall RCTLIB_Common_SetOperatingMode_r(const int meta_handle, unsigned int operating_mode);
int __stdcall RCTLIB_Common_Deinitialize_r(const int meta_handle);
int __stdcall RCTLIB_CheckStat_r(const int meta_handle, int status);
bool __stdcall RCTLIB_IsFreqBasedCableLoss_r(const int metaHandle);
bool __stdcall RCTLIB_IsCableLossNeedConfig_r(const int metaHandle, RF_Carkit_Index_E carkit);
bool __stdcall RCTLIB_IsForceConfigCableLoss_r(const int metaHandle);
int __stdcall RCTLIB_SetRefCarkitCableLoss_r(const int metaHandle);
int __stdcall RCTLIB_SetCableLossByCarkit_r(const int metaHandle, RF_Carkit_Index_E carkit, unsigned char dir, E_RCTLIB_RF_PORT_TYPE rfPort);
double __stdcall RCTLIB_GetCableLossOffsetByCarkitIdx_r(const int metaHandle, unsigned char direction, unsigned int freqKHz, RF_Carkit_Index_E routeCarkit);
double __stdcall RCTLIB_GetCableLossByCarkit_r(const int metaHandle, RF_Carkit_Index_E carkit, unsigned char direction, unsigned int freqKHz);
RF_Carkit_Index_E __stdcall RCTLIB_GetRefCarkit_r(const int metaHandle, RF_Carkit_Index_E carkit);
unsigned char __stdcall RCTLIB_GetCarkitByXmlMapInfo_r(const int metaHandle, unsigned short usBand, unsigned char rat, AntennaPortType eAntennaPortType);
unsigned char __stdcall RCTLIB_GetRfPortByCarkit_r(const int metaHandle, unsigned char carkitIndex);
unsigned char __stdcall RCTLIB_GetRfPortByXmlMapInfo_r(const int metaHandle, unsigned short band, unsigned char rat,
                                                       AntennaPortType eAntennaPortType);
int __stdcall RCTLIB_Common_SwitchPort_r(const int meta_handle, E_INSTRMENT_APP_FORMAT eRctToChange, E_RCTLIB_PORT_TYPE ePort);
int __stdcall RCTLIB_Common_SwitchPortForAutoSearchRFPort_r(const int meta_handle, AntennaPortType eRfPortType, E_RCTLIB_RF_PORT_TYPE ePort, E_INSTRMENT_APP_FORMAT eRctToChange);
//RCTLIB_Common_SwitchPortForCloseRFPort_r
int __stdcall RCTLIB_Common_SwitchPortForCloseRFPort_r(const int meta_handle, AntennaPortType eRfPortType, E_RCTLIB_RF_PORT_TYPE ePort, E_INSTRMENT_APP_FORMAT eRctToChange);
enum ENUM_RCT_ACTION
{
    ENUM_RCT_ACTION_FLOW_START,
    ENUM_RCT_ACTION_FLOW_Retry,
    ENUM_RCT_ACTION_WAIT_REGISTER,
    ENUM_RCT_ACTION_UPDATE_REGISTER_COUNT,
    ENUM_RCT_ACTION_TX_PRESETTING,
    ENUM_RCT_ACTION_TX_SYNC,
    ENUM_RCT_ACTION_TX_FETCH,
    ENUM_RCT_ACTION_RX_PRESETTING,
    ENUM_RCT_ACTION_RX_POSTSETTING,
    ENUM_RCT_ACTION_RX_INIT,
    ENUM_RCT_ACTION_RX_UPDATE_SYNC_POW,
    ENUM_RCT_ACTION_RX_UPDATE_SYNC_POW_DONE,
    ENUM_RCT_ACTION_RX_UPDATE_TEST_POW,
    ENUM_RCT_ACTION_RX_UPDATE_TEST_POW_DONE,
    ENUM_RCT_ACTION_RX_FETCH_DONE,
    ENUM_RCT_ACTION_EXCUTE_LIST_MODE_ACTION,
    ENUM_RCT_ACTION_MAX
};

enum ENUM_RAT
{
    ENUM_RAT_GSM,
    ENUM_RAT_WCDMA,
    ENUM_RAT_LTE,
    ENUM_RAT_NR,
    ENUM_RAT_MAX
};
int __stdcall RCTLIB_Common_Lock_r(const int meta_handle, ENUM_RAT enumRat, ENUM_RCT_ACTION enumAct, void *param);
int __stdcall RCTLIB_Common_Unlock_r(const int meta_handle, ENUM_RAT enumRat, ENUM_RCT_ACTION enumAct, void *param);
int __stdcall RCTLIB_Common_Join_r(const int meta_handle, ENUM_RAT enumRat, ENUM_RCT_ACTION enumAct, void *param);

bool __stdcall RCTLIB_Common_DoTaskOnceBegin_r(const int meta_handle, bool joinAllDuts = true);
void __stdcall RCTLIB_Common_MarkTaskSuccessful_r(const int meta_handle);
void __stdcall RCTLIB_Common_MarkTaskFailed_r(const int meta_handle);
bool __stdcall RCTLIB_Common_DoTaskOnceEnd_r(const int meta_handle, bool joinAllDuts = true);

// thread safe function
int __stdcall RCTLIB_OccupyHandler(int meta_handle);
int __stdcall RCTLIB_CheckStat(int status);
int __stdcall RCTLIB_Common_GetDLLVer(unsigned int *major_ver, unsigned int *minor_ver, unsigned int *build_num, unsigned int *patch_num);

/**
 * \ingroup General
 * \details Get full version information from METAInstrumentLibrary
 *
 * \param [in]  in_version_struct_type  specifies the library version structure type, which affects how to cast `out_version_info`
 * \param [out] out_version_info        pointer to the structure where the library version information will be stored
 *
 * \retval RCTLIB_SUCCESS The operation succeeded.
 * \retval RCTLIB_ERROR   The given version type and version info are not matched. Failed to get version information.
 *
 * \code
 * #include "METAVersionDefinition.h"
 * void example() {
 *     METALibraryVersionInfo info;
 *     int result = RCTLIB_Common_GetLibraryVersionInfo(META_LIBRARY_VERSION_V1, &info);
 *     if (result == RCTLIB_SUCCESS) {
 *         // Read value from info
 *     }
 * }
 * \endcode
 */
int __stdcall RCTLIB_Common_GetLibraryVersionInfo(int in_version_struct_type, void *out_version_info);

const char *__stdcall RCTLIB_Common_GetDeviceString(E_RCTLIB_DEVICE_TYPE type);
void __stdcall RCTLIB_Common_GetInsAppFormatSupport(E_RCTLIB_DEVICE_TYPE eDeviceNum, E_INSTRMENT_APP_FORMAT eAppFormat, bool *bSupport);


/**
 * the structure for enabling downlink signal with multiple RF port of instrument
 */
typedef struct
{
    unsigned char enablePortCount;
    unsigned char rfPort[RCTLIB_RF_PORT_COUNT];

} S_RCTLIB_ENABLE_MULTIPLE_RX_RF_PORT_T;


/**
 * the structure for cable loss setting for multiple RF port of instrument
 */
typedef struct
{
    unsigned char enableCarkitCount;
    unsigned char carkit[RF_Carkit_NUM];
} S_RCTLIB_ENABLE_MULTIPLE_RF_CARKIT_T;


/*****************************
 * GGE
 *****************************/
/**********************************************
 * GGE structure (shared library defined START)
 **********************************************/
/**
 * The structure for storing GGE FHC TX USS command
 */
typedef struct
{
    /// frequency band
    unsigned band;
    double m_ul_calbleloss[4];
    double m_dl_calbleloss[4];
} S_RCTLIB_GGE_Cableloss_CONFIG_T;

typedef struct
{
    /// frequency band indicator for the USS
    unsigned char           band;
    /// TCH/PDTCH ARFCN for the USS
    unsigned int                    arfcn;

    /// TCH/PDTCH mHz for the USS
    double d_mHz;

    /// mult-slot configuration for each frame in the USS
    char                                    timeslot_per_frame;
    /// APC DAC or PCL (0: PCL 1: DAC)
    unsigned char           apc_dac_pcl_sel;
    /// APC DAC selection for the USS (1: APC DAC 0: PCL)
    short                   apc_dac_pcl_value[4];
    /// PA vbias setting
    unsigned char           pa_vbias_val[4];
    /// low PCL
    unsigned char           is_low_pcl[4];
    /// repeat frame count in the USS
    int                         repeat_cnt;
} S_RCTLIB_GGE_FHC_TX_USS_T;
/**
 * The structure for storing GGE FHC TX UTS request (50 USS)
 */
typedef struct
{
    /// number of USS in the UTS
    unsigned char                      step_cnt;
    /// modulation (0:GMSK 1: EPSK)
    unsigned char                  modulation;
    /// total samples
    short                          s_measure_samples;
    /// UTS type (0: APC DAC calibration, 1: TX subband cal, 2: PCL check)
    unsigned char                  uts_type;
    /// max expected nominal power
    double                         d_max_expected_power;
    /// the number of uplink TDMA frames required in an uplink sequence step
    int                            num_frame;
    /// USS configurations
    S_RCTLIB_GGE_FHC_TX_USS_T          ApcUSS[50];
} S_RCTLIB_GGE_FHC_TX_UTS_T;
/**
 * The structure for storing GGE FHC TX UTS request (100 USS)
 */
typedef struct
{
    /// number of USS in the UTS
    unsigned char                      step_cnt;
    /// modulation (0:GMSK 1: EPSK)
    unsigned char                  modulation;
    /// total samples
    short                          s_measure_samples;
    /// UTS type (0: APC DAC calibration, 1: TX subband cal, 2: PCL check)
    unsigned char                  uts_type;
    /// max expected nominal power
    double                         d_max_expected_power;
    /// the number of uplink TDMA frames required in an uplink sequence step
    int                            num_frame;
    /// USS configurations
    S_RCTLIB_GGE_FHC_TX_USS_T          ApcUSS[100];
} S_RCTLIB_GGE_FHC_TX_UTS_BIG_T;

typedef struct
{
    /// number of USS in the UTS
    unsigned short                 step_cnt;
    /// modulation (0:GMSK 1: EPSK)
    unsigned char                  modulation;
    /// total samples
    short                          s_measure_samples;
    /// UTS type (0: APC DAC calibration, 1: TX subband cal, 2: PCL check)
    unsigned char                  uts_type;
    /// max expected nominal power
    double                         d_max_expected_power;
    /// the number of uplink TDMA frames required in an uplink sequence step
    int                            num_frame;
    /// USS configurations
    S_RCTLIB_GGE_FHC_TX_USS_T      ApcUSS[512];
} S_RCTLIB_GGE_FHC_TX_UTS_BIG_512P_T;

typedef enum
{
    RCTLIB_DB_FCB_DB = 0,
    RCTLIB_FSB_DB,
    RCTLIB_DB
} E_RCTLIB_FHC_DL_BURST_TYPE_T;

typedef struct
{
    E_RCTLIB_FHC_DL_BURST_TYPE_T BurstType[100];
    double        Freq_MHz[100];
    int           RepeatCount[100];
    double        Power_dBm[100];
    unsigned char band[100];
    short         arfcn;
    int           step_count;
    bool          doAFC;
} S_RCTLIB_FHC_DL_List_T;

typedef struct
{
    E_RCTLIB_FHC_DL_BURST_TYPE_T BurstType[512];
    double        Freq_MHz[512];
    int           RepeatCount[512];
    double        Power_dBm[512];
    unsigned char band[512];
    short         arfcn;
    int           step_count;
    bool          doAFC;
} S_RCTLIB_FHC_DL_List_512p_T;

/**
 * The structure for storing GGE FHC TX USS measurement result (upto 4 slots)
 */
typedef struct
{
    /// TX power measurement of slot 0 ~ slot 3
    double              d_power[4];
} S_RCTLIB_GGE_FHC_TX_USS_RESULT_T;
/**
 * The structure for storing GGE FHC TX UTS measurement resutl (upto 50 USS)
 */
typedef struct
{
    /// step counts in the UTS measurement
    unsigned char                      step_cnt;
    /// measurement results for each USS
    S_RCTLIB_GGE_FHC_TX_USS_RESULT_T   uss_result[50];
} S_RCTLIB_GGE_FHC_TX_UTS_RESULT_T;
/**
 * The structure for storing GGE FHC TX UTS measurement resutlt (upto 100 USS)
 */
typedef struct
{
    /// step counts in the UTS measurement
    unsigned char                      step_cnt;
    /// measurement results for each USS
    S_RCTLIB_GGE_FHC_TX_USS_RESULT_T   uss_result[100];
} S_RCTLIB_GGE_FHC_TX_UTS_BIG_RESULT_T;

typedef struct
{
    /// step counts in the UTS measurement
    unsigned short                     step_cnt;
    /// measurement results for each USS
    S_RCTLIB_GGE_FHC_TX_USS_RESULT_T   uss_result[512];
} S_RCTLIB_GGE_FHC_TX_UTS_BIG_512P_RESULT_T;
/**
 * The structure for setting TX IQ measurement settings
 */
typedef struct
{
    /// frequency band
    unsigned int band;
    /// TCH & BCH ARFCN
    unsigned int arfcn;
    /// expected MS TX PCL
    unsigned int pcl;
    /// TSC
    unsigned int tsc;
    /// coding scheme
    unsigned int mcs;
    /// EPSK (1: EPSK, 0:GMSK)
    unsigned int epsk;
    /// config power time out
    unsigned int iTimeout;
    /// measurement count
    unsigned int iMeasureCount;
} S_RCTLIB_GGE_TXIQ_SETTINGS_T;

/**
 * The structure for fetching TX IQ measurement results
 */
typedef struct
{
    double d_sbs;
    double d_oos;
} S_RCTLIB_GGE_TXIQ_RESULT_T;

/**
 * The structure for storing GGE NSFT TEST config
 */
typedef struct
{
    /// frequency band
    unsigned band;
    /// cell power
    double bch_power;
    /// TCH power reduction
    double tch_reduction;
    /// BCH arfcn
    unsigned int bch;
    /// TCH arfcn
    unsigned int tch;
    /// initial PCL
    unsigned int pcl;
    /// timeslot
    unsigned int timeslot;
    /// TSC
    unsigned int tsc;
    /// coding scheme
    unsigned int cs;
#if 0
    /* under construction !*/
    /* under construction !*/
    /* under construction !*/
    /* under construction !*/
#endif // #if 0
} S_RCTLIB_GGE_NSFT_TESTCONFIG_T;
/**
 * The structure for storing GGE NSFT BER measurement result
 */
typedef struct
{
    /// integrity
    long   FBERIntegrity;
    /// tested bits
    double FBERBitsTested;
    /// BER
    double FBERRatio;
    /// Bit error count
    double FBERCount;
    /// progress
    double progress;
    /// number of CRC errors
    double crc_error;
} S_RCTLIB_GGE_NSFT_BER_RESULT_T;
/**
 * The structure for storing GGE NSFT TX measurement result (PFER part)
 */
typedef struct
{
    /// min frequency error
    double MinFErr;
    /// max frequency error
    double MaxFErr;
    /// average frequency error
    double AvgFErr;
    /// worst peak frequency error
    double WorstFErr;
    /// min peak phase error
    double MinPKPErr;
    /// max peak phase error
    double MaxPKPErr;
    /// average peak phase error
    double AvgPKPErr;
    /// min RMS phase error
    double MinRMSPErr;
    /// max RMS phase error
    double MaxRMSPErr;
    /// average RMS phse error
    double AvgRMSPErr;
} S_RCTLIB_GGE_NSFT_PFER_RESULT_T;
/**
 * The structure for storing GGE NSFT TX measurement result (ORFS part)
 */
typedef struct
{
    /// modulation specturm (relative)
    double mod_spectrum[22];
    /// modulation specturm (absolute)
    double mod_spectrum_abs[22];
    /// switching specturm
    double switch_spectrum[8];
    /// ref. power of modulation spectrum
    double mod_ref_power;
} S_RCTLIB_GGE_NSFT_ORFS_RESULT_T;
/**
 * The structure for storing GGE NSFT EPSK measurement result (EVM part)
 */
typedef struct
{
    /// EVM 95 percentile
    double EVM_95P;
    /// magnitude error 95 percentile
    double MagErr_95P;
    /// phase error 95 percentile Agilent8960 is not supported
    double Pherr_95P;
    /// peak EVM Agilent8960 is not supported
    double PK_EVM;
    /// RMS EVM
    double RMS_EVM;
    /// peak magnitude error
    double PK_MagErr;
    /// RMS magnitude error
    double RMS_MagErr;
    /// peak RMS magnitude error
    double RMS_MagErrPeak;
    /// peak phase error
    double PK_Pherr;
    /// RMS phase error
    double RMS_Pherr;
    /// peak RMS phase error
    double RMS_PherrPeak;
    /// original offset
    double orig_offset;
    /// frequency error
    double FErr;
    /// Amplitude droop
    double Amp_Droop;
    /// IQ imbalance
    double IQ_Imbalance;
    /// Timing Alignment (Agilent8960 need new application (application #?) CMU supported)
    double TA;
    /// AM/PM Alignment
    double AmPm;
} S_RCTLIB_GGE_NSFT_EMAC_RESULT_T;
/**
 * The structure for storing GGE NSFT TX measurement result (power/pvt part)
 */
typedef struct
{
    /// burst match or not
    bool b_burst_match;
    /// TX power value
    double tx_power;
} S_RCTLIB_GGE_NSFT_TXPOWER_RESULT_T;
/**
 * The structure for storing GGE NSFT GMSK measurement result
 */
typedef struct
{
    S_RCTLIB_GGE_NSFT_TXPOWER_RESULT_T txp_result;
    S_RCTLIB_GGE_NSFT_PFER_RESULT_T pfer_result;
    S_RCTLIB_GGE_NSFT_ORFS_RESULT_T orfs_result;
} S_RCTLIB_GGE_NSFT_GMSK_RESULT_T;
/**
 * The structure for storing GGE NSFT EPSK measurement result
 */
typedef struct
{
    S_RCTLIB_GGE_NSFT_TXPOWER_RESULT_T txp_result;
    S_RCTLIB_GGE_NSFT_EMAC_RESULT_T ema_result;
    S_RCTLIB_GGE_NSFT_ORFS_RESULT_T orfs_result;
} S_RCTLIB_GGE_NSFT_EPSK_RESULT_T;
/**
 * The structure for setting CAP ID calibration settings
 */
typedef struct
{
    /// band indicator (0: GSM450 / 1: GSM850 / 2: GSM900 / 3: DCS1800 / 4: PCS1900)
    unsigned int band;
    /// measurement ARFCN
    unsigned int arfcn;
    /// expected MS TX PCL
    unsigned int pcl;
    /// expected MS TX TSC
    unsigned int tsc;
    /// number of multiple measurement
    unsigned int measurementCount;
    /// measurement timeout setting (ms)
    double timeout;
} S_RCTLIB_GGE_CAPID_SETTINGS_T;
/**
 * The structure for setting AFC calibration settings
 */
typedef struct
{
    /// band indicator (0: GSM450 / 1: GSM850 / 2: GSM900 / 3: DCS1800 / 4: PCS1900)
    unsigned int band;
    /// BCH ARFCN
    unsigned int arfcn;
    /// downlink power for used timeslot (dBm)
    double d_used;
    /// downlink power for unused timeslot (dB)
    double d_unused;
    /// frequency offset
    double d_offset;
    /// Continuous wave mode indicator
    unsigned char CWmode;
    /// TSC
    unsigned int tsc;
} S_RCTLIB_GGE_AFC_SETTINGS_T;

typedef struct
{
    /// band indicator (0: GSM450 / 1: GSM850 / 2: GSM900 / 3: DCS1800 / 4: PCS1900)
    unsigned int band;
    double freqOffst;
} S_RCTLIB_GGE_CRYSTAL_AFC_SWEEP_SETTINGS_T;

typedef struct
{
    double freqErr;
} S_RCTLIB_GGE_CRYSTAL_AFC_SWEEP_RESULTS_T;
/**
 * The structure for setting analyzer frequency offset
 */
typedef struct
{
    /// center frequency (Unit: MHz)
    double centerFrequency;
    /// frequency offset to center frequency (Unit: MHz)
    double frequencyOffset;
    /// manual control of the analyzer frequency offset (manual: 1, the center frequency/ frequency offset is used; auto: 0, both are not used)
    unsigned char manualControl;
} S_RCTLIB_GGE_FREQUENCY_OFFSET_SETTINGS_T;
/**
 * The structure for setting FB DAC calibration settings
 */
typedef struct
{
    /// band indicator (0: GSM450 / 1: GSM850 / 2: GSM900 / 3: DCS1800 / 4: PCS1900)
    unsigned int band;
    /// BCH & TCH ARFCN
    unsigned int arfcn;
    /// expected MS TX PCL
    unsigned int pcl;
    /// TSC
    unsigned int tsc;
    /// Measurement count
    unsigned int iMeasureCount;
    /// config power time out
    unsigned int iTimeout;
} S_RCTLIB_GGE_FBDAC_SETTINGS_T;

/**
 * The structure for setting TX Slope Skew calibration settings
 */
typedef struct
{
    /// band indicator (0: GSM450 / 1: GSM850 / 2: GSM900 / 3: DCS1800 / 4: PCS1900)
    unsigned int band;
    /// TCH & BCH ARFCN
    unsigned int arfcn;
    /// expected MS TX PCL
    unsigned int pcl;
    /// TSC
    unsigned int tsc;
    /// MCS
    unsigned int mcs;
    /// Measurement count
    unsigned int iMeasureCount;
    /// config power time out
    unsigned int iTimeout;
} S_RCTLIB_GGE_TXSLOPESKEW_SETTINGS_T;

/**
 * The structure for setting TRX Offset calibration settings
 */
typedef struct
{
    /// band indicator (0: GSM450 / 1: GSM850 / 2: GSM900 / 3: DCS1800 / 4: PCS1900)
    unsigned int band;
    /// TCH & BCH ARFCN
    unsigned int arfcn;
    /// expected MS TX PCL
    unsigned int pcl;
    /// cell power (dbm)
    double   d_used;
    /// TSC
    unsigned int tsc;
    /// time slot
    unsigned int iTimeslot;
    /// Measurement count
    unsigned int iMeasureCount;
    /// config power time out
    unsigned int iTimeout;
} S_RCTLIB_GGE_TRXOFFSET_SETTINGS_T;
/**
 * Description: GSM & EDGE intrument's spcial settings
 */
typedef struct
{
    /// TX IQ normal burst type
    int iTxIQ_BurstTypeNB;
    /// NSFT GSM time slot
    int iNSFT_GSM_TimeSlot;
    /// NSFT EDGE time slot
    int iNSFT_EDGE_TimeSlot;
    /// NSFT GSM tsc
    int iNSFT_GSM_tsc;
    /// NSFT EDGE tsc
    int iNSFT_EDGE_tsc;
    /// NSFT ListMode Minimum of TX Block
    int iNSFT_LM_TX_Block_Min;
    /// FHC fine sync SCB number
    int iFHC_fsSCB;
    /// FHC DTS Max Step Count
    int iFHC_DTS_MaxStepCount;
    /// FHC UTS Max Step Count
    int iFHC_UTS_MaxStepCount;
    /// FHC DTS Multiple Band Support
    int iFHC_DTS_MultiBand; // 0: single band, 1: high low band combine, 2: quad band combine
    /// FHC UTS Multiple Band Support
    int iFHC_UTS_MultiBand; // 0: single band, 1: high low band combine, 2: quad band combine
    /// Is cell power be overwrite
    bool bOverwriteNSFTCellPower;
    /// real cell power
    double dRealNSFTCellPower;
    /// Instrument source generator broadcast support
    bool bSgBroadcastSupport;
} S_RCTLIB_GGE_SPECIFIC_SETTINGS_T;



typedef struct
{
    unsigned char cmd_type;
} S_RCTLIB_GGE_NSFT_LIST_MODE_COMMON_T;
typedef struct
{
    /// Sync information
    S_RCTLIB_GGE_NSFT_LIST_MODE_COMMON_T common;
    /// band indicator (0: GSM450 / 1: GSM850 / 2: GSM900 / 3: DCS1800 / 4: PCS1900)
    unsigned int sync_band;
    /// BCH arfcn
    unsigned int sync_arfcn;
    /// sync power
    int sync_power;
    /// sync slot offset
    unsigned char sync_slot_offset;
} S_RCTLIB_GGE_NSFT_LIST_MODE_SYNC_T;

typedef struct
{
    /// Trigger information
    S_RCTLIB_GGE_NSFT_LIST_MODE_COMMON_T common;
    /// band indicator (0: GSM450 / 1: GSM850 / 2: GSM900 / 3: DCS1800 / 4: PCS1900)
    unsigned int trigger_band;
    /// TCH arfcn
    unsigned int trigger_arfcn;
    /// TSC
    unsigned int trigger_tsc;
    /// Modulation
    unsigned char trigger_rtx_types;
    /// TX pcl
    unsigned int trigger_pcl;
} S_RCTLIB_GGE_NSFT_LIST_MODE_TRIGGER_T;

typedef struct
{
#define MAX_MEASURE_NUM 8
    /// Trigger information
    S_RCTLIB_GGE_NSFT_LIST_MODE_COMMON_T common;
    /// band indicator (0: GSM450 / 1: GSM850 / 2: GSM900 / 3: DCS1800 / 4: PCS1900)
    unsigned int band;
    /// TCH arfcn
    unsigned int arfcn;
    /// TSC
    unsigned int tsc;
    /// Modulation (0: GMSK / 1: EPSK)
    unsigned char rtx_type;
    /// RX power
    int RX_power;
    /// Test pcl count
    int valid_test_count;
    /// TX pcl
    int tx_pcl[MAX_MEASURE_NUM];
    /// TX test count
    int repeat_count[MAX_MEASURE_NUM];
    /// Measurement slot offset
    unsigned char meas_slot_offset;
} S_RCTLIB_GGE_NSFT_LIST_MODE_MEASURE_T;

typedef union
{
    S_RCTLIB_GGE_NSFT_LIST_MODE_SYNC_T    sync;
    S_RCTLIB_GGE_NSFT_LIST_MODE_TRIGGER_T trigger;
    S_RCTLIB_GGE_NSFT_LIST_MODE_MEASURE_T test;
} S_RCTLIB_GGE_NSFT_LIST_MODE_COMMAND_T;

#define GGE_LIST_MODE_NSFT_MAX_SYNC_CMD_NUM    5
#define GGE_LIST_MODE_NSFT_MAX_TRIGGER_CMD_NUM 5
#define GGE_LIST_MODE_NSFT_MAX_MEAS_CMD_NUM    50

#define GGE_LIST_MODE_NSFT_MAX_MEAS_PCL_NUM    8

#define GGE_LIST_MODE_NSFT_MAX_CMD_NUM   \
    GGE_LIST_MODE_NSFT_MAX_MEAS_CMD_NUM + \
    GGE_LIST_MODE_NSFT_MAX_TRIGGER_CMD_NUM + \
    GGE_LIST_MODE_NSFT_MAX_SYNC_CMD_NUM // (Test + Sync + Trigger)
typedef struct
{
    unsigned int cmd_count;
    S_RCTLIB_GGE_NSFT_LIST_MODE_COMMAND_T nsft_cmd[GGE_LIST_MODE_NSFT_MAX_CMD_NUM];
} S_RCTLIB_GGE_NSFT_LIST_MODE_SETTINGS_T;

typedef union
{
    S_RCTLIB_GGE_NSFT_GMSK_RESULT_T gmsk;
    S_RCTLIB_GGE_NSFT_EPSK_RESULT_T epsk;
} S_RCTLIB_GGE_NSFT_LIST_MODE_RESULT_ELEMENT_T;

typedef struct
{
    S_RCTLIB_GGE_NSFT_LIST_MODE_RESULT_ELEMENT_T result[GGE_LIST_MODE_NSFT_MAX_CMD_NUM][GGE_LIST_MODE_NSFT_MAX_MEAS_PCL_NUM];
} S_RCTLIB_GGE_NSFT_LIST_MODE_RESULT_T;
/**********************************************
 * GGE function (shared library defined START)
 **********************************************/
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_ConfigCellPower(double power);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_ConfigDefaultSettings(void);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_ConfigAnalyzerFrequencyOffset(const S_RCTLIB_GGE_FREQUENCY_OFFSET_SETTINGS_T *pSettings, unsigned int sz);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_CAPID_PreSettings(const S_RCTLIB_GGE_CAPID_SETTINGS_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_CAPID_Iteration(double *frequency_error);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_AFC_PreSettings(const S_RCTLIB_GGE_AFC_SETTINGS_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_AGC_PreSettings(double d_power);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_AGC_ChangeCellBand(unsigned int band);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_AGC_ChangeChannel(unsigned int arfcn);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_APCDCOffset_PreSettings(unsigned int tsc);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_APCDCOffset_ChangeCellBand(unsigned int band);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_APCDCOffset_Iteration(unsigned int arfcn, double expected_power, int PCL, double *d_power);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_Pedestal_DCOffset_Iteration(unsigned int arfcn, double expected_power, double *d_power);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_EDGE_APCDCOffset_PreSettings(unsigned int tsc);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_EDGE_APCDCOffset_ChangeCellBand(unsigned int band);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_EDGE_APCDCOffset_Iteration(unsigned int arfcn, double expected_power, int PCL, double *d_power);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_EDGE_APC_SetExceptedPower(double expected_power);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_EDGE_APC_Iteration(unsigned int arfcn, double *d_power);

NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_FHC_DTS_PreSettings(void);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_FHC_DTS_Iteration(S_RCTLIB_FHC_DL_List_T &List, int ListLength);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_FHC_DTS_Iteration_512p(S_RCTLIB_FHC_DL_List_512p_T &List, int ListLength);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_FHC_DTS_START(void);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_FHC_DTS_STOP(void);

NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_FHC_UTS_PreSettings(void);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_FHC_UTS_Iteration(S_RCTLIB_GGE_FHC_TX_UTS_T *uts);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_FHC_UTS_FetchResult(const S_RCTLIB_GGE_FHC_TX_UTS_T *uts, S_RCTLIB_GGE_FHC_TX_UTS_RESULT_T *uts_result);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_FHC_UTS_Iteration_Ex(S_RCTLIB_GGE_FHC_TX_UTS_BIG_T *uts);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_FHC_UTS_FetchResult_Ex(const S_RCTLIB_GGE_FHC_TX_UTS_BIG_T *uts, S_RCTLIB_GGE_FHC_TX_UTS_BIG_RESULT_T *uts_result);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_FHC_UTS_Iteration_Ex_512p(S_RCTLIB_GGE_FHC_TX_UTS_BIG_512P_T *uts);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_FHC_UTS_FetchResult_Ex_512p(const S_RCTLIB_GGE_FHC_TX_UTS_BIG_512P_T *uts, S_RCTLIB_GGE_FHC_TX_UTS_BIG_512P_RESULT_T *uts_result);

NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_FBDAC_PreSettings(const S_RCTLIB_GGE_FBDAC_SETTINGS_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_FBDAC_Iteration(double *d_power);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_TXIQ_PreSettings(const S_RCTLIB_GGE_TXIQ_SETTINGS_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_TXIQ_ChangeBand(const S_RCTLIB_GGE_TXIQ_SETTINGS_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_TXIQ_Iteration(const S_RCTLIB_GGE_TXIQ_SETTINGS_T *pSettings, S_RCTLIB_GGE_TXIQ_RESULT_T *pTxIqResult);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_TXSlopeSkew_PreSettings(const S_RCTLIB_GGE_TXSLOPESKEW_SETTINGS_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_TXSlopeSkew_Iteration(double *d_mod_depth);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_TRXOffset_PreSettings(const S_RCTLIB_GGE_TRXOFFSET_SETTINGS_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_TRXOffset_InitAFC(const S_RCTLIB_GGE_TRXOFFSET_SETTINGS_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_TRXOffset_Iteration(double *frequency_err);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_NSFT_PreSettings(unsigned int measurement_count, unsigned int ber_count);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_NSFT_GMSKInit(const S_RCTLIB_GGE_NSFT_TESTCONFIG_T *nsft_config);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_NSFT_EPSKInit(const S_RCTLIB_GGE_NSFT_TESTCONFIG_T *nsft_config);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_NSFT_BERInit(const S_RCTLIB_GGE_NSFT_TESTCONFIG_T *nsft_config);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_NSFT_ChangePCL(int b_EPSK, unsigned int pcl);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_NSFT_ReadGMSKPerformance(S_RCTLIB_GGE_NSFT_GMSK_RESULT_T *gmsk_result);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_NSFT_InitiateBER(void);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_NSFT_FetchBER(S_RCTLIB_GGE_NSFT_BER_RESULT_T *ber_result);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_NSFT_ReadEPSKPerformance(S_RCTLIB_GGE_NSFT_EPSK_RESULT_T *epsk_result);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_GET_SpecificSettings(S_RCTLIB_GGE_SPECIFIC_SETTINGS_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_Sweep_PreSettings(const S_RCTLIB_GGE_CRYSTAL_AFC_SWEEP_SETTINGS_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_Crystal_AFC_Sweep_Initiate(const S_RCTLIB_GGE_CAPID_SETTINGS_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_Crystal_AFC_Sweep_Fetch(S_RCTLIB_GGE_CRYSTAL_AFC_SWEEP_RESULTS_T *pResults);
NON_REENTRANT_FUNC int __stdcall RCTLIB_GGE_NSFT_ChangePow(int b_EPSK, double pow);
// reentrant functions
int __stdcall RCTLIB_GGE_ConfigCellPower_r(const int meta_handle, double power);
int __stdcall RCTLIB_GGE_ConfigDefaultSettings_r(const int meta_handle);
int __stdcall RCTLIB_GGE_ConfigAnalyzerFrequencyOffset_r(const int meta_handle, const S_RCTLIB_GGE_FREQUENCY_OFFSET_SETTINGS_T *pSettings, unsigned int sz);
int __stdcall RCTLIB_GGE_CAPID_PreSettings_r(const int meta_handle, const S_RCTLIB_GGE_CAPID_SETTINGS_T *pSettings);
int __stdcall RCTLIB_GGE_CAPID_Iteration_r(const int meta_handle, double *frequency_error);
int __stdcall RCTLIB_GGE_AFC_PreSettings_r(const int meta_handle, const S_RCTLIB_GGE_AFC_SETTINGS_T *pSettings);
int __stdcall RCTLIB_GGE_AGC_PreSettings_r(const int meta_handle, double d_power);
int __stdcall RCTLIB_GGE_AGC_ChangeCellBand_r(const int meta_handle, unsigned int band);
int __stdcall RCTLIB_GGE_AGC_ChangeChannel_r(const int meta_handle, unsigned int arfcn);
int __stdcall RCTLIB_GGE_APCDCOffset_PreSettings_r(const int meta_handle, unsigned int tsc);
int __stdcall RCTLIB_GGE_APCDCOffset_ChangeCellBand_r(const int meta_handle, unsigned int band);
int __stdcall RCTLIB_GGE_APCDCOffset_Iteration_r(const int meta_handle, unsigned int arfcn, double expected_power, int PCL, double *d_power);
int __stdcall RCTLIB_GGE_Pedestal_DCOffset_Iteration_r(const int meta_handle, unsigned int arfcn, double expected_power, double *d_power);
int __stdcall RCTLIB_GGE_EDGE_APCDCOffset_PreSettings_r(const int meta_handle, unsigned int tsc);
int __stdcall RCTLIB_GGE_EDGE_APCDCOffset_ChangeCellBand_r(const int meta_handle, unsigned int band);
int __stdcall RCTLIB_GGE_EDGE_APCDCOffset_Iteration_r(const int meta_handle, unsigned int arfcn, double expected_power, int PCL, double *d_power);
int __stdcall RCTLIB_GGE_EDGE_APC_SetExceptedPower_r(const int meta_handle, double expected_power);
int __stdcall RCTLIB_GGE_EDGE_APC_Iteration_r(const int meta_handle, unsigned int arfcn, double *d_power);

int __stdcall RCTLIB_GGE_FHC_DTS_PreSettings_r(const int meta_handle);
int __stdcall RCTLIB_GGE_FHC_DTS_Iteration_r(const int meta_handle, S_RCTLIB_FHC_DL_List_T &List, int ListLength);
int __stdcall RCTLIB_GGE_FHC_DTS_Iteration_512p_r(const int meta_handle, S_RCTLIB_FHC_DL_List_512p_T &List, int ListLength);
int __stdcall RCTLIB_GGE_FHC_DTS_START_r(const int meta_handle);
int __stdcall RCTLIB_GGE_FHC_DTS_STOP_r(const int meta_handle);

int __stdcall RCTLIB_GGE_FHC_UTS_PreSettings_r(const int meta_handle);
int __stdcall RCTLIB_GGE_FHC_UTS_Iteration_r(const int meta_handle, S_RCTLIB_GGE_FHC_TX_UTS_T *uts);
int __stdcall RCTLIB_GGE_FHC_UTS_FetchResult_r(const int meta_handle, const S_RCTLIB_GGE_FHC_TX_UTS_T *uts, S_RCTLIB_GGE_FHC_TX_UTS_RESULT_T *uts_result);
int __stdcall RCTLIB_GGE_FHC_UTS_Iteration_Ex_r(const int meta_handle, S_RCTLIB_GGE_FHC_TX_UTS_BIG_T *uts);
int __stdcall RCTLIB_GGE_FHC_UTS_FetchResult_Ex_r(const int meta_handle, const S_RCTLIB_GGE_FHC_TX_UTS_BIG_T *uts, S_RCTLIB_GGE_FHC_TX_UTS_BIG_RESULT_T *uts_result);
int __stdcall RCTLIB_GGE_FHC_UTS_Iteration_Ex_512p_r(const int meta_handle, S_RCTLIB_GGE_FHC_TX_UTS_BIG_512P_T *uts);
int __stdcall RCTLIB_GGE_FHC_UTS_FetchResult_Ex_512p_r(const int meta_handle, const S_RCTLIB_GGE_FHC_TX_UTS_BIG_512P_T *uts, S_RCTLIB_GGE_FHC_TX_UTS_BIG_512P_RESULT_T *uts_result);

int __stdcall RCTLIB_GGE_FBDAC_PreSettings_r(const int meta_handle, const S_RCTLIB_GGE_FBDAC_SETTINGS_T *pSettings);
int __stdcall RCTLIB_GGE_FBDAC_Iteration_r(const int meta_handle, double *d_power);
int __stdcall RCTLIB_GGE_TXIQ_PreSettings_r(const int meta_handle, const S_RCTLIB_GGE_TXIQ_SETTINGS_T *pSettings);
int __stdcall RCTLIB_GGE_TXIQ_ChangeBand_r(const int meta_handle, const S_RCTLIB_GGE_TXIQ_SETTINGS_T *pSettings);
int __stdcall RCTLIB_GGE_TXIQ_Iteration_r(const int meta_handle, const S_RCTLIB_GGE_TXIQ_SETTINGS_T *pSettings, S_RCTLIB_GGE_TXIQ_RESULT_T *pTxIqResult);
int __stdcall RCTLIB_GGE_TXSlopeSkew_PreSettings_r(const int meta_handle, const S_RCTLIB_GGE_TXSLOPESKEW_SETTINGS_T *pSettings);
int __stdcall RCTLIB_GGE_TXSlopeSkew_Iteration_r(const int meta_handle, double *d_mod_depth);
int __stdcall RCTLIB_GGE_TRXOffset_PreSettings_r(const int meta_handle, const S_RCTLIB_GGE_TRXOFFSET_SETTINGS_T *pSettings);
int __stdcall RCTLIB_GGE_TRXOffset_InitAFC_r(const int meta_handle, const S_RCTLIB_GGE_TRXOFFSET_SETTINGS_T *pSettings);
int __stdcall RCTLIB_GGE_TRXOffset_Iteration_r(const int meta_handle, double *frequency_err);
int __stdcall RCTLIB_GGE_NSFT_PreSettings_r(const int meta_handle, unsigned int measurement_count, unsigned int ber_count);
int __stdcall RCTLIB_GGE_NSFT_GMSKInit_r(const int meta_handle, const S_RCTLIB_GGE_NSFT_TESTCONFIG_T *nsft_config);
int __stdcall RCTLIB_GGE_NSFT_EPSKInit_r(const int meta_handle, const S_RCTLIB_GGE_NSFT_TESTCONFIG_T *nsft_config);
int __stdcall RCTLIB_GGE_NSFT_BERInit_r(const int meta_handle, const S_RCTLIB_GGE_NSFT_TESTCONFIG_T *nsft_config);
int __stdcall RCTLIB_GGE_NSFT_ChangePCL_r(const int meta_handle, int b_EPSK, unsigned int pcl);
int __stdcall RCTLIB_GGE_NSFT_ReadGMSKPerformance_r(const int meta_handle, S_RCTLIB_GGE_NSFT_GMSK_RESULT_T *gmsk_result);
int __stdcall RCTLIB_GGE_NSFT_InitiateBER_r(const int meta_handle);
int __stdcall RCTLIB_GGE_NSFT_FetchBER_r(const int meta_handle, S_RCTLIB_GGE_NSFT_BER_RESULT_T *ber_result);
int __stdcall RCTLIB_GGE_NSFT_ReadEPSKPerformance_r(const int meta_handle, S_RCTLIB_GGE_NSFT_EPSK_RESULT_T *epsk_result);
int __stdcall RCTLIB_GGE_GET_SpecificSettings_r(const int meta_handle, S_RCTLIB_GGE_SPECIFIC_SETTINGS_T *pSettings);
int __stdcall RCTLIB_GGE_LIST_MODE_NSFT_PreSetting_r(const int meta_handle);
int __stdcall RCTLIB_GGE_LIST_MODE_NSFT_InitiateTestPlan_r(const int meta_handle, const S_RCTLIB_GGE_NSFT_LIST_MODE_SETTINGS_T *pSettings);
int __stdcall RCTLIB_GGE_LIST_MODE_NSFT_FetchTestPlanResult_r(const int meta_handle, const S_RCTLIB_GGE_NSFT_LIST_MODE_SETTINGS_T *pSettings, S_RCTLIB_GGE_NSFT_LIST_MODE_RESULT_T *pResult);
int __stdcall RCTLIB_GGE_LIST_MODE_STOP_r(const int meta_handle);
int __stdcall RCTLIB_GGE_Switch_RF_Port_Ex(const int meta_handle, AntennaPortType eRfPortType, E_RCTLIB_RF_PORT_TYPE ePort);
int __stdcall RCTLIB_GGE_Switch_RF_MultipleRxPort(const int meta_handle, S_RCTLIB_ENABLE_MULTIPLE_RX_RF_PORT_T rxPortList);
int __stdcall RCTLIB_GGE_Config_RF_MultipleRxPort_CableLoss(const int meta_handle, unsigned char ucBand, S_RCTLIB_ENABLE_MULTIPLE_RX_RF_PORT_T rxPortList);
int __stdcall RCTLIB_GGE_Sweep_PreSettings_r(const int meta_handle, const S_RCTLIB_GGE_CRYSTAL_AFC_SWEEP_SETTINGS_T *pSettings);
int __stdcall RCTLIB_GGE_Crystal_AFC_Sweep_Initiate_r(const int meta_handle, const S_RCTLIB_GGE_CAPID_SETTINGS_T *pSettings);
int __stdcall RCTLIB_GGE_Crystal_AFC_Sweep_Fetch_r(const int meta_handle, S_RCTLIB_GGE_CRYSTAL_AFC_SWEEP_RESULTS_T *pResults);
int __stdcall RCTLIB_GGE_NSFT_ChangePow_r(const int meta_handle, int b_EPSK, double pow);
/**********************************************
 * GGE function (shared library defined END)
 **********************************************/
/*****************************
 * WCDMA
 *****************************/

/**********************************************
 * WCDMA structure (shared library defined START)
 **********************************************/
/**
 * Descriptions: WCDMA AGC PreSetting
 */
typedef struct
{
    /// cell power for AGC calibration
    double cellPower;
    /// CPICH power (dB)
    double cpichPower;
    /// PICH power (dB)
    double pichPower;
    /// PCCPCH power (dB)
    double pccpchPower;
    /// DPCH power (dB)
    double dpchPower;
} S_RCTLIB_WCDMA_AGC_PRESETTINGS_T;
/**
 * Descriptions: WCDMA APC PreSetting
 */
typedef struct
{
    /// measurement timeout setting (ms)
    double timeout;
    /// measurement interval (us)
    double measurementInterval;
    /// trigger delay (us)
    double triggerDelay;
} S_RCTLIB_WCDMA_APC_PRESETTINGS_T;
typedef struct
{
    /// measurement timeout setting (ms)
    double timeout;
    /// measurement interval (us)
    double measurementInterval;
    /// trigger delay (us)
    double triggerDelay;
    unsigned int triggerType; //trigger type 0:immediate 1:Rise
} S_RCTLIB_WCDMA_APC_PRESETTINGS_EX_T;
typedef struct
{
    /// step witch unit: ms
    double stepWidth;
    /// CPICH power (dB)
    double cpichPower;
    /// PICH power (dB)
    double pichPower;
    /// PCCPCH power (dB)
    double pccpchPower;
    /// DPCH power (dB)
    double dpchPower;
} S_RCTLIB_WCDMA_FHC_PRESETTINGS_T;
typedef struct
{
    /// UARFCN in MHz
    double mHz;
    /// UARFCN
    unsigned short uarfcn;
} S_RCTLIB_WCDMA_FHC_FREQ_STEP_U;
/**
 * The structure for storing WCDMA FHC request
 */
typedef struct
{
    /// band
    unsigned int e_band;
    /// measurement timeout setting
    double d_meas_timeout_sec;
    /// start index of UE TX power step
    int    txPowerStepStartIndex;
    /// start index of UE TX frequency step
    int    txFrequencyStepStartIndex;
    /// start index of UE RX power step
    int    rxPowerStepStartIndex;
    /// start index of UE RX frequency step
    int    rxFrequencyStepStartIndex;
    /// number of power step
    int    numberOfPowerSteps;
    /// number of frequency step
    int    numberOfFrequencySteps;
    /// expected TX power level (dBm)
    double txPowerSteps[20];
    /// downlink power level (dBm)
    double rxPowerSteps[20];
    /// UE TX frequency setting in each frequency step (UARFCN or MHz)
    S_RCTLIB_WCDMA_FHC_FREQ_STEP_U txFreqSteps[20];
    /// UE RX frequency setting in each frequency step (UARFCN or MHz)
    S_RCTLIB_WCDMA_FHC_FREQ_STEP_U rxFreqSteps[20];

    unsigned short peakPower;
    double trigInterval;
    double trigOffset;
} S_RCTLIB_WCDMA_FHC_MEASUREMENT_PARAM_T;
/**
 * The structure for storing WCDMA FHC request
 */
#define MAX_RCTLIB_WCDMA_BAND_SUPPORT_V2    8
typedef struct
{
    unsigned short                         calBandNumber;
    S_RCTLIB_WCDMA_FHC_MEASUREMENT_PARAM_T bandParam[MAX_RCTLIB_WCDMA_BAND_SUPPORT_V2];
} S_RCTLIB_WCDMA_FHC_MEASUREMENT_PARAM_V2_T;
/**
 * The structure for storing WCDMA FHC TX power result
 */
typedef struct
{
    /// integrity of the measurement result
    int    integrity;
    /// frequency step
    int    numberOfFrequencySteps;
    /// power step
    int    numberOfPowerSteps;
    /// frequency step * power step count
    int    tx_pwr_cnt;
    /// [OUT] power measurement result array
    double d_tx_power[400];
    /// [OUT] power measurement result count
    int    real_cnt;
} S_RCTLIB_WCDMA_FHC_MEASUREMENT_RESULT_T;
/**
 * The structure for storing WCDMA FHC TX power result
 */
typedef struct
{
    unsigned short                          calBandNumber;
    S_RCTLIB_WCDMA_FHC_MEASUREMENT_RESULT_T bandResult[MAX_RCTLIB_WCDMA_BAND_SUPPORT_V2];
} S_RCTLIB_WCDMA_FHC_MEASUREMENT_RESULT_V2_T;
/**
 * The structure for storing WCDMA NSFT TPC control used in TX performance
 */
typedef struct
{
    /// specify the test pattern (0: All up for UE max power, 1: All down for UE min power, 2: active for specified power
    int i_Pattern;
    /// TPC algorithm  (1: algo1 2: algo2)
    unsigned int u_Algorithm;
    /// TPC step size
    int i_Step;
    /// the target power if the i_Pattern is 2, expected power if the tpc pattern is 0 or 1 (all up or all down)
    double d_TargetPower;
} S_RCTLIB_WCDMA_NSFT_TPC_REQUEST_T;
/**
 * The structure for storing WCDMA NSFT BER result
 */
typedef struct
{
    /// BER
    double d_ber;
    /// bit error count
    int i_err_cnt;
    /// total test count
    int i_total_cnt;
} S_RCTLIB_WCDMA_NSFT_BER_RESULT_T;
/**
 * The structure for storing WCDMA NSFT PRACH result
 */
typedef struct
{
    /// TX on power
    double d_on_power;
    /// TX power before TX on
    double d_before_power;
    /// TX power after  TX on
    double d_after_power;
    /// error between the actual power and expected power
    double d_error;
} S_RCTLIB_WCDMA_NSFT_PRACH_RESULT_T;
/**
 * The structure for storing WCDMA NSFT SEM result
 */
typedef struct
{
    /// pass flag (overall)
    bool bPass;
    /// SEM level results
    double d_sem_level[8];
    /// frequency offset for each SEM level
    double d_sem_freq_offset[8];
    /// margin for each SEM result
    double d_sem_mask_margin[8];
    /// SEM pass flag
    int    i_sem_pass[8];
} S_RCTLIB_WCDMA_NSFT_SEM_RESULT_T;
/**
 * The structure for storing WCDMA NSFT WWQ result
 */
typedef struct
{
    /// EVM (average, dB)
    double d_evm_rms_average;
    /// max frequency error (max, Hz)
    double d_evm_max_freq_error;
    /// OOS (average, dB)
    double d_evm_origin_offset;
    /// PCDE (max, dB)
    double d_pcde;
} S_RCTLIB_WCDMA_NSFT_WWQ_RESULT_T;
/**
 * The structure for storing WCDMA NSFT ILPC result
 */
typedef struct
{
    /// integrity
    int     i_Integrity;
    /// pass flag (overall)
    bool    bPass;
    /// number of slots
    int     i_NumSlots;
    /// power (ABS)
    double  d_Absolute[200];
    /// delta value between each step
    double  d_Delta[180];
    bool    bCal_fail;
} S_RCTLIB_WCDMA_NSFT_ILPC_RESULT_T;
/**
 * the structure for storing WCDMA NSFT spectrum measurement result
 */
typedef struct
{
    /// ACLR at negative 10 MHz (dB)
    double m_dOffsetn10;
    /// ACLR at negative 5 MHz (dB)
    double m_dOffsetn5;
    /// ACLR at positive 5 MHz (dB)
    double m_dOffsetp5;
    /// ACLR at positive 10 MHz (dB)
    double m_dOffsetp10;
    /// Occupied bandwidth (MHz)
    double m_dOBW;
} S_RCTLIB_WCDMA_NSFT_SPECTRUM_RESULT_T;
/**
 * the structure for storing WCDMA NSFT TX performance (max power) measurement result
 */
typedef struct
{
    /// UE power
    double m_dUEPower;
    /// SEM test result (emission mask)
    S_RCTLIB_WCDMA_NSFT_SEM_RESULT_T semResult;
    /// WWQ test result
    S_RCTLIB_WCDMA_NSFT_WWQ_RESULT_T wwqResult;
    /// spectrum test result (ACLR, OBW)
    S_RCTLIB_WCDMA_NSFT_SPECTRUM_RESULT_T spectrumResult;
} S_RCTLIB_WCDMA_NSFT_TX_PERF_MAX_POWER_RESULT_T;
/**
 * the structure for storing WCDMA NSFT TX performance (min power) measurement result
 */
typedef struct
{
    /// UE power
    double m_dUEPower;
} S_RCTLIB_WCDMA_NSFT_TX_PERF_MIN_POWER_RESULT_T;
/**
 * Descriptions: WCDMA NSFT PreSetting
 */
typedef struct
{
    char seg;
    unsigned char exception_num;
    double start_power;
    double stop_power;
    // single step error
    double single_step_error_algo1_1dB_upper[2];// TPC cmd: +1, -1
    double single_step_error_algo1_1dB_lower[2];// TPC cmd: +1, -1
    double single_step_error_algo1_2dB_upper[2];// TPC cmd: +1, -1
    double single_step_error_algo1_2dB_lower[2];// TPC cmd: +1, -1
    double single_step_error_algo2_upper[3];// +1, 0, -1
    double single_step_error_algo2_lower[3];// +1, 0, -1
    // aggregate step error (10 steps)
    double aggregate_step_error_algo1_1dB_upper[2];// +1, -1
    double aggregate_step_error_algo1_1dB_lower[2];// +1, -1
    double aggregate_step_error_algo1_2dB_upper[2];// +1, -1
    double aggregate_step_error_algo1_2dB_lower[2];// +1, -1
    double aggregate_step_error_algo2_upper[3];// +1, 0, -1
    double aggregate_step_error_algo2_lower[3];// +1, 0, -1
} S_RCTLIB_WCDMA_NSFT_ILPC_CONFIG_T;
/**
 * Descriptions: WCDMA NSFT PreSetting
 */
typedef struct
{
    /// measurement timeout value (ms)
    double timeout;
    /// cell power for UE NSFT sync
    double cellPower;
    /// BER test count;
    unsigned int ber_bit_count;
    /// TFCI
    unsigned int tfci;
    /// SC code
    unsigned int sc_code;
    /// OVSF
    unsigned int ovsf;
    /// downlink data pattern
    unsigned int dtch_data_type;
    /// CPICH power (dB)
    double cpichPower;
    /// PICH power (dB)
    double pichPower;
    /// PCCPCH power (dB)
    double pccpchPower;
    /// DPCH power (dB)
    double dpchPower;
    /// ILPC config
    S_RCTLIB_WCDMA_NSFT_ILPC_CONFIG_T ilpcConfig;
} S_RCTLIB_WCDMA_NSFT_PRESETTINGS_T;
/**
 * WCDMA NSFT test case config
 */
typedef struct
{
    /// uplink UARFCN
    unsigned int uarfcn_ul;
    /// downlink UARFCN
    unsigned int uarfcn_dl;
    /// uplink cable loss
    double cableloss_ul;
    /// downlink cable loss
    double cableloss_dl;
    /// init cell power
    double cell_power;
    /// ILPC Test segment
    unsigned char testSegment;
} S_RCTLIB_WCDMA_NSFT_CONFIG_T;
/**
 * WCDMA NSFT PRACH pre setting
 */
typedef struct
{
    /// timeout (ms)
    double timeout;
    /// PRACH Test Uplink Interference (dBm)
    double uplinkInterference;
    /// PRACH Test Primary CPICH Power (dBm)
    double pcpichPower;
    /// PRACH Test Constant Value
    double constantValue;
    /// PRACH Test CPICH Level offset (dB)
    double cpichLevelOffset;
} S_RCTLIB_WCDMA_NSFT_PRACH_PRESETTING_T;
/**
 * WCDMA NSFT PRACH test case config
 */
typedef struct
{
    /// uplink UARFCN
    unsigned int uarfcn_ul;
    /// downlink UARFCN
    unsigned int uarfcn_dl;
    /// uplink cable loss
    double cableloss_ul;
    /// downlink cable loss
    double cableloss_dl;
} S_RCTLIB_WCDMA_NSFT_PRACH_CONFIG_T;
/**
 * Descriptions: WCDMA HSPA NSFT Presetting
 */
typedef struct
{
    /// HSDSCH UE category
    unsigned int ueCategory;
    /// measurement timeout value (ms)
    double timeout;
    /// delta ACK
    int deltaAck;
    /// delta NACK
    int deltaNack;
    /// delta CQI
    int deltaCqi;
    /// CQI feedback cycle
    int cqiFeedbackCycle;
    /// CQI repetition factor
    int cqiRepetitionFactor;
    int ovsf256;
    int ovsf128;
    int eagchChannelizationCode;
    int ehichChannelizationCode;
    int firstHspdschChannelizationCode;
    int hsscch1ChannelizationCode;
    double hspaFddTestCpichLevel;
    double hspaFddTestPccpchLevel;
    double hspaFddTestPichLevel;
    double hspaFddTestDpchLevel;
    double hspaFddTestEagchLevel;
    double hspaFddTestEhichLevel;
    double hspaFddTestHspdschsLevel;
    double hspaFddTestHsscch1Level;
    /// R99 parameters
    S_RCTLIB_WCDMA_NSFT_PRESETTINGS_T r99Param;
} S_RCTLIB_WCDMA_HSPA_NSFT_PRESETTINGS_T;

typedef void (__stdcall *DutWaitingCallback)(void);
/**
 * Descriptions: WCDMA HSPA NSFT test case configuration
 */
typedef struct
{
    /// uplink test channel
    unsigned int uplinkUarfcn;
    /// uplink test channel
    unsigned int downlinkUarfcn;
    /// input(DUT uplink) cable loss
    double inputCableLoss;
    /// output(DUT downlink) cable loss;
    double outputCableLoss;
    /// HSPA sub test number: (1 ~ 4)
    unsigned int subTest;
    /// betaC
    unsigned int betaC;
    /// betaD
    unsigned int betaD;
    /// closed-loop target power for TPC (dBm)
    int closedLoopTargetPower;
    /// expected power for RF analyzer (dBm)
    int expectedPower;
    /// callback function for DUT control loop (for adding delay)
    DutWaitingCallback delayCallback;
} S_RCTLIB_WCDMA_HSPA_NSFT_CONFIG_T;
/**
 * Descriptions: WCDMA HSPA NSFT Test result
 */
typedef struct
{
    /// max power
    double maxPower;
    /// SEM
    S_RCTLIB_WCDMA_NSFT_SEM_RESULT_T semResult;
    /// ACLR
    S_RCTLIB_WCDMA_NSFT_SPECTRUM_RESULT_T aclrResult;
} S_RCTLIB_WCDMA_HSPA_NSFT_RESULT_T;
/**
 * Descriptions: WCDMA AFC presetting
 */
typedef struct
{
    /// UARFCN for AFC calibration
    unsigned int uarfcn_dl;
    /// cell power for AFC calibration
    double d_cell_power;
} S_RCTLIB_WCDMA_AFC_PRESETTINGS_T;
/**
 * Descriptions: WCDMA List mode specific setting
 */
typedef struct
{
    unsigned char  dpcch_pwr;///<default 8, depends on testing requirement
    unsigned char  dpdch_pwr;///<default 15, depends on testing requirement
    unsigned short full_test_time;///<the total time of one freq.
    unsigned char  ready_time; ///< for first sync after TX transmit
    unsigned char  sync_time; ///< from cs_start to LBK is 26 frame
    unsigned char  retrial; ///<just for first sync
    unsigned short test_time; ///<the time of test for max&min TX power tests
    unsigned short rx_rssi_start_time[3]; ///<RSSI start time for 3 LNA mode
    unsigned char  rx_rssi_duration[3];
    unsigned short rx_ber_start_time; ///<BER start time, no used now.
    unsigned char  rx_ber_duration;
    unsigned short ILPC_ABC_time; ///<the time of ILPC ABC
    unsigned short ILPC_EF_time; ///<the time of ILPC EF
    unsigned short ILPC_GH_time; ///<the time of ILPC GH
    unsigned short transition_time; ///<the limitation of UE state machine is 4 frame at least
    double         expected_power[2];
    unsigned short max_case_count;
} S_RCTLIB_WCDMA_LIST_MODE_NSFT_SPECIFIC_SETTING_T;
/**
 * Description: WCDMA intrument's spcial settings
 */
typedef struct
{
    /// Tranditional calibration support
    bool bTCSupport;
    /// Tranditional calibration support
    bool bFHCSupport;
    /// WCDMA NSFT CTFC
    unsigned char ucNSFT_ctfc[4];
    /// WCDMA NSFT TFCI
    unsigned short usNSFT_tfci;
    /// WCDMA NSFT TX and BER test reverse
    bool usNSFT_TxBer_Reverse;
    /// WCDMA Selected port for two port Rxd
    E_RCTLIB_PORT_TYPE eRxdPathLossPort;
    /// WCDMA list mode setting
    S_RCTLIB_WCDMA_LIST_MODE_NSFT_SPECIFIC_SETTING_T sListModeSetting;
    /// Instrument source generator broadcast support
    bool bSgBroadcastSupport;
} S_RCTLIB_WCDMA_SPECIFIC_SETTINGS_T;

/** list mode*/
typedef struct
{
    /// R99 parameters
    S_RCTLIB_WCDMA_NSFT_PRESETTINGS_T r99Param;
} S_RCTLIB_WCDMA_LIST_MODE_NSFT_PRESETTING_T;

typedef struct
{
#define WCDMA_LIST_MODE_NSFT_MAX_TX_STEP_TEST_NUM   50
    /// uplink UARFCN
    unsigned int uarfcn_ul[WCDMA_LIST_MODE_NSFT_MAX_TX_STEP_TEST_NUM];
    /// downlink UARFCN
    unsigned int uarfcn_dl[WCDMA_LIST_MODE_NSFT_MAX_TX_STEP_TEST_NUM];
    /// uplink cable loss
    double cableloss_ul[WCDMA_LIST_MODE_NSFT_MAX_TX_STEP_TEST_NUM];
    /// downlink cable loss
    double cableloss_dl[WCDMA_LIST_MODE_NSFT_MAX_TX_STEP_TEST_NUM];
    /// init cell power(unit: dBm)
    double cell_power;
    ///peak power (unit: dBm) , [0]: max peak power, [1]: min peak power
    //double dPeakpower[2];
    ///Expected power (unit: dBm) , [0]: Expected max power, [1]: Expected min power
    double dExpectedpower[2];
    ///Downlink power for Lna mode; 0: high mode, 1: middle mode, 2: low mode(unit: dBm)
    double   dWcdma_Lna_P_DL[3];
    ///BER power level(unit: dBm)
    double   dWcdma_BER_power[WCDMA_LIST_MODE_NSFT_MAX_TX_STEP_TEST_NUM];
    /// ILPC Test segment
    //unsigned char testSegment;

    unsigned char    ucBand[WCDMA_LIST_MODE_NSFT_MAX_TX_STEP_TEST_NUM];
    unsigned char valid_freq; //valid count of dl/ul freq.
    unsigned short test_item[WCDMA_LIST_MODE_NSFT_MAX_TX_STEP_TEST_NUM]; //bit0:reserved. bit1:single end BER. bit2:RSSI measure. bit3:ILPC_ABC. bit4:ILPC_EF. bit5:ILPC_GH
    /* the time settings, unit = frame */
    unsigned short full_test_time;//the total time of one freq.
    unsigned char ready_time; // for first sync after TX transmit(the time of power up to max and ready to start test (10frames=100ms))
    unsigned char sync_time; //the time of sync, the limitation of UE state machine is 42 frame at least for 5 retrial
    unsigned char retrial; //retry times
    unsigned short test_time; //the time of test for max&min TX power tests
    unsigned short rx_rssi_start_time[3];  //RSSI start time for 3 LNA mode
    unsigned char rx_rssi_duration[3]; //the time of RSSI measurement
    unsigned short rx_ber_start_time;  //RSSI start time for 3 LNA mode
    unsigned char rx_ber_duration; //the time of BER measurement
    unsigned short ILPC_ABC_time; //the time of ILPC ABC
    unsigned short ILPC_EF_time; //the time of ILPC EF
    unsigned short ILPC_GH_time; //the time of ILPC GH
    unsigned short transition_time; //the limitation of UE state machine is 4 frame at least
} S_RCTLIB_WCDMA_LIST_MODE_NSFT_INIT_SETTING_T;

/**
 * The structure for storing NSFT SEM result of list mode
 */
typedef struct
{
    /// pass flag (overall), pass(0)/fail(1)/not tested(-1)
    char cPass;
    /// Total power Ref.
    double dTotalPowerRef;
    /// Peak Ref Freq.
    double dPeakRefFreq;
    /// peak freq of lowers
    double dPeakFreqLower[6];
    /// peak freq of uppers
    double dPeakFreqUpper[6];
    /// Rel Int Power of lowers
    double dRelIntPowerLower[6];
    /// Rel Int Power of uppers
    double dRelIntPowerUpper[6];
    /// Abs Int Power of lowers
    double dAbsIntPowerLower[6];
    /// Abs Int Power of uppers
    double dAbsIntPowerUpper[6];
    /// Rel Peak Power of lowers
    double dRelPeakPowerLower[6];
    /// Rel Peak Power of uppers
    double dRelPeakPowerUpper[6];
    /// Abs Peak Power of lowers
    double dAbsPeakPowerLower[6];
    /// Abs Peak Power of uppers
    double dAbsPeakPowerUpper[6];
    /// Lower delta Limit
    double dDeltaLimitLower[6];
    /// Upper delta Limit
    double dDeltaLimitUpper[6];
} S_RCTLIB_WCDMA_LIST_MODE_NSFT_SEM_RESULT_T;
/**
 * The structure for storing NSFT EVM result of list moe
 */
typedef struct
{
    /// EVM (%rms)
    double dRMS_EVM;
    /// EVM Pk (%)
    double dPeak_EVM;
    /// Frequency Error
    double dFreq_Error;
    /// IQ Offset (dB)
    double dIq_Offset_dB;
    /// PCDE (max, dB)
    double dpcde;
} S_RCTLIB_WCDMA_LIST_MODE_NSFT_EVM_RESULT_T;

/**
 * the structure for storing ACP(ACLR) result of list mode
 */
typedef struct
{
    /// Total Carrier Power(unit: dBm)
    double dCarrierPower;
    /// ACLR at negative 10 MHz (dB)  10MHz Offs Rel Pwr
    double m_dOffsetn10;
    /// ACLR at negative 5 MHz (dB) -5MHz Offs Rel Pwr
    double m_dOffsetn5;
    /// ACLR at positive 5 MHz (dB) 5MHz Offs Rel Pwr
    double m_dOffsetp5;
    /// ACLR at positive 10 MHz (dB) -10MHz Offs Rel Pwr
    double m_dOffsetp10;
    /// Occupied bandwidth (MHz)
    double m_dOBW;
} S_RCTLIB_WCDMA_LIST_MODE_NSFT_ACP_RESULT_T;
/**
 * The structure for storing WCDMA list mode NSFT ILPC config
 */
typedef struct
{
    char seg;
    unsigned char exception_num;
    double start_power;
    double stop_power;
    /// single step error
    double single_step_error_algo1_1dB_upper[2];// TPC cmd: +1, -1
    double single_step_error_algo1_1dB_lower[2];// TPC cmd: +1, -1
    double single_step_error_algo1_2dB_upper[2];// TPC cmd: +1, -1
    double single_step_error_algo1_2dB_lower[2];// TPC cmd: +1, -1
    double single_step_error_algo2_upper[3];// +1, 0, -1
    double single_step_error_algo2_lower[3];// +1, 0, -1
    /// aggregate step error (10 steps)
    double aggregate_step_error_algo1_1dB_upper[2];// +1, -1
    double aggregate_step_error_algo1_1dB_lower[2];// +1, -1
    double aggregate_step_error_algo1_2dB_upper[2];// +1, -1
    double aggregate_step_error_algo1_2dB_lower[2];// +1, -1
    double aggregate_step_error_algo2_upper[3];// +1, 0, -1
    double aggregate_step_error_algo2_lower[3];// +1, 0, -1
} S_RCTLIB_WCDMA_LIST_MODE_NSFT_ILPC_CONFIG_T;
/**
 * The structure for storing WCDMA list mode NSFT ILPC result
 */
typedef struct
{
    /// integrity
    int     i_Integrity;
    /// pass flag (overall)
    bool    bPass;
    /// number of slots
    int     i_NumSlots;
    /// power (ABS)
    double  d_Absolute[200];
    /// delta value between each step
    double  d_Delta[100];
    bool    bCal_fail;
} S_RCTLIB_WCDMA_LIST_MODE_NSFT_ILPC_RESULT_PER_SEGMENT_T;
typedef struct
{
    S_RCTLIB_WCDMA_LIST_MODE_NSFT_ILPC_RESULT_PER_SEGMENT_T sIlpc_Segment_result[8];
} S_RCTLIB_WCDMA_LIST_MODE_NSFT_ILPC_RESULT_T;
/**
 * the structure for storing TX list mode step result
 */
typedef struct
{
#define WCDMA_LIST_MODE_NSFT_MAX_TX_STEP_TEST_NUM   50
    double  dMaxPower[WCDMA_LIST_MODE_NSFT_MAX_TX_STEP_TEST_NUM];
    double  dMinPower[WCDMA_LIST_MODE_NSFT_MAX_TX_STEP_TEST_NUM];
    S_RCTLIB_WCDMA_LIST_MODE_NSFT_SEM_RESULT_T  sSEMResult[WCDMA_LIST_MODE_NSFT_MAX_TX_STEP_TEST_NUM];
    S_RCTLIB_WCDMA_LIST_MODE_NSFT_ACP_RESULT_T  sACPResult[WCDMA_LIST_MODE_NSFT_MAX_TX_STEP_TEST_NUM];
    S_RCTLIB_WCDMA_LIST_MODE_NSFT_EVM_RESULT_T  sEVMResult[WCDMA_LIST_MODE_NSFT_MAX_TX_STEP_TEST_NUM];
    S_RCTLIB_WCDMA_LIST_MODE_NSFT_ILPC_RESULT_T sILPCResult[WCDMA_LIST_MODE_NSFT_MAX_TX_STEP_TEST_NUM];
    S_RCTLIB_WCDMA_LIST_MODE_NSFT_ILPC_CONFIG_T sILPCConfig;
} S_RCTLIB_WCDMA_LIST_MODE_NSFT_TEST_TX_STEP_RESULT_T;

typedef struct
{
    S_RCTLIB_WCDMA_LIST_MODE_NSFT_TEST_TX_STEP_RESULT_T txListModeResult;
} S_RCTLIB_WCDMA_LIST_MODE_NSFT_RESULT_T;

/**
 * Descriptions: WCDMA TPUT PreSetting
 */
typedef struct
{
    /// cell power for T-Put Tuning
    double cellPower;
} S_RCTLIB_WCDMA_TPUT_PRESETTINGS_T;
/**********************************************
 * WCDMA structure (shared library defined END)
 **********************************************/
/**********************************************
 * WCDMA function (shared library defined START)
 **********************************************/
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_ConfigCellPower(double power);
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_ConfigDefaultSettings(void);
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_AFC_PreSetting(const S_RCTLIB_WCDMA_AFC_PRESETTINGS_T *pSettings, unsigned int size);
// Main path AGC calibration RCT controls
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_AGC_PreSetting(const S_RCTLIB_WCDMA_AGC_PRESETTINGS_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_AGC_ChangeCellBand(unsigned int band);
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_AGC_ChangeChannel(unsigned int uarfcn);
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_AGC_ChangeCellPower(double cellPower);
// Diversity path AGC calibration RCT controls
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_AGC_RXD_PreSetting(const S_RCTLIB_WCDMA_AGC_PRESETTINGS_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_AGC_RXD_ChangeCellBand(unsigned int band);
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_AGC_RXD_ChangeChannel(unsigned int uarfcn);
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_AGC_RXD_ChangeCellPower(double cellPower);

NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_APC_PreSetting(const S_RCTLIB_WCDMA_APC_PRESETTINGS_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_APC_PreSetting_Ex(const S_RCTLIB_WCDMA_APC_PRESETTINGS_EX_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_APC_ChangeCellBand(unsigned int band);
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_APC_ChangeChannel(unsigned int uarfcn);
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_APC_ChangeExpectedPower(int expectedPower);
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_APC_Initiate(void);
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_APC_FetchResult(double *outputPower);
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_FHC_PreSetting(const S_RCTLIB_WCDMA_FHC_PRESETTINGS_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_FHC_StartIteration(const S_RCTLIB_WCDMA_FHC_MEASUREMENT_PARAM_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_FHC_FetchResult(const S_RCTLIB_WCDMA_FHC_MEASUREMENT_PARAM_T *pSettings, S_RCTLIB_WCDMA_FHC_MEASUREMENT_RESULT_T *pResult);
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_FHC_V2_Start(const S_RCTLIB_WCDMA_FHC_MEASUREMENT_PARAM_V2_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_FHC_V2_FetchResult(const S_RCTLIB_WCDMA_FHC_MEASUREMENT_PARAM_V2_T *pSettings, S_RCTLIB_WCDMA_FHC_MEASUREMENT_RESULT_V2_T *pResult);
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_NSFT_PreSetting(const S_RCTLIB_WCDMA_NSFT_PRESETTINGS_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_NSFT_InitiateTestCase(const S_RCTLIB_WCDMA_NSFT_CONFIG_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_NSFT_TPC(const S_RCTLIB_WCDMA_NSFT_TPC_REQUEST_T *tpc_request);
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_NSFT_FetchTXPerformanceMaxPower(S_RCTLIB_WCDMA_NSFT_TX_PERF_MAX_POWER_RESULT_T *tx_perf_result);
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_NSFT_FetchTXPerformanceMinPower(S_RCTLIB_WCDMA_NSFT_TX_PERF_MIN_POWER_RESULT_T *tx_perf_result);
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_NSFT_InitiateILPCTestCase(unsigned char testSegment);
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_NSFT_FetchILPCResult(S_RCTLIB_WCDMA_NSFT_ILPC_CONFIG_T *ilpcConfig, S_RCTLIB_WCDMA_NSFT_ILPC_RESULT_T *result);
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_NSFT_ChangeCellPower(double d_cell_power);
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_NSFT_InitiateBER(void);
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_NSFT_ReadBER(S_RCTLIB_WCDMA_NSFT_BER_RESULT_T *result);
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_NSFT_PRACH_PreSetting(const S_RCTLIB_WCDMA_NSFT_PRACH_PRESETTING_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_NSFT_PRACH_CasePreSetting(void);
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_NSFT_PRACH_InitiateTestCase(const S_RCTLIB_WCDMA_NSFT_PRACH_CONFIG_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_NSFT_PRACH_FetchTestCase(S_RCTLIB_WCDMA_NSFT_PRACH_RESULT_T *result);
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_NSFT_PRACH_ChangeCellPower(double d_cell_power);
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_HSDPA_NSFT_PreSetting(const S_RCTLIB_WCDMA_HSPA_NSFT_PRESETTINGS_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_HSDPA_NSFT_InitiateTestCase(const S_RCTLIB_WCDMA_HSPA_NSFT_CONFIG_T *pConfig);
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_HSDPA_NSFT_FetchResult(S_RCTLIB_WCDMA_HSPA_NSFT_RESULT_T *pResult);
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_HSUPA_NSFT_PreSetting(const S_RCTLIB_WCDMA_HSPA_NSFT_PRESETTINGS_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_HSUPA_NSFT_InitiateTestCase(const S_RCTLIB_WCDMA_HSPA_NSFT_CONFIG_T *pConfig);
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_HSUPA_NSFT_FetchResult(S_RCTLIB_WCDMA_HSPA_NSFT_RESULT_T *pResult);
NON_REENTRANT_FUNC int __stdcall RCTLIB_WCDMA_GET_SpecificSettings(S_RCTLIB_WCDMA_SPECIFIC_SETTINGS_T *pSettings);

// reentrant funtions
int __stdcall RCTLIB_WCDMA_ConfigCellPower_r(const int meta_handle, double power);
int __stdcall RCTLIB_WCDMA_ConfigDefaultSettings_r(const int meta_handle);
int __stdcall RCTLIB_WCDMA_AFC_PreSetting_r(const int meta_handle, const S_RCTLIB_WCDMA_AFC_PRESETTINGS_T *pSettings, unsigned int size);
// Main path AGC calibration RCT controls
int __stdcall RCTLIB_WCDMA_AGC_PreSetting_r(const int meta_handle, const S_RCTLIB_WCDMA_AGC_PRESETTINGS_T *pSettings);
int __stdcall RCTLIB_WCDMA_AGC_ChangeCellBand_r(const int meta_handle, unsigned int band);
int __stdcall RCTLIB_WCDMA_AGC_ChangeChannel_r(const int meta_handle, unsigned int uarfcn);
int __stdcall RCTLIB_WCDMA_AGC_ChangeCellPower_r(const int meta_handle, double cellPower);
// Diversity path AGC calibration RCT controls
int __stdcall RCTLIB_WCDMA_AGC_RXD_PreSetting_r(const int meta_handle, const S_RCTLIB_WCDMA_AGC_PRESETTINGS_T *pSettings);
int __stdcall RCTLIB_WCDMA_AGC_RXD_ChangeCellBand_r(const int meta_handle, unsigned int band);
int __stdcall RCTLIB_WCDMA_AGC_RXD_ChangeChannel_r(const int meta_handle, unsigned int uarfcn);
int __stdcall RCTLIB_WCDMA_AGC_RXD_ChangeCellPower_r(const int meta_handle, double cellPower);

int __stdcall RCTLIB_WCDMA_APC_PreSetting_r(const int meta_handle, const S_RCTLIB_WCDMA_APC_PRESETTINGS_T *pSettings);
int __stdcall RCTLIB_WCDMA_APC_PreSetting_Ex_r(const int meta_handle, const S_RCTLIB_WCDMA_APC_PRESETTINGS_EX_T *pSettings);
int __stdcall RCTLIB_WCDMA_APC_ChangeCellBand_r(const int meta_handle, unsigned int band);
int __stdcall RCTLIB_WCDMA_APC_ChangeChannel_r(const int meta_handle, unsigned int uarfcn);
int __stdcall RCTLIB_WCDMA_APC_ChangeExpectedPower_r(const int meta_handle, int expectedPower);
int __stdcall RCTLIB_WCDMA_APC_Initiate_r(const int meta_handle);
int __stdcall RCTLIB_WCDMA_APC_FetchResult_r(const int meta_handle, double *outputPower);
int __stdcall RCTLIB_WCDMA_FHC_PreSetting_r(const int meta_handle, const S_RCTLIB_WCDMA_FHC_PRESETTINGS_T *pSettings);
int __stdcall RCTLIB_WCDMA_FHC_StartIteration_r(const int meta_handle, const S_RCTLIB_WCDMA_FHC_MEASUREMENT_PARAM_T *pSettings);
int __stdcall RCTLIB_WCDMA_FHC_FetchResult_r(const int meta_handle, const S_RCTLIB_WCDMA_FHC_MEASUREMENT_PARAM_T *pSettings, S_RCTLIB_WCDMA_FHC_MEASUREMENT_RESULT_T *pResult);
int __stdcall RCTLIB_WCDMA_FHC_V2_Start_r(const int meta_handle, const S_RCTLIB_WCDMA_FHC_MEASUREMENT_PARAM_V2_T *pSettings);
int __stdcall RCTLIB_WCDMA_FHC_V2_FetchResult_r(const int meta_handle, const S_RCTLIB_WCDMA_FHC_MEASUREMENT_PARAM_V2_T *pSettings, S_RCTLIB_WCDMA_FHC_MEASUREMENT_RESULT_V2_T *pResult);
int __stdcall RCTLIB_WCDMA_NSFT_PreSetting_r(const int meta_handle, const S_RCTLIB_WCDMA_NSFT_PRESETTINGS_T *pSettings);
int __stdcall RCTLIB_WCDMA_NSFT_InitiateTestCase_r(const int meta_handle, const S_RCTLIB_WCDMA_NSFT_CONFIG_T *pSettings);
int __stdcall RCTLIB_WCDMA_NSFT_TPC_r(const int meta_handle, const S_RCTLIB_WCDMA_NSFT_TPC_REQUEST_T *tpc_request);
int __stdcall RCTLIB_WCDMA_NSFT_FetchTXPerformanceMaxPower_r(const int meta_handle, S_RCTLIB_WCDMA_NSFT_TX_PERF_MAX_POWER_RESULT_T *tx_perf_result);
int __stdcall RCTLIB_WCDMA_NSFT_FetchTXPerformanceMinPower_r(const int meta_handle, S_RCTLIB_WCDMA_NSFT_TX_PERF_MIN_POWER_RESULT_T *tx_perf_result);
int __stdcall RCTLIB_WCDMA_NSFT_InitiateILPCTestCase_r(const int meta_handle, unsigned char testSegment);
int __stdcall RCTLIB_WCDMA_NSFT_FetchILPCResult_r(const int meta_handle, S_RCTLIB_WCDMA_NSFT_ILPC_CONFIG_T *ilpcConfig, S_RCTLIB_WCDMA_NSFT_ILPC_RESULT_T *result);
int __stdcall RCTLIB_WCDMA_NSFT_ChangeCellPower_r(const int meta_handle, double d_cell_power);
int __stdcall RCTLIB_WCDMA_NSFT_InitiateBER_r(const int meta_handle);
int __stdcall RCTLIB_WCDMA_NSFT_ReadBER_r(const int meta_handle, S_RCTLIB_WCDMA_NSFT_BER_RESULT_T *result);
int __stdcall RCTLIB_WCDMA_NSFT_STOP_r(const int meta_handle);
int __stdcall RCTLIB_WCDMA_NSFT_PRACH_PreSetting_r(const int meta_handle, const S_RCTLIB_WCDMA_NSFT_PRACH_PRESETTING_T *pSettings);
int __stdcall RCTLIB_WCDMA_NSFT_PRACH_CasePreSetting_r(const int meta_handle);
int __stdcall RCTLIB_WCDMA_NSFT_PRACH_InitiateTestCase_r(const int meta_handle, const S_RCTLIB_WCDMA_NSFT_PRACH_CONFIG_T *pSettings);
int __stdcall RCTLIB_WCDMA_NSFT_PRACH_FetchTestCase_r(const int meta_handle, S_RCTLIB_WCDMA_NSFT_PRACH_RESULT_T *result);
int __stdcall RCTLIB_WCDMA_NSFT_PRACH_ChangeCellPower_r(const int meta_handle, double d_cell_power);
int __stdcall RCTLIB_WCDMA_HSDPA_NSFT_PreSetting_r(const int meta_handle, const S_RCTLIB_WCDMA_HSPA_NSFT_PRESETTINGS_T *pSettings);
int __stdcall RCTLIB_WCDMA_HSDPA_NSFT_InitiateTestCase_r(const int meta_handle, const S_RCTLIB_WCDMA_HSPA_NSFT_CONFIG_T *pConfig);
int __stdcall RCTLIB_WCDMA_HSDPA_NSFT_FetchResult_r(const int meta_handle, S_RCTLIB_WCDMA_HSPA_NSFT_RESULT_T *pResult);
int __stdcall RCTLIB_WCDMA_HSUPA_NSFT_PreSetting_r(const int meta_handle, const S_RCTLIB_WCDMA_HSPA_NSFT_PRESETTINGS_T *pSettings);
int __stdcall RCTLIB_WCDMA_HSUPA_NSFT_InitiateTestCase_r(const int meta_handle, const S_RCTLIB_WCDMA_HSPA_NSFT_CONFIG_T *pConfig);
int __stdcall RCTLIB_WCDMA_HSUPA_NSFT_FetchResult_r(const int meta_handle, S_RCTLIB_WCDMA_HSPA_NSFT_RESULT_T *pResult);
int __stdcall RCTLIB_WCDMA_GET_SpecificSettings_r(const int meta_handle, S_RCTLIB_WCDMA_SPECIFIC_SETTINGS_T *pSettings);
int __stdcall RCTLIB_WCDMA_LIST_MODE_NSFT_PreSetting_r(const int meta_handle, const S_RCTLIB_WCDMA_LIST_MODE_NSFT_PRESETTING_T *pSetting, const unsigned int length);
int __stdcall RCTLIB_WCDMA_LIST_MODE_NSFT_InitiateTestPlan_r(const int meta_handle, const S_RCTLIB_WCDMA_LIST_MODE_NSFT_INIT_SETTING_T *pSetting, const unsigned int length);
int __stdcall RCTLIB_WCDMA_LIST_MODE_NSFT_FetchTestPlanResult_r(const int meta_handle, S_RCTLIB_WCDMA_LIST_MODE_NSFT_RESULT_T *pResult, const unsigned int length);
int __stdcall RCTLIB_WCDMA_Switch_RF_Port_Ex(const int meta_handle, AntennaPortType eRfPortType, E_RCTLIB_RF_PORT_TYPE ePort);
int __stdcall RCTLIB_WCDMA_Switch_RF_MultipleRxPort(const int meta_handle, S_RCTLIB_ENABLE_MULTIPLE_RX_RF_PORT_T rxPortList);
int __stdcall RCTLIB_WCDMA_Config_RF_MultipleRxPort_CableLoss(const int meta_handle, unsigned char ucBand, S_RCTLIB_ENABLE_MULTIPLE_RX_RF_PORT_T rxPortList);
int __stdcall RCTLIB_WCDMA_TPUT_PreSetting(const unsigned int settingSize, const S_RCTLIB_WCDMA_TPUT_PRESETTINGS_T *pSettings);
int __stdcall RCTLIB_WCDMA_TPUT_PreSetting_r(const int meta_handle, const unsigned int settingSize, const S_RCTLIB_WCDMA_TPUT_PRESETTINGS_T *pSettings);
/**********************************************
 * WCDMA function (shared library defined END)
 **********************************************/
/**********************************************
 * TDSCDMA
 **********************************************/

/*****************************************************
 * AFC structure (START)
 *****************************************************/
typedef struct
{
    /// cell power seetings
    double         d_cell_power;
    /// expected power
    int            expected_power;
    /// Average Count
    unsigned int   ui_average_cnt;
    /// UARFCN
    unsigned short us_uarfcn;
} S_RCTLIB_TDSCDMA_AFC_TESTER_CONFIG_BEFORE_CAL_T;
/*****************************************************
 * AFC structure (END)
 *****************************************************/
/*****************************************************
 * AGC structure (START)
 *****************************************************/
typedef struct
{
    /// cell power for the calibration
    double d_cell_power;
} S_RCTLIB_TDSCDMA_AGC_TESTER_CONFIG_BEFORE_CAL_T;
/*****************************************************
 * AGC structure (END)
 *****************************************************/
/*****************************************************
 * APC structure (START)
 *****************************************************/
typedef struct
{
    /// timeout value (unit: ms)
    double timeout;
    /// meaurement count
    int measurement_count;
} S_RCTLIB_TDSCDMA_APC_TESTER_CONFIG_BEFORE_CAL_T;
typedef struct
{
    /// UARFCN
    unsigned int uarfcn;
    /// expected power (dBm)
    int expected_power;
    /// [IN/OUT] channel power (dBm)
    double output_power;
} S_RCTLIB_TDSCDMA_APC_MEASUREMENT_PARAM_T;
/*****************************************************
 * APC structure (END)
 *****************************************************/
/*****************************************************
 * FHC structure (START)
 *****************************************************/
typedef struct
{
    /// timeout value (unit: ms)
    double timeout;
    bool CW;
} S_RCTLIB_TDSCDMA_FHC_TESTER_CONFIG_BEFORE_CAL_T;
typedef struct
{
    /// number of frequency steps (Max 20)
    unsigned int freq_num;
    /// frequnecy steps (used in both TX/RX since it's TDD) (MHz)
    double freq_steps[40];
    /// number of tx power steps
    unsigned int tx_power_steps;
    /// expected power level of tx power step (Max 40)
    double tx_expected_power[40];
    /// unmber of rx power steps
    unsigned int rx_power_steps;
    /// power level of rx power step (Max 40)
    double rx_power_level[40];
    /// RX retune step length (unit sub-frame)
    unsigned int rx_retune_step;
    /// TX retune step length (unit sub-frame)
    unsigned int tx_retune_step;
    /// power step length (sub-frame)
    unsigned int power_step_length;
    ///Use CW or not
    bool CW;
} S_RCTLIB_TDSCDMA_FHC_MEASUREMENT_PARAM_T;
typedef struct
{
    int integrity;
    /// number of frequencye steps
    int freq_steps;
    /// number of tx power steps
    int tx_power_steps;
    /// number of tx power samples
    int tx_power_samples;
    /// TX power measurement result
    double tx_power[400];
} S_RCTLIB_TDSCDMA_FHC_MEASUREMENT_RESULT_T;
/*****************************************************
 * FHC structure (END)
 *****************************************************/
typedef struct
{
    unsigned char sc_code;
    int           m_iDtchType; // PRBS9 (0) | PRBS15 (1)| INCRement (2) | ZERos (3) | ONEs (4) |ALTernating (5)
} S_RCTLIB_TD_NSFT_TESTER_COMMON_CONFIG_T;

typedef struct
{
    unsigned short  m_usTD_NSFT_UARFCN;
    double          m_dTD_NSFT_CableLoss;
    double          m_dTD_NSFT_Cell_Power;
    //      double          m_dTD_NSFT_BER_LEVEL;
    double          m_dTD_NSFT_MAX_PWR_AVG_HIGH;
} S_RCTLIB_TD_NSFT_TESTER_CONFIG_T;


#define RCTLIB_TD_NSFT_SEM_MARGIN_NUM        3

typedef struct
{
    double m_dMinMsTargetPwr;
    double m_dMaxMsTargetPwr;
} S_RCTLIB_TD_NSFT_TPC_CONFIG_ENTRY_T;


//#define TD_NSFT_SEM_MARGIN_NUM 3

typedef struct
{
    bool   bPass;
    double d_sem_level[RCTLIB_TD_NSFT_SEM_MARGIN_NUM];
    double d_sem_freq_offset[RCTLIB_TD_NSFT_SEM_MARGIN_NUM];
    double d_sem_margin[RCTLIB_TD_NSFT_SEM_MARGIN_NUM];
    bool   b_sem_pass[RCTLIB_TD_NSFT_SEM_MARGIN_NUM];
    double d_in_channel_pwr[RCTLIB_TD_NSFT_SEM_MARGIN_NUM];
} S_RCTLIB_TDSCDMA_NSFT_SEM_RESULT_T;


typedef struct
{
    bool bPass;
    double d_aclr_result[4];  // -1.6/1.6/-3.2/3.2 MHz Offset
    bool   b_aclr_pass[4];    // -1.6/1.6/-3.2/3.2 MHz Offset
} S_TDSCDMA_NSFT_ACLR_RESULT_T;


typedef struct
{
    double d_evm_rms;
    double d_evm_max_freq_error;
    double d_evm_origin_offset;
    double d_max_pcde;
} S_TDSCDMA_NSFT_WQ_RESULT_T;

typedef struct
{
    bool bPass;
    double d_avg_on_pwr;
    bool   b_oop_pass[3];
    double d_avg_pwr[3];
} S_TDSCDMA_NSFT_OOP_RESULT_T;

/*
integrity indicator, overall pass/fail, maximum power, minimum power, worst REL1POW step index, worst REL1POW absolute power, worst REL1POW, worst REL10POW step index, worst REL10POW absolute power and worst REL10POW
*/


typedef struct
{
    bool bPass;
    double d_max_pwr;
    double d_min_pwr;
    int    i_worst_step_index[2];  // [0]: REL1POW, [1]:REL10POW
    double d_worst_abs_pwr[2]; // [0]: REL1POW, [1]:REL10POW
    double d_worst_pwr[2];  // [0]: REL1POW, [1]:REL10POW


} S_TDSCDMA_NSFT_CLP_RESULT_T;




typedef struct
{
    double d_min_avg_pwr;
    double d_max_avg_pwr;
    double d_obw_max;

    S_RCTLIB_TDSCDMA_NSFT_SEM_RESULT_T  r_sem_result;
    S_TDSCDMA_NSFT_ACLR_RESULT_T r_aclr_result;
    S_TDSCDMA_NSFT_WQ_RESULT_T   r_wq_result;
    S_TDSCDMA_NSFT_WQ_RESULT_T   r_wq_result_at_low_power;
    S_TDSCDMA_NSFT_OOP_RESULT_T  r_oop_result;
    S_TDSCDMA_NSFT_CLP_RESULT_T  r_clp_result;

    double d_ACLR_Low5;
    double d_ACLR_Up5;
    double d_ACLR_Low10;
    double d_ACLR_Up10;

    double d_ber_from_target;
    double d_ber_from_tester;   // the ber measured by tester

} S_RCTLIB_TDA_NSFT_MEASURE_RESULT_ENTRY_T;

typedef struct
{
    double d_cell_pwr;
    bool   b_loopback_ber;  // loopback BER
    int    i_bit_cnt;   // valid when b_loopback_ber = true;

} S_RCTLIB_TD_NSFT_BER_CONFIG_ENTRY_T;
/**
 * Description: TDSCDMA intrument's spcial settings
 */
typedef struct
{
    /// Tranditional calibration support
    bool bTCSupport;
    /// Tranditional calibration support
    bool bFHCSupport;
    /// NSFT UL midamble config
    unsigned char  usNSFT_midamble;
    /// NSFT Single-End BER bit pattern, 0: all zero, 1: all one
    unsigned char  usNSFTSingleEndBitPattern;
    /// The PCCPCH setting for NSFT
    int iPCCPCH;
    /// The PCCPCH setting for calibration
    int iPCCPCH_CAL;
    /// FHC Tx Rx Alignment
    bool bTxRxAlign;
} S_RCTLIB_TDSCDMA_SPECIFIC_SETTINGS_T;

typedef struct
{
    S_RCTLIB_TD_NSFT_TESTER_COMMON_CONFIG_T  tda_nsft_common_cfg;
} S_RCTLIB_TDSCDMA_LIST_MODE_NSFT_PRESETTING_T;

#define RCTLIB_TD_NSFT_LIST_MODE_CHANNEL_NUM_MAX   2
#define RCTLIB_TD_NSFT_LIST_MODE_MEAS_RSCP_NUM_MAX 3

typedef struct
{
    unsigned short  tda_nsft_freq_num;
    unsigned short  tda_nsft_arfcn[RCTLIB_TD_NSFT_LIST_MODE_CHANNEL_NUM_MAX];
    double          tda_nsft_cableloss;
    double          tda_nsft_ber_cell_pwr[RCTLIB_TD_NSFT_LIST_MODE_CHANNEL_NUM_MAX];
    double          tda_nsft_rscp_cell_pwr[RCTLIB_TD_NSFT_LIST_MODE_CHANNEL_NUM_MAX][RCTLIB_TD_NSFT_LIST_MODE_MEAS_RSCP_NUM_MAX];
} S_RCTLIB_TDSCDMA_LIST_MODE_NSFT_INIT_SETTING_T;

typedef struct
{
    unsigned short  tda_nsft_freq_num;
    S_RCTLIB_TDA_NSFT_MEASURE_RESULT_ENTRY_T  tda_nsft_measure_result[2];
} S_RCTLIB_TDSCDMA_LIST_MODE_NSFT_RESULT_T;
/*****************************************************
 * exported function (START)
 *****************************************************/
NON_REENTRANT_FUNC int __stdcall RCTLIB_TDSCDMA_ConfigTesterCommonBeforeCal(void);

NON_REENTRANT_FUNC int __stdcall RCTLIB_TDSCDMA_ConfigTesterBeforeAFC(const S_RCTLIB_TDSCDMA_AFC_TESTER_CONFIG_BEFORE_CAL_T *p_rConfig);
NON_REENTRANT_FUNC int __stdcall RCTLIB_TDSCDMA_MeasureAFC(double *p_dFreqOffset);

NON_REENTRANT_FUNC int __stdcall RCTLIB_TDSCDMA_ConfigTesterBeforeAGC(const S_RCTLIB_TDSCDMA_AGC_TESTER_CONFIG_BEFORE_CAL_T *p_rConfig);
NON_REENTRANT_FUNC int __stdcall RCTLIB_TDSCDMA_AGC_ChangeCellPower(double cell_power);
NON_REENTRANT_FUNC int __stdcall RCTLIB_TDSCDMA_AGC_ChangeCellBand(unsigned int band);
NON_REENTRANT_FUNC int __stdcall RCTLIB_TDSCDMA_AGC_ChangeChannel(unsigned int uarfcn);

NON_REENTRANT_FUNC int __stdcall RCTLIB_TDSCDMA_ConfigTesterBeforeAPC(const S_RCTLIB_TDSCDMA_APC_TESTER_CONFIG_BEFORE_CAL_T *p_rConfig);
NON_REENTRANT_FUNC int __stdcall RCTLIB_TDSCDMA_APC_ChangeCellBand(unsigned int band);
NON_REENTRANT_FUNC int __stdcall RCTLIB_TDSCDMA_APC_MeasurePower(S_RCTLIB_TDSCDMA_APC_MEASUREMENT_PARAM_T *param);

NON_REENTRANT_FUNC int __stdcall RCTLIB_TDSCDMA_ConfigTesterBeforeFHC(const S_RCTLIB_TDSCDMA_FHC_TESTER_CONFIG_BEFORE_CAL_T *p_rConfig);
NON_REENTRANT_FUNC int __stdcall RCTLIB_TDSCDMA_FHC_StartIteration(const S_RCTLIB_TDSCDMA_FHC_MEASUREMENT_PARAM_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_TDSCDMA_FHC_FetchResult(const S_RCTLIB_TDSCDMA_FHC_MEASUREMENT_PARAM_T *pSettings, S_RCTLIB_TDSCDMA_FHC_MEASUREMENT_RESULT_T *pResult);

NON_REENTRANT_FUNC int __stdcall RCTLIB_TDSCDMA_ConfigTesterCommonBeforeNSFT(S_RCTLIB_TD_NSFT_TESTER_COMMON_CONFIG_T *common_cfg);
NON_REENTRANT_FUNC int __stdcall RCTLIB_TDSCDMA_ConfigTesterForNSFT(const S_RCTLIB_TD_NSFT_TESTER_CONFIG_T *cfg);
NON_REENTRANT_FUNC int __stdcall RCTLIB_TDSCDMA_MeasureTPCForNSFT(const S_RCTLIB_TD_NSFT_TPC_CONFIG_ENTRY_T *req,
                                                                  S_RCTLIB_TDA_NSFT_MEASURE_RESULT_ENTRY_T *res);

NON_REENTRANT_FUNC int __stdcall RCTLIB_TDSCDMA_ConfigTesterForNSFTBer(const S_RCTLIB_TD_NSFT_BER_CONFIG_ENTRY_T *cfg);
NON_REENTRANT_FUNC int __stdcall RCTLIB_TDSCDMA_MeasureLBerForNSFT(S_RCTLIB_TDA_NSFT_MEASURE_RESULT_ENTRY_T *res);


NON_REENTRANT_FUNC int __stdcall RCTLIB_TDSCDMA_ConfigTesterCommonBeforeFT(S_RCTLIB_TD_NSFT_TESTER_COMMON_CONFIG_T *common_cfg);
NON_REENTRANT_FUNC int __stdcall RCTLIB_TDSCDMA_ConfigTesterForFT(const S_RCTLIB_TD_NSFT_TESTER_CONFIG_T *cfg);
NON_REENTRANT_FUNC int __stdcall RCTLIB_TDSCDMA_MeasureTPCForFT(const S_RCTLIB_TD_NSFT_TPC_CONFIG_ENTRY_T *req,
                                                                S_RCTLIB_TDA_NSFT_MEASURE_RESULT_ENTRY_T *res);

NON_REENTRANT_FUNC int __stdcall RCTLIB_TDSCDMA_ConfigTesterForFTBer(const S_RCTLIB_TD_NSFT_BER_CONFIG_ENTRY_T *cfg);
NON_REENTRANT_FUNC int __stdcall RCTLIB_TDSCDMA_MeasureLBerForFT(S_RCTLIB_TDA_NSFT_MEASURE_RESULT_ENTRY_T *res);

NON_REENTRANT_FUNC int __stdcall RCTLIB_TDSCDMA_GET_SpecificSettings(S_RCTLIB_TDSCDMA_SPECIFIC_SETTINGS_T *pSettings);

// reentrant funtions
int __stdcall RCTLIB_TDSCDMA_ConfigTesterCommonBeforeCal_r(const int meta_handle);

int __stdcall RCTLIB_TDSCDMA_ConfigTesterBeforeAFC_r(const int meta_handle, const S_RCTLIB_TDSCDMA_AFC_TESTER_CONFIG_BEFORE_CAL_T *p_rConfig);
int __stdcall RCTLIB_TDSCDMA_MeasureAFC_r(const int meta_handle, double *p_dFreqOffset);

int __stdcall RCTLIB_TDSCDMA_ConfigTesterBeforeAGC_r(const int meta_handle, const S_RCTLIB_TDSCDMA_AGC_TESTER_CONFIG_BEFORE_CAL_T *p_rConfig);
int __stdcall RCTLIB_TDSCDMA_AGC_ChangeCellPower_r(const int meta_handle, double cell_power);
int __stdcall RCTLIB_TDSCDMA_AGC_ChangeCellBand_r(const int meta_handle, unsigned int band);
int __stdcall RCTLIB_TDSCDMA_AGC_ChangeChannel_r(const int meta_handle, unsigned int uarfcn);

int __stdcall RCTLIB_TDSCDMA_ConfigTesterBeforeAPC_r(const int meta_handle, const S_RCTLIB_TDSCDMA_APC_TESTER_CONFIG_BEFORE_CAL_T *p_rConfig);
int __stdcall RCTLIB_TDSCDMA_APC_ChangeCellBand_r(const int meta_handle, unsigned int band);
int __stdcall RCTLIB_TDSCDMA_APC_MeasurePower_r(const int meta_handle, S_RCTLIB_TDSCDMA_APC_MEASUREMENT_PARAM_T *param);

int __stdcall RCTLIB_TDSCDMA_ConfigTesterBeforeFHC_r(const int meta_handle, const S_RCTLIB_TDSCDMA_FHC_TESTER_CONFIG_BEFORE_CAL_T *p_rConfig);
int __stdcall RCTLIB_TDSCDMA_FHC_StartIteration_r(const int meta_handle, const S_RCTLIB_TDSCDMA_FHC_MEASUREMENT_PARAM_T *pSettings);
int __stdcall RCTLIB_TDSCDMA_FHC_FetchResult_r(const int meta_handle, const S_RCTLIB_TDSCDMA_FHC_MEASUREMENT_PARAM_T *pSettings, S_RCTLIB_TDSCDMA_FHC_MEASUREMENT_RESULT_T *pResult);

int __stdcall RCTLIB_TDSCDMA_ConfigTesterCommonBeforeNSFT_r(const int meta_handle, S_RCTLIB_TD_NSFT_TESTER_COMMON_CONFIG_T *common_cfg);
int __stdcall RCTLIB_TDSCDMA_ConfigTesterForNSFT_r(const int meta_handle, const S_RCTLIB_TD_NSFT_TESTER_CONFIG_T *cfg);
int __stdcall RCTLIB_TDSCDMA_MeasureTPCForNSFT_r(const int meta_handle, const S_RCTLIB_TD_NSFT_TPC_CONFIG_ENTRY_T *req,
                                                 S_RCTLIB_TDA_NSFT_MEASURE_RESULT_ENTRY_T *res);

int __stdcall RCTLIB_TDSCDMA_ConfigTesterForNSFTBer_r(const int meta_handle, const S_RCTLIB_TD_NSFT_BER_CONFIG_ENTRY_T *cfg);
int __stdcall RCTLIB_TDSCDMA_MeasureLBerForNSFT_r(const int meta_handle, S_RCTLIB_TDA_NSFT_MEASURE_RESULT_ENTRY_T *res);


int __stdcall RCTLIB_TDSCDMA_ConfigTesterCommonBeforeFT_r(const int meta_handle, S_RCTLIB_TD_NSFT_TESTER_COMMON_CONFIG_T *common_cfg);
int __stdcall RCTLIB_TDSCDMA_ConfigTesterForFT_r(const int meta_handle, const S_RCTLIB_TD_NSFT_TESTER_CONFIG_T *cfg);
int __stdcall RCTLIB_TDSCDMA_MeasureTPCForFT_r(const int meta_handle, const S_RCTLIB_TD_NSFT_TPC_CONFIG_ENTRY_T *req,
                                               S_RCTLIB_TDA_NSFT_MEASURE_RESULT_ENTRY_T *res);

int __stdcall RCTLIB_TDSCDMA_ConfigTesterForFTBer_r(const int meta_handle, const S_RCTLIB_TD_NSFT_BER_CONFIG_ENTRY_T *cfg);
int __stdcall RCTLIB_TDSCDMA_MeasureLBerForFT_r(const int meta_handle, S_RCTLIB_TDA_NSFT_MEASURE_RESULT_ENTRY_T *res);

int __stdcall RCTLIB_TDSCDMA_GET_SpecificSettings_r(const int meta_handle, S_RCTLIB_TDSCDMA_SPECIFIC_SETTINGS_T *pSettings);
int __stdcall RCTLIB_TDSCDMA_LIST_MODE_NSFT_PreSetting_r(const int meta_handle, const S_RCTLIB_TDSCDMA_LIST_MODE_NSFT_PRESETTING_T *pSetting, const unsigned int length);
int __stdcall RCTLIB_TDSCDMA_LIST_MODE_NSFT_InitiateTestPlan_r(const int meta_handle, const S_RCTLIB_TDSCDMA_LIST_MODE_NSFT_INIT_SETTING_T *pSetting, const unsigned int length);
int __stdcall RCTLIB_TDSCDMA_LIST_MODE_NSFT_FetchTestPlanResult_r(const int meta_handle, S_RCTLIB_TDSCDMA_LIST_MODE_NSFT_RESULT_T *pResult, const unsigned int length);
int __stdcall RCTLIB_TDSCDMA_Switch_RF_Port_Ex(const int meta_handle, AntennaPortType eRfPortType, E_RCTLIB_RF_PORT_TYPE ePort);
int __stdcall RCTLIB_TDSCDMA_NSFT_STOP_r(const int meta_handle);

#ifdef __META_LTE__
/*****************************
 * LTE
 *****************************/

/**********************************************
 * LTE structure (shared library defined START)
 **********************************************/
/**
 * Descriptions: LTE AGC Change Frequency
 */
typedef struct
{
    unsigned short usFrequency;  /* the frequency to siwtch. Unit: 100KHz */
    short sFreqOffset; /* the frequency to offset. Unit: KHz */
} S_RCTLIB_LTE_FREQUENCY_T;
/**
 * Descriptions: LTE APC PreSetting
 */
typedef struct
{
    char cMeasurementCount;  /* measurement count */
    char cTriggerMode;      /*0: rising trigger 1: free run */
    unsigned int uiTriggerDelay;      /* trigger delay (Unit: ms) */
} S_RCTLIB_LTE_APC_PRESETTINGS_T;

typedef struct
{
    char dummy;
} S_RCTLIB_LTE_FHC_PRESETTINGS_T;

typedef struct
{
    unsigned short   usFrequency; /* the frequency to siwtch. Unit: 100KHz */
    short            sFreqOffset; /* the frequency to offset. Unit: KHz */
    double           dPowerSteps[27];  /// TX expected power level (dBm) or RX downlink power (dBm)
    unsigned char    ucPwrStepNum;
} S_RCTLIB_LTE_FHC_FREQ_STEP_U;
/**
 * The structure for storing LTE FHC request by band
 */
typedef struct
{
    /// band
    unsigned int  uiBand; // index start from 0 ==> band1
    /// 0:TDD, 1:FDD
    unsigned char duplex_mode;
    /// number of frequency step
    int           iNumberOfFrequencySteps;
    /// UE TX frequency setting in each frequency step (KHz + offset)
    S_RCTLIB_LTE_FHC_FREQ_STEP_U sTxFreqSteps[22];
    /// UE RX frequency setting in each frequency step (KHz + offset)
    S_RCTLIB_LTE_FHC_FREQ_STEP_U sRxFreqSteps[22];
} S_RCTLIB_LTE_FHC_BAND_PARAM_T;

/**
 * The structure for storing LTE FHC request
 */
typedef struct
{
    unsigned char  fdd_tx_to_rx_time;//ms unit
    unsigned char  tdd_tx_to_rx_time;//ms unit
    unsigned char  freq_switch_time;//ms unit
    unsigned char  band_switch_time;//ms unit
    unsigned char  tx_step_width;//ms unit
    unsigned char  tdd_to_fdd_switch_time;//ms unit
    unsigned char  fdd_to_tdd_switch_time;//ms unit
    unsigned char  band_num;
    /// TX + RX Band parameter
    S_RCTLIB_LTE_FHC_BAND_PARAM_T sTRxBandParam[10];
} S_RCTLIB_LTE_FHC_MEASUREMENT_PARAM_T;

typedef struct
{
    double           dMeasuredPower[27];  /// TX measured power level (dBm)
    unsigned char    ucPwrStepNum;
} S_RCTLIB_LTE_FHC_FREQ_RESULT_U;

typedef struct
{
    /// integrity of the measurement result
    int              integrity;
    /// band
    unsigned int     uiBand; // index start from 0 ==> band1
    /// frequency step
    int              iNumberOfFrequencySteps;
    S_RCTLIB_LTE_FHC_FREQ_RESULT_U  sTxFreqResult[22];
} S_RCTLIB_LTE_FHC_BAND_RESULT_U;

/**
 * The structure for storing LTE FHC TX power result
 */
typedef struct
{
    /// TX Band Result
    S_RCTLIB_LTE_FHC_BAND_RESULT_U sTxBandResult[10];
} S_RCTLIB_LTE_FHC_MEASUREMENT_RESULT_T;
/**
 * The structure for storing LTE NSFT SEM result
 * Description: the sections are defined in 3GPP spec Table *******.5-1
 *              General E-UTRA spectrum emission mask
 */
typedef struct
{
    /// pass flag (overall), pass(0)/fail(1)/not tested(-1)
    char cPass;
    /// Abs Peak Power of lowers
    double dAbsPeakPowerLower[4];
    /// Abs Peak Power of uppers
    double dAbsPeakPowerUpper[4];
    /// Lower delta Limit
    double dDeltaLimitLower[4];
    /// Upper delta Limit
    double dDeltaLimitUpper[4];
} S_RCTLIB_LTE_NSFT_SEM_RESULT_T;

typedef struct
{
    unsigned short    usFrequency; /* the frequency to siwtch. Unit: 100KHz */
    short            sFreqOffset; /* the frequency to offset. Unit: KHz */
    unsigned char     ucPwrStepNum;
    double           dPowerSteps[7 * 3 + 72];  /// RX downlink power (dBm)
} S_RCTLIB_LTE_FHC_CA_FREQ_STEP_U;
/**
 * The structure for storing LTE FHC CA request by band
 */
typedef struct
{
    /// band
    unsigned int  uiBand; // index start from 0 ==> band1
    /// 0:TDD, 1:FDD
    unsigned char duplex_mode;
    /// number of frequency step
    int           iNumberOfFrequencySteps;
    /// UE TX frequency setting in each frequency step (KHz + offset)
    S_RCTLIB_LTE_FHC_FREQ_STEP_U    sTxFreqSteps[15];
    /// UE RX CA frequency setting in each frequency step (KHz + offset)
    S_RCTLIB_LTE_FHC_CA_FREQ_STEP_U sRxFreqSteps[15];
} S_RCTLIB_LTE_FHC_CA_BAND_PARAM_T;

/**
 * The structure for storing LTE FHC request
 */
typedef struct
{
    unsigned char  fdd_tx_to_rx_time;//ms unit
    unsigned char  tdd_tx_to_rx_time;//ms unit
    unsigned char  freq_switch_time;//ms unit
    unsigned char  band_switch_time;//ms unit
    unsigned char  tx_step_width;//ms unit
    unsigned char  fdd_to_tdd_switch_time;//ms unit
    unsigned char  band_num;
    unsigned char  tx_cal_enable;
    unsigned char  rx_cal_enable;
    /// TX + RX Band parameter
    S_RCTLIB_LTE_FHC_CA_BAND_PARAM_T sTRxBandParam[10];
} S_RCTLIB_LTE_FHC_CA_MEASUREMENT_PARAM_T;

typedef struct
{
    double           dMeasuredPower[11];  /// TX measured power level (dBm)
    unsigned char    ucPwrStepNum;
} S_RCTLIB_LTE_FHC_CA_FREQ_RESULT_U;

typedef struct
{
    /// integrity of the measurement result
    int              integrity;
    /// band
    unsigned int     uiBand; // index start from 0 ==> band1
    /// frequency step
    int              iNumberOfFrequencySteps;
    S_RCTLIB_LTE_FHC_FREQ_RESULT_U  sTxFreqResult[15];
} S_RCTLIB_LTE_FHC_CA_BAND_RESULT_U;
/**
 * The structure for storing LTE FHC TX power result
 */
typedef struct
{
    /// TX Band Result
    S_RCTLIB_LTE_FHC_CA_BAND_RESULT_U sTxBandResult[10];
} S_RCTLIB_LTE_FHC_CA_MEASUREMENT_RESULT_T;
/**
 * The enum for LTE CA categroy definition
 */
typedef enum
{
    RCTLIB_LTE_CA_BAND_USAGE_SINGLE_BAND = 0,
    RCTLIB_LTE_CA_BAND_USAGE_INTER_BAND_CA,
    RCTLIB_LTE_CA_BAND_USAGE_INTER_BAND_CA_ALT,
    RCTLIB_LTE_CA_BAND_USAGE_INTER_BAND_NCCA,
    RCTLIB_LTE_CA_BAND_USAGE_COUNT
} LteCaBandUsage;

typedef struct
{
    unsigned short   usFrequency; /* the frequency to siwtch. Unit: 100KHz */
    short            sFreqOffset; /* the frequency to offset. Unit: KHz */
    double           dPowerSteps[13];  /// TX expected power level (dBm) or RX downlink power (dBm)
    unsigned char    ucPwrStepNum;
} S_RCTLIB_LTE_FHC_V2_ROUTE_STEP_U;
typedef struct
{
    unsigned char ucRouteNum;
    S_RCTLIB_LTE_FHC_V2_ROUTE_STEP_U route[16];
} S_RCTLIB_LTE_FHC_V2_FREQ_STEP_U;

typedef struct
{
    unsigned char     ucPwrStepNum;
    unsigned char     ucPwrStepDuration[12]; /// Unit ms (HPM 6 + LPM 6)
    double           dPowerSteps[12];  /// RX downlink power (dBm) (HPM 6 + LPM 6)
} S_RCTLIB_LTE_FHC_CA_V2_TYPE1_ROUTE_STEP_U;

typedef struct
{
    unsigned char     ucPccPartialOnPwrStepNum;
    unsigned char     ucSccPartialOnPwrStepNum;
    unsigned char     ucGbgPwrStepNum;
    unsigned char     ucGbgLpmPwrStepNum;
    unsigned char     ucPccPartialOnPwrStepDuration[9]; /// Unit ms (HPM 6 + LPM 6) + (HPM 6 + LPM 6)
    double           dPccPartialOnPowerSteps[9];       /// RX downlink power (dBm)
    unsigned char     ucSccPartialOnPwrStepDuration[9]; /// Unit ms (HPM 6 + LPM 6) + (HPM 6 + LPM 6)
    double           dSccPartialOnPowerSteps[9];      /// RX downlink power (dBm)
    unsigned char     ucGbgPwrStepDuration[18];       /// Unit ms (GBG 36 * 2)
    double           dGbgPowerSteps[18];             /// RX downlink power (dBm)
    unsigned char     ucGbgLpmPwrStepDuration[3];     /// Unit ms (LPM only 3 step)
    double           dGbgLpmPowerSteps[3];           /// RX downlink power (dBm)
} S_RCTLIB_LTE_FHC_CA_V2_TYPE2_ROUTE_STEP_U;

typedef struct
{
    unsigned short    usFrequency; /* the frequency to siwtch. Unit: 100KHz */
    short            sType1FreqOffset; /* the type1 frequency to offset. Unit: KHz */
    short            sType2FreqOffset; /* the type2 frequency to offset. Unit: KHz */
    unsigned char    ucType1RouteNum;
    unsigned char    ucType2RouteNum;
    S_RCTLIB_LTE_FHC_CA_V2_TYPE1_ROUTE_STEP_U type1_route[16];
    S_RCTLIB_LTE_FHC_CA_V2_TYPE2_ROUTE_STEP_U type2_route[8];
} S_RCTLIB_LTE_FHC_CA_V2_FREQ_STEP_U;

/**
 * The structure for storing LTE FHC CA request by band
 */
typedef struct
{
    /// band
    unsigned int  uiBand; // index start from 0 ==> band1
    /// 0:TDD, 1:FDD
    unsigned char duplex_mode;
    /// number of frequency step
    int           iNumberOfTxFrequencySteps;
    /// number of frequency step
    int           iNumberOfRxFrequencySteps;
    /// UE TX frequency setting in each frequency step (KHz + offset)
    S_RCTLIB_LTE_FHC_V2_FREQ_STEP_U    sTxFreqSteps[15];
    /// UE RX CA frequency setting in each frequency step (KHz + offset)
    S_RCTLIB_LTE_FHC_CA_V2_FREQ_STEP_U sRxFreqSteps[15];
} S_RCTLIB_LTE_FHC_CA_V2_BAND_PARAM_T;

/**
 * The structure for storing LTE FHC request
 */
typedef struct
{
    unsigned char  tx_to_rx_time;//ms unit
    unsigned char  switch_time;//ms unit
    unsigned char  tx_step_width;//ms unit
    unsigned char  band_num;
    unsigned char  tx_cal_enable;
    unsigned char  rx_cal_enable;
    bool          bRxOnlyBand;
    /// TX + RX Band parameter
    bool iSRxdCal;    //jin
    S_RCTLIB_LTE_FHC_CA_V2_BAND_PARAM_T sTRxBandParam[16];
} S_RCTLIB_LTE_FHC_CA_V2_MEASUREMENT_PARAM_T;

typedef struct
{
    unsigned char    ucPwrStepNum;
    double           dMeasuredPower[13];  /// TX measured power level (dBm)
} S_RCTLIB_LTE_FHC_V2_ROUTE_RESULT_U;

typedef struct
{
    unsigned char                   ucRouteNum;
    S_RCTLIB_LTE_FHC_V2_ROUTE_RESULT_U route[16];
} S_RCTLIB_LTE_FHC_CA_V2_FREQ_RESULT_U;

typedef struct
{
    /// integrity of the measurement result
    int              integrity;
    /// band
    unsigned int     uiBand; // index start from 0 ==> band1
    /// frequency step
    int              iNumberOfFrequencySteps;
    S_RCTLIB_LTE_FHC_CA_V2_FREQ_RESULT_U  sTxFreqResult[15];
} S_RCTLIB_LTE_FHC_CA_V2_BAND_RESULT_U, S_RCTLIB_LTE_FHC_CA_V3_BAND_RESULT_U;
/**
 * The structure for storing LTE FHC TX power result
 */
typedef struct
{
    /// TX Band Result
    S_RCTLIB_LTE_FHC_CA_V2_BAND_RESULT_U sTxBandResult[16];
} S_RCTLIB_LTE_FHC_CA_V2_MEASUREMENT_RESULT_T, S_RCTLIB_LTE_FHC_CA_V3_MEASUREMENT_RESULT_T;

/********************* LTE FHC V3 RCTLIB Structure Definitions *********************/

typedef struct
{
    unsigned char     ucPwrStepNum;
    unsigned char     ucPwrStepDuration[14]; /// Unit ms (HPM 7 + LPM 7)
    double            dPowerSteps[14];  /// RX downlink power (dBm) (HPM 7 + LPM 7)
} S_RCTLIB_LTE_FHC_CA_V3_TYPE1_ROUTE_STEP_U;

typedef struct
{
    unsigned char     ucPccPartialOnPwrStepNum;
    unsigned char     ucSccPartialOnPwrStepNum;
    unsigned char     ucGbgPwrStepNum;
    unsigned char     ucGbgLpmPwrStepNum;
    unsigned char     ucPccPartialOnPwrStepDuration[14]; /// Unit ms (HPM 6 + LPM 6) + (HPM 6 + LPM 6)
    double           dPccPartialOnPowerSteps[14];       /// RX downlink power (dBm)
    unsigned char     ucSccPartialOnPwrStepDuration[14]; /// Unit ms (HPM 6 + LPM 6) + (HPM 6 + LPM 6)
    double           dSccPartialOnPowerSteps[14];      /// RX downlink power (dBm)
    unsigned char     ucGbgPwrStepDuration[18];       /// Unit ms (HPM 18 or 15 steps)
    double           dGbgPowerSteps[18];             /// RX downlink power (dBm)
    unsigned char     ucGbgLpmPwrStepDuration[4];     /// Unit ms (LPM 3 or 4 step)
    double           dGbgLpmPowerSteps[4];           /// RX downlink power (dBm)
} S_RCTLIB_LTE_FHC_CA_V3_TYPE2_ROUTE_STEP_U;

typedef struct
{
    unsigned short    usFrequency; /* the frequency to siwtch. Unit: 100KHz */
    short            sType1FreqOffset; /* the type1 frequency to offset. Unit: KHz */
    short            sType2FreqOffset; /* the type2 frequency to offset. Unit: KHz */
    unsigned char    ucType1RouteNum;
    unsigned char    ucType2RouteNum;
    S_RCTLIB_LTE_FHC_CA_V3_TYPE1_ROUTE_STEP_U type1_route[8];
    S_RCTLIB_LTE_FHC_CA_V3_TYPE2_ROUTE_STEP_U type2_route[4];
} S_RCTLIB_LTE_FHC_CA_V3_FREQ_STEP_U;

/**
 * The structure for storing LTE FHC CA request by band
 */
typedef struct
{
    /// band
    unsigned int  uiBand; // index start from 0 ==> band1
    /// 0:TDD, 1:FDD
    unsigned char duplex_mode;
    /// number of frequency step
    int           iNumberOfTxFrequencySteps;
    /// number of frequency step
    int           iNumberOfRxFrequencySteps;
    /// UE TX frequency setting in each frequency step (KHz + offset)
    S_RCTLIB_LTE_FHC_V2_FREQ_STEP_U    sTxFreqSteps[15];
    /// UE RX CA frequency setting in each frequency step (KHz + offset)
    S_RCTLIB_LTE_FHC_CA_V3_FREQ_STEP_U sRxFreqSteps[15];
} S_RCTLIB_LTE_FHC_CA_V3_BAND_PARAM_T;

/**
 * The structure for storing LTE FHC request
 */
typedef struct
{
    unsigned char  tx_to_rx_time;//ms unit
    unsigned char  switch_time;//ms unit
    unsigned char  tx_step_width;//ms unit
    unsigned char  band_num;
    unsigned char  tx_cal_enable;
    unsigned char  rx_cal_enable;
    bool          bRxOnlyBand;
    /// TX + RX Band parameter
    bool iSRxdCal;    //jin
    S_RCTLIB_LTE_FHC_CA_V3_BAND_PARAM_T sTRxBandParam[15];
} S_RCTLIB_LTE_FHC_CA_V3_MEASUREMENT_PARAM_T;

typedef struct
{
    RF_Carkit_Index_E eRfCarkit;  // For divider usage
    unsigned short   usFrequency; /* the frequency to siwtch. Unit: 100KHz */
    short            sFreqOffset; /* the frequency to offset. Unit: KHz */
    double           dPowerSteps[13];  /// TX expected power level (dBm) or RX downlink power (dBm)
    unsigned char    ucPwrStepNum;
} S_RCTLIB_LTE_FHC_V5_ROUTE_STEP_U;
typedef struct
{
    unsigned char ucRouteNum;
    S_RCTLIB_LTE_FHC_V5_ROUTE_STEP_U route[16];
} S_RCTLIB_LTE_FHC_V5_FREQ_STEP_U;

typedef struct
{
    RF_Carkit_Index_E eRfCarkit;  // For divider usage
    unsigned char     ucPwrStepNum;
    unsigned char     ucPwrStepDuration[14]; /// Unit ms (HPM 7 + LPM 7)
    double            dPowerSteps[14];  /// RX downlink power (dBm) (HPM 7 + LPM 7)
} S_RCTLIB_LTE_FHC_CA_V5_TYPE1_ROUTE_STEP_U;

typedef struct
{
    RF_Carkit_Index_E eRfCarkit;  // For divider usage
    unsigned char     ucPccPartialOnPwrStepNum;
    unsigned char     ucSccPartialOnPwrStepNum;
    unsigned char     ucGbgPwrStepNum;
    unsigned char     ucGbgLpmPwrStepNum;
    unsigned char     ucPccPartialOnPwrStepDuration[14]; /// Unit ms (HPM 7 + LPM 7)
    double           dPccPartialOnPowerSteps[14];       /// RX downlink power (dBm)
    unsigned char     ucSccPartialOnPwrStepDuration[14]; /// Unit ms (HPM 7 + LPM 7)
    double           dSccPartialOnPowerSteps[14];      /// RX downlink power (dBm)
    unsigned char     ucGbgPwrStepDuration[18];       /// Unit ms (HPM 18 or 15 steps)
    double           dGbgPowerSteps[18];             /// RX downlink power (dBm)
    unsigned char     ucGbgLpmPwrStepDuration[7];     /// Unit ms (LPM max 7 steps for 13.5db Always On LowTxISO)
    double           dGbgLpmPowerSteps[7];           /// RX downlink power (dBm)
} S_RCTLIB_LTE_FHC_CA_V5_TYPE2_ROUTE_STEP_U;

typedef struct
{
    unsigned short    usFrequency; /* the frequency to siwtch. Unit: 100KHz */
    short            sType1FreqOffset; /* the type1 frequency to offset. Unit: KHz */
    short            sType2FreqOffset; /* the type2 frequency to offset. Unit: KHz */
    unsigned char    ucType1RouteNum;
    unsigned char    ucType2RouteNum;
    S_RCTLIB_LTE_FHC_CA_V5_TYPE1_ROUTE_STEP_U type1_route[12];  // Align ERF_MAX_FHC_RX_TYPE1_CMD_NUM_V3
    S_RCTLIB_LTE_FHC_CA_V5_TYPE2_ROUTE_STEP_U type2_route[6];   // Align ERF_MAX_FHC_RX_TYPE2_CMD_NUM_V3
} S_RCTLIB_LTE_FHC_CA_V5_FREQ_STEP_U;

/**
 * The structure for storing LTE FHC CA request by band
 */
typedef struct
{
    /// band
    unsigned int  uiBand; // index start from 0 ==> band1
    /// 0:TDD, 1:FDD
    unsigned char duplex_mode;
    /// number of frequency step
    int           iNumberOfTxFrequencySteps;
    /// number of frequency step
    int           iNumberOfRxFrequencySteps;
    /// UE TX frequency setting in each frequency step (KHz + offset)
    S_RCTLIB_LTE_FHC_V5_FREQ_STEP_U    sTxFreqSteps[15];
    /// UE RX CA frequency setting in each frequency step (KHz + offset)
    S_RCTLIB_LTE_FHC_CA_V5_FREQ_STEP_U sRxFreqSteps[15];
} S_RCTLIB_LTE_FHC_CA_V5_BAND_PARAM_T;

/**
 * The structure for storing LTE FHC request
 */
typedef struct
{
    unsigned char  tx_to_rx_time;//ms unit
    unsigned char  switch_time;//ms unit
    unsigned char  tx_step_width;//ms unit
    unsigned char  band_num;
    unsigned char  tx_cal_enable;
    unsigned char  rx_cal_enable;
    bool          bRxOnlyBand;
    RF_Carkit_Index_E eCurrectRXCarkit; // RF_Carkit_Null means in divider flow, other means multiport flow
    RF_Carkit_Index_E eCurrectTXCarkit; // RF_Carkit_Null means in divider flow, other means multiport flow
    /// TX + RX Band parameter
    S_RCTLIB_LTE_FHC_CA_V5_BAND_PARAM_T sTRxBandParam[15];
} S_RCTLIB_LTE_FHC_CA_V5_MEASUREMENT_PARAM_T;

/**
 * Description: LTE instrument's spcial settings for V3
 */
typedef struct
{
    /// ========== FHC instrument parameters ==========
    /// FHC RX measurement offset
    unsigned short usFHC_RX_Measurement_Offset;
    ///  Support source generator broadcast
    bool bSgBroadcastSupport;
    ///  Support port combine
    bool bSwitchPortInSeqModeSupport;
} S_RCTLIB_LTE_FHC_V3_SPECIFIC_SETTINGS_T;

/********************* LTE FHC V3 RCTLIB Structure Definitions *********************/

/********************* LTE FHC V7 RCTLIB Structure Definitions Start *********************/
#define MAX_EN_FHC_CA_V7_RCTLIB_TX_PWR_STEP_COUNT (10)
typedef struct
{
    RF_Carkit_Index_E   eRfCarkit;          // For divider usage
    unsigned int        frequency;          /* the frequency to siwtch. Unit: KHz */
    double              powerSteps[MAX_EN_FHC_CA_V7_RCTLIB_TX_PWR_STEP_COUNT];     /* TX expected power level (dBm) */
    unsigned char       pwrStepNum;
} S_RCTLIB_EN_FHC_CA_V7_TX_BLOCK_STEP_T;

#define MAX_EN_FHC_CA_V7_RCTLIB_TX_BLK_COUNT (100)

typedef struct
{
    unsigned char   totalTxBlkCount;
    /// used for trigger RX (when rx freq step > tx freq step)
    bool            IsDummyTx;
    S_RCTLIB_EN_FHC_CA_V7_TX_BLOCK_STEP_T txBlock[MAX_EN_FHC_CA_V7_RCTLIB_TX_BLK_COUNT]; // MIN((61440 / tx_sc_1x_raw),(61440 / tx_sc_1x_rpt)) = 111
} S_RCTLIB_EN_FHC_CA_V7_TX_FREQ_STEP_T;


#define MAX_EN_FHC_CA_V7_RCTLIB_RX_SC_PWR_STEP_COUNT (20)

typedef struct
{
    RF_Carkit_Index_E eRfCarkit;  // For divider usage
    unsigned int      frequency; /* the frequency to siwtch. Unit: KHz */
    unsigned char     pwrStepNum;
    unsigned char     pwrStepDuration[MAX_EN_FHC_CA_V7_RCTLIB_RX_SC_PWR_STEP_COUNT];
    double            powerSteps[MAX_EN_FHC_CA_V7_RCTLIB_RX_SC_PWR_STEP_COUNT];
} S_RCTLIB_EN_FHC_CA_V7_RX_SC_BLOCK_STEP_T;

#define MAX_EN_FHC_CA_V7_RCTLIB_RX_GBG_PWR_STEP_COUNT (48)

typedef struct
{
    RF_Carkit_Index_E eRfCarkit;  // For divider usage
    unsigned int      frequency; /* the frequency to siwtch. Unit: KHz */
    unsigned char     hpmPwrStepNum;
    unsigned char     hpmPwrStepDuration[MAX_EN_FHC_CA_V7_RCTLIB_RX_GBG_PWR_STEP_COUNT];
    double            hpmPowerSteps[MAX_EN_FHC_CA_V7_RCTLIB_RX_GBG_PWR_STEP_COUNT];
    unsigned char     lpmPwrStepNum;
    unsigned char     lpmPwrStepDuration[MAX_EN_FHC_CA_V7_RCTLIB_RX_GBG_PWR_STEP_COUNT];
    double            lpmPowerSteps[MAX_EN_FHC_CA_V7_RCTLIB_RX_GBG_PWR_STEP_COUNT];
} S_RCTLIB_EN_FHC_CA_V7_RX_GBG_BLOCK_STEP_T;

#define MAX_EN_FHC_CA_V7_RCTLIB_SC_BLK_COUNT (48)
#define MAX_EN_FHC_CA_V7_RCTLIB_GBG_BLK_COUNT (4)

typedef struct
{
    unsigned char       totalRxScBlkCount;
    unsigned char       totalRxGbgBlkCount;
    S_RCTLIB_EN_FHC_CA_V7_RX_SC_BLOCK_STEP_T  rxScBlk[MAX_EN_FHC_CA_V7_RCTLIB_SC_BLK_COUNT]; // (61440 / rx_sc_2x_rpt)
    S_RCTLIB_EN_FHC_CA_V7_RX_GBG_BLOCK_STEP_T rxGbgBlk[MAX_EN_FHC_CA_V7_RCTLIB_GBG_BLK_COUNT]; // (61440 / rx_gbg_2x_rpt)
} S_RCTLIB_EN_FHC_CA_V7_RX_FREQ_STEP_T;

#define MAX_EN_FHC_CA_V7_RCTLIB_FREQ_COUNT (16)

typedef struct
{
    /// band
    unsigned short  rfBand; // index start from 0 ==> band1
    /// number of frequency step
    int             iNumberOfTxFrequencySteps;
    /// number of frequency step
    int             iNumberOfRxFrequencySteps;
    /* TX frequency offset. Unit: KHz */
    unsigned int    txFreqOffset;
    /* RX SC frequency offset. Unit: KHz */
    unsigned int    rxScFreqOffset;
    /* RX GBG frequency offset. Unit: KHz */
    unsigned int    rxGbgFreqOffset;
    /// UE TX frequency setting in each frequency step (KHz + offset)
    S_RCTLIB_EN_FHC_CA_V7_TX_FREQ_STEP_T  sTxFreqSteps[MAX_EN_FHC_CA_V7_RCTLIB_FREQ_COUNT]; // MMTST_MAX_FHC_FREQ_NUM_IN_BLK_V7
    /// UE RX CA frequency setting in each frequency step (KHz + offset)
    S_RCTLIB_EN_FHC_CA_V7_RX_FREQ_STEP_T  sRxFreqSteps[MAX_EN_FHC_CA_V7_RCTLIB_FREQ_COUNT]; // MMTST_MAX_FHC_FREQ_NUM_IN_BLK_V7
} S_RCTLIB_EN_FHC_CA_V7_BAND_PARAM_T;

#define MAX_EN_FHC_CA_V7_RCTLIB_BAND_COUNT (25)
typedef struct
{
    unsigned char       ratIdx;
    unsigned char       tx_to_rx_time;//ms unit
    unsigned char       switch_time;//ms unit
    unsigned char       tx_step_width;//ms unit
    unsigned char       num_of_band;
    bool                bRxOnlyBand;
    RF_Carkit_Index_E   eCurrectRXCarkit; // RF_Carkit_Null means in divider flow, other means multiport flow
    RF_Carkit_Index_E   eCurrectTXCarkit; // RF_Carkit_Null means in divider flow, other means multiport flow
    /// TX + RX Band parameter
    S_RCTLIB_EN_FHC_CA_V7_BAND_PARAM_T sTRxBandParam[MAX_EN_FHC_CA_V7_RCTLIB_BAND_COUNT];
} S_RCTLIB_EN_FHC_CA_V7_MEASUREMENT_PARAM_T;

/* fetch results */

typedef struct
{
    unsigned char    pwrStepNum;
    double           measuredPower[10];  /// TX measured power level (dBm)
} S_RCTLIB_EN_FHC_CA_V7_TX_BLOCK_STEP_RESULT_T;

typedef struct
{
    unsigned char                                   totalTxBlkCount;
    S_RCTLIB_EN_FHC_CA_V7_TX_BLOCK_STEP_RESULT_T    txBlock[100];
} S_RCTLIB_LTE_FHC_CA_V7_TX_FREQ_STEP_RESULT_T;

typedef struct
{
    /// band
    unsigned short     rfBand;
    S_RCTLIB_LTE_FHC_CA_V7_TX_FREQ_STEP_RESULT_T  sTxFreqResult[16];
} S_RCTLIB_EN_FHC_CA_V7_BAND_RESULT_U;

typedef struct
{
    S_RCTLIB_EN_FHC_CA_V7_BAND_RESULT_U sTxBandResult[25];
} S_RCTLIB_EN_FHC_CA_V7_MEASUREMENT_RESULT_T;
/********************* LTE FHC V7 RCTLIB Structure Definitions End *********************/


/********************* EN FHC V8 RCTLIB Structure Definitions Start *********************/

typedef struct
{
    unsigned char     txRfPort;
    unsigned int      frequencyKhz;
    double            pwrLevel;
    unsigned char     pwrStepDuration;
} S_RCTLIB_EN_FHC_TX_PWR_STEP_T;

typedef struct
{
    unsigned char     rxRfPortCount;
    unsigned char     rxRfPort[8];
    unsigned int      frequencyKhz;
    double            pwrLevel;
    unsigned char     pwrStepDuration;
    float             timingOffset;
} S_RCTLIB_EN_FHC_RX_PWR_STEP_T;

typedef struct
{
    unsigned char     pwrStepDuration;
} S_RCTLIB_EN_FHC_SWITCH_PWR_STEP_T;

typedef enum
{
    RCTLIB_EN_FHC_TX_PWR_STEP = 0,
    RCTLIB_EN_FHC_RX_PWR_STEP,
    RCTLIB_EN_FHC_SWITCH_PWR_STEP,
    RCTLIB_EN_FHC_PWR_STEP_TYPE_NUM
} EnFhcPowerStepType;

typedef struct
{
    EnFhcPowerStepType stepType;
    union
    {
        S_RCTLIB_EN_FHC_TX_PWR_STEP_T txPwrStep;
        S_RCTLIB_EN_FHC_RX_PWR_STEP_T rxPwrStep;
        S_RCTLIB_EN_FHC_SWITCH_PWR_STEP_T switchPwrStep;
    };
} S_RCTLIB_EN_FHC_PWR_STEP_T;


typedef struct
{
    unsigned char ratIdx; //LTE:0, NR:1
    bool isSwitchPortMode; // if true, need to switch port in a sequence
    int pwrStepCount;
    S_RCTLIB_EN_FHC_PWR_STEP_T  *pwrSteps;
} S_RCTLIB_EN_FHC_MEASUREMENT_PARAM_T;


typedef struct
{
    bool stepLimitCheckPass;
} S_RCTLIB_EN_FHC_MEASUREMENT_PARAM_CHECK_RESULT_T;

typedef struct
{
    double measuredPower;
} S_RCTLIB_EN_FHC_TX_PWR_STEP_RESULT_T;


typedef struct
{
    EnFhcPowerStepType stepType;
    union
    {
        S_RCTLIB_EN_FHC_TX_PWR_STEP_RESULT_T txPwrStepResult;
    };
} S_RCTLIB_EN_FHC_PWR_STEP_RESULT_T;

typedef struct
{
    int pwrStepCount;
    S_RCTLIB_EN_FHC_PWR_STEP_RESULT_T  *pwrStepsResult;
} S_RCTLIB_EN_FHC_MEASUREMENT_RESULT_T;


/********************* EN FHC V8 RCTLIB Structure Definitions End *********************/


/********************* EN FHC SRS V7 RCTLIB Structure Definitions Start *********************/
typedef struct
{
    /// band
    unsigned short     rfBand;
    /// number of frequency step
    int                iNumberOfTxFrequencySteps;
    /// TX frequency offset. Unit: KHz
    unsigned int       txFreqOffset;
    /// the frequency to siwtch. Unit: KHz
    unsigned int       frequency[3]; // MMRF_MAX_SRS_CAL_FREQ_USE_NUM_V7
    /// number of carkit step
    int                iNumberOfCarkit;
    /// carkit steps, for divider usage
    RF_Carkit_Index_E  eRfCarkit[12]; //MMRF_MAX_SRS_CAL_CARKIT_USE_NUM_V7
    /// TX expected power level (dBm), used for all carkit steps
    double             power;
} S_RCTLIB_EN_FHC_SRS_V7_BAND_PARAM_T;

typedef struct
{
    unsigned char       ratIdx;
    unsigned char       switch_time;   //ms unit
    unsigned char       tx_step_width; //ms unit
    unsigned char       num_of_band;
    bool                isBandCombine;
    RF_Carkit_Index_E   eCurrentTXCarkit; // RF_Carkit_Null means in divider flow, other means multiport flow
    /// TX Band parameter
    S_RCTLIB_EN_FHC_SRS_V7_BAND_PARAM_T sTxBandParam[100];
} S_RCTLIB_EN_FHC_SRS_V7_MEASUREMENT_PARAM_T;

/* fetch results */

typedef struct
{
    unsigned char    pwrStepNum;
    double           measuredPower[12];  /// TX measured power level (dBm)
} S_RCTLIB_EN_FHC_SRS_V7_TX_FREQ_STEP_RESULT_T;

typedef struct
{
    /// band
    unsigned short     rfBand;
    S_RCTLIB_EN_FHC_SRS_V7_TX_FREQ_STEP_RESULT_T  sTxFreqResult[3];
} S_RCTLIB_EN_FHC_SRS_V7_BAND_RESULT_T;

typedef struct
{
    S_RCTLIB_EN_FHC_SRS_V7_BAND_RESULT_T sTxBandResult[100];
} S_RCTLIB_EN_FHC_SRS_V7_MEASUREMENT_RESULT_T;
/********************* EN FHC SRS V7 RCTLIB Structure Definitions End *********************/
/**
 * The structure for storing LTE NSFT EVM result
 */
typedef struct
{
    /// EVM (%rms)
    double dRMS_EVM;
    /// Frequency Error(ppm)
    double dFreq_Error_Ppm;
    /// Magnitude Error (%)
    double dMagnitude_Error;
    /// Phase Error (Degree)
    double dPhase_Error_Deg;
    /// IQ Offset (dB)
    double dIq_Offset_dB;
} S_RCTLIB_LTE_NSFT_EVM_RESULT_T;
/**
 * The structure for storing LTE NSFT Ripple flatnes result
 */
typedef struct
{
    /// RP 1 pass(0)/fail(1)/not tested(-1)
    char cRP1Pass;
    /// RP 2 pass/fail
    char cRP2Pass;
    /// RP 12 pass/fail
    char cRP12Pass;
    /// RP 21 pass/fail
    char cRP21Pass;
    /// maximum Ripple in Range 1
    double dRipple1;
    /// maximum Ripple in Range 2
    double dRipple2;
    /// RP 12 Value - the maximum ripple between the upper side of Range 1 and lower side of Range 2
    double dRipple12;
    /// RP 21 Value - the maximum ripple between the upper side of Range 2 and lower side of Range 1
    double dRipple21;
} S_RCTLIB_LTE_NSFT_RIPPLE_RESULT_T;

/**
 * the structure for storing ACP(ACLR) result
 */
typedef struct
{
    /// Total Carrier Power
    double dCarrierPower;
    /// Lower Offset EUTRA - relative power
    double dRel_Low_Power_Eutra;
    /// Upper Offset EUTRA - relative power
    double dRel_Upper_Power_Eutra;
    /// Lower Offset UTRA1 - relative power
    double dRel_Low_Power_Utra1;
    /// Upper Offset UTRA1 - relative power
    double dRel_Upper_Power_Utra1;
    /// Lower Offset UTRA2 - relative power
    double dRel_Low_Power_Utra2;
    /// Upper Offset UTRA2 - relative power
    double dRel_Upper_Power_Utra2;
} S_RCTLIB_LTE_NSFT_ACP_RESULT_T;

/**
 * the structure for storing 2UL CCA NSFT ACP(ACLR) result
 */
typedef struct
{
    /// Total Carrier Power
    double dCarrierPower;
    /// Lower Offset EUTRA - relative power
    double dRel_Low_Power_Eutra;
    /// Upper Offset EUTRA - relative power
    double dRel_Upper_Power_Eutra;
    /// Lower Offset UTRA1 - relative power
    double dRel_Low_Power_Utra1;
    /// Upper Offset UTRA1 - relative power
    double dRel_Upper_Power_Utra1;
    /// Lower Offset UTRA2 - relative power
    double dRel_Low_Power_Utra2;
    /// Upper Offset UTRA2 - relative power
    double dRel_Upper_Power_Utra2;
} S_RCTLIB_LTE_NSFT_2UL_CCA_ACP_RESULT_T;

typedef struct
{
    /// Occupied Bandwidth (MHz)
    double dOccupied_Bandwidth_MHz;
} S_RCTLIB_LTE_NSFT_OBW_RESULT_T;
/**
 * the structure for storing TX list mode step result
 */
typedef struct
{
#define LTE_NSFT_MAX_TX_STEP_TEST_NUM               8
    double                            dPoutPower[LTE_NSFT_MAX_TX_STEP_TEST_NUM];
    S_RCTLIB_LTE_NSFT_EVM_RESULT_T    sEVMResult[LTE_NSFT_MAX_TX_STEP_TEST_NUM];
    S_RCTLIB_LTE_NSFT_RIPPLE_RESULT_T sRpResult[LTE_NSFT_MAX_TX_STEP_TEST_NUM];
    S_RCTLIB_LTE_NSFT_ACP_RESULT_T    sACPResult[LTE_NSFT_MAX_TX_STEP_TEST_NUM];
    S_RCTLIB_LTE_NSFT_SEM_RESULT_T    sSEMResult[LTE_NSFT_MAX_TX_STEP_TEST_NUM];
    S_RCTLIB_LTE_NSFT_OBW_RESULT_T    sOBWResult[LTE_NSFT_MAX_TX_STEP_TEST_NUM];
} S_RCTLIB_LTE_NSFT_TEST_TX_STEP_RESULT_T;
/**
 * the structure for storing 2UL CCA NSFT result
 */
typedef struct
{
#define LTE_NSFT_MAX_TX_STEP_TEST_NUM               8
    double                            dPoutPower[LTE_NSFT_MAX_TX_STEP_TEST_NUM];
    S_RCTLIB_LTE_NSFT_EVM_RESULT_T    sEVMResult[LTE_NSFT_MAX_TX_STEP_TEST_NUM];
    S_RCTLIB_LTE_NSFT_2UL_CCA_ACP_RESULT_T    sACPResult[LTE_NSFT_MAX_TX_STEP_TEST_NUM];
    S_RCTLIB_LTE_NSFT_SEM_RESULT_T    sSEMResult[LTE_NSFT_MAX_TX_STEP_TEST_NUM];
} S_RCTLIB_LTE_NSFT_2UL_CCA_RESULT_T;
/**
 * the structure for storing TX list mode sweep result
 */
typedef struct
{
#define LTE_NSFT_MAX_TX_SWEEP_TEST_NUM              63
    double                            dPoutPower[LTE_NSFT_MAX_TX_SWEEP_TEST_NUM];
} S_RCTLIB_LTE_NSFT_TEST_TX_SWEEP_RESULT_T;
/* TX NSFT test results */
typedef struct
{
    unsigned char                     ucMeasureStep;
    char                              cRequestOpetions;
    /* Inst. setting and RX test check items */
    union
    {
        S_RCTLIB_LTE_NSFT_TEST_TX_STEP_RESULT_T      txStepResult;
        S_RCTLIB_LTE_NSFT_TEST_TX_SWEEP_RESULT_T     txSweepResult;
    };
} S_RCTLIB_LTE_NSFT_TX_TEST_FREQ_RESULT_T;

typedef struct
{
    S_RCTLIB_LTE_NSFT_TX_TEST_FREQ_RESULT_T      txFreqResults[13];
} S_RCTLIB_LTE_NSFT_TX_TEST_BAND_RESULT_T;

typedef struct
{
    S_RCTLIB_LTE_NSFT_TX_TEST_BAND_RESULT_T      txBandResults[10];
} S_RCTLIB_LTE_NSFT_TX_RESULT_T;

#define S_RCTLIB_LTE_NSFT_TRX_TEST_ROUTE_RESULT_T S_RCTLIB_LTE_NSFT_TX_TEST_FREQ_RESULT_T

typedef struct
{
    S_RCTLIB_LTE_NSFT_TRX_TEST_ROUTE_RESULT_T      txRouteResults[8];
} S_RCTLIB_LTE_NSFT_TRX_TEST_FREQ_RESULT_T;

typedef struct
{
    S_RCTLIB_LTE_NSFT_TRX_TEST_FREQ_RESULT_T      txFreqResults[8];
} S_RCTLIB_LTE_NSFT_TRX_TEST_BAND_RESULT_T;

typedef struct
{
    S_RCTLIB_LTE_NSFT_TRX_TEST_BAND_RESULT_T      txBandResults[8];
} S_RCTLIB_LTE_NSFT_TRX_RESULT_T;

/**
 * Descriptions: LTE NSFT PreSetting
 */
typedef struct
{
    unsigned char ucBandWidth; //0: 1.4MHz, 1: 3MHz, 2: 5MHz, 3: 10MHz, 4: 15MHz, 5: 20MHz
    bool bLteFddBandsInTestCases; // Indicate the LTE FDD bands are containing in the test cases for instrument presetting
    bool bLteTddBandsInTestCases; // Indicate the LTE TDD bands are containing in the test cases for instrument presetting
    bool bSlotBoundary; // Gen91 needs to set slot boundary instead of sub-frame since uplink and downlink CID is inconsistent
} S_RCTLIB_LTE_NSFT_PRESETTINGS_T;

/**
 * Descriptions: LTE NSFT PreSetting with type
 */
typedef struct
{
    unsigned char type; // 0: LTE NSFT Tx list mode 1: LTE NSFT TRx list mode
    unsigned char reserved;
    union
    {
        S_RCTLIB_LTE_NSFT_PRESETTINGS_T nsft_presetting;
    };
} S_RCTLIB_LTE_NSFT_LIST_MODE_PRESETTINGS_T;

/**
 * Descriptions: LTE NSFT V2 list mode PreSetting with type
 */
typedef struct
{
    unsigned char ucBandWidth; //0: 1.4MHz, 1: 3MHz, 2: 5MHz, 3: 10MHz, 4: 15MHz, 5: 20MHz
    bool bLteFddBandsInTestCases; // Indicate the LTE FDD bands are containing in the test cases for instrument presetting
    bool bLteTddBandsInTestCases; // Indicate the LTE TDD bands are containing in the test cases for instrument presetting
    unsigned char ucFddPhyCellId;
    unsigned char ucTddPhyCellId;
} S_RCTLIB_LTE_NSFT_V2_PRESETTINGS_T;

typedef struct
{
    unsigned char mode;          // 0: LTE NSFT Tx list mode 1: LTE NSFT TRx list mode triggered by TX, 2: LTE NSFT TRx list mode triggered by RX
    unsigned char setting_type;  // 0: nsft_v2_presetting
    union
    {
        S_RCTLIB_LTE_NSFT_V2_PRESETTINGS_T  nsft_v2_presetting;
    };
} S_RCTLIB_LTE_NSFT_LIST_MODE_V2_PRESETTINGS_T;

/**
 * Descriptions: LTE NSFT RX BER
 */
typedef struct
{
    unsigned char       ucBandWidth; //0: 1.4MHz, 1: 3MHz, 2: 5MHz, 3: 10MHz, 4: 15MHz, 5: 20MHz
    unsigned char       ucDuplexMode; /* Refer to Tx duplexMode - tdd,fdd */
    unsigned char       ucBand;
    unsigned char       mcsMode; /* Refer to Tx mcsMode */
    unsigned short      usDlFrequency; // unit: 100KHz
    unsigned char       syncMode; // 0x00 waveform, 0x01 CW.
    bool                isRxSnr;
} S_RCTLIB_LTE_NSFT_RX_BER_T;

/**
 * The structure for cable loss in frequency (Read from cfg)
 */
typedef struct
{
    unsigned short     usFrequencyIn100KHz;
    double             loss;
} S_RCTLIB_LTE_CABLE_LOSS_T;

// ======== TX NSFT test command start ============
typedef struct
{
    /* TX test check items */
    bool                          bTxPowerCheck;
    /* MS setting */
    unsigned char                 ucTxTestStep;
    char                          cRbOffset1;          //start VRB for the first period
    unsigned char                 ucRbLength1;
    char                          cRbOffset2;          //start VRB for the second period
    unsigned char                 ucRbLength2;
    unsigned char                 ucNoStep2ChangeVRB;  //0: Tx VRB will be changed to p2 at step 0
    float                         fStartTargetPower;
    float                         fStepPower;
    float                         fEndTargetPower;
} S_RCTLIB_LTE_NSFT_TEST_TX_SWEEP_REQ_T;

typedef struct
{
    /* TX test check items */
    bool                          bTxPowerCheck[LTE_NSFT_MAX_TX_STEP_TEST_NUM];
    bool                          bEvmCheck[LTE_NSFT_MAX_TX_STEP_TEST_NUM];
    bool                          bObwCheck[LTE_NSFT_MAX_TX_STEP_TEST_NUM];
    bool                          bAclrCheck[LTE_NSFT_MAX_TX_STEP_TEST_NUM];
    bool                          bSemCheck[LTE_NSFT_MAX_TX_STEP_TEST_NUM];
    bool                          bFlatnessCheck[LTE_NSFT_MAX_TX_STEP_TEST_NUM];
    bool                          bGainErrCheck[LTE_NSFT_MAX_TX_STEP_TEST_NUM];
    bool                          bPhaseErrCheck[LTE_NSFT_MAX_TX_STEP_TEST_NUM];
    /* MS setting */
    unsigned char                 ucTxTestStep;
    char                          cRbOffset[LTE_NSFT_MAX_TX_STEP_TEST_NUM];
    unsigned char                 ucRbLength[LTE_NSFT_MAX_TX_STEP_TEST_NUM];
    float                         fTargetPower[LTE_NSFT_MAX_TX_STEP_TEST_NUM];
} S_RCTLIB_LTE_NSFT_TEST_TX_STEP_REQ_T;

typedef struct
{
    /* MS setting */
    int                           dDownlinkSyncPowerDbm;
    unsigned short                usBand;
    unsigned short                usUlFrequency[2];
    unsigned short                usDlFrequency;
    int                           ucUlBandwidth[2];
} S_RCTLIB_LTE_NSFT_2UL_CCA_PRESETTINGS_T;

typedef struct
{
    unsigned char ucband;
    double                            dPoutPower;
    S_RCTLIB_LTE_NSFT_EVM_RESULT_T    sEVMResult;
    S_RCTLIB_LTE_NSFT_2UL_CCA_ACP_RESULT_T    sACPResult;
    S_RCTLIB_LTE_NSFT_SEM_RESULT_T    sSEMResult;
} S_RCTLIB_LTE_NSFT_2UL_CCA_STEP_RESULT_T;

typedef struct
{
    /* TX test check items */
    bool                          bTxPowerCheck[LTE_NSFT_MAX_TX_STEP_TEST_NUM];
    bool                          bEvmCheck[LTE_NSFT_MAX_TX_STEP_TEST_NUM];
    bool                          bAclrCheck[LTE_NSFT_MAX_TX_STEP_TEST_NUM];
    bool                          bSemCheck[LTE_NSFT_MAX_TX_STEP_TEST_NUM];
    bool                          bFlatnessCheck[LTE_NSFT_MAX_TX_STEP_TEST_NUM];
    bool                          bGainErrCheck[LTE_NSFT_MAX_TX_STEP_TEST_NUM];
    bool                          bPhaseErrCheck[LTE_NSFT_MAX_TX_STEP_TEST_NUM];
    /* MS setting */
    unsigned char                 ucCarrierNum;
    unsigned char                 ucDuplexMode; /* Refer to Tx duplexMode - tdd,fdd */
    unsigned short                usBand;
    unsigned short                usUlFrequency[2]; // unit: 100KHz
    unsigned short                usDlFrequency; // unit: 100KHz
    int                         ulBandwidth[2];
    unsigned char                 ucTxTestStep;
    char                          cRbOffset[LTE_NSFT_MAX_TX_STEP_TEST_NUM];
    unsigned char                 ucRbLength[2][LTE_NSFT_MAX_TX_STEP_TEST_NUM];
    float                         fTargetPower[2][LTE_NSFT_MAX_TX_STEP_TEST_NUM];
    float                         fExpectedPower[LTE_NSFT_MAX_TX_STEP_TEST_NUM];
} S_RCTLIB_LTE_NSFT_2UL_CCA_REQ_T;

/**
 * Descriptions: 2UL CCA NSFT test config (per step)
 */
typedef struct
{
    unsigned short                usBand;
    unsigned short                measureItem;
    int                           ucUlBandwidth[2];
    float                         fTargetPower[2];
} S_RCTLIB_LTE_NSFT_2UL_CCA_CONFIG_T;

/**
 * Descriptions: Gen92 ET lab cal
 */
typedef struct
{
    /* MS setting */
    unsigned short  usBand;
    bool            enableTestCCA;
} S_RCTLIB_LTE_ET_LAB_CAL_V2_PRESETTINGS_T;

typedef struct
{
    unsigned char                 carrierCount;
    unsigned short                usBand;
    unsigned short                usUlFrequency[2];
    int                           ucUlBandwidth[2];
    unsigned char                 ucUlRbLength[2];
    unsigned short                usDlFrequency;
    bool                          bEdgeRbLeft;
    float                         fTargetPower;
    bool                          enableTestCCA;
    unsigned char                 ucDlBandwidth; // 1: 1.4M, 2: 3M, 3: 5M, 4: 10M, 5: 15M, 6: 20M, others: 5M
    unsigned char                 ucUlMcs;       // 0: QPSK, 1: 16QAM, 2: 64QAM, 3: 256QAM
} S_RCTLIB_LTE_ET_LAB_CAL_V2_CONFIG_T;

typedef struct
{
    unsigned char carrierCount;
    S_RCTLIB_LTE_NSFT_2UL_CCA_ACP_RESULT_T    sACPResult;
} S_RCTLIB_LTE_ET_LAB_CAL_V2_RESULT_T;

typedef struct
{
    unsigned short measureItem;
    S_RCTLIB_LTE_ET_LAB_CAL_V2_CONFIG_T pSettings;
} S_RCTLIB_LTE_ET_LAB_BIAS_TUNING_CONFIG_T;

typedef struct
{
    unsigned short measureItem;
    unsigned char carrierCount;
    union
    {
        S_RCTLIB_LTE_NSFT_SEM_RESULT_T          sSEMResult;
        S_RCTLIB_LTE_NSFT_2UL_CCA_ACP_RESULT_T  sACPResult;
    };
} S_RCTLIB_LTE_ET_LAB_BIAS_TUNING_RESULT_T;

/**
 * Descriptions: Gen95 ET GC Search
 */
typedef struct
{
    unsigned short  rf_band;
    int             pccUlBW;
    bool            enableTestCCA;
    bool            enableDlSync;
} S_RCTLIB_LTE_ET_GC_SEARCH_V5_PRESETTINGS_T;
typedef struct
{
    unsigned short measureItem;
    S_RCTLIB_LTE_ET_LAB_CAL_V2_CONFIG_T pSettings;
} S_RCTLIB_LTE_ET_GC_SEARCH_V5_CONFIG_T;
typedef struct
{
    union
    {
        S_RCTLIB_LTE_NSFT_SEM_RESULT_T          sSEMResult;
        S_RCTLIB_LTE_NSFT_2UL_CCA_ACP_RESULT_T  sACPResult;
        S_RCTLIB_LTE_NSFT_EVM_RESULT_T          sEVMResult;
    };
} S_RCTLIB_LTE_ET_GC_SEARCH_V5_RESULT_T;
/* TX NSFT test command */
typedef struct
{
    /* Inst. setting and RX test check items */
    union
    {
        S_RCTLIB_LTE_NSFT_TEST_TX_STEP_REQ_T      txStepCmd;
        S_RCTLIB_LTE_NSFT_TEST_TX_SWEEP_REQ_T     txSweepCmd;
    };
    /* TX test command */
    unsigned short ulFrequency;

    char           cRequestOpetions; //0: follow power steps, 1: sweep powers with step (dBm)
    unsigned char  mcsMode; /* Refer to Tx mcsMode */
} S_RCTLIB_LTE_NSFT_TX_FREQ_CONFIG_T;

typedef struct
{
    unsigned char                 ucDuplexMode; /* Refer to Tx duplexMode - tdd,fdd */
    unsigned char                 ucBand;
    unsigned char                 ucFreqNum;
    unsigned short                usDlFrequency; // unit: 100KHz
    S_RCTLIB_LTE_NSFT_TX_FREQ_CONFIG_T  txFreqCmd[13];
} S_RCTLIB_LTE_NSFT_TX_BAND_CONFIG_T;

typedef struct
{
    unsigned char    ucFddTxStepWidth;//ms unit
    unsigned char    ucTddTxStepWidth;//ms unit
    unsigned char    ucFddToTddSwitchTime;//ms unit
    unsigned char    ucFddFreqSwitchTime;//ms unit
    unsigned char    ucTddFreqSwitchTime;//ms unit
    unsigned char    ucFddBandSwitchTime;//ms unit
    unsigned char    ucTddBandSwitchTime;//ms unit
    unsigned char    ucBandNum;
    unsigned short   usFddDlSyncTime;//ms unit
    unsigned short   usTddDlSyncTime;//ms unit
    double           dDownlinkSyncPowerDbm;
    unsigned char    ucBandwidth; /* Refer to Tx ulBandwidth - 6,15,25,50,75,100RB */
    S_RCTLIB_LTE_NSFT_TX_BAND_CONFIG_T  txBandCmd[10];
} S_RCTLIB_LTE_NSFT_TX_CONFIG_T;

typedef struct
{
#define LTE_NSFT_MAX_RX_STEP_TEST_NUM               5
    unsigned char                 ucRxTestStep;
    char                          cRbOffset[LTE_NSFT_MAX_RX_STEP_TEST_NUM];
    unsigned char                 ucRbLength[LTE_NSFT_MAX_RX_STEP_TEST_NUM];
    float                         fTargetPower[LTE_NSFT_MAX_RX_STEP_TEST_NUM];
} S_RCTLIB_LTE_NSFT_TEST_RX_STEP_REQ_T;

typedef struct
{
    union
    {
        S_RCTLIB_LTE_NSFT_TEST_TX_STEP_REQ_T      txStepCmd;
        S_RCTLIB_LTE_NSFT_TEST_TX_SWEEP_REQ_T     txSweepCmd;
    };
    /* TRX test command */
    unsigned short ulFrequency;
    unsigned short dlFrequency;
    char           cRequestOpetions; //0: follow power steps, 1: sweep powers with step (dBm)
    unsigned char  mcsMode; /* Refer to Tx mcsMode */
    union
    {
        S_RCTLIB_LTE_NSFT_TEST_RX_STEP_REQ_T      rxStepCmd;
    };
    unsigned short                usTxRouteNum;
    unsigned short                usRxRouteNum;
} S_RCTLIB_LTE_NSFT_TRX_FREQ_CONFIG_T;

#define MAX_ROUTE_NUM_MARKER        65535

typedef struct
{
    unsigned char                 ucDuplexMode; /* Refer to Tx duplexMode - tdd,fdd */
    unsigned char                 ucBand;
    unsigned char                 ucFreqNum;
    unsigned short                usDlFrequency; // unit: 100KHz
    unsigned short                usTxRouteNum; //if == MAX_ROUTE_NUM_MARKER, then use S_RCTLIB_LTE_NSFT_TRX_FREQ_CONFIG_T's
    unsigned short                usRxRouteNum; //if == MAX_ROUTE_NUM_MARKER, then use S_RCTLIB_LTE_NSFT_TRX_FREQ_CONFIG_T's
    S_RCTLIB_LTE_NSFT_TRX_FREQ_CONFIG_T  trxFreqCmd[8];
} S_RCTLIB_LTE_NSFT_TRX_BAND_CONFIG_T;

typedef struct
{
    unsigned char    ucFddTxStepWidth;//ms unit
    unsigned char    ucTddTxStepWidth;//ms unit
    unsigned char    ucFddToTddSwitchTime;//ms unit
    unsigned char    ucFddRouteSwitchTime;//ms unit
    unsigned char    ucTddRouteSwitchTime;//ms unit
    unsigned char    ucFddFreqSwitchTime;//ms unit
    unsigned char    ucTddFreqSwitchTime;//ms unit
    unsigned char    ucFddBandSwitchTime;//ms unit
    unsigned char    ucTddBandSwitchTime;//ms unit
    unsigned char    ucBandNum;
    unsigned short   usFddDlSyncTime;//ms unit
    unsigned short   usTddDlSyncTime;//ms unit
    double           dDownlinkSyncPowerDbm;
    unsigned char    ucBandwidth; /* Refer to Tx ulBandwidth - 6,15,25,50,75,100RB */
    bool iSRxdTest;
    bool bSkipCompCableLoss;    // for Gen95
    bool bEnableOtfc; // for Gen97
    S_RCTLIB_LTE_NSFT_TRX_BAND_CONFIG_T  trxBandCmd[8];
} S_RCTLIB_LTE_NSFT_TRX_CONFIG_T;

/**
 * Descriptions: Gen97 ET Tuning
 */
typedef struct
{
    unsigned short  rf_band;
    int             pccUlBW;
    bool            enableTestCCA;
    bool            enableDlSync;
} S_RCTLIB_LTE_ET_TUNING_V7_PRESETTINGS_T;
typedef struct
{
    unsigned short measureItem;
    unsigned char  ucDuplexMode; // 0: TDD, 1: FDD
    unsigned char  ucUlRbStart[2];
    int            averageCount;
    bool           enableOtfc;
    unsigned char  tddConfig;    // Uplink Downlink, range: 0 to 6
    unsigned char  tddSfConfig;  // Special Subframe, range: 0 to 8
    S_RCTLIB_LTE_ET_LAB_CAL_V2_CONFIG_T pSettings;
} S_RCTLIB_LTE_ET_TUNING_V7_CONFIG_T;
typedef struct
{
    S_RCTLIB_LTE_NSFT_SEM_RESULT_T          sSEMResult;
    S_RCTLIB_LTE_NSFT_2UL_CCA_ACP_RESULT_T  sACPResult;
    S_RCTLIB_LTE_NSFT_EVM_RESULT_T          sEVMResult;
} S_RCTLIB_LTE_ET_TUNING_V7_RESULT_T;

#define MAX_DPD_KPI_TX_TEST_STEP_NUM (500)
typedef struct
{
    unsigned short waitTime;
    unsigned short intervalTime;
    unsigned short measureNum;
    unsigned short measureItem;
    unsigned char  ucUlRbStart[2];
    int            averageCount;
    S_RCTLIB_LTE_ET_LAB_CAL_V2_CONFIG_T pSettings;
} S_RCTLIB_LTE_DPD_KPI_V7_CONFIG_T;
typedef struct
{
    unsigned short resultNum;
    S_RCTLIB_LTE_ET_TUNING_V7_RESULT_T result[MAX_DPD_KPI_TX_TEST_STEP_NUM];
} S_RCTLIB_LTE_DPD_KPI_V7_RESULT_T;

// ======== TX NSFT test command end ============
/**
 * Descriptions: LTE AFC presetting
 */
typedef struct
{
    unsigned short usFrequency;  /* the frequency to siwtch. Unit: 100KHz */
    short sFreqOffset;  /* the frequency to offset. Unit: KHz */
    double dExpectedPower;  /* the expected power, dBm */
    char cMeasurementCount;  /* measurement count */
} S_RCTLIB_LTE_AFC_PRESETTINGS_T;
/**
 * Descriptions: NR AFC presetting
 */
typedef struct
{
    unsigned int usFrequency;  /* the frequency to siwtch. Unit: KHz */
    short sFreqOffset;  /* the frequency to offset. Unit: KHz */
    double dExpectedPower;  /* the expected power, dBm */
    char cMeasurementCount;  /* measurement count */
} S_RCTLIB_NR_AFC_PRESETTINGS_T;
typedef struct
{
    unsigned short FREQuency; //2412
    unsigned short ENPower; //20
    //string REPetition; //SING
    unsigned short SCOunt; //1
    //string MOEXception; // OFF
    unsigned short FSPan; //20
    unsigned short FFTLength; //2412
    //string DETector; //PEAK
    //string AMODe; //LOG
    unsigned short THReshold; //-25
    //string OMODe; // FIXED
    unsigned short TOUT; //5s
    unsigned short PSEarch_NOAMarkers; //1
    //string PSEarch_ON ;//-1206+6,1206+6,OFF,-1206+6,1206+6,OFF,-1206+6,1206+6,OFF,-1206+6,1206+6,OFF,-1206+6,1206+6;
} S_RCTLIB_SINGLETONE_AFC_PRESETTINGS_T;
/**
 * Description: LTE instrument's spcial settings
 */
typedef struct
{
    /// ========== FHC instrument parameters ==========
    /// FHC RX Max Step Count
    unsigned int uiFHC_RX_MaxStepCount;
    /// FHC TX Max Step Count
    unsigned int uiFHC_TX_MaxStepCount;
    /// FHC Multiple Band Support
    unsigned char ucFHC_MultiBand; // 0: single band, 1: multiple band support
    /// FHC FDD to TDD switch time (ms)
    unsigned char ucFHC_Fdd2TddSwitchTimeMS;
    /// FHC TDD to FDD switch time (ms)
    unsigned char ucFHC_Tdd2FddSwitchTimeMS;
    /// FHC FDD TX to RX switch time (ms)
    unsigned char ucFHC_FddTx2RxSwitchTimeMS;
    /// FHC TDD TX to RX switch time (ms)
    unsigned char ucFHC_TddTx2RxSwitchTimeMS;
    /// FHC frequency switch time (ms)
    unsigned char ucFHC_FreqSwitchTimeMS;
    /// FHC band switch time (ms)
    unsigned char ucFHC_BandSwitchTimeMS;
    /// FHC tx step width (ms)
    unsigned char ucFHC_TxStepWidthMS;
    /// FHC internal trigger delay time (us)
    unsigned int  uiFHC_InternalTriggerDelayTimeUs;
    /// FHC power adjust time (us)
    unsigned int  uiFHC_PowerAdjustTimeUs;
    /// FHC CA NCCA Separate
    unsigned char ucFHC_NCCASeparate; // 1: Separate NCCA from stock CA Cal. flow
    /// FHC RX support broadcast for multiport
    bool bFHC_RxBroadcast4Multiport;
    /// ========== NSFT instrument parameters ==========
    /// NSFT RX Max Step Count
    unsigned int uiNSFT_RX_MaxStepCount;
    /// NSFT TX Max Step Count
    unsigned int uiNSFT_TX_MaxStepCount;
    /// NSFT Multiple Band Support
    unsigned char ucNSFT_MultiBand; // 0: single band, 1: multiple band support
    /// NSFT FDD TX step width (ms) bandwidth[1.4MB, 5MB, 10MB, 15MB, 20MB]
    unsigned char ucNSFT_FddTxSetpWidthMS[5];
    /// NSFT TDD TX step width (ms) bandwidth[1.4MB, 5MB, 10MB, 15MB, 20MB]
    unsigned char ucNSFT_TddTxSetpWidthMS[5];
    /// NSFT FDD to TDD switch time (ms)
    unsigned char ucNSFT_Fdd2TddSwitchTimeMS;
    /// NSFT FDD frequency switch time (ms)
    unsigned char ucNSFT_FddFreqSwitchTimeMS;
    /// NSFT TDD frequency switch time (ms)
    unsigned char ucNSFT_TddFreqSwitchTimeMS;
    /// NSFT FDD band switch time (ms)
    unsigned char ucNSFT_FddBandSwitchTimeMS;
    /// NSFT TDD band switch time (ms)
    unsigned char ucNSFT_TddBandSwitchTimeMS;
    /// NSFT FDD Downlink sync time (ms)
    unsigned short usNSFT_FddDownlinkSyncTimeMS;
    /// NSFT TDD Downlink sync time (ms)
    unsigned short usNSFT_TddDownlinkSyncTimeMS;
    /// NSFT list mode support
    bool bNSFT_ListModeSupported;
    /// NSFT MCS mode supported by frequency if false only by band
    bool bNSFT_McsModeByFrequencySupported;
    /// AFC list mode support
    bool bAFC_ListModeSupported;
    bool bSgBroadcastSupport;
} S_RCTLIB_LTE_SPECIFIC_SETTINGS_T;

typedef struct
{
    unsigned short rf_band;
    unsigned char ucBandwidth;
} S_RCTLIB_LTE_WAVEFORM_CELLID_CONFIG_T;

typedef struct
{
    unsigned char cell_id;
} S_RCTLIB_LTE_WAVEFORM_CELLID_RESULT_T;

/**
 * Description: LTE instrument's spcial settings
 */
typedef struct
{
    /// ========== FHC instrument parameters ==========
    /// FHC RX Max Step Count
    unsigned int uiFHC_RX_MaxStepCount;
    /// FHC TX Max Step Count
    unsigned int uiFHC_TX_MaxStepCount;
    /// FHC Multiple Band Support
    unsigned char ucFHC_MultiBand; // 0: single band, 1: multiple band support
    /// FHC TX to RX switch time (ms)
    unsigned char ucFHC_Tx2RxSwitchTimeMS;
    /// FHC switch time (ms)
    unsigned char ucFHC_SwitchTimeMS;
    /// FHC tx step width (ms)
    unsigned char ucFHC_TxStepWidthMS;
} S_RCTLIB_LTE_FHC_V2_SPECIFIC_SETTINGS_T;


/**
 * Description: LTE NSFT instrument's spcial settings
 */
typedef struct
{
    /// NSFT TX+RX Max Step Count
    unsigned int uiNSFT_MaxStepCount;
    /// NSFT RX Max Step Count
    unsigned int uiNSFT_RX_MaxStepCount;
    /// NSFT TX Max Step Count
    unsigned int uiNSFT_TX_MaxStepCount;
    /// NSFT step limit consider switch time number
    bool bNSFT_SwitchNumStepCost;
    /// NSFT step limit consider switch time duration
    bool bNSFT_SwitchTimeStepCost;
    /// NSFT arrange different duplex to the next run
    bool bNSFT_SeparateDuplex;
    /// NSFT measure time limit
    unsigned int uiNSFT_MaxTimeMs;
    /// NSFT Multiple Band Support
    unsigned char ucNSFT_MultiBand; // 0: single band, 1: multiple band support
    /// NSFT FDD TX step width (ms) bandwidth[1.4MB, 5MB, 10MB, 15MB, 20MB]
    unsigned char ucNSFT_FddTxStepWidthMS[5];
    /// NSFT TDD TX step width (ms) bandwidth[1.4MB, 5MB, 10MB, 15MB, 20MB]
    unsigned char ucNSFT_TddTxStepWidthMS[5];
    /// NSFT FDD to TDD switch time (ms)
    unsigned char ucNSFT_Fdd2TddSwitchTimeMS;
    /// NSFT FDD frequency switch time (ms)
    unsigned char ucNSFT_FddFreqSwitchTimeMS;
    /// NSFT TDD frequency switch time (ms)
    unsigned char ucNSFT_TddFreqSwitchTimeMS;
    /// NSFT FDD band switch time (ms)
    unsigned char ucNSFT_FddBandSwitchTimeMS;
    /// NSFT TDD band switch time (ms)
    unsigned char ucNSFT_TddBandSwitchTimeMS;
    /// NSFT FDD Downlink sync time (ms)
    unsigned short usNSFT_FddDownlinkSyncTimeMS;
    /// NSFT TDD Downlink sync time (ms)
    unsigned short usNSFT_TddDownlinkSyncTimeMS;
    /// NSFT FDD Tx Fetch Offset (ms)
    unsigned short usNSFT_FddTxFetchOffsetMS;
    /// NSFT TDD Tx Fetch Offset (ms)
    unsigned short usNSFT_TddTxFetchOffsetMS;
    /// NSFT list mode support
    bool bNSFT_ListModeSupported;
    /// NSFT MCS mode supported by frequency if false only by band
    bool bNSFT_McsModeByFrequencySupported;
    /// NSFT Test Case Combine
    bool bNSFT_TestCaseCombine;
    /// Use traditional waveform type, 0:TDD cfg2/7 1:TDD cfg 3/7
    unsigned char ucNSFT_tradWaveformType;
    ///  Support source generator broadcast
    bool bSgBroadcastSupport;
    /// Support change waveform (Different bandwidth, duplex mode)
    /// If bNSFT_ChangeWaveformSupported is true, the arrangement will combine different bandwidth, duplex mode in a sequence.
    /// If there are other rules for waveform and instrument does not support waveform change,
    /// also let bNSFT_ChangeFrequecnySupported = false to ensure that waveform will not need to be changed in a sequence.
    bool bNSFT_ChangeWaveformSupported;
} S_RCTLIB_LTE_NSFT_V2_SPECIFIC_SETTINGS_T;
/**
 * Descriptions: LTE traditional NSFT PreSetting
 */
typedef struct
{
    unsigned char ucBandWidth; //0: 1.4MHz, 1: 3MHz, 2: 5MHz, 3: 10MHz, 4: 15MHz, 5: 20MHz
} S_RCTLIB_LTE_NSFT_TX_FREQ_STEP_PRESETTINGS_T;
/**
 * Descriptions: traditional TX NSFT test config (per step)
 */
typedef struct
{
    /* TX test check items */
    bool                          bTxPowerCheck;
    bool                          bEvmCheck;
    bool                          bAclrCheck;
    bool                          bSemCheck;
    bool                          bObwCheck;
    bool                          bFlatnessCheck;
    bool                          bGainErrCheck;
    bool                          bPhaseErrCheck;
    /* MS setting */
    char                          cRbOffset;
    unsigned char                 ucRbLength;
    float                         fTargetPower;
    unsigned short usBand;
    unsigned short ulFrequency;
    unsigned short dlFrequency;
    unsigned char  ucDuplexMode; // Indicate the duplex mode (0: TDD 1: FDD)
    unsigned char  syncMode; // 0x00 waveform, 0x01 CW, (0x2 no sync: only implemented on CMW100)
    float dlSyncPower;
    float syncPowerThreshold;
    bool  enableOtfc;
    unsigned char mcsMode;
    unsigned char evmMode; // 0x0: EVM-RSM, 0x1: EVM-1st
} S_RCTLIB_LTE_NSFT_TX_FREQ_STEP_CONFIG_T;

/**
 * Descriptions: traditional TX NSFT test result (per step)
 */
typedef struct
{
    double                            dPoutPower;
    S_RCTLIB_LTE_NSFT_EVM_RESULT_T    sEVMResult;
    S_RCTLIB_LTE_NSFT_RIPPLE_RESULT_T sRpResult;
    S_RCTLIB_LTE_NSFT_ACP_RESULT_T    sACPResult;
    S_RCTLIB_LTE_NSFT_SEM_RESULT_T    sSEMResult;
    S_RCTLIB_LTE_NSFT_OBW_RESULT_T    sOBWResult;
} S_RCTLIB_LTE_NSFT_TX_TEST_FREQ_STEP_RESULT_T;

typedef struct
{
    /* TX test check items */
    bool                          bTxPowerCheck;
    bool                          bEvmCheck;
    bool                          bAclrCheck;
    bool                          bSemCheck;
    bool                          bFlatnessCheck;
    bool                          bGainErrCheck;
    bool                          bPhaseErrCheck;
    /* MS setting */
    char                          cRbOffset;
    unsigned char                 ucRbLength;
    float                         fTargetPower;
    unsigned short usBand;
    unsigned short ulFrequency;
    unsigned short dlFrequency;
    unsigned char  ucDuplexMode; // Indicate the duplex mode (0: TDD 1: FDD)
    unsigned char  syncMode; // 0x00 waveform, 0x01 CW, (0x2 no sync: only implemented on CMW100)
    float dlSyncPower;
    float syncPowerThreshold;
    float           expectedPowerMargin; // Excepted power = targetPower + expectedPowerMargin (positive value)
    float           triggerLevelMargin; // Trigger level = Excepted power + triggerLevelMargin (negative value)

} S_RCTLIB_LTE_SAR_TX_FREQ_STEP_CONFIG_T;

typedef struct
{
    unsigned short band;
    unsigned char  bandwidthMhz;
    unsigned char  dlScs;
} S_RCTLIB_WAVEFORM_CONFIG_T;

/**
 * The enum is for the instrument command of Sync Free List Mode
 * LTE, NRFR1, and NRFR2 use the same enum to specify the list mode step type
 */
typedef enum
{
    E_INST_LIST_MODE_STEP_TYPE_WAIT = 0,
    E_INST_LIST_MODE_STEP_TYPE_UPLINK_SYNC,
    E_INST_LIST_MODE_STEP_TYPE_DOWNLINK_SYNC,
    E_INST_LIST_MODE_STEP_TYPE_PUSCH_TX,
    E_INST_LIST_MODE_STEP_TYPE_RX_RSSI,
    E_INST_LIST_MODE_STEP_TYPE_SFFT_RX_SENS,
    E_INST_LIST_MODE_STEP_TYPE_FREQ_CHANGE,
    E_INST_LIST_MODE_STEP_TYPE_SWITCH_PORT,
    E_INST_LIST_MODE_STEP_TYPE_TX_START,
    E_INST_LIST_MODE_STEP_TYPE_TX_STOP,
    E_INST_LIST_MODE_STEP_TYPE_STOP,
    E_INST_LIST_MODE_STEP_TYPE_SFFT_TRIGGER_BY_RX,
    E_INST_LIST_MODE_STEP_TYPE_SFFT_RX_OFF,
    E_INST_LIST_MODE_STEP_TYPE_SFFT_RX_SENS_4_TONE,
    E_INST_LIST_MODE_STEP_TYPE_MAX
} E_INST_LIST_MODE_STEP_TYPE;

typedef struct
{
    bool isTxPowerCheck;
    bool isEvmCheck;
    bool isAclrCheck;
    bool isSemCheck;
    bool isObwCheck;
    bool isFlatnessCheck;
    bool isGainErrCheck;
    bool isPhaseErrCheck;
    bool isCustomizationBand;

    unsigned short band;
    unsigned char  duplexMode;
    unsigned int   ulFrequencyKhz;
    unsigned int   dlFrequencyKhz;  // same channel, add 600kHz offset for CW tone
    unsigned char  bandwidth;
    float          txPower;
    unsigned char  mcsMode;
    short          rbOffset;
    unsigned short rbLength;

    float          dlSyncPower;     // Tx sync step: sync power / Tx update step: -100
    unsigned short offsetWidthMs;   // if need more offset time for route change, specified the offset time

    char txCarkit;
    char rxmCarkit;
    char rxdCarkit;
} S_RCTLIB_LTE_SFFT_LIST_TX_STEP_INFO;

typedef struct
{
    E_INST_LIST_MODE_STEP_TYPE stepType;
    unsigned short stepWidthMs;
    float stepWidthMsOffset;
    void *stepInfoPtr;      // TX_START, SFFT_TX, FREQ_CHANGE use TX_STEP_INFO / RX_RSSI, RX_SENS use RX_STEP_INFO / TX_STOP, STOP use NULL
} S_RCTLIB_SFFT_LIST_STEP_CMD;

typedef struct
{
    bool isBift;
    bool isEnableOtfc;
    bool isSwitchTxPort;
    bool isSwitchRxmPort;
    bool isSwitchRxdPort;
    unsigned char triggerMode; // 0: triggered by TX, 1: triggered by RX
    unsigned int stepCount;
    S_RCTLIB_SFFT_LIST_STEP_CMD *stepCmd;
} S_RCTLIB_SFFT_LIST_CMD;

/**
 * The structure is for the result of LTE Sync Free List Mode
 */
typedef struct
{
    double                            dPoutPower;
    S_RCTLIB_LTE_NSFT_EVM_RESULT_T    sEVMResult;
    S_RCTLIB_LTE_NSFT_RIPPLE_RESULT_T sRpResult;
    S_RCTLIB_LTE_NSFT_ACP_RESULT_T    sACPResult;
    S_RCTLIB_LTE_NSFT_SEM_RESULT_T    sSEMResult;
    S_RCTLIB_LTE_NSFT_OBW_RESULT_T    sOBWResult;
} S_RCTLIB_LTE_SFFT_LIST_TX_STEP_RESULT;

typedef struct
{
    unsigned int stepCount;
    S_RCTLIB_LTE_SFFT_LIST_TX_STEP_RESULT  *stepResult;
} S_RCTLIB_LTE_SFFT_LIST_RESULT;

/**********************************************
 * LTE structure (shared library defined END)
 **********************************************/
/**********************************************
 * LTE function (shared library defined START)
 **********************************************/
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_ConfigDefaultSettings(void);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_GET_SpecificSettings(S_RCTLIB_LTE_SPECIFIC_SETTINGS_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_AFC_PreSetting(const S_RCTLIB_LTE_AFC_PRESETTINGS_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_AFC_Initiate(void);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_AFC_FetchResult(double *freqerror);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_AGC_PreSetting(void);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_AGC_ChangeCellBand(unsigned int band);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_AGC_ChangeFrequency(const S_RCTLIB_LTE_FREQUENCY_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_AGC_ChangeCellPower(double cellPower);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_APC_PreSetting(const S_RCTLIB_LTE_APC_PRESETTINGS_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_APC_ChangeCellBand(unsigned int band);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_APC_ChangeUlFrequency(const S_RCTLIB_LTE_FREQUENCY_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_APC_ChangeExpectedPower(int expectedPower);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_APC_Initiate(void);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_APC_FetchResult(double *outputPower);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_FHC_PreSetting(const S_RCTLIB_LTE_FHC_PRESETTINGS_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_FHC_StartIteration(const S_RCTLIB_LTE_FHC_MEASUREMENT_PARAM_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_FHC_FetchResult(const S_RCTLIB_LTE_FHC_MEASUREMENT_PARAM_T *pSettings, S_RCTLIB_LTE_FHC_MEASUREMENT_RESULT_T *pResult);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_FHC_CA_StartIteration(const S_RCTLIB_LTE_FHC_CA_MEASUREMENT_PARAM_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_FHC_CA_FetchResult(const S_RCTLIB_LTE_FHC_CA_MEASUREMENT_PARAM_T *pSettings, S_RCTLIB_LTE_FHC_CA_MEASUREMENT_RESULT_T *pResult);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_FHC_CA_V2_StartIteration(const S_RCTLIB_LTE_FHC_CA_V2_MEASUREMENT_PARAM_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_FHC_CA_V2_FetchResult(const S_RCTLIB_LTE_FHC_CA_V2_MEASUREMENT_PARAM_T *pSettings, S_RCTLIB_LTE_FHC_CA_V2_MEASUREMENT_RESULT_T *pResult);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_FHC_CA_V3_StartIteration(const S_RCTLIB_LTE_FHC_CA_V3_MEASUREMENT_PARAM_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_FHC_CA_V3_GetRxExtraStepNumByBand(const S_RCTLIB_LTE_FHC_CA_V3_BAND_PARAM_T *pBandSettings, int &stepNum);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_FHC_CA_V3_FetchResult(const S_RCTLIB_LTE_FHC_CA_V3_MEASUREMENT_PARAM_T *pSettings, S_RCTLIB_LTE_FHC_CA_V3_MEASUREMENT_RESULT_T *pResult);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_FHC_CA_V5_StartIteration(const S_RCTLIB_LTE_FHC_CA_V5_MEASUREMENT_PARAM_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_FHC_CA_V5_GetRxExtraStepNumByBand(const S_RCTLIB_LTE_FHC_CA_V5_BAND_PARAM_T *pBandSettings, int &stepNum);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_FHC_CA_V5_FetchResult(const S_RCTLIB_LTE_FHC_CA_V5_MEASUREMENT_PARAM_T *pSettings, S_RCTLIB_LTE_FHC_CA_V3_MEASUREMENT_RESULT_T *pResult);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_GET_FHC_V3_SpecificSettings(S_RCTLIB_LTE_FHC_V3_SPECIFIC_SETTINGS_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_NSFT_PreSetting(const S_RCTLIB_LTE_NSFT_PRESETTINGS_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_NSFT_TX_ListMode(const S_RCTLIB_LTE_NSFT_TX_CONFIG_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_NSFT_TX_FetchListMode(const S_RCTLIB_LTE_NSFT_TX_CONFIG_T *pSettings, S_RCTLIB_LTE_NSFT_TX_RESULT_T *pResult);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_NSFT_ChangeCellPower(double d_cell_power);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_NSFT_InitiateBER(const S_RCTLIB_LTE_NSFT_RX_BER_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_NSFT_V3_InitiateBER(const unsigned char waveform_ver, const S_RCTLIB_LTE_NSFT_RX_BER_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_NSFT_TxFreqStepPreSetting(const S_RCTLIB_LTE_NSFT_TX_FREQ_STEP_PRESETTINGS_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_NSFT_TxFreqStepInitiate(const S_RCTLIB_LTE_NSFT_TX_FREQ_STEP_CONFIG_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_NSFT_TxFreqStepFetch(const S_RCTLIB_LTE_NSFT_TX_FREQ_STEP_CONFIG_T *pSettings, S_RCTLIB_LTE_NSFT_TX_TEST_FREQ_STEP_RESULT_T *pResult);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_GET_RxdLossOffset(unsigned short usBand, unsigned short uiFreq100KHz, double *lossOffset);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_NSFT_GET_CableLossOffset(unsigned short usBand, AntennaPortType eAntennaPortType, unsigned short uiFreq100KHz, double *lossoffset);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_NSFT_ListMode_PreSetting(const  S_RCTLIB_LTE_NSFT_LIST_MODE_PRESETTINGS_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_GET_NSFT_V2_SpecificSettings(S_RCTLIB_LTE_NSFT_V2_SPECIFIC_SETTINGS_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_NSFT_TRX_ListMode(const S_RCTLIB_LTE_NSFT_TRX_CONFIG_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_NSFT_TRX_FetchListMode(const S_RCTLIB_LTE_NSFT_TRX_CONFIG_T *pSettings, S_RCTLIB_LTE_NSFT_TRX_RESULT_T *pResult);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_NSFT_2UL_CCA_PreSetting(const S_RCTLIB_LTE_NSFT_2UL_CCA_PRESETTINGS_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_NSFT_2UL_CCA_Initiate(const S_RCTLIB_LTE_NSFT_2UL_CCA_CONFIG_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_NSFT_2UL_CCA_FetchResult(const S_RCTLIB_LTE_NSFT_2UL_CCA_CONFIG_T *pSettings, S_RCTLIB_LTE_NSFT_2UL_CCA_STEP_RESULT_T *pResult);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_GET_FHC_V2_SpecificSettings(S_RCTLIB_LTE_FHC_V2_SPECIFIC_SETTINGS_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_NSFT_ListMode_V2_PreSetting(const  S_RCTLIB_LTE_NSFT_LIST_MODE_V2_PRESETTINGS_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_NSFT_ListMode_V2_PreSetting(const S_RCTLIB_LTE_NSFT_LIST_MODE_V2_PRESETTINGS_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_ET_LAB_BIAS_TUNING_Initiate(const S_RCTLIB_LTE_ET_LAB_BIAS_TUNING_CONFIG_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_ET_LAB_BIAS_TUNING_FetchResult(const S_RCTLIB_LTE_ET_LAB_BIAS_TUNING_CONFIG_T *psInsCmd, S_RCTLIB_LTE_ET_LAB_BIAS_TUNING_RESULT_T *pResult);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_ET_GC_SEARCH_V5_PreSetting(const S_RCTLIB_LTE_ET_GC_SEARCH_V5_PRESETTINGS_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_ET_GC_SEARCH_V5_Initiate(const S_RCTLIB_LTE_ET_GC_SEARCH_V5_CONFIG_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_ET_GC_SEARCH_V5_FetchResult(const S_RCTLIB_LTE_ET_GC_SEARCH_V5_CONFIG_T *pSettings, S_RCTLIB_LTE_ET_GC_SEARCH_V5_RESULT_T *pResult);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_GET_Waveform_CellID(const S_RCTLIB_LTE_WAVEFORM_CELLID_CONFIG_T *pSettings, S_RCTLIB_LTE_WAVEFORM_CELLID_RESULT_T *pResult);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_ET_TUNING_V7_PreSetting(const S_RCTLIB_LTE_ET_TUNING_V7_PRESETTINGS_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_ET_TUNING_V7_Initiate(const S_RCTLIB_LTE_ET_TUNING_V7_CONFIG_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_ET_TUNING_V7_FetchResult(const S_RCTLIB_LTE_ET_TUNING_V7_CONFIG_T *pSettings, S_RCTLIB_LTE_ET_TUNING_V7_RESULT_T *pResult);
NON_REENTRANT_FUNC int __stdcall RCTLIB_LTE_NSFT_ListMode_Stop(const bool isSyncFree);

// reentrant
int __stdcall RCTLIB_LTE_ConfigDefaultSettings_r(const int meta_handle);
int __stdcall RCTLIB_LTE_AFC_PreSetting_r(const int meta_handle, const S_RCTLIB_LTE_AFC_PRESETTINGS_T *pSettings);
int __stdcall RCTLIB_LTE_AFC_Initiate_r(const int meta_handle);
int __stdcall RCTLIB_LTE_AFC_FetchResult_r(const int meta_handle, double *freqerror);
int __stdcall RCTLIB_LTE_AGC_PreSetting_r(const int meta_handle);
int __stdcall RCTLIB_LTE_AGC_ChangeCellBand_r(const int meta_handle, unsigned int band);
int __stdcall RCTLIB_LTE_AGC_ChangeFrequency_r(const int meta_handle, const S_RCTLIB_LTE_FREQUENCY_T *pSettings);
int __stdcall RCTLIB_LTE_AGC_ChangeCellPower_r(const int meta_handle, double cellPower);
int __stdcall RCTLIB_LTE_APC_PreSetting_r(const int meta_handle, const S_RCTLIB_LTE_APC_PRESETTINGS_T *pSettings);
int __stdcall RCTLIB_LTE_APC_ChangeCellBand_r(const int meta_handle, unsigned int band);
int __stdcall RCTLIB_LTE_APC_ChangeUlFrequency_r(const int meta_handle, const S_RCTLIB_LTE_FREQUENCY_T *pSettings);
int __stdcall RCTLIB_LTE_APC_ChangeExpectedPower_r(const int meta_handle, int expectedPower);
int __stdcall RCTLIB_LTE_APC_Initiate_r(const int meta_handle);
int __stdcall RCTLIB_LTE_APC_FetchResult_r(const int meta_handle, double *outputPower);
int __stdcall RCTLIB_LTE_FHC_PreSetting_r(const int meta_handle, const S_RCTLIB_LTE_FHC_PRESETTINGS_T *pSettings);
int __stdcall RCTLIB_LTE_FHC_StartIteration_r(const int meta_handle, const S_RCTLIB_LTE_FHC_MEASUREMENT_PARAM_T *pSettings);
int __stdcall RCTLIB_LTE_FHC_FetchResult_r(const int meta_handle, const S_RCTLIB_LTE_FHC_MEASUREMENT_PARAM_T *pSettings, S_RCTLIB_LTE_FHC_MEASUREMENT_RESULT_T *pResult);
int __stdcall RCTLIB_LTE_FHC_CA_StartIteration_r(const int meta_handle, const S_RCTLIB_LTE_FHC_CA_MEASUREMENT_PARAM_T *pSettings);
int __stdcall RCTLIB_LTE_FHC_CA_FetchResult_r(const int meta_handle, const S_RCTLIB_LTE_FHC_CA_MEASUREMENT_PARAM_T *pSettings, S_RCTLIB_LTE_FHC_CA_MEASUREMENT_RESULT_T *pResult);
int __stdcall RCTLIB_LTE_FHC_CA_V2_StartIteration_r(const int meta_handle, const S_RCTLIB_LTE_FHC_CA_V2_MEASUREMENT_PARAM_T *pSettings);
int __stdcall RCTLIB_LTE_FHC_CA_V2_FetchResult_r(const int meta_handle, const S_RCTLIB_LTE_FHC_CA_V2_MEASUREMENT_PARAM_T *pSettings, S_RCTLIB_LTE_FHC_CA_V2_MEASUREMENT_RESULT_T *pResult);
int __stdcall RCTLIB_LTE_FHC_CA_V3_StartIteration_r(const int meta_handle, const S_RCTLIB_LTE_FHC_CA_V3_MEASUREMENT_PARAM_T *pSettings);
int __stdcall RCTLIB_LTE_FHC_CA_V3_GetRxExtraStepNumByBand_r(const int meta_handle, const S_RCTLIB_LTE_FHC_CA_V3_BAND_PARAM_T *pBandSettings, int &stepNum);
int __stdcall RCTLIB_LTE_FHC_CA_V3_FetchResult_r(const int meta_handle, const S_RCTLIB_LTE_FHC_CA_V3_MEASUREMENT_PARAM_T *pSettings, S_RCTLIB_LTE_FHC_CA_V3_MEASUREMENT_RESULT_T *pResult);
int __stdcall RCTLIB_LTE_FHC_CA_V5_StartIteration_r(const int meta_handle, const S_RCTLIB_LTE_FHC_CA_V5_MEASUREMENT_PARAM_T *pSettings);
int __stdcall RCTLIB_LTE_FHC_CA_V5_GetRxExtraStepNumByBand_r(const int meta_handle, const S_RCTLIB_LTE_FHC_CA_V5_BAND_PARAM_T *pBandSettings, int &stepNum);
int __stdcall RCTLIB_LTE_FHC_CA_V5_FetchResult_r(const int meta_handle, const S_RCTLIB_LTE_FHC_CA_V5_MEASUREMENT_PARAM_T *pSettings, S_RCTLIB_LTE_FHC_CA_V3_MEASUREMENT_RESULT_T *pResult);
int __stdcall RCTLIB_LTE_GET_FHC_V3_SpecificSettings_r(const int meta_handle, S_RCTLIB_LTE_FHC_V3_SPECIFIC_SETTINGS_T *pSettings);
int __stdcall RCTLIB_LTE_NSFT_PreSetting_r(const int meta_handle, const S_RCTLIB_LTE_NSFT_PRESETTINGS_T *pSettings);
int __stdcall RCTLIB_LTE_NSFT_TX_ListMode_r(const int meta_handle, const S_RCTLIB_LTE_NSFT_TX_CONFIG_T *pSettings);
int __stdcall RCTLIB_LTE_NSFT_TX_FetchListMode_r(const int meta_handle, const S_RCTLIB_LTE_NSFT_TX_CONFIG_T *pSettings, S_RCTLIB_LTE_NSFT_TX_RESULT_T *pResult);
int __stdcall RCTLIB_LTE_NSFT_ChangeCellPower_r(const int meta_handle, double d_cell_power);
int __stdcall RCTLIB_LTE_NSFT_InitiateBER_r(const int meta_handle, const S_RCTLIB_LTE_NSFT_RX_BER_T *pSettings);
int __stdcall RCTLIB_LTE_NSFT_V3_InitiateBER_r(const int meta_handle, const unsigned char waveform_ver, const S_RCTLIB_LTE_NSFT_RX_BER_T *pSettings);
int __stdcall RCTLIB_LTE_GET_SpecificSettings_r(const int meta_handle, S_RCTLIB_LTE_SPECIFIC_SETTINGS_T *pSettings);
int __stdcall RCTLIB_LTE_NSFT_TxFreqStepPreSetting_r(const int meta_handle, const S_RCTLIB_LTE_NSFT_TX_FREQ_STEP_PRESETTINGS_T *pSettings);
int __stdcall RCTLIB_LTE_NSFT_TxFreqStepInitiate_r(const int meta_handle, const S_RCTLIB_LTE_NSFT_TX_FREQ_STEP_CONFIG_T *pSettings);
int __stdcall RCTLIB_LTE_NSFT_TxFreqStepFetch_r(const int meta_handle, const S_RCTLIB_LTE_NSFT_TX_FREQ_STEP_CONFIG_T *pSettings, S_RCTLIB_LTE_NSFT_TX_TEST_FREQ_STEP_RESULT_T *pResult);
int __stdcall RCTLIB_LTE_NSFT_TxFreqStepDone_r(const int meta_handle);
int __stdcall RCTLIB_LTE_NSFT_RxFreqStepDone_r(const int meta_handle);
int __stdcall RCTLIB_LTE_GET_RxdLossOffset_r(const int meta_handle, unsigned short usBand, unsigned short uiFreq100KHz, double *lossOffset);
int __stdcall RCTLIB_LTE_NSFT_GET_CableLossOffset_r(const int meta_handle, unsigned short usBand, AntennaPortType eAntennaPortType, unsigned short uiFreq100KHz, double *lossoffset);
int __stdcall RCTLIB_LTE_NSFT_ListMode_PreSetting_r(const int meta_handle, const S_RCTLIB_LTE_NSFT_LIST_MODE_PRESETTINGS_T *pSettings);
int __stdcall RCTLIB_LTE_GET_NSFT_V2_SpecificSettings_r(const int meta_handle, S_RCTLIB_LTE_NSFT_V2_SPECIFIC_SETTINGS_T *pSettings);
int __stdcall RCTLIB_LTE_NSFT_TRX_ListMode_r(const int meta_handle, const S_RCTLIB_LTE_NSFT_TRX_CONFIG_T *pSettings);
int __stdcall RCTLIB_LTE_NSFT_TRX_FetchListMode_r(const int meta_handle, const S_RCTLIB_LTE_NSFT_TRX_CONFIG_T *pSettings, S_RCTLIB_LTE_NSFT_TRX_RESULT_T *pResult);
int __stdcall RCTLIB_LTE_NSFT_2UL_CCA_PreSetting_r(const int meta_handle, const S_RCTLIB_LTE_NSFT_2UL_CCA_PRESETTINGS_T *pSettings);
int __stdcall RCTLIB_LTE_NSFT_2UL_CCA_Initiate_r(const int meta_handle, const S_RCTLIB_LTE_NSFT_2UL_CCA_CONFIG_T *pSettings);
int __stdcall RCTLIB_LTE_NSFT_2UL_CCA_FetchResult_r(const int meta_handle, const S_RCTLIB_LTE_NSFT_2UL_CCA_CONFIG_T *pSettings, S_RCTLIB_LTE_NSFT_2UL_CCA_STEP_RESULT_T *pResult);
int __stdcall RCTLIB_LTE_ET_LAB_CAL_V2_PreSetting_r(const int meta_handle, const S_RCTLIB_LTE_ET_LAB_CAL_V2_PRESETTINGS_T *pSettings);
int __stdcall RCTLIB_LTE_ET_LAB_CAL_V2_Initiate_r(const int meta_handle, const S_RCTLIB_LTE_ET_LAB_CAL_V2_CONFIG_T *pSettings);
int __stdcall RCTLIB_LTE_ET_LAB_CAL_V2_FetchResult_r(const int meta_handle, S_RCTLIB_LTE_ET_LAB_CAL_V2_RESULT_T *pResult);
int __stdcall RCTLIB_LTE_Switch_RF_Port_Ex(const int meta_handle, AntennaPortType eRfPortType, E_RCTLIB_RF_PORT_TYPE ePort);
int __stdcall RCTLIB_LTE_GET_FHC_V2_SpecificSettings_r(const int meta_handle, S_RCTLIB_LTE_FHC_V2_SPECIFIC_SETTINGS_T *pSettings);
int __stdcall RCTLIB_LTE_NSFT_ListMode_V2_PreSetting_r(const int meta_handle, const S_RCTLIB_LTE_NSFT_LIST_MODE_V2_PRESETTINGS_T *pSettings);
int __stdcall RCTLIB_LTE_Get_TAS_RF_Port_Mapping_Port_Number(const int meta_handle, unsigned short usBand, AntennaPortType eAntennaPortType, unsigned char ucTasState);
int __stdcall RCTLIB_LTE_Config_Tas_CableLoss_Ex(const int meta_handle, unsigned short usBand, AntennaPortType eAntennaPortType, bool isBandCombine, unsigned char ucTasState);
int __stdcall RCTLIB_LTE_ET_LAB_BIAS_TUNING_Initiate_r(const int meta_handle, const S_RCTLIB_LTE_ET_LAB_BIAS_TUNING_CONFIG_T *pSettings);
int __stdcall RCTLIB_LTE_ET_LAB_BIAS_TUNING_FetchResult_r(const int meta_handle, const S_RCTLIB_LTE_ET_LAB_BIAS_TUNING_CONFIG_T *psInsCmd, S_RCTLIB_LTE_ET_LAB_BIAS_TUNING_RESULT_T *pResult);
int __stdcall RCTLIB_LTE_ET_GC_SEARCH_V5_PreSetting_r(const int meta_handle, const S_RCTLIB_LTE_ET_GC_SEARCH_V5_PRESETTINGS_T *pSettings);
int __stdcall RCTLIB_LTE_ET_GC_SEARCH_V5_Initiate_r(const int meta_handle, const S_RCTLIB_LTE_ET_GC_SEARCH_V5_CONFIG_T *pSettings);
int __stdcall RCTLIB_LTE_ET_GC_SEARCH_V5_FetchResult_r(const int meta_handle, const S_RCTLIB_LTE_ET_GC_SEARCH_V5_CONFIG_T *pSettings, S_RCTLIB_LTE_ET_GC_SEARCH_V5_RESULT_T *pResult);
int __stdcall RCTLIB_LTE_MM_AFC_PreSetting_r(const int meta_handle, const S_RCTLIB_LTE_AFC_PRESETTINGS_T *pSettings);
int __stdcall RCTLIB_LTE_MM_AFC_Initiate_r(const int meta_handle);
int __stdcall RCTLIB_LTE_MM_AFC_FetchResult_r(const int meta_handle, const unsigned char count, double *freqerror);
int __stdcall RCTLIB_LTE_GET_Waveform_CellID_r(const int meta_handle, const S_RCTLIB_LTE_WAVEFORM_CELLID_CONFIG_T *pSettings, S_RCTLIB_LTE_WAVEFORM_CELLID_RESULT_T *pResult);
int __stdcall RCTLIB_LTE_ET_TUNING_V7_PreSetting_r(const int meta_handle, const S_RCTLIB_LTE_ET_TUNING_V7_PRESETTINGS_T *pSettings);
int __stdcall RCTLIB_LTE_ET_TUNING_V7_Initiate_r(const int meta_handle, const S_RCTLIB_LTE_ET_TUNING_V7_CONFIG_T *pSettings);
int __stdcall RCTLIB_LTE_ET_TUNING_V7_FetchResult_r(const int meta_handle, const S_RCTLIB_LTE_ET_TUNING_V7_CONFIG_T *pSettings, S_RCTLIB_LTE_ET_TUNING_V7_RESULT_T *pResult);

int __stdcall RCTLIB_LTE_DPD_KPI_V7_Initiate_r(const int meta_handle, const S_RCTLIB_LTE_DPD_KPI_V7_CONFIG_T *pSettings);
int __stdcall RCTLIB_LTE_DPD_KPI_V7_FetchResult_r(const int meta_handle, const S_RCTLIB_LTE_DPD_KPI_V7_CONFIG_T *pSettings, S_RCTLIB_LTE_DPD_KPI_V7_RESULT_T *pResult);
int __stdcall RCTLIB_LTE_LoadWaveform_r(const int meta_handle, const S_RCTLIB_WAVEFORM_CONFIG_T *pSettings);

int __stdcall RCTLIB_LTE_Get_RF_Port_Mapping_By_RF_Carkit(const int meta_handle, unsigned short ucBand, RF_Carkit_Index_E eRFCarkitIndex, AntennaPortType eAntennaPortType);
double __stdcall RCTLIB_LTE_GetCableLossOffsetByCarkit(const int meta_handle, unsigned short usBand, AntennaPortType eAntennaPortType, unsigned short usFreqIn100KHz, RF_Carkit_Index_E generalCarkit, RF_Carkit_Index_E routeCarkit);
int __stdcall RCTLIB_LTE_GET_RxdLossOffsetByCarkit_r(const int meta_handle, unsigned short usBand, unsigned short uiFreq100KHz, RF_Carkit_Index_E mainRxRouteCarkit, RF_Carkit_Index_E divRxRouteCarkit, double *lossOffset);

int __stdcall RCTLIB_LTE_Switch_RF_MultipleRxPort(const int meta_handle, const S_RCTLIB_ENABLE_MULTIPLE_RX_RF_PORT_T rxPortList);

int __stdcall RCTLIB_LTE_Config_RF_MultipleRxCableLossByCarkit(const int meta_handle, const unsigned short usBand, const S_RCTLIB_ENABLE_MULTIPLE_RF_CARKIT_T rxCarkitList);
int __stdcall RCTLIB_LTE_Config_RF_MultipleAverageCableLossByCarkit(const int meta_handle, const unsigned short usBand, const S_RCTLIB_ENABLE_MULTIPLE_RF_CARKIT_T txCarkitList, const S_RCTLIB_ENABLE_MULTIPLE_RF_CARKIT_T rxCarkitList, bool isBandCombine, bool bDivider = true);

int __stdcall RCTLIB_LTE_Open_RF_MultipleRxPort(const int meta_handle, const S_RCTLIB_ENABLE_MULTIPLE_RX_RF_PORT_T *portList);
int __stdcall RCTLIB_LTE_Close_RF_MultipleRxPort(const int meta_handle, const S_RCTLIB_ENABLE_MULTIPLE_RX_RF_PORT_T *portList);

int __stdcall RCTLIB_LTE_SAR_TxFreqStepInitiate_r(const int meta_handle, const S_RCTLIB_LTE_SAR_TX_FREQ_STEP_CONFIG_T *pSettings);
int __stdcall RCTLIB_LTE_SAR_TxFreqStepFetch_r(const int meta_handle, const S_RCTLIB_LTE_SAR_TX_FREQ_STEP_CONFIG_T *pSettings, S_RCTLIB_LTE_NSFT_TX_TEST_FREQ_STEP_RESULT_T *pResult);

int __stdcall RCTLIB_LTE_SFFT_List_Config_r(const int meta_handle, const S_RCTLIB_SFFT_LIST_CMD *pSettings);
int __stdcall RCTLIB_LTE_SFFT_List_Start_r(const int meta_handle, bool isTriggeredByDownlinkSignal, bool isBift);
int __stdcall RCTLIB_LTE_SFFT_List_FetchListMode_r(const int meta_handle, const S_RCTLIB_SFFT_LIST_CMD *pSettings, S_RCTLIB_LTE_SFFT_LIST_RESULT *pResult);
int __stdcall RCTLIB_LTE_NSFT_ListMode_Stop_r(const int meta_handle, const bool isSyncFree);
int __stdcall RCTLIB_LTE_NSFT_SET_WaveformVersion_r(const int meta_handle, unsigned char waveformVersion);

int __stdcall RCTLIB_LTE_GPRF_CW_Config_And_TurnOn(const int meta_handle, unsigned int uiFrequency, double dDownlinkPower);
int __stdcall RCTLIB_LTE_GPRF_CW_TurnOff(const int meta_handle);
int __stdcall RCTLIB_LTE_GPRF_CW_Tone_Presetting(const int meta_handle);
int __stdcall RCTLIB_LTE_Config_Repetition_r(const int meta_handle);
int __stdcall RCTLIB_LTE_Display_Local_r(const int meta_handle);
int __stdcall RCTLIB_LTE_Meas_Start_r(const int meta_handle);
int __stdcall RCTLIB_LTE_GPRF_Meas_Start_r(const int meta_handle);
int __stdcall RCTLIB_LTE_Single_Tone_Tx_r(const int meta_handle, const S_RCTLIB_LTE_NSFT_TX_FREQ_STEP_CONFIG_T *pSettings);
//=====================================================
//           LTE Callback defines
//=====================================================
typedef int (*RCTLIB_LTE_InstrumentInit_CALLBACK)(void);
typedef int (*RCTLIB_LTE_ConfigDefaultSettings_CALLBACK)(void);
typedef int (*RCTLIB_LTE_AFC_PreSetting_CALLBACK)(const S_RCTLIB_LTE_AFC_PRESETTINGS_T *pSettings);
typedef int (*RCTLIB_LTE_AFC_Initiate_CALLBACK)(void);
typedef int (*RCTLIB_LTE_AFC_FetchResult_CALLBACK)(double *freqerror);
typedef int (*RCTLIB_LTE_AGC_PreSetting_CALLBACK)(void);
typedef int (*RCTLIB_LTE_AGC_ChangeCellBand_CALLBACK)(unsigned int band);
typedef int (*RCTLIB_LTE_AGC_ChangeFrequency_CALLBACK)(const S_RCTLIB_LTE_FREQUENCY_T *pSettings);
typedef int (*RCTLIB_LTE_AGC_ChangeCellPower_CALLBACK)(double cellPower);
typedef int (*RCTLIB_LTE_APC_PreSetting_CALLBACK)(const S_RCTLIB_LTE_APC_PRESETTINGS_T *pSettings);
typedef int (*RCTLIB_LTE_APC_ChangeCellBand_CALLBACK)(unsigned int band);
typedef int (*RCTLIB_LTE_APC_ChangeUlFrequency_CALLBACK)(const S_RCTLIB_LTE_FREQUENCY_T *pSettings);
typedef int (*RCTLIB_LTE_APC_ChangeExpectedPower_CALLBACK)(int expectedPower);
typedef int (*RCTLIB_LTE_APC_Initiate_CALLBACK)(void);
typedef int (*RCTLIB_LTE_APC_FetchResult_CALLBACK)(double *outputPower);
typedef int (*RCTLIB_LTE_FHC_PreSetting_CALLBACK)(const S_RCTLIB_LTE_FHC_PRESETTINGS_T *pSettings);
typedef int (*RCTLIB_LTE_FHC_StartIteration_CALLBACK)(const S_RCTLIB_LTE_FHC_MEASUREMENT_PARAM_T *pSettings);
typedef int (*RCTLIB_LTE_FHC_FetchResult_CALLBACK)(const S_RCTLIB_LTE_FHC_MEASUREMENT_PARAM_T *pSettings, S_RCTLIB_LTE_FHC_MEASUREMENT_RESULT_T *pResult);
typedef int (*RCTLIB_LTE_FHC_CA_StartIteration_CALLBACK)(const S_RCTLIB_LTE_FHC_CA_MEASUREMENT_PARAM_T *pSettings);
typedef int (*RCTLIB_LTE_FHC_CA_FetchResult_CALLBACK)(const S_RCTLIB_LTE_FHC_CA_MEASUREMENT_PARAM_T *pSettings, S_RCTLIB_LTE_FHC_CA_MEASUREMENT_RESULT_T *pResult);
typedef int (*RCTLIB_LTE_FHC_CA_V2_StartIteration_CALLBACK)(const S_RCTLIB_LTE_FHC_CA_V2_MEASUREMENT_PARAM_T *pSettings);
typedef int (*RCTLIB_LTE_FHC_CA_V2_FetchResult_CALLBACK)(const S_RCTLIB_LTE_FHC_CA_V2_MEASUREMENT_PARAM_T *pSettings, S_RCTLIB_LTE_FHC_CA_V2_MEASUREMENT_RESULT_T *pResult);
typedef int (*RCTLIB_LTE_FHC_CA_V3_StartIteration_CALLBACK)(const S_RCTLIB_LTE_FHC_CA_V3_MEASUREMENT_PARAM_T *pSettings);
typedef int (*RCTLIB_LTE_FHC_CA_V3_GetRxExtraStepNumByBand_CALLBACK)(const S_RCTLIB_LTE_FHC_CA_V3_BAND_PARAM_T *pBandSettings, int &stepNum);
typedef int (*RCTLIB_LTE_FHC_CA_V3_FetchResult_CALLBACK)(const S_RCTLIB_LTE_FHC_CA_V3_MEASUREMENT_PARAM_T *pSettings, S_RCTLIB_LTE_FHC_CA_V3_MEASUREMENT_RESULT_T *pResult);
typedef int (*RCTLIB_LTE_FHC_CA_V5_StartIteration_CALLBACK)(const S_RCTLIB_LTE_FHC_CA_V5_MEASUREMENT_PARAM_T *pSettings);
typedef int (*RCTLIB_LTE_FHC_CA_V5_GetRxExtraStepNumByBand_CALLBACK)(const S_RCTLIB_LTE_FHC_CA_V5_BAND_PARAM_T *pBandSettings, int &stepNum);
typedef int (*RCTLIB_LTE_FHC_CA_V5_FetchResult_CALLBACK)(const S_RCTLIB_LTE_FHC_CA_V5_MEASUREMENT_PARAM_T *pSettings, S_RCTLIB_LTE_FHC_CA_V3_MEASUREMENT_RESULT_T *pResult);
typedef int (*RCTLIB_LTE_GET_FHC_V3_SpecificSettings_CALLBACK)(const S_RCTLIB_LTE_FHC_V3_SPECIFIC_SETTINGS_T *pSettings);
typedef int (*RCTLIB_LTE_NSFT_PreSetting_CALLBACK)(const S_RCTLIB_LTE_NSFT_PRESETTINGS_T *pSettings);
typedef int (*RCTLIB_LTE_NSFT_TX_ListMode_CALLBACK)(const S_RCTLIB_LTE_NSFT_TX_CONFIG_T *pSettings);
typedef int (*RCTLIB_LTE_NSFT_TX_FetchListMode_CALLBACK)(const S_RCTLIB_LTE_NSFT_TX_CONFIG_T *pSettings, S_RCTLIB_LTE_NSFT_TX_RESULT_T *pResult);
typedef int (*RCTLIB_LTE_NSFT_ChangeCellPower_CALLBACK)(double d_cell_power);
typedef int (*RCTLIB_LTE_NSFT_InitiateBER_CALLBACK)(const S_RCTLIB_LTE_NSFT_RX_BER_T *pSettings);
typedef int (*RCTLIB_LTE_GET_SpecificSettings_CALLBACK)(S_RCTLIB_LTE_SPECIFIC_SETTINGS_T *pSettings);
typedef int (*RCTLIB_LTE_NSFT_TxFreqStepPreSetting_CALLBACK)(const S_RCTLIB_LTE_NSFT_TX_FREQ_STEP_PRESETTINGS_T *pSettings);
typedef int (*RCTLIB_LTE_NSFT_TxFreqStepInitiate_CALLBACK)(const S_RCTLIB_LTE_NSFT_TX_FREQ_STEP_CONFIG_T *pSettings);
typedef int (*RCTLIB_LTE_NSFT_TxFreqStepFetch_CALLBACK)(const S_RCTLIB_LTE_NSFT_TX_FREQ_STEP_CONFIG_T *pSettings, S_RCTLIB_LTE_NSFT_TX_TEST_FREQ_STEP_RESULT_T *pResult);
typedef int (*RCTLIB_LTE_GET_RxdLossOffset_CALLBACK)(unsigned char ucBand, unsigned short uiFreq100KHz, double *lossOffset);
typedef int (*RCTLIB_LTE_GET_GetCableLossMappingPort_CALLBACK)(unsigned char ucBand, AntennaPortType eAntennaPortType, unsigned char *port);
typedef int (*RCTLIB_LTE_GET_GetCableLossCalcByFrequency100KHz_CALLBACK)(unsigned char ucPort, unsigned short uiFreq100KHz, double *lossOffset);
typedef int (*RCTLIB_LTE_NSFT_GET_CableLossOffset_CALLBACK)(unsigned char ucBand, AntennaPortType eAntennaPortType, unsigned short uiFreq100KHz, double *lossoffset);
typedef int (*RCTLIB_LTE_NSFT_ListMode_PreSetting_CALLBACK)(const S_RCTLIB_LTE_NSFT_LIST_MODE_PRESETTINGS_T *pSettings);
typedef int (*RCTLIB_LTE_GET_NSFT_V2_SpecificSettings_CALLBACK)(S_RCTLIB_LTE_NSFT_V2_SPECIFIC_SETTINGS_T *pSettings);
typedef int (*RCTLIB_LTE_NSFT_TRX_ListMode_CALLBACK)(const S_RCTLIB_LTE_NSFT_TRX_CONFIG_T *pSettings);
typedef int (*RCTLIB_LTE_NSFT_TRX_FetchListMode_CALLBACK)(const S_RCTLIB_LTE_NSFT_TRX_CONFIG_T *pSettings, S_RCTLIB_LTE_NSFT_TRX_RESULT_T *pResult);
typedef int (*RCTLIB_LTE_NSFT_2UL_CCA_PreSetting_CALLBACK)(const S_RCTLIB_LTE_NSFT_2UL_CCA_PRESETTINGS_T *pSettings);
typedef int (*RCTLIB_LTE_NSFT_2UL_CCA_Initiate_CALLBACK)(const S_RCTLIB_LTE_NSFT_2UL_CCA_CONFIG_T *pSettings);
typedef int (*RCTLIB_LTE_NSFT_2UL_CCA_FetchResult_CALLBACK)(const S_RCTLIB_LTE_NSFT_2UL_CCA_CONFIG_T *pSettings, S_RCTLIB_LTE_NSFT_2UL_CCA_STEP_RESULT_T *pResult);
typedef int (*RCTLIB_LTE_ET_LAB_CAL_V2_PreSetting_CALLBACK)(const S_RCTLIB_LTE_ET_LAB_CAL_V2_PRESETTINGS_T *pSettings);
typedef int (*RCTLIB_LTE_ET_LAB_CAL_V2_Initiate_CALLBACK)(const S_RCTLIB_LTE_ET_LAB_CAL_V2_CONFIG_T *pSettings);
typedef int (*RCTLIB_LTE_ET_LAB_CAL_V2_FetchResult_CALLBACK)(S_RCTLIB_LTE_ET_LAB_CAL_V2_RESULT_T *pResult);
typedef int (*RCTLIB_LTE_GET_FHC_V2_SpecificSettings_CALLBACK)(S_RCTLIB_LTE_FHC_V2_SPECIFIC_SETTINGS_T *pSettings);
typedef int (*RCTLIB_LTE_NSFT_ListMode_V2_PreSetting_CALLBACK)(const S_RCTLIB_LTE_NSFT_LIST_MODE_V2_PRESETTINGS_T *pSettings);
typedef int (*RCTLIB_LTE_ET_LAB_BIAS_TUNING_Initiate_CALLBACK)(const S_RCTLIB_LTE_ET_LAB_BIAS_TUNING_CONFIG_T *pSettings);
typedef int (*RCTLIB_LTE_ET_LAB_BIAS_TUNING_FetchResult_CALLBACK)(const S_RCTLIB_LTE_ET_LAB_BIAS_TUNING_CONFIG_T *psInsCmd, S_RCTLIB_LTE_ET_LAB_BIAS_TUNING_RESULT_T *pResult);
typedef int (*RCTLIB_LTE_ET_GC_SEARCH_V5_PreSetting_CALLBACK)(const S_RCTLIB_LTE_ET_GC_SEARCH_V5_PRESETTINGS_T *pSettings);
typedef int (*RCTLIB_LTE_ET_GC_SEARCH_V5_Initiate_CALLBACK)(const S_RCTLIB_LTE_ET_GC_SEARCH_V5_CONFIG_T *pSettings);
typedef int (*RCTLIB_LTE_ET_GC_SEARCH_V5_FetchResult_CALLBACK)(const S_RCTLIB_LTE_ET_GC_SEARCH_V5_CONFIG_T *pSettings, S_RCTLIB_LTE_ET_GC_SEARCH_V5_RESULT_T *pResult);
typedef int (*RCTLIB_LTE_GET_Waveform_CellID_CALLBACK)(const S_RCTLIB_LTE_WAVEFORM_CELLID_CONFIG_T *pSettings, S_RCTLIB_LTE_WAVEFORM_CELLID_RESULT_T *pResult);
typedef int (*RCTLIB_LTE_ET_TUNING_V7_PreSetting_CALLBACK)(const S_RCTLIB_LTE_ET_TUNING_V7_PRESETTINGS_T *pSettings);
typedef int (*RCTLIB_LTE_ET_TUNING_V7_Initiate_CALLBACK)(const S_RCTLIB_LTE_ET_TUNING_V7_CONFIG_T *pSettings);
typedef int (*RCTLIB_LTE_ET_TUNING_V7_FetchResult_CALLBACK)(const S_RCTLIB_LTE_ET_TUNING_V7_CONFIG_T *pSettings, S_RCTLIB_LTE_ET_TUNING_V7_RESULT_T *pResult);
typedef struct
{
    RCTLIB_LTE_InstrumentInit_CALLBACK InstrumentInit_CALLBACK;
    RCTLIB_LTE_ConfigDefaultSettings_CALLBACK ConfigDefaultSettings_CALLBACK;
    RCTLIB_LTE_AFC_PreSetting_CALLBACK AFC_PreSetting_CALLBACK;
    RCTLIB_LTE_AFC_Initiate_CALLBACK AFC_Initiate_CALLBACK;
    RCTLIB_LTE_AFC_FetchResult_CALLBACK AFC_FetchResult_CALLBACK;
    RCTLIB_LTE_AGC_PreSetting_CALLBACK AGC_PreSetting_CALLBACK;
    RCTLIB_LTE_AGC_ChangeCellBand_CALLBACK AGC_ChangeCellBand_CALLBACK;
    RCTLIB_LTE_AGC_ChangeFrequency_CALLBACK AGC_ChangeFrequency_CALLBACK;
    RCTLIB_LTE_AGC_ChangeCellPower_CALLBACK AGC_ChangeCellPower_CALLBACK;
    RCTLIB_LTE_APC_PreSetting_CALLBACK APC_PreSetting_CALLBACK;
    RCTLIB_LTE_APC_ChangeCellBand_CALLBACK APC_ChangeCellBand_CALLBACK;
    RCTLIB_LTE_APC_ChangeUlFrequency_CALLBACK APC_ChangeUlFrequency_CALLBACK;
    RCTLIB_LTE_APC_ChangeExpectedPower_CALLBACK APC_ChangeExpectedPower_CALLBACK;
    RCTLIB_LTE_APC_Initiate_CALLBACK APC_Initiate_CALLBACK;
    RCTLIB_LTE_APC_FetchResult_CALLBACK APC_FetchResult_CALLBACK;
    RCTLIB_LTE_FHC_PreSetting_CALLBACK FHC_PreSetting_CALLBACK;
    RCTLIB_LTE_FHC_StartIteration_CALLBACK FHC_StartIteration_CALLBACK;
    RCTLIB_LTE_FHC_FetchResult_CALLBACK FHC_FetchResult_CALLBACK;
    RCTLIB_LTE_FHC_CA_StartIteration_CALLBACK FHC_CA_StartIteration_CALLBACK;
    RCTLIB_LTE_FHC_CA_FetchResult_CALLBACK FHC_CA_FetchResult_CALLBACK;
    RCTLIB_LTE_FHC_CA_V2_StartIteration_CALLBACK FHC_CA_V2_StartIteration_CALLBACK;
    RCTLIB_LTE_FHC_CA_V2_FetchResult_CALLBACK FHC_CA_V2_FetchResult_CALLBACK;
    RCTLIB_LTE_FHC_CA_V3_StartIteration_CALLBACK FHC_CA_V3_StartIteration_CALLBACK;
    RCTLIB_LTE_FHC_CA_V3_FetchResult_CALLBACK FHC_CA_V3_FetchResult_CALLBACK;
    RCTLIB_LTE_FHC_CA_V5_StartIteration_CALLBACK FHC_CA_V5_StartIteration_CALLBACK;
    RCTLIB_LTE_FHC_CA_V5_FetchResult_CALLBACK FHC_CA_V5_FetchResult_CALLBACK;
    RCTLIB_LTE_GET_FHC_V3_SpecificSettings_CALLBACK GET_FHC_V3_SpecificSettings_CALLBACK;
    RCTLIB_LTE_NSFT_PreSetting_CALLBACK NSFT_PreSetting_CALLBACK;
    RCTLIB_LTE_NSFT_TX_ListMode_CALLBACK NSFT_TX_ListMode_CALLBACK;
    RCTLIB_LTE_NSFT_TX_FetchListMode_CALLBACK NSFT_TX_FetchListMode_CALLBACK;
    RCTLIB_LTE_NSFT_ChangeCellPower_CALLBACK NSFT_ChangeCellPower_CALLBACK;
    RCTLIB_LTE_NSFT_InitiateBER_CALLBACK NSFT_InitiateBER_CALLBACK;
    RCTLIB_LTE_GET_SpecificSettings_CALLBACK GET_SpecificSettings_CALLBACK;
    RCTLIB_LTE_NSFT_TxFreqStepPreSetting_CALLBACK NSFT_TxFreqStepPreSetting_CALLBACK;
    RCTLIB_LTE_NSFT_TxFreqStepInitiate_CALLBACK NSFT_TxFreqStepInitiate_CALLBACK;
    RCTLIB_LTE_NSFT_TxFreqStepFetch_CALLBACK NSFT_TxFreqStepFetch_CALLBACK;
    RCTLIB_LTE_GET_RxdLossOffset_CALLBACK RxdLossOffset_CALLBACK;
    RCTLIB_LTE_GET_GetCableLossMappingPort_CALLBACK GetCableLossMappingPort_CALLBACK;
    RCTLIB_LTE_GET_GetCableLossCalcByFrequency100KHz_CALLBACK GetCableLossCalcByFrequency100KHz_CALLBACK;
    RCTLIB_LTE_NSFT_GET_CableLossOffset_CALLBACK GetCableLossOffset_CALLBACK;
    RCTLIB_LTE_NSFT_ListMode_PreSetting_CALLBACK NSFT_ListMode_PreSetting_CALLBACK;
    RCTLIB_LTE_GET_NSFT_V2_SpecificSettings_CALLBACK GET_NSFT_V2_SpecificSettings_CALLBACK;
    RCTLIB_LTE_NSFT_TRX_ListMode_CALLBACK NSFT_TRX_ListMode_CALLBACK;
    RCTLIB_LTE_NSFT_TRX_FetchListMode_CALLBACK NSFT_TRX_FetchListMode_CALLBACK;
    RCTLIB_LTE_NSFT_2UL_CCA_PreSetting_CALLBACK NSFT_2UL_CCA_PreSetting_CALLBACK;
    RCTLIB_LTE_NSFT_2UL_CCA_Initiate_CALLBACK NSFT_2UL_CCA_Initiate_CALLBACK;
    RCTLIB_LTE_NSFT_2UL_CCA_FetchResult_CALLBACK NSFT_2UL_CCA_FetchResult_CALLBACK;
    RCTLIB_LTE_ET_LAB_CAL_V2_PreSetting_CALLBACK ET_LAB_CAL_V2_PreSetting_CALLBACK;
    RCTLIB_LTE_ET_LAB_CAL_V2_Initiate_CALLBACK ET_LAB_CAL_V2_Initiate_CALLBACK;
    RCTLIB_LTE_ET_LAB_CAL_V2_FetchResult_CALLBACK ET_LAB_CAL_V2_FetchResult_CALLBACK;
    RCTLIB_LTE_GET_FHC_V2_SpecificSettings_CALLBACK GET_FHC_V2_SpecificSettings_CALLBACK;
    RCTLIB_LTE_NSFT_ListMode_V2_PreSetting_CALLBACK NSFT_ListMode_V2_PreSetting_CALLBACK;
    RCTLIB_LTE_ET_LAB_BIAS_TUNING_Initiate_CALLBACK ET_LAB_BIAS_TUNING_Initiate_CALLBACK;
    RCTLIB_LTE_ET_LAB_BIAS_TUNING_FetchResult_CALLBACK ET_LAB_BIAS_TUNING_FetchResult_CALLBACK;
    RCTLIB_LTE_ET_GC_SEARCH_V5_PreSetting_CALLBACK ET_GC_SEARCH_V5_PreSetting_CALLBACK;
    RCTLIB_LTE_ET_GC_SEARCH_V5_Initiate_CALLBACK ET_GC_SEARCH_V5_Initiate_CALLBACK;
    RCTLIB_LTE_ET_GC_SEARCH_V5_FetchResult_CALLBACK ET_GC_SEARCH_V5_FetchResult_CALLBACK;
    RCTLIB_LTE_GET_Waveform_CellID_CALLBACK GET_Waveform_CellID_CALLBACK;
    RCTLIB_LTE_FHC_CA_V3_GetRxExtraStepNumByBand_CALLBACK FHC_CA_V3_GetRxExtraStepNumByBand_CALLBACK;
    RCTLIB_LTE_FHC_CA_V5_GetRxExtraStepNumByBand_CALLBACK FHC_CA_V5_GetRxExtraStepNumByBand_CALLBACK;
    RCTLIB_LTE_ET_TUNING_V7_PreSetting_CALLBACK ET_TUNING_V7_PreSetting_CALLBACK;
    RCTLIB_LTE_ET_TUNING_V7_Initiate_CALLBACK ET_TUNING_V7_Initiate_CALLBACK;
    RCTLIB_LTE_ET_TUNING_V7_FetchResult_CALLBACK ET_TUNING_V7_FetchResult_CALLBACK;
    // please add in the last
} RCTLIB_LTE_CALLBACKS_CFG_T;

/**
 * Register callbacks interface
 **/
NON_REENTRANT_FUNC int __stdcall RCTLIB_SetLTEInstrumentInstance(RCTLIB_LTE_CALLBACKS_CFG_T *instCallbacks);

// reentrant functions
int __stdcall RCTLIB_SetLTEInstrumentInstance_r(const int meta_handle, RCTLIB_LTE_CALLBACKS_CFG_T *instCallbacks);
/**********************************************
 * LTE function (shared library defined END)
 **********************************************/
#endif //#ifdef __META_LTE__

NON_REENTRANT_FUNC int __stdcall RCTLIB_EN_FHC_CA_V7_StartIteration(const S_RCTLIB_EN_FHC_CA_V7_MEASUREMENT_PARAM_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_EN_FHC_CA_V7_FetchResult(const S_RCTLIB_EN_FHC_CA_V7_MEASUREMENT_PARAM_T *pSettings, S_RCTLIB_EN_FHC_CA_V7_MEASUREMENT_RESULT_T *pResult);
int __stdcall RCTLIB_EN_FHC_CA_V7_StartIteration_r(const int meta_handle, const S_RCTLIB_EN_FHC_CA_V7_MEASUREMENT_PARAM_T *pSettings);
int __stdcall RCTLIB_EN_FHC_CA_V7_FetchResult_r(const int meta_handle, const S_RCTLIB_EN_FHC_CA_V7_MEASUREMENT_PARAM_T *pSettings, S_RCTLIB_EN_FHC_CA_V7_MEASUREMENT_RESULT_T *pResult);
int __stdcall RCTLIB_EN_FHC_SRS_V7_StartIteration_r(const int meta_handle, const S_RCTLIB_EN_FHC_SRS_V7_MEASUREMENT_PARAM_T *pSettings);
int __stdcall RCTLIB_EN_FHC_SRS_V7_FetchResult_r(const int meta_handle, const S_RCTLIB_EN_FHC_SRS_V7_MEASUREMENT_PARAM_T *pSettings, S_RCTLIB_EN_FHC_SRS_V7_MEASUREMENT_RESULT_T *pResult);

int __stdcall RCTLIB_EN_FHC_CheckStepLimitation_r(const int meta_handle, const S_RCTLIB_EN_FHC_MEASUREMENT_PARAM_T *pSettings, S_RCTLIB_EN_FHC_MEASUREMENT_PARAM_CHECK_RESULT_T *pResult);
int __stdcall RCTLIB_EN_FHC_StartIteration_r(const int meta_handle, const S_RCTLIB_EN_FHC_MEASUREMENT_PARAM_T *pSettings);
int __stdcall RCTLIB_EN_FHC_Config_r(const int meta_handle, const S_RCTLIB_EN_FHC_MEASUREMENT_PARAM_T *pSettings);
int __stdcall RCTLIB_EN_FHC_Start_r(const int meta_handle, bool isTriggeredByDownlinkSignal);
int __stdcall RCTLIB_EN_FHC_FetchResult_r(const int meta_handle, const S_RCTLIB_EN_FHC_MEASUREMENT_PARAM_T *pSettings, S_RCTLIB_EN_FHC_MEASUREMENT_RESULT_T *pResult);
int __stdcall RCTLIB_EN_NSFT_SET_WaveformVersion_r(const int meta_handle, unsigned char waveformVersion);
int __stdcall RCTLIB_EN_FHC_Stop_r(const int meta_handle);

/**
 * Description: LTE instrument's spcial settings
 */

typedef struct
{
    /// NSFT list mode support
    bool bNSFT_ListModeSupported;
    /// Support source generator broadcast
    bool bSgBroadcastSupport;
} S_RCTLIB_NR_SPECIFIC_SETTINGS_T;

typedef struct
{
    bool           txTestEnabled;
    bool           rxTestEnabled;
    unsigned char  txBandwidthMhz;
    unsigned char  rxBandwidthMhz;
    unsigned char  averageCount;
    unsigned char   syncMode; // 0x00 waveform, 0x01 CW, (0x2 no sync: only implemented on CMW100)
    bool lteWaveformEnabled;
} S_RCTLIB_NR_TRAD_NSFT_PRESETTINGS_T;

typedef struct
{
    bool  bTxPowerCheck;
    bool  bEvmCheck;
    bool  bAclrCheck;
    bool  bSemCheck;
    bool  bObwCheck;
    bool  bFlatnessCheck;
    bool  bGainErrCheck;
    bool  bPhaseErrCheck;
    bool  bCaLeakageCheck;
} S_RCTLIB_NR_TRAD_NSFT_TX_STEP_CHECK_ITEM_T;

typedef struct
{
    unsigned short  band;
    unsigned int    ulFrequencyKhz;
    float           targetPower;
    unsigned int    dlSyncFrequencyKhz;
    float           dlSyncPower;
    unsigned char   duplexMode; // Indicate the duplex mode (0: TDD 1: FDD)
    unsigned char   scs;
    unsigned char   mcsMode;
    unsigned short  bandwidthMhz;
    short           rbOffset;
    unsigned short  rbLength;
    unsigned char   syncMode; // 0x00 waveform, 0x01 CW, (0x2 no sync: only implemented on CMW100)
    float           syncPowerThreshold;
    unsigned char   slotFormat;
    bool            enableOtfc;
    unsigned char   evmMode; // 0x0: EVM-RSM, 0x1: EVM-1st
    bool            enableSoftwareResourceLock;
    int             averageCount;
} S_RCTLIB_NR_TRAD_NSFT_TX_STEP_MS_SETTING_T;

typedef struct
{
    S_RCTLIB_NR_TRAD_NSFT_TX_STEP_CHECK_ITEM_T checkItem;
    S_RCTLIB_NR_TRAD_NSFT_TX_STEP_MS_SETTING_T msSetting;
} S_RCTLIB_NR_TRAD_NSFT_TX_STEP_CONFIG_T;

typedef S_RCTLIB_LTE_NSFT_SEM_RESULT_T    S_RCTLIB_NR_NSFT_SEM_RESULT_T;
typedef S_RCTLIB_LTE_NSFT_RIPPLE_RESULT_T S_RCTLIB_NR_NSFT_RIPPLE_RESULT_T;




typedef struct
{
    unsigned short  band;
    unsigned int    ulFrequencyKhz;
    float           targetPower;
    unsigned int    dlSyncFrequencyKhz;
    float           dlSyncPower;
    unsigned char   duplexMode; // Indicate the duplex mode (0: TDD 1: FDD)
    unsigned char   scs;
    unsigned char   mcsMode;
    unsigned short  bandwidthMhz;
    short           rbOffset;
    unsigned short  rbLength;
    unsigned char   syncMode; // 0x00 waveform, 0x01 CW, (0x2 no sync: only implemented on CMW100)
    float           syncPowerThreshold;
    unsigned char   slotFormat;
    float           expectedPowerMargin; // Excepted power = targetPower + expectedPowerMargin (positive value)
    float           triggerLevelMargin; // Trigger level = Excepted power + triggerLevelMargin (negative value)

} S_RCTLIB_NR_SAR_TX_STEP_MS_SETTING_T;

typedef struct
{
    S_RCTLIB_NR_TRAD_NSFT_TX_STEP_CHECK_ITEM_T checkItem;
    S_RCTLIB_NR_SAR_TX_STEP_MS_SETTING_T msSetting;
} S_RCTLIB_NR_SAR_TX_STEP_CONFIG_T;


/**
 * The structure for storing NR NSFT EVM result
 */
typedef struct
{
    /// EVM (%rms)
    double dRMS_EVM;
    /// Frequency Error(ppm)
    double dFreq_Error_Ppm;
    /// Magnitude Error (%)
    double dMagnitude_Error;
    /// Phase Error (Degree)
    double dPhase_Error_Deg;
    /// IQ Offset (dB)
    double dIq_Offset_dB;
} S_RCTLIB_NR_NSFT_EVM_RESULT_T;

/**
 * the structure for storing NR NSFT ACP(ACLR) result
 */
typedef struct
{
    /// Total Carrier Power
    double dCarrierPower;
    /// Lower Offset NR ALCR - relative power
    double dRel_Low_Power_NrAclr;
    /// Upper Offset NR ALCR - relative power
    double dRel_Upper_Power_NrAclr;
    /// Lower Offset UTRA1 - relative power
    double dRel_Low_Power_Utra1;
    /// Upper Offset UTRA1 - relative power
    double dRel_Upper_Power_Utra1;
    /// Lower Offset UTRA2 - relative power
    double dRel_Low_Power_Utra2;
    /// Upper Offset UTRA2 - relative power
    double dRel_Upper_Power_Utra2;
} S_RCTLIB_NR_NSFT_ACP_RESULT_T;

/**
 * The structure for storing NR NSFT carrier leakage result
 */
typedef struct
{
    /// Carrier leakage (dB)
    double dCarrier_Leakage_dB;
} S_RCTLIB_NR_NSFT_CA_LEAKAGE_RESULT_T;

typedef struct
{
    /// Occupied Bandwidth (MHz)
    double dOccupied_Bandwidth_MHz;
} S_RCTLIB_NR_NSFT_OBW_RESULT_T;

#define MAX_INFO_LENGTH 256
typedef struct
{
    double                               dPoutPower;
    char                                 errorInfoReport[MAX_INFO_LENGTH];
    S_RCTLIB_NR_NSFT_EVM_RESULT_T        sEVMResult;
    S_RCTLIB_NR_NSFT_RIPPLE_RESULT_T     sRpResult;
    S_RCTLIB_NR_NSFT_ACP_RESULT_T        sACPResult;
    S_RCTLIB_NR_NSFT_SEM_RESULT_T        sSEMResult;
    S_RCTLIB_NR_NSFT_CA_LEAKAGE_RESULT_T sCaLeakageResult;
    S_RCTLIB_NR_NSFT_OBW_RESULT_T        sOBWResult;
    double                               currentInMilliAmps;
} S_RCTLIB_NR_TRAD_NSFT_TX_STEP_RESULT_T;



typedef struct
{
    unsigned char       band;
    unsigned char       duplexMode;
    unsigned char       scs;
    unsigned int        bandwidth;
    unsigned int        dlFrequency;
    unsigned char       syncMode; // 0x00 waveform, 0x01 CW.
    bool                isRxSnr;
} S_RCTLIB_NR_TRAD_NSFT_RX_BER_T;

typedef struct
{
    unsigned short  averageCount; ///average statistic count
    short           mixerLevel;   ///mixer level offset
    unsigned int    rbw;          ///RBW in 1KHz
    unsigned int    vbw;          ///VBW in 1KHz
} S_RCTLIB_EN_TX_SPUR_PRESETTINGS_T;
typedef struct
{
    unsigned char   ratIdx;
    unsigned char   carkit;
    unsigned short  band;
    short           expPower;       ///expected nominal power
    unsigned int    lowerStartFreq; ///lower band start frequency in 1KHz
    unsigned int    lowerStopFreq;  ///lower band stop frequency in 1KHz
    unsigned int    upperStartFreq; ///upper band start frequency in 1KHz
    unsigned int    upperStopFreq;  ///upper band stop frequency in 1KHz
    unsigned short  sweepTimeMs;    ///sweep time in in milliseconds. If the value is set to 0 , the auto sweep time mode is used.
} S_RCTLIB_EN_TX_SPUR_CONFIG_T;
typedef struct
{
    double dLowerPower;
    double dUpperPower;
} S_RCTLIB_EN_TX_SPUR_RESULT_T;

typedef struct
{
    unsigned short waitTime;
    unsigned short intervalTime;
    unsigned short measureNum;
    S_RCTLIB_NR_TRAD_NSFT_TX_STEP_CHECK_ITEM_T checkItem;
    S_RCTLIB_NR_TRAD_NSFT_TX_STEP_MS_SETTING_T msSetting;
} S_RCTLIB_NR_DPD_KPI_V7_CONFIG_T;
typedef struct
{
    double                               dPoutPower;
    S_RCTLIB_NR_NSFT_EVM_RESULT_T        sEVMResult;
    S_RCTLIB_NR_NSFT_ACP_RESULT_T        sACPResult;
    S_RCTLIB_NR_NSFT_SEM_RESULT_T        sSEMResult;
} S_RCTLIB_NR_DPD_KPI_STEP_RESULT_T;
typedef struct
{
    unsigned short resultNum;
    S_RCTLIB_NR_DPD_KPI_STEP_RESULT_T    result[MAX_DPD_KPI_TX_TEST_STEP_NUM];
} S_RCTLIB_NR_DPD_KPI_V7_RESULT_T;

NON_REENTRANT_FUNC int __stdcall RCTLIB_NR_GET_SpecificSettings(S_RCTLIB_NR_SPECIFIC_SETTINGS_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_NR_TRAD_NSFT_PreSetting(const S_RCTLIB_NR_TRAD_NSFT_PRESETTINGS_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_NR_TRAD_NSFT_TxStepInitiate(const S_RCTLIB_NR_TRAD_NSFT_TX_STEP_CONFIG_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_NR_TRAD_NSFT_TxStepFetch(const S_RCTLIB_NR_TRAD_NSFT_TX_STEP_CONFIG_T *pSettings, S_RCTLIB_NR_TRAD_NSFT_TX_STEP_RESULT_T *pResult);
NON_REENTRANT_FUNC int __stdcall RCTLIB_NR_TRAD_NSFT_TxStepDone();
NON_REENTRANT_FUNC int __stdcall RCTLIB_NR_TRAD_NSFT_RxChangeCellPower(double cellPower);
NON_REENTRANT_FUNC int __stdcall RCTLIB_NR_TRAD_NSFT_RxInitiateBER(const S_RCTLIB_NR_TRAD_NSFT_RX_BER_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_NR_ET_Tuning_PreSetting(const S_RCTLIB_NR_TRAD_NSFT_PRESETTINGS_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_NR_ET_Tuning_TxStepInitiate(const S_RCTLIB_NR_TRAD_NSFT_TX_STEP_CONFIG_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_NR_ET_Tuning_TxStepFetch(const S_RCTLIB_NR_TRAD_NSFT_TX_STEP_CONFIG_T *pSettings, S_RCTLIB_NR_TRAD_NSFT_TX_STEP_RESULT_T *pResult);

int __stdcall RCTLIB_NR_GET_SpecificSettings_r(const int meta_handle, S_RCTLIB_NR_SPECIFIC_SETTINGS_T *pSettings);
int __stdcall RCTLIB_NR_TRAD_NSFT_PreSetting_r(const int meta_handle, const S_RCTLIB_NR_TRAD_NSFT_PRESETTINGS_T *pSettings);
int __stdcall RCTLIB_NR_TRAD_NSFT_TxStepInitiate_r(const int meta_handle, const S_RCTLIB_NR_TRAD_NSFT_TX_STEP_CONFIG_T *pSettings);
int __stdcall RCTLIB_NR_TRAD_NSFT_TxStepFetch_r(const int meta_handle, const S_RCTLIB_NR_TRAD_NSFT_TX_STEP_CONFIG_T *pSettings, S_RCTLIB_NR_TRAD_NSFT_TX_STEP_RESULT_T *pResult);
int __stdcall RCTLIB_NR_TRAD_NSFT_TxStepDone_r(const int meta_handle);
int __stdcall RCTLIB_NR_TRAD_NSFT_RxChangeCellPower_r(const int meta_handle, double cellPower);
int __stdcall RCTLIB_NR_TRAD_NSFT_RxInitiateBER_r(const int meta_handle, const S_RCTLIB_NR_TRAD_NSFT_RX_BER_T *pSettings);
int __stdcall RCTLIB_NR_TRAD_NSFT_RxStepDone_r(const int meta_handle);

int __stdcall RCTLIB_NR_Get_RF_Port_Mapping_By_RF_Carkit(const int meta_handle, unsigned short rfBand, RF_Carkit_Index_E eRFCarkitIndex, AntennaPortType eAntennaPortType);
int __stdcall RCTLIB_NR_GetCableLossOffsetByCarkit(const int meta_handle, unsigned short ucBand, AntennaPortType eAntennaPortType, unsigned short usFreqIn100KHz, RF_Carkit_Index_E generalCarkit, RF_Carkit_Index_E routeCarkit, double *lossOffset);
int __stdcall RCTLIB_NR_Switch_RF_Port_Ex(const int meta_handle, AntennaPortType eRfPortType, E_RCTLIB_RF_PORT_TYPE ePort);
int __stdcall RCTLIB_NR_Open_RF_MultipleRxPort(const int meta_handle, const S_RCTLIB_ENABLE_MULTIPLE_RX_RF_PORT_T *portList);
int __stdcall RCTLIB_NR_Close_RF_MultipleRxPort(const int meta_handle, const S_RCTLIB_ENABLE_MULTIPLE_RX_RF_PORT_T *portList);

int __stdcall RCTLIB_NR_Switch_RF_MultipleRxPort(const int meta_handle, const S_RCTLIB_ENABLE_MULTIPLE_RX_RF_PORT_T rxPortList);
int __stdcall RCTLIB_NR_Config_RF_MultipleRxCableLossByCarkit(const int meta_handle, const unsigned char ucBand, const S_RCTLIB_ENABLE_MULTIPLE_RF_CARKIT_T rxCarkitList);
int __stdcall RCTLIB_NR_Config_RF_MultipleTxCableLossByCarkit(const int meta_handle, const unsigned char ucBand, const S_RCTLIB_ENABLE_MULTIPLE_RF_CARKIT_T txCarkitList);
int __stdcall RCTLIB_NR_Config_RF_MultipleAverageCableLossByCarkit(const int meta_handle, const unsigned char ucBand, const S_RCTLIB_ENABLE_MULTIPLE_RF_CARKIT_T txCarkitList, const S_RCTLIB_ENABLE_MULTIPLE_RF_CARKIT_T rxCarkitList, bool bDivider);
int __stdcall RCTLIB_NR_ET_Tuning_PreSetting_r(const int meta_handle, const S_RCTLIB_NR_TRAD_NSFT_PRESETTINGS_T *pSettings);
int __stdcall RCTLIB_NR_ET_Tuning_TxStepInitiate_r(const int meta_handle, const S_RCTLIB_NR_TRAD_NSFT_TX_STEP_CONFIG_T *pSettings);
int __stdcall RCTLIB_NR_ET_Tuning_TxStepFetch_r(const int meta_handle, const S_RCTLIB_NR_TRAD_NSFT_TX_STEP_CONFIG_T *pSettings, S_RCTLIB_NR_TRAD_NSFT_TX_STEP_RESULT_T *pResult);
int __stdcall RCTLIB_EN_ET_Tuning_TxSpurPreSetting_r(const int meta_handle, const S_RCTLIB_EN_TX_SPUR_PRESETTINGS_T *pSettings);
int __stdcall RCTLIB_EN_ET_Tuning_TxSpurFetch_r(const int meta_handle, const S_RCTLIB_EN_TX_SPUR_CONFIG_T *pSettings, S_RCTLIB_EN_TX_SPUR_RESULT_T *pResult);
int __stdcall RCTLIB_NR_DPD_KPI_V7_Initiate_r(const int meta_handle, const S_RCTLIB_NR_DPD_KPI_V7_CONFIG_T *pSettings);
int __stdcall RCTLIB_NR_DPD_KPI_V7_FetchResult_r(const int meta_handle, const S_RCTLIB_NR_DPD_KPI_V7_CONFIG_T *pSettings, S_RCTLIB_NR_DPD_KPI_V7_RESULT_T *pResult);
int __stdcall RCTLIB_NR_LoadWaveform_r(const int meta_handle, const S_RCTLIB_WAVEFORM_CONFIG_T *pSettings);

int __stdcall RCTLIB_NR_SAR_TxStepInitiate_r(const int meta_handle, const S_RCTLIB_NR_SAR_TX_STEP_CONFIG_T *pSettings);
int __stdcall RCTLIB_NR_SAR_TxStepFetch_r(const int meta_handle, const S_RCTLIB_NR_SAR_TX_STEP_CONFIG_T *pSettings, S_RCTLIB_NR_TRAD_NSFT_TX_STEP_RESULT_T *pResult);


int __stdcall RCTLIB_EN_Get_RF_Port_Mapping_By_RF_Carkit(const int meta_handle, unsigned char ratIdx, unsigned short rfBand, RF_Carkit_Index_E eRFCarkitIndex, AntennaPortType eAntennaPortType);
int __stdcall RCTLIB_EN_Config_CableLoss_By_Carkit_Ex(const int meta_handle, unsigned char ratIdx, unsigned short rfBand, AntennaPortType eAntennaPortType, bool isBandCombine, bool bDivider = true);
int __stdcall RCTLIB_EN_Switch_RF_Port_Ex(const int meta_handle, AntennaPortType eRfPortType, E_RCTLIB_RF_PORT_TYPE ePort);

int __stdcall RCTLIB_NR_AFC_PreSetting_r(const int meta_handle, const S_RCTLIB_NR_AFC_PRESETTINGS_T *pSettings);
int __stdcall RCTLIB_NR_AFC_Initiate_r(const int meta_handle);
int __stdcall RCTLIB_NR_AFC_FetchResult_r(const int meta_handle, double *dFreqError);

int __stdcall RCTLIB_NR_GPRF_CW_Config_And_TurnOn(const int meta_handle, unsigned int uiFrequency, double dDownlinkPower);
int __stdcall RCTLIB_NR_GPRF_CW_TurnOff(const int meta_handle);
int __stdcall RCTLIB_NR_GPRF_CW_Tone_Presetting(const int meta_handle);
int __stdcall RCTLIB_NR_Config_Repetition_r(const int meta_handle);
int __stdcall RCTLIB_NR_Display_Local_r(const int meta_handle);
int __stdcall RCTLIB_NR_Meas_Start_r(const int meta_handle);
int __stdcall RCTLIB_NR_GPRF_Meas_Start_r(const int meta_handle);
int __stdcall RCTLIB_NR_Single_Tone_Tx_r(const int meta_handle, const S_RCTLIB_NR_TRAD_NSFT_TX_STEP_CONFIG_T *pSettings);
/**
 * The structure is NR NSFT list mode specific setting
 */
typedef struct
{
    /// NSFT TX+RX Max Step Count
    unsigned int uiNSFT_MaxStepCount;
    /// NSFT RX Max Step Count
    unsigned int uiNSFT_RX_MaxStepCount;
    /// NSFT TX Max Step Count
    unsigned int uiNSFT_TX_MaxStepCount;
    /// NSFT step limit consider switch time number
    bool bNSFT_SwitchNumStepCost;
    /// NSFT step limit consider switch time duration
    bool bNSFT_SwitchTimeStepCost;
    /// NSFT measure time limit
    unsigned int uiNSFT_MaxTimeMs;
    /// NSFT frequency switch time (ms)
    /// Freq switch time = band switch time = duplex mode switch time
    unsigned char ucNSFT_FreqSwitchTimeMS;
    /// NSFT TX step width (ms)
    unsigned char ucNSFT_TxStepWidthMS;
    /// NSFT Tx Fetch Offset (ms)
    unsigned short usNSFT_TxFetchOffsetMS;

    /// Support list mode
    bool bNSFT_ListModeSupported;

    /// Support switch instrument ports by route in a sequence.
    /// If bNSFT_SwitchPortSupported is true, the arrangement will combine routes with differnet carkits in a sequence.
    bool bNSFT_SwitchPortSupported;

    /// Support change waveform (Different bandwidth, SCS, duplex mode)
    /// If bNSFT_ChangeWaveformSupported is true, the arrangement will combine different bandwidth, SCS, duplex mode in a sequence.
    /// If there are other rules for waveform and instrument does not support waveform change,
    /// also let bNSFT_ChangeFrequecnySupported = false to ensure that waveform will not need to be changed in a sequence.
    bool bNSFT_ChangeWaveformSupported;

    /// Support change frequecny
    /// If bNSFT_ChangeFrequecnySupported is true, the arrangement may combine more than one frequency configs in a sequence.
    bool bNSFT_ChangeFrequecnySupported;

    /// Support source generator broadcast
    bool bNSFT_SgBroadcastSupported;
} S_RCTLIB_NR_NSFT_LIST_MODE_SPECIFIC_SETTINGS_T;

/**
 * The structure is for NR NSFT list mode presetting
 */
typedef struct
{
    unsigned char ucBandWidth;
    unsigned char triggerMode; // 0: triggered by TX, 1: triggered by RX
    bool lteWaveformEnabled;
} S_RCTLIB_NR_NSFT_LIST_MODE_PRESETTINGS_T;

/**
 * The structure is for NR NSFT list mode
 */

#define MAX_NR_NSFT_TX_TEST_STEP_NUM (8)
#define MAX_NR_NSFT_RX_TEST_STEP_NUM (5)
#define MAX_NR_NSFT_LIST_TX_ROUTE_NUM (8)
#define MAX_NR_NSFT_LIST_RX_ROUTE_NUM (8)
#define MAX_NR_NSFT_LIST_RXTX_FREQ_NUM (8)
#define MAX_NR_NSFT_LIST_RXTX_BAND_NUM (8)

typedef struct
{
    /* TX test check items */
    bool                          bTxPowerCheck;
    bool                          bEvmCheck;
    bool                          bAclrCheck;
    bool                          bSemCheck;
    bool                          bObwCheck;
    bool                          bFlatnessCheck;
    bool                          bGainErrCheck;
    bool                          bPhaseErrCheck;
    bool                          bCaLeakageCheck;
    /* MS setting */
    short                         sRbOffset;
    unsigned short                usRbLength;
    float                         fTargetPower;
    float                         fPowerHighDelta;
    float                         fPowerLowDelta;
    unsigned char                 ucMcsMode;
} S_RCTLIB_NR_NSFT_TEST_TX_STEP_REQ_T;

typedef struct
{
    float                         fTargetPower;
} S_RCTLIB_NR_NSFT_TEST_RX_STEP_REQ_T;

typedef struct
{
    unsigned char ucTxTestStepNum;
    unsigned char ucRxTestStepNum;
    S_RCTLIB_NR_NSFT_TEST_TX_STEP_REQ_T txStepCmd[MAX_NR_NSFT_TX_TEST_STEP_NUM];
    S_RCTLIB_NR_NSFT_TEST_RX_STEP_REQ_T rxStepCmd[MAX_NR_NSFT_RX_TEST_STEP_NUM];

    unsigned char ucBandwidth;
    unsigned char ucScs;

    unsigned int usUlFrequency;
    unsigned int usDlFrequency;

    unsigned short usTxRouteNum;
    unsigned short usRxRouteNum;

    bool bSwitchTxPort;  // if bSwitchTxPort is true, need to switch TX ports by txCarkit[]
    bool bSwitchRxmPort; // if bSwitchRxmPort is true, need to switch RXM ports by rxmCarkit[]
    bool bSwitchRxdPort; // if bSwitchRxdPort is true, need to switch RXD ports by rxdCarkit[]

    char txCarkit[MAX_NR_NSFT_LIST_TX_ROUTE_NUM];
    char rxmCarkit[MAX_NR_NSFT_LIST_RX_ROUTE_NUM];
    char rxdCarkit[MAX_NR_NSFT_LIST_RX_ROUTE_NUM];

} S_RCTLIB_NR_NSFT_TRX_FREQ_CONFIG_T;

typedef struct
{
    unsigned char  ucBand;
    unsigned char  ucDuplexMode;
    unsigned char  ucFreqNum;

    S_RCTLIB_NR_NSFT_TRX_FREQ_CONFIG_T  trxFreqCmd[MAX_NR_NSFT_LIST_RXTX_FREQ_NUM];
} S_RCTLIB_NR_NSFT_TRX_BAND_CONFIG_T;

typedef struct
{
    unsigned char  ucTxStepWidth;//ms unit
    unsigned char  ucRxStepWidth;//ms unit
    // Freq switch time = band switch time = duplex mode switch time
    unsigned char  ucFreqSwitchTime;//ms unit
    unsigned char  ucRouteSwitchTime;//ms unit
    unsigned short usDlSyncTime;//ms unit

    double         dDownlinkSyncPowerDbm; //dbm unit

    unsigned char    ucBandNum;
    bool            bEnableOtfc;

    S_RCTLIB_NR_NSFT_TRX_BAND_CONFIG_T trxBandCmd[MAX_NR_NSFT_LIST_RXTX_BAND_NUM];
} S_RCTLIB_NR_NSFT_TRX_CONFIG_T;

/**
 * The structure is for the result of NR NSFT list mode
 */
typedef struct
{
    double                               dPoutPower;
    S_RCTLIB_NR_NSFT_EVM_RESULT_T        sEVMResult;
    S_RCTLIB_NR_NSFT_RIPPLE_RESULT_T     sRpResult;
    S_RCTLIB_NR_NSFT_ACP_RESULT_T        sACPResult;
    S_RCTLIB_NR_NSFT_SEM_RESULT_T        sSEMResult;
    S_RCTLIB_NR_NSFT_CA_LEAKAGE_RESULT_T sCaLeakageResult;
    S_RCTLIB_NR_NSFT_OBW_RESULT_T        sOBWResult;
} S_RCTLIB_NR_NSFT_TEST_TX_STEP_RESULT_T;

typedef struct
{
    unsigned char ucMeasureStep;
    S_RCTLIB_NR_NSFT_TEST_TX_STEP_RESULT_T txStepResult[MAX_NR_NSFT_TX_TEST_STEP_NUM];
} S_RCTLIB_NR_NSFT_TRX_TEST_ROUTE_RESULT_T;


typedef struct
{
    S_RCTLIB_NR_NSFT_TRX_TEST_ROUTE_RESULT_T txRouteResults[MAX_NR_NSFT_LIST_TX_ROUTE_NUM];
} S_RCTLIB_NR_NSFT_TRX_TEST_FREQ_RESULT_T;

typedef struct
{
    S_RCTLIB_NR_NSFT_TRX_TEST_FREQ_RESULT_T txFreqResults[MAX_NR_NSFT_LIST_RXTX_FREQ_NUM];
} S_RCTLIB_NR_NSFT_TRX_TEST_BAND_RESULT_T;

typedef struct
{
    S_RCTLIB_NR_NSFT_TRX_TEST_BAND_RESULT_T txBandResults[MAX_NR_NSFT_LIST_RXTX_BAND_NUM];
} S_RCTLIB_NR_NSFT_TRX_RESULT_T;


NON_REENTRANT_FUNC int __stdcall RCTLIB_NR_GET_NSFT_ListMode_SpecificSettings(S_RCTLIB_NR_NSFT_LIST_MODE_SPECIFIC_SETTINGS_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_NR_NSFT_ListMode_PreSetting(const S_RCTLIB_NR_NSFT_LIST_MODE_PRESETTINGS_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_NR_NSFT_TRX_ListMode(const S_RCTLIB_NR_NSFT_TRX_CONFIG_T *pSettings);
NON_REENTRANT_FUNC int __stdcall RCTLIB_NR_NSFT_TRX_FetchListMode(const S_RCTLIB_NR_NSFT_TRX_CONFIG_T *pSettings, S_RCTLIB_NR_NSFT_TRX_RESULT_T *pResult);
NON_REENTRANT_FUNC int __stdcall RCTLIB_NR_NSFT_ListMode_Stop(const bool isSyncFree);

int __stdcall RCTLIB_NR_GET_NSFT_ListMode_SpecificSettings_r(const int meta_handle, S_RCTLIB_NR_NSFT_LIST_MODE_SPECIFIC_SETTINGS_T *pSettings);
int __stdcall RCTLIB_NR_NSFT_ListMode_PreSetting_r(const int meta_handle, const S_RCTLIB_NR_NSFT_LIST_MODE_PRESETTINGS_T *pSettings);
int __stdcall RCTLIB_NR_NSFT_TRX_ListMode_r(const int meta_handle, const S_RCTLIB_NR_NSFT_TRX_CONFIG_T *pSettings);
int __stdcall RCTLIB_NR_NSFT_TRX_FetchListMode_r(const int meta_handle, const S_RCTLIB_NR_NSFT_TRX_CONFIG_T *pSettings, S_RCTLIB_NR_NSFT_TRX_RESULT_T *pResult);
int __stdcall RCTLIB_NR_NSFT_ListMode_Stop_r(const int meta_handle, const bool isSyncFree);
int __stdcall RCTLIB_NR_NSFT_SET_WaveformVersion_r(const int meta_handle, unsigned char waveformVersion);

/**
 * The structure is for the command of NRFR1 Sync Free List Mode
 */
typedef struct
{
    bool isRxSnr;

    unsigned short band;
    unsigned char  duplexMode;
    unsigned int   ulFrequencyKhz;
    unsigned int   dlFrequencyKhz;  // Rx Snr add 600kHz offset for CW tone
    unsigned char  bandwidth;
    unsigned char  scs;
    float          rxPower;

    char txCarkit;
    char rxmCarkit;
    char rxdCarkit;

    unsigned int metaHandleCount;                                    // count = 1 : single DUTs, >1 : multi-DUTs
    int metaHandles[METAInstrumentLibrary_MAX_CONCURRENT_THREADS];   // 32
} S_RCTLIB_SFFT_LIST_RX_STEP_INFO;

typedef struct
{
    bool isTxPowerCheck;
    bool isEvmCheck;
    bool isAclrCheck;
    bool isSemCheck;
    bool isObwCheck;
    bool isFlatnessCheck;
    bool isGainErrCheck;
    bool isPhaseErrCheck;
    bool isCaLeakageCheck;
    bool isCustomizationBand;

    unsigned short band;
    unsigned char  duplexMode;
    unsigned int   ulFrequencyKhz;
    unsigned int   dlFrequencyKhz;  // same channel, add 600kHz offset for CW tone
    unsigned char  bandwidth;
    unsigned char  scs;
    float          txPower;
    unsigned char  mcsMode;
    short          rbOffset;
    unsigned short rbLength;
    unsigned char  tddSlotConfig;

    float          dlSyncPower;     // Tx sync step: sync power / Tx update step: -100
    unsigned short offsetWidthMs;   // if need more offset time for route change, specified the offset time

    char txCarkit;
    char rxmCarkit;
    char rxdCarkit;
} S_RCTLIB_NRFR1_SFFT_LIST_TX_STEP_INFO;

/**
 * The structure is for the result of NRFR1 Sync Free List Mode
 */
typedef struct
{
    unsigned int stepCount;
    S_RCTLIB_NR_NSFT_TEST_TX_STEP_RESULT_T  *stepResult;
} S_RCTLIB_NRFR1_SFFT_LIST_RESULT;

int __stdcall RCTLIB_NRFR1_SFFT_List_Config_r(const int meta_handle, const S_RCTLIB_SFFT_LIST_CMD *pSettings);
int __stdcall RCTLIB_NRFR1_SFFT_List_Start_r(const int meta_handle, bool isTriggeredByDownlinkSignal, bool isBift);
int __stdcall RCTLIB_NRFR1_SFFT_List_FetchResult_r(const int meta_handle, const S_RCTLIB_SFFT_LIST_CMD *pSettings, S_RCTLIB_NRFR1_SFFT_LIST_RESULT *pResult);

//=====================================================
//           NR Callback defines
//=====================================================
typedef int (*RCTLIB_NR_InstrumentInit_CALLBACK)(void);
typedef int (*RCTLIB_NR_GET_SpecificSettings_CALLBACK)(S_RCTLIB_NR_SPECIFIC_SETTINGS_T *pSettings);
typedef int (*RCTLIB_NR_TRAD_NSFT_PreSetting_CALLBACK)(const S_RCTLIB_NR_TRAD_NSFT_PRESETTINGS_T *pSettings);
typedef int (*RCTLIB_NR_TRAD_NSFT_TxStepInitiate_CALLBACK)(const S_RCTLIB_NR_TRAD_NSFT_TX_STEP_CONFIG_T *pSettings);
typedef int (*RCTLIB_NR_TRAD_NSFT_TxStepFetch_CALLBACK)(const S_RCTLIB_NR_TRAD_NSFT_TX_STEP_CONFIG_T *pSettings, S_RCTLIB_NR_TRAD_NSFT_TX_STEP_RESULT_T *pResult);
typedef int (*RCTLIB_NR_TRAD_NSFT_TxStepDone_CALLBACK)(void);
typedef int (*RCTLIB_NR_TRAD_NSFT_RxChangeCellPower_CALLBACK)(double cellPower);
typedef int (*RCTLIB_NR_TRAD_NSFT_RxInitiateBER_CALLBACK)(const S_RCTLIB_NR_TRAD_NSFT_RX_BER_T *pSettings);
typedef int (*RCTLIB_NR_ET_Tuning_PreSetting_CALLBACK)(const S_RCTLIB_NR_TRAD_NSFT_PRESETTINGS_T *pSettings);
typedef int (*RCTLIB_NR_ET_Tuning_TxStepInitiate_CALLBACK)(const S_RCTLIB_NR_TRAD_NSFT_TX_STEP_CONFIG_T *pSettings);
typedef int (*RCTLIB_NR_ET_Tuning_TxStepFetch_CALLBACK)(const S_RCTLIB_NR_TRAD_NSFT_TX_STEP_CONFIG_T *pSettings, S_RCTLIB_NR_TRAD_NSFT_TX_STEP_RESULT_T *pResult);



typedef struct
{
    RCTLIB_NR_InstrumentInit_CALLBACK                 InstrumentInit_CALLBACK;
    RCTLIB_NR_GET_SpecificSettings_CALLBACK           NR_GET_SpecificSettings_CALLBACK;
    RCTLIB_NR_TRAD_NSFT_PreSetting_CALLBACK           TRAD_NSFT_PreSetting_CALLBACK;
    RCTLIB_NR_TRAD_NSFT_TxStepInitiate_CALLBACK       TRAD_NSFT_TxStepInitiate_CALLBACK;
    RCTLIB_NR_TRAD_NSFT_TxStepFetch_CALLBACK          TRAD_NSFT_TxStepFetch_CALLBACK;
    RCTLIB_NR_TRAD_NSFT_TxStepDone_CALLBACK           TRAD_NSFT_TxStepDone_CALLBACK;
    RCTLIB_NR_TRAD_NSFT_RxChangeCellPower_CALLBACK    TRAD_NSFT_RxChangeCellPower_CALLBACK;
    RCTLIB_NR_TRAD_NSFT_RxInitiateBER_CALLBACK        TRAD_NSFT_RxInitiateBER_CALLBACK;
    RCTLIB_NR_ET_Tuning_PreSetting_CALLBACK           ET_Tuning_PreSetting_CALLBACK;
    RCTLIB_NR_ET_Tuning_TxStepInitiate_CALLBACK       ET_Tuning_TxStepInitiate_CALLBACK;
    RCTLIB_NR_ET_Tuning_TxStepFetch_CALLBACK          ET_Tuning_TxStepFetch_CALLBACK;
    // please add in the last
} RCTLIB_NR_CALLBACKS_CFG_T;

/**
 * Register callbacks interface
 **/
NON_REENTRANT_FUNC int __stdcall RCTLIB_SetNRInstrumentInstance(RCTLIB_NR_CALLBACKS_CFG_T *instCallbacks);
// reentrant functions
int __stdcall RCTLIB_SetNRInstrumentInstance_r(const int meta_handle, RCTLIB_NR_CALLBACKS_CFG_T *instCallbacks);

//=====================================================
//           NR Callback defines
//=====================================================
typedef int (*RCTLIB_EN_InstrumentInit_CALLBACK)(void);
typedef int (*RCTLIB_EN_FHC_CA_V7_StartIteration_CALLBACK)(const S_RCTLIB_EN_FHC_CA_V7_MEASUREMENT_PARAM_T *pSettings);
typedef int (*RCTLIB_EN_FHC_CA_V7_FetchResult_CALLBACK)(const S_RCTLIB_EN_FHC_CA_V7_MEASUREMENT_PARAM_T *pSettings, S_RCTLIB_EN_FHC_CA_V7_MEASUREMENT_RESULT_T *pResult);
typedef int (*RCTLIB_EN_ET_Tuning_TxSpurPreSetting_CALLBACK)(const S_RCTLIB_EN_TX_SPUR_PRESETTINGS_T *pSettings);
typedef int (*RCTLIB_EN_ET_Tuning_TxSpurFetch_CALLBACK)(const S_RCTLIB_EN_TX_SPUR_CONFIG_T *pSettings, S_RCTLIB_EN_TX_SPUR_RESULT_T *pResult);

typedef struct
{
    RCTLIB_EN_InstrumentInit_CALLBACK                 InstrumentInit_CALLBACK;
    RCTLIB_EN_FHC_CA_V7_StartIteration_CALLBACK       FHC_CA_V7_StartIteration_CALLBACK;
    RCTLIB_EN_FHC_CA_V7_FetchResult_CALLBACK          FHC_CA_V7_FetchResult_CALLBACK;
    RCTLIB_EN_ET_Tuning_TxSpurPreSetting_CALLBACK     ET_Tuning_TxSpurPreSetting_CALLBACK;
    RCTLIB_EN_ET_Tuning_TxSpurFetch_CALLBACK          ET_Tuning_TxSpurFetch_CALLBACK;
    // please add in the last
} RCTLIB_EN_CALLBACKS_CFG_T;
/**
 * Register callbacks interface
 **/
NON_REENTRANT_FUNC int __stdcall RCTLIB_SetENInstrumentInstance(RCTLIB_EN_CALLBACKS_CFG_T *instCallbacks);
// reentrant functions
int __stdcall RCTLIB_SetENInstrumentInstance_r(const int meta_handle, RCTLIB_EN_CALLBACKS_CFG_T *instCallbacks);


/*****************************
 * NR FR2
 *****************************/
/**********************************************
 * NR FR2 structure (shared library defined START)
 **********************************************/
typedef enum
{
    RCTLIB_NRFR2_TEST_ONE_MODE = 0,
    RCTLIB_NRFR2_TEST_TWO_COMBINE_MODE = 1,
    RCTLIB_NRFR2_TEST_NUM
} E_RCTLIB_NRFR2_TEST_MODE;


typedef struct
{
    bool enablePhaseTracking;
    unsigned char uePowerClass;
} S_RCTLIB_NRFR2_TRAD_NSFT_PRESETTINGS_T;


typedef struct
{

} S_RCTLIB_NRFR2_SPECIFIC_SETTINGS_T;


typedef struct
{
    E_RCTLIB_NRFR2_TEST_MODE txTestMode;
    int fetchId;

    unsigned short band;
    unsigned int   ulFrequencyKhz;
    float          targetPower;
    unsigned int   dlSyncFrequencyKhz;
    float          dlSyncPower;
    unsigned char  duplexMode; // 0: TDD 1: FDD
    unsigned char  scs;
    unsigned char  mcsMode;
    unsigned short bandwidthMhz;
    unsigned short rbOffset;
    unsigned short rbLength;
    unsigned char tddSlotConfig;
    unsigned char dmrsAntennaPort; // only V or only H : dmrsAntennaPort = 0
    unsigned char syncMode; //0: modulation, 1: single tone.
} S_RCTLIB_NRFR2_TRAD_NSFT_TX_STEP_MS_SETTING_T;


typedef struct
{
    bool bTxPowerCheck;
    bool bEvmCheck;
    bool bAclrCheck;
    bool bSemCheck;
    bool bFlatnessCheck;
    bool bGainErrCheck;
    bool bPhaseErrCheck;
    bool bCaLeakageCheck;
    bool bIQImbalanceCheck;
} S_RCTLIB_NRFR2_TRAD_NSFT_TX_STEP_CHECK_ITEM_T;

typedef struct
{
    S_RCTLIB_NRFR2_TRAD_NSFT_TX_STEP_MS_SETTING_T msSetting;
    S_RCTLIB_NRFR2_TRAD_NSFT_TX_STEP_CHECK_ITEM_T checkItem;
} S_RCTLIB_NRFR2_TRAD_NSFT_TX_STEP_CONFIG_T;



typedef struct
{
    unsigned short band;
    unsigned char duplexMode; // 0: TDD 1: FDD
    unsigned char scs;
    unsigned char mcsMode;
    unsigned short bandwidthMhz;
    unsigned int dlFrequencyKhz;
    float dlSyncPower;
    unsigned char syncMode; // 0x00 waveform, 0x01 CW
    unsigned int dlFrequencyToneOffsetKhz;
} S_RCTLIB_NRFR2_TRAD_NSFT_RX_STEP_CONFIG_T;

typedef struct
{
    /// Data EVM (%rms)
    double dData_EVM;
    /// DMRS EVM (%rms)
    double dDRMS_EVM;
    /// Frequency Error (ppm)
    double dFreq_Error_Ppm;
    /// IQ Offset (dB)
    double dIq_Offset_dB;

    /// Magnitude Error (%)
    double dMagnitude_Error;
    /// Phase Error (deg)
    double dPhase_Error_Deg;
    //Is IQ Imbalance Measurement Invalid
    bool bInvalid_IQ_Imbalance;
    /// IQ Imbalance Gain (dB)
    double dIQ_Imbalance_Gain_dB;
    /// IQ Imbalance Phase (deg)
    double dIQ_Imbalance_Phase_Deg;
    /// DC Leakage (dB)
    double dDC_Leakage_dB;
    //Is LO Leakage  Measurement Invalid
    bool bInvalid_LO_Leakage;
    /// LO Leakage (dB)
    double dLO_Leakage_dB;

} S_RCTLIB_NRFR2_NSFT_EVM_RESULT_T;

typedef struct
{
    /// RP 1 test or not
    bool isRP1Tested;
    /// RP 2 test or not
    bool isRP2Tested;
    /// RP 12 test or not
    bool isRP12Tested;
    /// RP 21 test or not
    bool isRP21Tested;
    /// maximum Ripple in Range 1
    double dRipple1;
    /// maximum Ripple in Range 2
    double dRipple2;
    /// RP 12 Value - the maximum ripple between the upper side of Range 1 and lower side of Range 2
    double dRipple12;
    /// RP 21 Value - the maximum ripple between the upper side of Range 2 and lower side of Range 1
    double dRipple21;
} S_RCTLIB_NRFR2_NSFT_RIPPLE_RESULT_T;

typedef struct
{
    /// Total Carrier Power
    double dCarrierPower;
    /// Lower Offset NR ALCR - relative power
    double dRel_Low_Power_NrAclr;
    /// Upper Offset NR ALCR - relative power
    double dRel_Upper_Power_NrAclr;
} S_RCTLIB_NRFR2_NSFT_ACP_RESULT_T;

typedef struct
{
    /// Abs Peak Power of lowers
    double dAbsPeakPowerLower[2];
    /// Abs Peak Power of uppers
    double dAbsPeakPowerUpper[2];
    /// Lower delta Limit
    double dDeltaLimitLower[2];
    /// Upper delta Limit
    double dDeltaLimitUpper[2];
} S_RCTLIB_NRFR2_NSFT_SEM_RESULT_T;

typedef struct
{
    /// Carrier leakage (dB)
    double dCarrier_Leakage_dB;
} S_RCTLIB_NRFR2_NSFT_CA_LEAKAGE_RESULT_T;


typedef struct
{
    double dPoutPower;
    S_RCTLIB_NRFR2_NSFT_EVM_RESULT_T sEVMResult;
    S_RCTLIB_NRFR2_NSFT_RIPPLE_RESULT_T sRpResult;
    S_RCTLIB_NRFR2_NSFT_ACP_RESULT_T sACPResult;
    S_RCTLIB_NRFR2_NSFT_SEM_RESULT_T sSEMResult;
} S_RCTLIB_NRFR2_TRAD_NSFT_TX_STEP_RESULT_T;

typedef struct
{
    bool enablePhaseTracking;
    unsigned char uePowerClass;
} S_RCTLIB_NRFR2_NSFT_LIST_MODE_PRESETTINGS_T;

typedef struct
{
    unsigned int frequencyKhz;
} S_RCTLIB_NRFR2_NSFT_LIST_MODE_CHANGE_FREQ_T;

typedef struct
{
    E_INST_LIST_MODE_STEP_TYPE stepType;
    float stepWidthMs;
    void *stepInfoPtr;
} S_RCTLIB_NRFR2_NSFT_LIST_MODE_STEP_UNIT_T;

typedef struct
{
    unsigned int stepCount;
    S_RCTLIB_NRFR2_NSFT_LIST_MODE_STEP_UNIT_T *trxStepAry;
} S_RCTLIB_NRFR2_NSFT_LIST_MODE_TRX_CONFIG_T;

typedef struct
{
    unsigned int resultCount;
    S_RCTLIB_NRFR2_TRAD_NSFT_TX_STEP_RESULT_T *trxStepAry;
} S_RCTLIB_NRFR2_NSFT_LIST_MODE_TRX_RESULT_T;

typedef struct
{
    unsigned short rfBand;
    unsigned short headIdx;
    unsigned char polarIdx;
    AntennaPortType eAntennaPortType;
} S_RCTLIB_NRFR2_NSFT_LIST_MODE_PORT_T;

/**********************************************
 * NR FR2 structure (shared library defined END)
 **********************************************/

/**********************************************
 * NR FR2 function (shared library defined START)
 **********************************************/
int __stdcall RCTLIB_NRFR2_GetPortByHeadAndPolar(const int meta_handle, const unsigned short headIdx, const unsigned char polarIdx, unsigned short *instPortNo, unsigned short *switchPortNo);
int __stdcall RCTLIB_NRFR2_SetRfPortSetting(const int meta_handle, E_RCTLIB_RF_PORT_TYPE eRfPort, bool enableVSA, bool enableVSG);

int __stdcall RCTLIB_NRFR2_GET_SpecificSettings(const int meta_handle, S_RCTLIB_NRFR2_SPECIFIC_SETTINGS_T *pSettings);
int __stdcall RCTLIB_NRFR2_TRAD_NSFT_PreSetting(const int meta_handle, const S_RCTLIB_NRFR2_TRAD_NSFT_PRESETTINGS_T *pSettings);
int __stdcall RCTLIB_NRFR2_TRAD_NSFT_TxStepInitiate(const int meta_handle, const S_RCTLIB_NRFR2_TRAD_NSFT_TX_STEP_CONFIG_T *pSettings);
int __stdcall RCTLIB_NRFR2_TRAD_NSFT_TxStepFetch(const int meta_handle, const S_RCTLIB_NRFR2_TRAD_NSFT_TX_STEP_CONFIG_T *pSettings, S_RCTLIB_NRFR2_TRAD_NSFT_TX_STEP_RESULT_T *pResult);
int __stdcall RCTLIB_NRFR2_TRAD_NSFT_TxStepDone(const int meta_handle);
int __stdcall RCTLIB_NRFR2_TRAD_NSFT_RxStepInitiate(const int meta_handle, const S_RCTLIB_NRFR2_TRAD_NSFT_RX_STEP_CONFIG_T *pSettings);
int __stdcall RCTLIB_NRFR2_TRAD_NSFT_RxChangeCellPower(const int meta_handle, const double cellPower);
int __stdcall RCTLIB_NRFR2_TRAD_NSFT_RxStepDone(const int meta_handle);
int __stdcall RCTLIB_NRFR2_ConfigOTALoss(const int meta_handle, const unsigned short rfBand, const unsigned short headIdx, const unsigned char polarIdx, const AntennaPortType eAntennaPortType);


int __stdcall RCTLIB_NRFR2_ConnectSwitch(const int meta_handle, short port);
int __stdcall RCTLIB_NRFR2_DisconnSwitch(const int meta_handle);
int __stdcall RCTLIB_NRFR2_SetSwitchSetting(const int meta_handle, short antNum);
int __stdcall RCTLIB_NRFR2_GetSwitchSetting(const int meta_handle, short *antNum);
int __stdcall RCTLIB_NRFR2_GetSwitchPortStatus(const int meta_handle, const short antNum, int *retStatus);

int __stdcall RCTLIB_NRFR2_GET_NSFT_ListMode_SpecificSettings(const int meta_handle, S_RCTLIB_NRFR2_SPECIFIC_SETTINGS_T *pSettings);
int __stdcall RCTLIB_NRFR2_NSFT_ListMode_PreSetting(const int meta_handle, const S_RCTLIB_NRFR2_NSFT_LIST_MODE_PRESETTINGS_T *pSettings);
int __stdcall RCTLIB_NRFR2_NSFT_ListMode_Init(const int meta_handle, const S_RCTLIB_NRFR2_NSFT_LIST_MODE_TRX_CONFIG_T *pSettings);
int __stdcall RCTLIB_NRFR2_NSFT_ListMode_Fetch(const int meta_handle, const S_RCTLIB_NRFR2_NSFT_LIST_MODE_TRX_CONFIG_T *pSettings, S_RCTLIB_NRFR2_NSFT_LIST_MODE_TRX_RESULT_T *pResult);
int __stdcall RCTLIB_NRFR2_NSFT_ListMode_ConfigMultipleOTALoss(const int meta_handle, const int count, const S_RCTLIB_NRFR2_NSFT_LIST_MODE_PORT_T *port);


/**********************************************
 * NR FR2 function (shared library defined END)
 **********************************************/


#ifdef __META_C2K__
/*****************************
 * C2K
 *****************************/
/**********************************************
 * C2K structure (shared library defined START)
 **********************************************/
/**
 * Descriptions: C2K AGC PreSetting
 */
typedef struct
{
    /// tester operating mode
    unsigned int mode;
    /// cell power for AGC calibration
    double cellPower;
    /// F-Pilot channel power (dB)
    double pilotPower;
    /// F-SYNC channel power (dB)
    double syncPower;
    /// F-PCH power (dB)
    double pchPower;
    /// F-FCH power (dB)
    double fchPower;
} S_RCTLIB_C2K_AGC_PRESETTINGS_T;
/**
 * Descriptions: C2K APC PreSetting
 */
typedef struct
{
    /// measurement timeout setting (ms)
    double timeout;
    /// measurement interval (us)
    double measurementInterval;
    /// trigger delay (us)
    double triggerDelay;
} S_RCTLIB_C2K_APC_PRESETTINGS_T;
typedef struct
{
    /// measurement timeout setting (ms)
    double timeout;
    /// measurement interval (us)
    double measurementInterval;
    /// trigger delay (us)
    double triggerDelay;
    unsigned int triggerType; //trigger type 0:immediate 1:Rise
} S_RCTLIB_C2K_APC_PRESETTINGS_EX_T;
typedef struct
{
    /// step width (ms)
    double stepWidth;
    /// F-Pilot channel power (dB)
    double pilotPower;
    /// F-SYNC channel power (dB)
    double syncPower;
    /// F-PCH power (dB)
    double pchPower;
    /// F-FCH power (dB)
    double fchPower;
} S_RCTLIB_C2K_FHC_PRESETTINGS_T;
typedef struct
{
    /// UARFCN in MHz
    double mHz;
    /// UARFCN
    unsigned short uarfcn;
} S_RCTLIB_C2K_FHC_FREQ_STEP_U;
/**
 * The structure for storing C2K FHC request
 */
typedef struct
{
    /// band class
    unsigned int e_band;
    /// measurement timeout setting
    double d_meas_timeout_sec;
    /// retune step width (ms)
    double retuneWidth;
    /// start index of UE TX power step
    int    txPowerStepStartIndex;
    /// start index of UE TX frequency step
    int    txFrequencyStepStartIndex;
    /// start index of UE RX power step
    int    rxPowerStepStartIndex;
    /// start index of UE RX frequency step
    int    rxFrequencyStepStartIndex;
    /// number of power step
    int    numberOfPowerSteps;
    /// number of frequency step
    int    numberOfFrequencySteps;
    /// expected TX power level (dBm)
    double txPowerSteps[20];
    /// down-link power level (dBm)
    double rxPowerSteps[20];
    /// UE TX frequency setting in each frequency step (UARFCN or MHz)
    S_RCTLIB_C2K_FHC_FREQ_STEP_U txFreqSteps[20];
    /// UE RX frequency setting in each frequency step (UARFCN or MHz)
    S_RCTLIB_C2K_FHC_FREQ_STEP_U rxFreqSteps[20];
} S_RCTLIB_C2K_FHC_BAND_PARAM_T;
typedef struct
{
    unsigned int bandNum;
    /// TX + RX Band parameter
    S_RCTLIB_C2K_FHC_BAND_PARAM_T sTRxBandParam[10];
} S_RCTLIB_C2K_FHC_MEASUREMENT_PARAM_T;
/**
 * The structure for storing C2K FHC TX power result
 */
typedef struct
{
    /// integrity of the measurement result
    int    integrity;
    /// frequency step
    int    numberOfFrequencySteps;
    /// power step
    int    numberOfPowerSteps;
    /// frequency step * power step count
    int    tx_pwr_cnt;
    /// [OUT] power measurement result array
    double d_tx_power[400];
    /// [OUT] power measurement result count
    int    real_cnt;
} S_RCTLIB_C2K_FHC_BAND_RESULT_T;
typedef struct
{
    /// TX Band Result
    S_RCTLIB_C2K_FHC_BAND_RESULT_T sTxBandResult[10];
} S_RCTLIB_C2K_FHC_MEASUREMENT_RESULT_T;
/**
 * The structure for storing C2K NSFT TPC control used in TX performance
 */
typedef struct
{
    /// specify the test pattern (0: All up for UE max power, 1: All down for UE min power, 2: active for specified power
    int i_Pattern;
    /// TPC algorithm  (1: algo1 2: algo2)
    unsigned int u_Algorithm;
    /// TPC step size
    int i_Step;
    /// the target power if the i_Pattern is 2, expected power if the tpc pattern is 0 or 1 (all up or all down)
    double d_TargetPower;
} S_RCTLIB_C2K_NSFT_TPC_REQUEST_T;
/**
 * The structure for storing C2K NSFT FER result
 */
typedef struct
{
    /// FER
    double d_fer;
    /// frame error count
    int i_err_cnt;
    /// total test count
    int i_total_cnt;
} S_RCTLIB_C2K_NSFT_FER_RESULT_T;
/**
 * The structure for storing C2K NSFT APP result
 */
typedef struct
{
    /// access probe power
    double d_probe_power;
} S_RCTLIB_C2K_NSFT_APP_RESULT_T;
/**
 * The structure for storing C2K NSFT SPUR result
 */
typedef struct
{
    /// pass flag (overall)
    bool bPass;
    /// lower adjacent emission
    double d_spur_lower_adj;
    /// upper adjacent emission
    double d_spur_upper_adj;
    /// lower alternate emission
    double d_spur_lower_alt;
    /// upper alternate emission
    double d_spur_upper_alt;
} S_RCTLIB_C2K_NSFT_SPUR_RESULT_T;
/**
 * The structure for storing C2K NSFT WQ result
 */
typedef struct
{
    /// Rho
    double d_rho;
    /// Frequency Error
    double d_freq_error;
    /// Time Error
    double d_time_error;
    /// Carrier Feed through
    double d_carrier_feed;
    /// Phase Error
    double d_phase_error;
    /// Magnitude Error
    double d_mag_error;
    /// EVM (%)
    double d_evm;
} S_RCTLIB_C2K_NSFT_WQ_RESULT_T;
/**
 * The structure for storing C2K NSFT ILPC result
 */
typedef struct
{
    /// integrity
    int     i_Integrity;
    /// pass flag (overall)
    bool    bPass;
    /// number of slots
    int     i_NumSlots;
    /// power (ABS)
    double  d_Absolute[100];
    /// delta value between each step
    double  d_Delta[100];
    bool    bCal_fail;
} S_RCTLIB_C2K_NSFT_ILPC_RESULT_T;
/**
 * the structure for storing C2K NSFT TX performance (max power) measurement result
 */
typedef struct
{
    /// UE power
    double m_dUEPower;
    /// spurious emission test result (emission mask)
    S_RCTLIB_C2K_NSFT_SPUR_RESULT_T spurResult;
    /// WQ test result
    S_RCTLIB_C2K_NSFT_WQ_RESULT_T wqResult;
} S_RCTLIB_C2K_NSFT_TX_PERF_MAX_POWER_RESULT_T;
/**
 * the structure for storing C2K NSFT TX performance (min power) measurement result
 */
typedef struct
{
    /// UE power
    double m_dUEPower;
} S_RCTLIB_C2K_NSFT_TX_PERF_MIN_POWER_RESULT_T;
/**
 * Descriptions: C2K NSFT PreSetting
 */
typedef struct
{
    /// measurement timeout value (ms)
    double timeout;
    /// cell power for UE NSFT sync
    double cellPower;
    /// FER test count;
    unsigned int fer_frame_count;
    /// F-Pilot channel power (dB)
    double pilotPower;
    /// F-SYNC channel power (dB)
    double syncPower;
    /// F-PCH power (dB)
    double pchPower;
    /// F-FCH power (dB)
    double fchPower;
} S_RCTLIB_C2K_NSFT_PRESETTINGS_T;
/**
 * C2K NSFT test case config
 */
typedef struct
{
    /// Band Class
    unsigned int bandclass;
    /// UARFCN
    unsigned int uarfcn;
    /// up-link cable loss
    double cableloss_ul;
    /// down-link cable loss
    double cableloss_dl;
    /// init cell power
    double cell_power;
} S_RCTLIB_C2K_NSFT_CONFIG_T;
/**
 * C2K NSFT APP PreSetting
 */
typedef struct
{
    /// timeout (second)
    double timeout;
    /// F-Pilot channel power (dB)
    double pilotPower;
    /// F-SYNC channel power (dB)
    double syncPower;
    /// F-PCH power (dB)
    double pchPower;
    /// F-FCH power (dB)
    double fchPower;
    /// power increase between each access probe
    int probeSize;
    /// power step count
    int probeSteps;
} S_RCTLIB_C2K_NSFT_APP_PRESETTING_T;
/**
 * C2K NSFT APP test case config
 */
typedef struct
{
    /// Band Class
    unsigned int bandclass;
    /// UARFCN
    unsigned int uarfcn;
    /// up-link cable loss
    double cableloss_ul;
    /// down-link cable loss
    double cableloss_dl;
} S_RCTLIB_C2K_NSFT_APP_CONFIG_T;
/**
 * Descriptions: C2K AFC presetting
 */
typedef struct
{
    /// Band Class for AFC calibration
    unsigned int bandclass;
    /// UARFCN for AFC calibration
    unsigned int uarfcn;
    /// tx power for AFC calibration
    double d_tx_power;
    int freq_offset_hz;
} S_RCTLIB_C2K_AFC_PRESETTINGS_T;
/**
 * Descriptions: C2K instrument's special settings
 */
typedef struct
{
    /// Traditional calibration support
    bool bTCSupport;
    /// Fast calibration support
    bool bFHCSupport;
    /// C2K NSFT TX and FER test reverse
    bool usNSFT_TxFer_Reverse;
    /// C2K Selected port for two port Rxd
    E_RCTLIB_PORT_TYPE eRxdPathLossPort;

    /// ========== FHC instrument parameters ==========
    /// FHC RX Max Step Count
    unsigned int uiFHC_RX_MaxStepCount;
    /// FHC TX Max Step Count
    unsigned int uiFHC_TX_MaxStepCount;
    /// FHC Multiple Band Support
    unsigned int uiFHC_MultiBand; // 0: single band, 1: multiple band support
    /// FHC RX to TX ready time (us)
    unsigned int uiFHC_RxToTxReadyTimeUS;
    /// FHC step duration (ms)
    unsigned int uiFHC_StepDurationMS;
    /// FHC retune time (ms)
    unsigned int uiFHC_RetuneTimeMS;
} S_RCTLIB_C2K_SPECIFIC_SETTINGS_T;

/* list mode */
typedef struct
{
    /// CS0011 parameters
    S_RCTLIB_C2K_NSFT_PRESETTINGS_T cs0011Param;
} S_RCTLIB_C2K_LIST_MODE_NSFT_PRESETTING_T;

typedef struct
{
#define C2K_LIST_MODE_NSFT_MAX_TX_STEP_TEST_NUM   50
    /// UARFCN
    unsigned int uarfcn[C2K_LIST_MODE_NSFT_MAX_TX_STEP_TEST_NUM];
    /// uplink cable loss
    double cableloss_ul[C2K_LIST_MODE_NSFT_MAX_TX_STEP_TEST_NUM];
    /// downlink cable loss
    double cableloss_dl[C2K_LIST_MODE_NSFT_MAX_TX_STEP_TEST_NUM];
    /// init cell power(unit: dBm)
    double cell_power;
    ///peak power (unit: dBm) , [0]: max peak power, [1]: min peak power
    double dPeakpower[2];
    ///Expected power (unit: dBm) , [0]: Expected max power, [1]: Expected min power
    double dExpectedpower[2];
    ///FER power level(unit: dBm)
    double dC2k_FER_power[C2K_LIST_MODE_NSFT_MAX_TX_STEP_TEST_NUM];

    unsigned char ucBand[C2K_LIST_MODE_NSFT_MAX_TX_STEP_TEST_NUM];
    unsigned char valid_freq; //valid count of freq.
    /* the time settings, unit = frame */
    unsigned short test_time[C2K_LIST_MODE_NSFT_MAX_TX_STEP_TEST_NUM]; //the total time of each freq. for freq. array
    unsigned int rssi_count;
    double rssi_level[10];
    unsigned int tx_level_count;
    double tx_level[10];
    unsigned int type;
} S_RCTLIB_C2K_LIST_MODE_NSFT_INIT_SETTING_T;

/**
 * The structure for storing NSFT EVM result of list moe
 */
typedef struct
{
    /// Rho
    double d_rho;
    /// Frequency Error
    double d_freq_error;
    /// Time Error
    double d_time_error;
    /// Carrier Feed Through
    double d_carrier_feed;
    /// Phase Error
    double d_phase_error;
    /// Magnitude Error
    double d_mag_error;
    /// EVM RMS (%)
    double dRMS_EVM;
    /// EVM Pk (%)
    double dPeak_EVM;
    /// IQ Offset (dB)
    //double dIq_Offset_dB;
    /// PCDE (max, dB)
    double dpcde;
} S_RCTLIB_C2K_LIST_MODE_NSFT_EVM_RESULT_T;

/**
 * the structure for storing ACP(Tx Spurious) result of list mode
 */
typedef struct
{
    /// pass flag (overall)
    bool bPass;
    /// lower adjacent emission
    double d_spur_lower_adj;
    /// upper adjacent emission
    double d_spur_upper_adj;
    /// lower alternate emission
    double d_spur_lower_alt;
    /// upper alternate emission
    double d_spur_upper_alt;
    /// Total Carrier Power(unit: dBm)
    double dCarrierPower;
    /// Occupied bandwidth (MHz)
    double dOBW;
} S_RCTLIB_C2K_LIST_MODE_NSFT_ACP_RESULT_T;

/**
 * the structure for storing TX list mode step result
 */
typedef struct
{
#define C2K_LIST_MODE_NSFT_MAX_TX_STEP_TEST_NUM   50
    double  dMaxPower[C2K_LIST_MODE_NSFT_MAX_TX_STEP_TEST_NUM];
    double  dMinPower[C2K_LIST_MODE_NSFT_MAX_TX_STEP_TEST_NUM];
    S_RCTLIB_C2K_LIST_MODE_NSFT_ACP_RESULT_T  sACPResult[C2K_LIST_MODE_NSFT_MAX_TX_STEP_TEST_NUM];
    S_RCTLIB_C2K_LIST_MODE_NSFT_EVM_RESULT_T  sEVMResult[C2K_LIST_MODE_NSFT_MAX_TX_STEP_TEST_NUM];
    double dTxPower[C2K_LIST_MODE_NSFT_MAX_TX_STEP_TEST_NUM][10];
    unsigned int tx_level_count;
} S_RCTLIB_C2K_LIST_MODE_NSFT_TEST_TX_STEP_RESULT_T;

typedef struct
{
    int nValid_Freq;
    S_RCTLIB_C2K_LIST_MODE_NSFT_TEST_TX_STEP_RESULT_T txListModeResult;
} S_RCTLIB_C2K_LIST_MODE_NSFT_RESULT_T;
/**********************************************
 * C2K structure (shared library defined END)
 **********************************************/
/**********************************************
 * C2K function (shared library defined START)
 **********************************************/
int __stdcall RCTLIB_C2K_ConfigCellPower(double power);
int __stdcall RCTLIB_C2K_ConfigDefaultSettings(void);
int __stdcall RCTLIB_C2K_AFC_PreSetting(const S_RCTLIB_C2K_AFC_PRESETTINGS_T *pSettings, unsigned int size);
int __stdcall RCTLIB_C2K_AFC_FetchResult(double *freqError);
// Main path AGC calibration RCT controls
int __stdcall RCTLIB_C2K_AGC_PreSetting(const S_RCTLIB_C2K_AGC_PRESETTINGS_T *pSettings);
int __stdcall RCTLIB_C2K_AGC_ChangeCellBand(unsigned int band);
int __stdcall RCTLIB_C2K_AGC_ChangeChannel(unsigned int uarfcn);
int __stdcall RCTLIB_C2K_AGC_ChangeCellPower(double cellPower);
// Diversity path AGC calibration RCT controls
int __stdcall RCTLIB_C2K_AGC_RXD_PreSetting(const S_RCTLIB_C2K_AGC_PRESETTINGS_T *pSettings);
int __stdcall RCTLIB_C2K_AGC_RXD_ChangeCellBand(unsigned int band);
int __stdcall RCTLIB_C2K_AGC_RXD_ChangeChannel(unsigned int uarfcn);
int __stdcall RCTLIB_C2K_AGC_RXD_ChangeCellPower(double cellPower);

int __stdcall RCTLIB_C2K_APC_PreSetting(const S_RCTLIB_C2K_APC_PRESETTINGS_T *pSettings);
int __stdcall RCTLIB_C2K_APC_PreSetting_Ex(const S_RCTLIB_C2K_APC_PRESETTINGS_EX_T *pSettings);
int __stdcall RCTLIB_C2K_APC_ChangeCellBand(unsigned int band);
int __stdcall RCTLIB_C2K_APC_ChangeChannel(unsigned int uarfcn);
int __stdcall RCTLIB_C2K_APC_ChangeExpectedPower(int expectedPower);
int __stdcall RCTLIB_C2K_APC_Initiate(void);
int __stdcall RCTLIB_C2K_APC_FetchResult(double *outputPower);
int __stdcall RCTLIB_C2K_FHC_PreSetting(const S_RCTLIB_C2K_FHC_PRESETTINGS_T *pSettings);
int __stdcall RCTLIB_C2K_FHC_StartIteration(const S_RCTLIB_C2K_FHC_MEASUREMENT_PARAM_T *pSettings);
int __stdcall RCTLIB_C2K_FHC_FetchResult(const S_RCTLIB_C2K_FHC_MEASUREMENT_PARAM_T *pSettings, S_RCTLIB_C2K_FHC_MEASUREMENT_RESULT_T *pResult);
int __stdcall RCTLIB_C2K_NSFT_PreSetting(const S_RCTLIB_C2K_NSFT_PRESETTINGS_T *pSettings);
int __stdcall RCTLIB_C2K_NSFT_InitiateTestCase(const S_RCTLIB_C2K_NSFT_CONFIG_T *pSettings);
int __stdcall RCTLIB_C2K_NSFT_ChangeCellPower(double d_cell_power);
int __stdcall RCTLIB_C2K_NSFT_TPC(const S_RCTLIB_C2K_NSFT_TPC_REQUEST_T *tpc_request);
int __stdcall RCTLIB_C2K_NSFT_FetchTXPerformanceMaxPower(S_RCTLIB_C2K_NSFT_TX_PERF_MAX_POWER_RESULT_T *tx_perf_result);
int __stdcall RCTLIB_C2K_NSFT_FetchTXPerformanceMinPower(S_RCTLIB_C2K_NSFT_TX_PERF_MIN_POWER_RESULT_T *tx_perf_result);
int __stdcall RCTLIB_C2K_NSFT_InitiateFER(void);
int __stdcall RCTLIB_C2K_NSFT_ReadFER(S_RCTLIB_C2K_NSFT_FER_RESULT_T *result);
int __stdcall RCTLIB_C2K_NSFT_APP_PreSetting(const S_RCTLIB_C2K_NSFT_APP_PRESETTING_T *pSettings);
int __stdcall RCTLIB_C2K_NSFT_APP_CasePreSetting(void);
int __stdcall RCTLIB_C2K_NSFT_APP_InitiateTestCase(const S_RCTLIB_C2K_NSFT_APP_CONFIG_T *pSettings);
int __stdcall RCTLIB_C2K_NSFT_APP_ChangeCellPower(double d_cell_power);
int __stdcall RCTLIB_C2K_NSFT_APP_FetchTestCase(S_RCTLIB_C2K_NSFT_APP_RESULT_T *result);
int __stdcall RCTLIB_C2K_EVDO_NSFT_PreSetting(const S_RCTLIB_C2K_NSFT_PRESETTINGS_T *pSettings);
int __stdcall RCTLIB_C2K_EVDO_NSFT_InitiateTestCase(const S_RCTLIB_C2K_NSFT_CONFIG_T *pSettings);
int __stdcall RCTLIB_C2K_EVDO_NSFT_TPC(const S_RCTLIB_C2K_NSFT_TPC_REQUEST_T *tpc_request);
int __stdcall RCTLIB_C2K_EVDO_NSFT_FetchTXPerformanceMaxPower(S_RCTLIB_C2K_NSFT_TX_PERF_MAX_POWER_RESULT_T *tx_perf_result);
int __stdcall RCTLIB_C2K_EVDO_NSFT_FetchTXPerformanceMinPower(S_RCTLIB_C2K_NSFT_TX_PERF_MIN_POWER_RESULT_T *tx_perf_result);
int __stdcall RCTLIB_C2K_EVDO_NSFT_InitiatePER(void);
int __stdcall RCTLIB_C2K_EVDO_NSFT_ReadPER(S_RCTLIB_C2K_NSFT_FER_RESULT_T *result);
int __stdcall RCTLIB_C2K_GET_SpecificSettings(S_RCTLIB_C2K_SPECIFIC_SETTINGS_T *pSettings);

// reentrant functions
int __stdcall RCTLIB_C2K_ConfigCellPower_r(const int meta_handle, double power);
int __stdcall RCTLIB_C2K_ConfigDefaultSettings_r(const int meta_handle);
int __stdcall RCTLIB_C2K_AFC_PreSetting_r(const int meta_handle, const S_RCTLIB_C2K_AFC_PRESETTINGS_T *pSettings, unsigned int size);
int __stdcall RCTLIB_C2K_AFC_FetchResult_r(const int meta_handle, double *freqError);
// Main path AGC calibration RCT controls
int __stdcall RCTLIB_C2K_AGC_PreSetting_r(const int meta_handle, const S_RCTLIB_C2K_AGC_PRESETTINGS_T *pSettings);
int __stdcall RCTLIB_C2K_AGC_ChangeCellBand_r(const int meta_handle, unsigned int band);
int __stdcall RCTLIB_C2K_AGC_ChangeChannel_r(const int meta_handle, unsigned int uarfcn);
int __stdcall RCTLIB_C2K_AGC_ChangeCellPower_r(const int meta_handle, double cellPower);
// Diversity path AGC calibration RCT controls
int __stdcall RCTLIB_C2K_AGC_RXD_PreSetting_r(const int meta_handle, const S_RCTLIB_C2K_AGC_PRESETTINGS_T *pSettings);
int __stdcall RCTLIB_C2K_AGC_RXD_ChangeCellBand_r(const int meta_handle, unsigned int band);
int __stdcall RCTLIB_C2K_AGC_RXD_ChangeChannel_r(const int meta_handle, unsigned int uarfcn);
int __stdcall RCTLIB_C2K_AGC_RXD_ChangeCellPower_r(const int meta_handle, double cellPower);

int __stdcall RCTLIB_C2K_APC_PreSetting_r(const int meta_handle, const S_RCTLIB_C2K_APC_PRESETTINGS_T *pSettings);
int __stdcall RCTLIB_C2K_APC_PreSetting_Ex_r(const int meta_handle, const S_RCTLIB_C2K_APC_PRESETTINGS_EX_T *pSettings);
int __stdcall RCTLIB_C2K_APC_ChangeCellBand_r(const int meta_handle, unsigned int band);
int __stdcall RCTLIB_C2K_APC_ChangeChannel_r(const int meta_handle, unsigned int uarfcn);
int __stdcall RCTLIB_C2K_APC_ChangeExpectedPower_r(const int meta_handle, int expectedPower);
int __stdcall RCTLIB_C2K_APC_Initiate_r(const int meta_handle);
int __stdcall RCTLIB_C2K_APC_FetchResult_r(const int meta_handle, double *outputPower);
int __stdcall RCTLIB_C2K_FHC_PreSetting_r(const int meta_handle, const S_RCTLIB_C2K_FHC_PRESETTINGS_T *pSettings);
int __stdcall RCTLIB_C2K_FHC_StartIteration_r(const int meta_handle, const S_RCTLIB_C2K_FHC_MEASUREMENT_PARAM_T *pSettings);
int __stdcall RCTLIB_C2K_FHC_FetchResult_r(const int meta_handle, const S_RCTLIB_C2K_FHC_MEASUREMENT_PARAM_T *pSettings, S_RCTLIB_C2K_FHC_MEASUREMENT_RESULT_T *pResult);
int __stdcall RCTLIB_C2K_NSFT_PreSetting_r(const int meta_handle, const S_RCTLIB_C2K_NSFT_PRESETTINGS_T *pSettings);
int __stdcall RCTLIB_C2K_NSFT_InitiateTestCase_r(const int meta_handle, const S_RCTLIB_C2K_NSFT_CONFIG_T *pSettings);
int __stdcall RCTLIB_C2K_NSFT_ChangeCellPower_r(const int meta_handle, double d_cell_power);
int __stdcall RCTLIB_C2K_NSFT_TPC_r(const int meta_handle, const S_RCTLIB_C2K_NSFT_TPC_REQUEST_T *tpc_request);
int __stdcall RCTLIB_C2K_NSFT_FetchTXPerformanceMaxPower_r(const int meta_handle, S_RCTLIB_C2K_NSFT_TX_PERF_MAX_POWER_RESULT_T *tx_perf_result);
int __stdcall RCTLIB_C2K_NSFT_FetchTXPerformanceMinPower_r(const int meta_handle, S_RCTLIB_C2K_NSFT_TX_PERF_MIN_POWER_RESULT_T *tx_perf_result);
int __stdcall RCTLIB_C2K_NSFT_InitiateFER_r(const int meta_handle);
int __stdcall RCTLIB_C2K_NSFT_ReadFER_r(const int meta_handle, S_RCTLIB_C2K_NSFT_FER_RESULT_T *result);
int __stdcall RCTLIB_C2K_NSFT_APP_PreSetting_r(const int meta_handle, const S_RCTLIB_C2K_NSFT_APP_PRESETTING_T *pSettings);
int __stdcall RCTLIB_C2K_NSFT_APP_CasePreSetting_r(const int meta_handle);
int __stdcall RCTLIB_C2K_NSFT_APP_InitiateTestCase_r(const int meta_handle, const S_RCTLIB_C2K_NSFT_APP_CONFIG_T *pSettings);
int __stdcall RCTLIB_C2K_NSFT_APP_ChangeCellPower_r(const int meta_handle, double d_cell_power);
int __stdcall RCTLIB_C2K_NSFT_APP_FetchTestCase_r(const int meta_handle, S_RCTLIB_C2K_NSFT_APP_RESULT_T *result);
int __stdcall RCTLIB_C2K_EVDO_NSFT_PreSetting_r(const int meta_handle, const S_RCTLIB_C2K_NSFT_PRESETTINGS_T *pSettings);
int __stdcall RCTLIB_C2K_EVDO_NSFT_InitiateTestCase_r(const int meta_handle, const S_RCTLIB_C2K_NSFT_CONFIG_T *pSettings);
int __stdcall RCTLIB_C2K_EVDO_NSFT_TPC_r(const int meta_handle, const S_RCTLIB_C2K_NSFT_TPC_REQUEST_T *tpc_request);
int __stdcall RCTLIB_C2K_EVDO_NSFT_FetchTXPerformanceMaxPower_r(const int meta_handle, S_RCTLIB_C2K_NSFT_TX_PERF_MAX_POWER_RESULT_T *tx_perf_result);
int __stdcall RCTLIB_C2K_EVDO_NSFT_FetchTXPerformanceMinPower_r(const int meta_handle, S_RCTLIB_C2K_NSFT_TX_PERF_MIN_POWER_RESULT_T *tx_perf_result);
int __stdcall RCTLIB_C2K_EVDO_NSFT_InitiatePER_r(const int meta_handle);
int __stdcall RCTLIB_C2K_EVDO_NSFT_ReadPER_r(const int meta_handle, S_RCTLIB_C2K_NSFT_FER_RESULT_T *result);
int __stdcall RCTLIB_C2K_GET_SpecificSettings_r(const int meta_handle, S_RCTLIB_C2K_SPECIFIC_SETTINGS_T *pSettings);
int __stdcall RCTLIB_C2K_LIST_MODE_NSFT_PreSetting_r(const int meta_handle, const S_RCTLIB_C2K_LIST_MODE_NSFT_PRESETTING_T *pSetting, const unsigned int length);
int __stdcall RCTLIB_C2K_LIST_MODE_NSFT_InitiateTestPlan_r(const int meta_handle, const S_RCTLIB_C2K_LIST_MODE_NSFT_INIT_SETTING_T *pSetting, const unsigned int length);
int __stdcall RCTLIB_C2K_LIST_MODE_NSFT_FetchTestPlanResult_r(const int meta_handle, S_RCTLIB_C2K_LIST_MODE_NSFT_RESULT_T *pResult, const unsigned int length);
int __stdcall RCTLIB_C2K_Switch_RF_Port_Ex(const int meta_handle, AntennaPortType eRfPortType, E_RCTLIB_RF_PORT_TYPE ePort);
int __stdcall RCTLIB_C2K_SetRxCalCW(const int meta_handle, int enable);
/**********************************************
 * C2K function (shared library defined END)
 **********************************************/
#endif //#ifdef __META_C2K__

/*****************************************************
 * exported function (END)
 *****************************************************/


/*****************************************************
 * Customized Instrument register interface
 *****************************************************/

//=====================================================
// Customzied common interface
//=====================================================
typedef int (*RCTLIB_InitializeConfiguration_CALLBACK)(const wchar_t *cfg_file_path);
typedef int (*RCTLIB_ConnectRCT_CALLBACK)(void);
typedef int (*RCTLIB_DisconnectRCT_CALLBACK)(void);
typedef int (*RCTLIB_QueryCurrentApplicationFormat_CALLBACK)(char *buf, int buf_len);
typedef int (*RCTLIB_ConfigApplicationFormat_CALLBACK)(unsigned int format);
typedef int (*RCTLIB_ConfigOperatingMode_CALLBACK)(unsigned int OperatingMode);
typedef int (*RCTLIB_Reset_CALLBACK)(void);


typedef struct
{
    RCTLIB_InitializeConfiguration_CALLBACK  InitializeConfiguration_CALLBACK;
    RCTLIB_ConnectRCT_CALLBACK  ConnectRCT_CALLBACK;
    RCTLIB_DisconnectRCT_CALLBACK  DisconnectRCT_CALLBACK;
    RCTLIB_QueryCurrentApplicationFormat_CALLBACK  QueryCurrentApplicationFormat_CALLBACK;
    RCTLIB_ConfigApplicationFormat_CALLBACK  ConfigApplicationFormat_CALLBACK;
    RCTLIB_ConfigOperatingMode_CALLBACK  ConfigOperatingMode_CALLBACK;
    RCTLIB_Reset_CALLBACK  Reset_CALLBACK;
    // please add in the last
} RCTLIB_COMMON_CALLBACKS_CFG_T;

//=====================================================
//           GGE Callback defines
//=====================================================

typedef int (*RCTLIB_GGE_InstrumentInit_CALLBACK)(void);
typedef int (*RCTLIB_GGE_Cableloss_Settings_CALLBACK)(S_RCTLIB_GGE_Cableloss_CONFIG_T cable_loss);
typedef int (*RCTLIB_GGE_ConfigCellPower_CALLBACK)(double power);
typedef int (*RCTLIB_GGE_ConfigDefaultSettings_CALLBACK)(void);
typedef int (*RCTLIB_GGE_ConfigAnalyzerFrequencyOffset_CALLBACK)(const S_RCTLIB_GGE_FREQUENCY_OFFSET_SETTINGS_T *pSettings, unsigned int sz);
typedef int (*RCTLIB_GGE_CAPID_PreSettings_CALLBACK)(const S_RCTLIB_GGE_CAPID_SETTINGS_T *pSettings);
typedef int (*RCTLIB_GGE_CAPID_Iteration_CALLBACK)(double *frequency_error);
typedef int (*RCTLIB_GGE_AFC_PreSettings_CALLBACK)(const S_RCTLIB_GGE_AFC_SETTINGS_T *pSettings);
typedef int (*RCTLIB_GGE_AGC_PreSettings_CALLBACK)(double d_power);
typedef int (*RCTLIB_GGE_AGC_ChangeCellBand_CALLBACK)(unsigned int band);
typedef int (*RCTLIB_GGE_AGC_ChangeChannel_CALLBACK)(unsigned int arfcn);
typedef int (*RCTLIB_GGE_APCDCOffset_PreSettings_CALLBACK)(unsigned int tsc);
typedef int (*RCTLIB_GGE_APCDCOffset_ChangeCellBand_CALLBACK)(unsigned int band);
typedef int (*RCTLIB_GGE_APCDCOffset_Iteration_CALLBACK)(unsigned int arfcn, double expected_power, int PCL, double *d_power);
typedef int (*RCTLIB_GGE_PedestalDCOffset_Iteration_CALLBACK)(unsigned int arfcn, double expected_power, double *d_power);
typedef int (*RCTLIB_GGE_EDGE_APCDCOffset_PreSettings_CALLBACK)(unsigned int tsc);
typedef int (*RCTLIB_GGE_EDGE_APCDCOffset_ChangeCellBand_CALLBACK)(unsigned int band);
typedef int (*RCTLIB_GGE_EDGE_APCDCOffset_Iteration_CALLBACK)(unsigned int arfcn, double expected_power, int PCL, double *d_power);
typedef int (*RCTLIB_GGE_EDGE_APC_SetExceptedPower_CALLBACK)(double expected_power);
typedef int (*RCTLIB_GGE_EDGE_APC_Iteration_CALLBACK)(unsigned int arfcn, double *d_power);

typedef int (*RCTLIB_GGE_FHC_DTS_PreSettings_CALLBACK)(void);
typedef int (*RCTLIB_GGE_FHC_DTS_Iteration_CALLBACK)(S_RCTLIB_FHC_DL_List_T List, int ListLength);
typedef int (*RCTLIB_GGE_FHC_DTS_Iteration_512p_CALLBACK)(S_RCTLIB_FHC_DL_List_512p_T List, int ListLength);
typedef int (*RCTLIB_GGE_FHC_DTS_START_CALLBACK)(void);
typedef int (*RCTLIB_GGE_FHC_DTS_STOP_CALLBACK)(void);

typedef int (*RCTLIB_GGE_FHC_UTS_PreSettings_CALLBACK)(void);
typedef int (*RCTLIB_GGE_FHC_UTS_Iteration_CALLBACK)(S_RCTLIB_GGE_FHC_TX_UTS_T *uts);
typedef int (*RCTLIB_GGE_FHC_UTS_FetchResult_CALLBACK)(const S_RCTLIB_GGE_FHC_TX_UTS_T *uts, S_RCTLIB_GGE_FHC_TX_UTS_RESULT_T *uts_result);
typedef int (*RCTLIB_GGE_FHC_UTS_Iteration_Ex_CALLBACK)(S_RCTLIB_GGE_FHC_TX_UTS_BIG_T *uts);
typedef int (*RCTLIB_GGE_FHC_UTS_FetchResult_Ex_CALLBACK)(const S_RCTLIB_GGE_FHC_TX_UTS_BIG_T *uts, S_RCTLIB_GGE_FHC_TX_UTS_BIG_RESULT_T *uts_result);
typedef int (*RCTLIB_GGE_FHC_UTS_Iteration_Ex_512p_CALLBACK)(S_RCTLIB_GGE_FHC_TX_UTS_BIG_512P_T *uts);
typedef int (*RCTLIB_GGE_FHC_UTS_FetchResult_Ex_512p_CALLBACK)(const S_RCTLIB_GGE_FHC_TX_UTS_BIG_512P_T *uts, S_RCTLIB_GGE_FHC_TX_UTS_BIG_512P_RESULT_T *uts_result);

typedef int (*RCTLIB_GGE_FBDAC_PreSettings_CALLBACK)(const S_RCTLIB_GGE_FBDAC_SETTINGS_T *pSettings);
typedef int (*RCTLIB_GGE_FBDAC_Iteration_CALLBACK)(double *d_power);
typedef int (*RCTLIB_GGE_TXIQ_PreSettings_CALLBACK)(const S_RCTLIB_GGE_TXIQ_SETTINGS_T *pSettings);
typedef int (*RCTLIB_GGE_TXIQ_ChangeBand_CALLBACK)(const S_RCTLIB_GGE_TXIQ_SETTINGS_T *pSettings);
typedef int (*RCTLIB_GGE_TXIQ_Iteration_CALLBACK)(const S_RCTLIB_GGE_TXIQ_SETTINGS_T *pSettings, S_RCTLIB_GGE_TXIQ_RESULT_T *pTxIqResult);
typedef int (*RCTLIB_GGE_TXSlopeSkew_PreSettings_CALLBACK)(const S_RCTLIB_GGE_TXSLOPESKEW_SETTINGS_T *pSettings);
typedef int (*RCTLIB_GGE_TXSlopeSkew_Iteration_CALLBACK)(double *d_mod_depth);
typedef int (*RCTLIB_GGE_TRXOffset_PreSettings_CALLBACK)(const S_RCTLIB_GGE_TRXOFFSET_SETTINGS_T *pSettings);
typedef int (*RCTLIB_GGE_TRXOffset_InitAFC_CALLBACK)(const S_RCTLIB_GGE_TRXOFFSET_SETTINGS_T *pSettings);
typedef int (*RCTLIB_GGE_TRXOffset_Iteration_CALLBACK)(double *frequency_err);
typedef int (*RCTLIB_GGE_NSFT_PreSettings_CALLBACK)(unsigned int measurement_count, unsigned int ber_count);
typedef int (*RCTLIB_GGE_NSFT_GMSKInit_CALLBACK)(const S_RCTLIB_GGE_NSFT_TESTCONFIG_T *nsft_config);
typedef int (*RCTLIB_GGE_NSFT_EPSKInit_CALLBACK)(const S_RCTLIB_GGE_NSFT_TESTCONFIG_T *nsft_config);
typedef int (*RCTLIB_GGE_NSFT_BERInit_CALLBACK)(const S_RCTLIB_GGE_NSFT_TESTCONFIG_T *nsft_config);
typedef int (*RCTLIB_GGE_NSFT_ChangePCL_CALLBACK)(int b_EPSK, unsigned int pcl);
typedef int (*RCTLIB_GGE_NSFT_ReadGMSKPerformance_CALLBACK)(S_RCTLIB_GGE_NSFT_GMSK_RESULT_T *gmsk_result);
typedef int (*RCTLIB_GGE_NSFT_InitiateBER_CALLBACK)(void);
typedef int (*RCTLIB_GGE_NSFT_FetchBER_CALLBACK)(S_RCTLIB_GGE_NSFT_BER_RESULT_T *ber_result);
typedef int (*RCTLIB_GGE_NSFT_ReadEPSKPerformance_CALLBACK)(S_RCTLIB_GGE_NSFT_EPSK_RESULT_T *epsk_result);
typedef int (*RCTLIB_GGE_GET_SpecificSettings_CALLBACK)(S_RCTLIB_GGE_SPECIFIC_SETTINGS_T *pSettings);
typedef int (*RCTLIB_GGE_Sweep_PreSettings_CALLBACK)(const S_RCTLIB_GGE_CRYSTAL_AFC_SWEEP_SETTINGS_T *pSettings);
typedef int (*RCTLIB_GGE_Crystal_AFC_Sweep_Initiate_CALLBACK)(const S_RCTLIB_GGE_CAPID_SETTINGS_T *pSettings);
typedef int (*RCTLIB_GGE_Crystal_AFC_Sweep_Fetch_CALLBACK)(S_RCTLIB_GGE_CRYSTAL_AFC_SWEEP_RESULTS_T *pResults);
typedef int (*RCTLIB_GGE_NSFT_ChangePow_CALLBACK)(int b_EPSK, double pow);

typedef struct
{
    RCTLIB_GGE_InstrumentInit_CALLBACK  InstrumentInit_CALLBACK;
    RCTLIB_GGE_Cableloss_Settings_CALLBACK  Cableloss_Settings_CALLBACK;
    RCTLIB_GGE_ConfigCellPower_CALLBACK  ConfigCellPower_CALLBACK;
    RCTLIB_GGE_ConfigDefaultSettings_CALLBACK  ConfigDefaultSettings_CALLBACK;
    RCTLIB_GGE_ConfigAnalyzerFrequencyOffset_CALLBACK  ConfigAnalyzerFrequencyOffset_CALLBACK;
    RCTLIB_GGE_CAPID_PreSettings_CALLBACK  CAPID_PreSettings_CALLBACK;
    RCTLIB_GGE_CAPID_Iteration_CALLBACK  CAPID_Iteration_CALLBACK;
    RCTLIB_GGE_AFC_PreSettings_CALLBACK  AFC_PreSettings_CALLBACK;
    RCTLIB_GGE_AGC_PreSettings_CALLBACK  AGC_PreSettings_CALLBACK;
    RCTLIB_GGE_AGC_ChangeCellBand_CALLBACK  AGC_ChangeCellBand_CALLBACK;
    RCTLIB_GGE_AGC_ChangeChannel_CALLBACK  AGC_ChangeChannel_CALLBACK;
    RCTLIB_GGE_APCDCOffset_PreSettings_CALLBACK  APCDCOffset_PreSettings_CALLBACK;
    RCTLIB_GGE_APCDCOffset_ChangeCellBand_CALLBACK  APCDCOffset_ChangeCellBand_CALLBACK;
    RCTLIB_GGE_APCDCOffset_Iteration_CALLBACK  APCDCOffset_Iteration_CALLBACK;
    RCTLIB_GGE_PedestalDCOffset_Iteration_CALLBACK  Pedestal_DCOffset_Iteration_CALLBACK;
    RCTLIB_GGE_EDGE_APCDCOffset_PreSettings_CALLBACK  EDGE_APCDCOffset_PreSettings_CALLBACK;
    RCTLIB_GGE_EDGE_APCDCOffset_ChangeCellBand_CALLBACK  EDGE_APCDCOffset_ChangeCellBand_CALLBACK;
    RCTLIB_GGE_EDGE_APCDCOffset_Iteration_CALLBACK  EDGE_APCDCOffset_Iteration_CALLBACK;
    RCTLIB_GGE_FHC_DTS_PreSettings_CALLBACK  FHC_DTS_PreSettings_CALLBACK;
    RCTLIB_GGE_FHC_DTS_Iteration_CALLBACK  FHC_DTS_Iteration_CALLBACK;
    RCTLIB_GGE_FHC_DTS_START_CALLBACK  FHC_DTS_START_CALLBACK;
    RCTLIB_GGE_FHC_DTS_STOP_CALLBACK  FHC_DTS_STOP_CALLBACK;
    RCTLIB_GGE_FHC_UTS_PreSettings_CALLBACK  FHC_UTS_PreSettings_CALLBACK;
    RCTLIB_GGE_FHC_UTS_Iteration_CALLBACK  FHC_UTS_Iteration_CALLBACK;
    RCTLIB_GGE_FHC_UTS_FetchResult_CALLBACK  FHC_UTS_FetchResult_CALLBACK;
    RCTLIB_GGE_FBDAC_PreSettings_CALLBACK  FBDAC_PreSettings_CALLBACK;
    RCTLIB_GGE_FBDAC_Iteration_CALLBACK  FBDAC_Iteration_CALLBACK;
    RCTLIB_GGE_TXIQ_PreSettings_CALLBACK  TXIQ_PreSettings_CALLBACK;
    RCTLIB_GGE_TXIQ_ChangeBand_CALLBACK  TXIQ_ChangeBand_CALLBACK;
    RCTLIB_GGE_TXIQ_Iteration_CALLBACK  TXIQ_Iteration_CALLBACK;
    RCTLIB_GGE_TXSlopeSkew_PreSettings_CALLBACK  TXSlopeSkew_PreSettings_CALLBACK;
    RCTLIB_GGE_TXSlopeSkew_Iteration_CALLBACK  TXSlopeSkew_Iteration_CALLBACK;
    RCTLIB_GGE_TRXOffset_PreSettings_CALLBACK  TRXOffset_PreSettings_CALLBACK;
    RCTLIB_GGE_TRXOffset_InitAFC_CALLBACK  TRXOffset_InitAFC_CALLBACK;
    RCTLIB_GGE_TRXOffset_Iteration_CALLBACK  TRXOffset_Iteration_CALLBACK;
    RCTLIB_GGE_NSFT_PreSettings_CALLBACK  NSFT_PreSettings_CALLBACK;
    RCTLIB_GGE_NSFT_GMSKInit_CALLBACK  NSFT_GMSKInit_CALLBACK;
    RCTLIB_GGE_NSFT_EPSKInit_CALLBACK  NSFT_EPSKInit_CALLBACK;
    RCTLIB_GGE_NSFT_BERInit_CALLBACK  NSFT_BERInit_CALLBACK;
    RCTLIB_GGE_NSFT_ChangePCL_CALLBACK  NSFT_ChangePCL_CALLBACK;
    RCTLIB_GGE_NSFT_ReadGMSKPerformance_CALLBACK  NSFT_ReadGMSKPerformance_CALLBACK;
    RCTLIB_GGE_NSFT_InitiateBER_CALLBACK  NSFT_InitiateBER_CALLBACK;
    RCTLIB_GGE_NSFT_FetchBER_CALLBACK  NSFT_FetchBER_CALLBACK;
    RCTLIB_GGE_NSFT_ReadEPSKPerformance_CALLBACK  NSFT_ReadEPSKPerformance_CALLBACK;
    RCTLIB_GGE_GET_SpecificSettings_CALLBACK  GET_SpecificSettings_CALLBACK;
    RCTLIB_GGE_FHC_UTS_Iteration_Ex_CALLBACK  FHC_UTS_Iteration_Ex_CALLBACK;
    RCTLIB_GGE_FHC_UTS_FetchResult_Ex_CALLBACK  FHC_UTS_FetchResult_Ex_CALLBACK;
    RCTLIB_GGE_FHC_UTS_Iteration_Ex_512p_CALLBACK  FHC_UTS_Iteration_Ex_512p_CALLBACK;
    RCTLIB_GGE_FHC_UTS_FetchResult_Ex_512p_CALLBACK  FHC_UTS_FetchResult_Ex_512p_CALLBACK;
    RCTLIB_GGE_EDGE_APC_SetExceptedPower_CALLBACK  EDGE_APC_SetExceptedPower_CALLBACK;
    RCTLIB_GGE_EDGE_APC_Iteration_CALLBACK  EDGE_APC_Iteration_CALLBACK;
    RCTLIB_GGE_FHC_DTS_Iteration_512p_CALLBACK  FHC_DTS_Iteration_512p_CALLBACK;
    RCTLIB_GGE_Sweep_PreSettings_CALLBACK   Sweep_PreSettings_CALLBACK ;
    RCTLIB_GGE_Crystal_AFC_Sweep_Initiate_CALLBACK  Crystal_AFC_Sweep_Initiate_CALLBACK;
    RCTLIB_GGE_Crystal_AFC_Sweep_Fetch_CALLBACK Crystal_AFC_Sweep_Fetch_CALLBACK;
    RCTLIB_GGE_NSFT_ChangePow_CALLBACK NSFT_ChangePow_CALLBACK;
    // please add in the last
} RCTLIB_GGE_CALLBACKS_CFG_T;
//=====================================================
//           WCDMA Callback defines
//=====================================================
typedef int (*RCTLIB_WCDMA_InstrumentInit_CALLBACK)(void);
typedef int (*RCTLIB_WCDMA_ConfigCellPower_CALLBACK)(double power);
typedef int (*RCTLIB_WCDMA_ConfigDefaultSettings_CALLBACK)(void);
typedef int (*RCTLIB_WCDMA_AFC_PreSetting_CALLBACK)(const S_RCTLIB_WCDMA_AFC_PRESETTINGS_T *pSettings, unsigned int size);
typedef int (*RCTLIB_WCDMA_AGC_PreSetting_CALLBACK)(const S_RCTLIB_WCDMA_AGC_PRESETTINGS_T *pSettings);
typedef int (*RCTLIB_WCDMA_AGC_ChangeCellBand_CALLBACK)(unsigned int band);
typedef int (*RCTLIB_WCDMA_AGC_ChangeChannel_CALLBACK)(unsigned int uarfcn);
typedef int (*RCTLIB_WCDMA_AGC_ChangeCellPower_CALLBACK)(double cellPower);
typedef int (*RCTLIB_WCDMA_APC_PreSetting_CALLBACK)(const S_RCTLIB_WCDMA_APC_PRESETTINGS_T *pSettings);
typedef int (*RCTLIB_WCDMA_APC_PreSetting_Ex_CALLBACK)(const S_RCTLIB_WCDMA_APC_PRESETTINGS_EX_T *pSettings);
typedef int (*RCTLIB_WCDMA_APC_ChangeCellBand_CALLBACK)(unsigned int band);
typedef int (*RCTLIB_WCDMA_APC_ChangeChannel_CALLBACK)(unsigned int uarfcn);
typedef int (*RCTLIB_WCDMA_APC_ChangeExpectedPower_CALLBACK)(int expectedPower);
typedef int (*RCTLIB_WCDMA_APC_Initiate_CALLBACK)(void);
typedef int (*RCTLIB_WCDMA_APC_FetchResult_CALLBACK)(double *outputPower);
typedef int (*RCTLIB_WCDMA_FHC_PreSetting_CALLBACK)(const S_RCTLIB_WCDMA_FHC_PRESETTINGS_T *pSettings);
typedef int (*RCTLIB_WCDMA_FHC_StartIteration_CALLBACK)(const S_RCTLIB_WCDMA_FHC_MEASUREMENT_PARAM_T *pSettings);
typedef int (*RCTLIB_WCDMA_FHC_FetchResult_CALLBACK)(const S_RCTLIB_WCDMA_FHC_MEASUREMENT_PARAM_T *pSettings, S_RCTLIB_WCDMA_FHC_MEASUREMENT_RESULT_T *pResult);
typedef int (*RCTLIB_WCDMA_NSFT_PreSetting_CALLBACK)(const S_RCTLIB_WCDMA_NSFT_PRESETTINGS_T *pSettings);
typedef int (*RCTLIB_WCDMA_NSFT_InitiateTestCase_CALLBACK)(const S_RCTLIB_WCDMA_NSFT_CONFIG_T *pSettings);
typedef int (*RCTLIB_WCDMA_NSFT_TPC_CALLBACK)(const S_RCTLIB_WCDMA_NSFT_TPC_REQUEST_T *tpc_request);
typedef int (*RCTLIB_WCDMA_NSFT_FetchTXPerformanceMaxPower_CALLBACK)(S_RCTLIB_WCDMA_NSFT_TX_PERF_MAX_POWER_RESULT_T *tx_perf_result);
typedef int (*RCTLIB_WCDMA_NSFT_FetchTXPerformanceMinPower_CALLBACK)(S_RCTLIB_WCDMA_NSFT_TX_PERF_MIN_POWER_RESULT_T *tx_perf_result);
typedef int (*RCTLIB_WCDMA_NSFT_InitiateILPCTestCase_CALLBACK)(unsigned char testSegment);
typedef int (*RCTLIB_WCDMA_NSFT_FetchILPCResult_CALLBACK)(S_RCTLIB_WCDMA_NSFT_ILPC_CONFIG_T *ilpcConfig, S_RCTLIB_WCDMA_NSFT_ILPC_RESULT_T *result);
typedef int (*RCTLIB_WCDMA_NSFT_ChangeCellPower_CALLBACK)(double d_cell_power);
typedef int (*RCTLIB_WCDMA_NSFT_InitiateBER_CALLBACK)(void);
typedef int (*RCTLIB_WCDMA_NSFT_ReadBER_CALLBACK)(S_RCTLIB_WCDMA_NSFT_BER_RESULT_T *result);
typedef int (*RCTLIB_WCDMA_NSFT_PRACH_PreSetting_CALLBACK)(const S_RCTLIB_WCDMA_NSFT_PRACH_PRESETTING_T *pSettings);
typedef int (*RCTLIB_WCDMA_NSFT_PRACH_CasePreSetting_CALLBACK)(void);
typedef int (*RCTLIB_WCDMA_NSFT_PRACH_InitiateTestCase_CALLBACK)(const S_RCTLIB_WCDMA_NSFT_PRACH_CONFIG_T *pSettings);
typedef int (*RCTLIB_WCDMA_NSFT_PRACH_FetchTestCase_CALLBACK)(S_RCTLIB_WCDMA_NSFT_PRACH_RESULT_T *result);
typedef int (*RCTLIB_WCDMA_HSDPA_NSFT_PreSetting_CALLBACK)(const S_RCTLIB_WCDMA_HSPA_NSFT_PRESETTINGS_T *pSettings);
typedef int (*RCTLIB_WCDMA_HSDPA_NSFT_InitiateTestCase_CALLBACK)(const S_RCTLIB_WCDMA_HSPA_NSFT_CONFIG_T *pConfig);
typedef int (*RCTLIB_WCDMA_HSDPA_NSFT_FetchResult_CALLBACK)(S_RCTLIB_WCDMA_HSPA_NSFT_RESULT_T *pResult);
typedef int (*RCTLIB_WCDMA_HSUPA_NSFT_PreSetting_CALLBACK)(const S_RCTLIB_WCDMA_HSPA_NSFT_PRESETTINGS_T *pSettings);
typedef int (*RCTLIB_WCDMA_HSUPA_NSFT_InitiateTestCase_CALLBACK)(const S_RCTLIB_WCDMA_HSPA_NSFT_CONFIG_T *pConfig);
typedef int (*RCTLIB_WCDMA_HSUPA_NSFT_FetchResult_CALLBACK)(S_RCTLIB_WCDMA_HSPA_NSFT_RESULT_T *pResult);
typedef int (*RCTLIB_WCDMA_GET_SpecificSettings_CALLBACK)(S_RCTLIB_WCDMA_SPECIFIC_SETTINGS_T *pSettings);

typedef struct
{
    RCTLIB_WCDMA_InstrumentInit_CALLBACK  InstrumentInit_CALLBACK;
    RCTLIB_WCDMA_ConfigCellPower_CALLBACK  ConfigCellPower_CALLBACK;
    RCTLIB_WCDMA_ConfigDefaultSettings_CALLBACK  ConfigDefaultSettings_CALLBACK;
    RCTLIB_WCDMA_AFC_PreSetting_CALLBACK  AFC_PreSetting_CALLBACK;
    RCTLIB_WCDMA_AGC_PreSetting_CALLBACK  AGC_PreSetting_CALLBACK;
    RCTLIB_WCDMA_AGC_ChangeCellBand_CALLBACK  AGC_ChangeCellBand_CALLBACK;
    RCTLIB_WCDMA_AGC_ChangeChannel_CALLBACK  AGC_ChangeChannel_CALLBACK;
    RCTLIB_WCDMA_AGC_ChangeCellPower_CALLBACK  AGC_ChangeCellPower_CALLBACK;
    RCTLIB_WCDMA_AGC_PreSetting_CALLBACK  AGC_RXD_PreSetting_CALLBACK;
    RCTLIB_WCDMA_AGC_ChangeCellBand_CALLBACK  AGC_RXD_ChangeCellBand_CALLBACK;
    RCTLIB_WCDMA_AGC_ChangeChannel_CALLBACK  AGC_RXD_ChangeChannel_CALLBACK;
    RCTLIB_WCDMA_AGC_ChangeCellPower_CALLBACK  AGC_RXD_ChangeCellPower_CALLBACK;
    RCTLIB_WCDMA_APC_PreSetting_CALLBACK  APC_PreSetting_CALLBACK;
    RCTLIB_WCDMA_APC_ChangeCellBand_CALLBACK  APC_ChangeCellBand_CALLBACK;
    RCTLIB_WCDMA_APC_ChangeChannel_CALLBACK  APC_ChangeChannel_CALLBACK;
    RCTLIB_WCDMA_APC_ChangeExpectedPower_CALLBACK  APC_ChangeExpectedPower_CALLBACK;
    RCTLIB_WCDMA_APC_Initiate_CALLBACK  APC_Initiate_CALLBACK;
    RCTLIB_WCDMA_APC_FetchResult_CALLBACK  APC_FetchResult_CALLBACK;
    RCTLIB_WCDMA_FHC_PreSetting_CALLBACK  FHC_PreSetting_CALLBACK;
    RCTLIB_WCDMA_FHC_StartIteration_CALLBACK  FHC_StartIteration_CALLBACK;
    RCTLIB_WCDMA_FHC_FetchResult_CALLBACK  FHC_FetchResult_CALLBACK;
    RCTLIB_WCDMA_NSFT_PreSetting_CALLBACK  NSFT_PreSetting_CALLBACK;
    RCTLIB_WCDMA_NSFT_InitiateTestCase_CALLBACK  NSFT_InitiateTestCase_CALLBACK;
    RCTLIB_WCDMA_NSFT_TPC_CALLBACK  NSFT_TPC_CALLBACK;
    RCTLIB_WCDMA_NSFT_FetchTXPerformanceMaxPower_CALLBACK  NSFT_FetchTXPerformanceMaxPower_CALLBACK;
    RCTLIB_WCDMA_NSFT_FetchTXPerformanceMinPower_CALLBACK  NSFT_FetchTXPerformanceMinPower_CALLBACK;
    RCTLIB_WCDMA_NSFT_InitiateILPCTestCase_CALLBACK  NSFT_InitiateILPCTestCase_CALLBACK;
    RCTLIB_WCDMA_NSFT_FetchILPCResult_CALLBACK  NSFT_FetchILPCResult_CALLBACK;
    RCTLIB_WCDMA_NSFT_ChangeCellPower_CALLBACK  NSFT_ChangeCellPower_CALLBACK;
    RCTLIB_WCDMA_NSFT_InitiateBER_CALLBACK  NSFT_InitiateBER_CALLBACK;
    RCTLIB_WCDMA_NSFT_ReadBER_CALLBACK  NSFT_ReadBER_CALLBACK;
    RCTLIB_WCDMA_NSFT_PRACH_PreSetting_CALLBACK  NSFT_PRACH_PreSetting_CALLBACK;
    RCTLIB_WCDMA_NSFT_PRACH_CasePreSetting_CALLBACK  NSFT_PRACH_CasePreSetting_CALLBACK;
    RCTLIB_WCDMA_NSFT_PRACH_InitiateTestCase_CALLBACK  NSFT_PRACH_InitiateTestCase_CALLBACK;
    RCTLIB_WCDMA_NSFT_PRACH_FetchTestCase_CALLBACK  NSFT_PRACH_FetchTestCase_CALLBACK;
    RCTLIB_WCDMA_HSDPA_NSFT_PreSetting_CALLBACK  HSDPA_NSFT_PreSetting_CALLBACK;
    RCTLIB_WCDMA_HSDPA_NSFT_InitiateTestCase_CALLBACK  HSDPA_NSFT_InitiateTestCase_CALLBACK;
    RCTLIB_WCDMA_HSDPA_NSFT_FetchResult_CALLBACK  HSDPA_NSFT_FetchResult_CALLBACK;
    RCTLIB_WCDMA_HSUPA_NSFT_PreSetting_CALLBACK  HSUPA_NSFT_PreSetting_CALLBACK;
    RCTLIB_WCDMA_HSUPA_NSFT_InitiateTestCase_CALLBACK  HSUPA_NSFT_InitiateTestCase_CALLBACK;
    RCTLIB_WCDMA_HSUPA_NSFT_FetchResult_CALLBACK  HSUPA_NSFT_FetchResult_CALLBACK;
    RCTLIB_WCDMA_GET_SpecificSettings_CALLBACK  GET_SpecificSettings_CALLBACK;
    RCTLIB_WCDMA_NSFT_ChangeCellPower_CALLBACK  NSFT_ChangePrachCellPower_CALLBACK;
    RCTLIB_WCDMA_APC_PreSetting_Ex_CALLBACK  APC_PreSetting_Ex_CALLBACK;
    // please add in the last
} RCTLIB_WCDMA_CALLBACKS_CFG_T;
//=====================================================
//           TDSCDMA Callback defines
//=====================================================
typedef int (*RCTLIB_TDSCDMA_InstrumentInit_CALLBACK)(void);
typedef int (*RCTLIB_TDSCDMA_ConfigTesterCommonBeforeCal_CALLBACK)(void);
typedef int (*RCTLIB_TDSCDMA_ConfigTesterBeforeAFC_CALLBACK)(const S_RCTLIB_TDSCDMA_AFC_TESTER_CONFIG_BEFORE_CAL_T *p_rConfig);
typedef int (*RCTLIB_TDSCDMA_MeasureAFC_CALLBACK)(double *p_dFreqOffset);
typedef int (*RCTLIB_TDSCDMA_ConfigTesterBeforeAGC_CALLBACK)(const S_RCTLIB_TDSCDMA_AGC_TESTER_CONFIG_BEFORE_CAL_T *p_rConfig);
typedef int (*RCTLIB_TDSCDMA_AGC_ChangeCellPower_CALLBACK)(double cell_power);
typedef int (*RCTLIB_TDSCDMA_AGC_ChangeCellBand_CALLBACK)(unsigned int band);
typedef int (*RCTLIB_TDSCDMA_AGC_ChangeChannel_CALLBACK)(unsigned int uarfcn);
typedef int (*RCTLIB_TDSCDMA_ConfigTesterBeforeAPC_CALLBACK)(const S_RCTLIB_TDSCDMA_APC_TESTER_CONFIG_BEFORE_CAL_T *p_rConfig);
typedef int (*RCTLIB_TDSCDMA_APC_ChangeCellBand_CALLBACK)(unsigned int band);
typedef int (*RCTLIB_TDSCDMA_APC_MeasurePower_CALLBACK)(S_RCTLIB_TDSCDMA_APC_MEASUREMENT_PARAM_T *param);
typedef int (*RCTLIB_TDSCDMA_ConfigTesterBeforeFHC_CALLBACK)(const S_RCTLIB_TDSCDMA_FHC_TESTER_CONFIG_BEFORE_CAL_T *p_rConfig);
typedef int (*RCTLIB_TDSCDMA_FHC_StartIteration_CALLBACK)(const S_RCTLIB_TDSCDMA_FHC_MEASUREMENT_PARAM_T *pSettings);
typedef int (*RCTLIB_TDSCDMA_FHC_FetchResult_CALLBACK)(const S_RCTLIB_TDSCDMA_FHC_MEASUREMENT_PARAM_T *pSettings, S_RCTLIB_TDSCDMA_FHC_MEASUREMENT_RESULT_T *pResult);
typedef int (*RCTLIB_TDSCDMA_ConfigTesterCommonBeforeNSFT_CALLBACK)(S_RCTLIB_TD_NSFT_TESTER_COMMON_CONFIG_T *common_cfg);
typedef int (*RCTLIB_TDSCDMA_ConfigTesterForNSFT_CALLBACK)(const S_RCTLIB_TD_NSFT_TESTER_CONFIG_T *cfg);
typedef int (*RCTLIB_TDSCDMA_MeasureTPCForNSFT_CALLBACK)(const S_RCTLIB_TD_NSFT_TPC_CONFIG_ENTRY_T *req, S_RCTLIB_TDA_NSFT_MEASURE_RESULT_ENTRY_T *res);
typedef int (*RCTLIB_TDSCDMA_ConfigTesterForNSFTBer_CALLBACK)(const S_RCTLIB_TD_NSFT_BER_CONFIG_ENTRY_T *cfg);
typedef int (*RCTLIB_TDSCDMA_MeasureLBerForNSFT_CALLBACK)(S_RCTLIB_TDA_NSFT_MEASURE_RESULT_ENTRY_T *res);
typedef int (*RCTLIB_TDSCDMA_ConfigTesterCommonBeforeFT_CALLBACK)(S_RCTLIB_TD_NSFT_TESTER_COMMON_CONFIG_T *common_cfg);
typedef int (*RCTLIB_TDSCDMA_ConfigTesterForFT_CALLBACK)(const S_RCTLIB_TD_NSFT_TESTER_CONFIG_T *cfg);
typedef int (*RCTLIB_TDSCDMA_MeasureTPCForFT_CALLBACK)(const S_RCTLIB_TD_NSFT_TPC_CONFIG_ENTRY_T *req, S_RCTLIB_TDA_NSFT_MEASURE_RESULT_ENTRY_T *res);
typedef int (*RCTLIB_TDSCDMA_ConfigTesterForFTBer_CALLBACK)(const S_RCTLIB_TD_NSFT_BER_CONFIG_ENTRY_T *cfg);
typedef int (*RCTLIB_TDSCDMA_MeasureLBerForFT_CALLBACK)(S_RCTLIB_TDA_NSFT_MEASURE_RESULT_ENTRY_T *res);
typedef int (*RCTLIB_TDSCDMA_GET_SpecificSettings_CALLBACK)(S_RCTLIB_TDSCDMA_SPECIFIC_SETTINGS_T *pSettings);

typedef struct
{
    RCTLIB_TDSCDMA_InstrumentInit_CALLBACK  InstrumentInit_CALLBACK;
    RCTLIB_TDSCDMA_ConfigTesterCommonBeforeCal_CALLBACK  ConfigTesterCommonBeforeCal_CALLBACK;
    RCTLIB_TDSCDMA_ConfigTesterBeforeAFC_CALLBACK  ConfigTesterBeforeAFC_CALLBACK;
    RCTLIB_TDSCDMA_MeasureAFC_CALLBACK  MeasureAFC_CALLBACK;
    RCTLIB_TDSCDMA_ConfigTesterBeforeAGC_CALLBACK  ConfigTesterBeforeAGC_CALLBACK;
    RCTLIB_TDSCDMA_AGC_ChangeCellPower_CALLBACK  AGC_ChangeCellPower_CALLBACK;
    RCTLIB_TDSCDMA_AGC_ChangeCellBand_CALLBACK  AGC_ChangeCellBand_CALLBACK;
    RCTLIB_TDSCDMA_AGC_ChangeChannel_CALLBACK  AGC_ChangeChannel_CALLBACK;
    RCTLIB_TDSCDMA_ConfigTesterBeforeAPC_CALLBACK  ConfigTesterBeforeAPC_CALLBACK;
    RCTLIB_TDSCDMA_APC_ChangeCellBand_CALLBACK  APC_ChangeCellBand_CALLBACK;
    RCTLIB_TDSCDMA_APC_MeasurePower_CALLBACK  APC_MeasurePower_CALLBACK;
    RCTLIB_TDSCDMA_ConfigTesterBeforeFHC_CALLBACK  ConfigTesterBeforeFHC_CALLBACK;
    RCTLIB_TDSCDMA_FHC_StartIteration_CALLBACK  FHC_StartIteration_CALLBACK;
    RCTLIB_TDSCDMA_FHC_FetchResult_CALLBACK  FHC_FetchResult_CALLBACK;
    RCTLIB_TDSCDMA_ConfigTesterCommonBeforeNSFT_CALLBACK  ConfigTesterCommonBeforeNSFT_CALLBACK;
    RCTLIB_TDSCDMA_ConfigTesterForNSFT_CALLBACK  ConfigTesterForNSFT_CALLBACK;
    RCTLIB_TDSCDMA_MeasureTPCForNSFT_CALLBACK  MeasureTPCForNSFT_CALLBACK;
    RCTLIB_TDSCDMA_ConfigTesterForNSFTBer_CALLBACK  ConfigTesterForNSFTBer_CALLBACK;
    RCTLIB_TDSCDMA_MeasureLBerForNSFT_CALLBACK  MeasureLBerForNSFT_CALLBACK;
    RCTLIB_TDSCDMA_ConfigTesterCommonBeforeFT_CALLBACK  ConfigTesterCommonBeforeFT_CALLBACK;
    RCTLIB_TDSCDMA_ConfigTesterForFT_CALLBACK  ConfigTesterForFT_CALLBACK;
    RCTLIB_TDSCDMA_MeasureTPCForFT_CALLBACK  MeasureTPCForFT_CALLBACK;
    RCTLIB_TDSCDMA_ConfigTesterForFTBer_CALLBACK  ConfigTesterForFTBer_CALLBACK;
    RCTLIB_TDSCDMA_MeasureLBerForFT_CALLBACK  MeasureLBerForFT_CALLBACK;
    RCTLIB_TDSCDMA_GET_SpecificSettings_CALLBACK  GET_SpecificSettings_CALLBACK;
    // please add in the last
} RCTLIB_TDSCDMA_CALLBACKS_CFG_T;
#ifdef __META_C2K__
//=====================================================
//           C2K Callback defines
//=====================================================
typedef int (*RCTLIB_C2K_InstrumentInit_CALLBACK)(void);
typedef int (*RCTLIB_C2K_ConfigCellPower_CALLBACK)(double power);
typedef int (*RCTLIB_C2K_ConfigDefaultSettings_CALLBACK)(void);
typedef int (*RCTLIB_C2K_AFC_PreSetting_CALLBACK)(const S_RCTLIB_C2K_AFC_PRESETTINGS_T *pSettings, unsigned int size);
typedef int (*RCTLIB_C2K_AFC_FetchResult_CALLBACK)(double *freqError);
typedef int (*RCTLIB_C2K_AGC_PreSetting_CALLBACK)(const S_RCTLIB_C2K_AGC_PRESETTINGS_T *pSettings);
typedef int (*RCTLIB_C2K_AGC_ChangeCellBand_CALLBACK)(unsigned int band);
typedef int (*RCTLIB_C2K_AGC_ChangeChannel_CALLBACK)(unsigned int uarfcn);
typedef int (*RCTLIB_C2K_AGC_ChangeCellPower_CALLBACK)(double cellPower);
typedef int (*RCTLIB_C2K_APC_PreSetting_CALLBACK)(const S_RCTLIB_C2K_APC_PRESETTINGS_T *pSettings);
typedef int (*RCTLIB_C2K_APC_PreSetting_Ex_CALLBACK)(const S_RCTLIB_C2K_APC_PRESETTINGS_EX_T *pSettings);
typedef int (*RCTLIB_C2K_APC_ChangeCellBand_CALLBACK)(unsigned int band);
typedef int (*RCTLIB_C2K_APC_ChangeChannel_CALLBACK)(unsigned int uarfcn);
typedef int (*RCTLIB_C2K_APC_ChangeExpectedPower_CALLBACK)(int expectedPower);
typedef int (*RCTLIB_C2K_APC_Initiate_CALLBACK)(void);
typedef int (*RCTLIB_C2K_APC_FetchResult_CALLBACK)(double *outputPower);
typedef int (*RCTLIB_C2K_FHC_PreSetting_CALLBACK)(const S_RCTLIB_C2K_FHC_PRESETTINGS_T *pSettings);
typedef int (*RCTLIB_C2K_FHC_StartIteration_CALLBACK)(const S_RCTLIB_C2K_FHC_MEASUREMENT_PARAM_T *pSettings);
typedef int (*RCTLIB_C2K_FHC_FetchResult_CALLBACK)(const S_RCTLIB_C2K_FHC_MEASUREMENT_PARAM_T *pSettings, S_RCTLIB_C2K_FHC_MEASUREMENT_RESULT_T *pResult);
typedef int (*RCTLIB_C2K_NSFT_PreSetting_CALLBACK)(const S_RCTLIB_C2K_NSFT_PRESETTINGS_T *pSettings);
typedef int (*RCTLIB_C2K_NSFT_InitiateTestCase_CALLBACK)(const S_RCTLIB_C2K_NSFT_CONFIG_T *pSettings);
typedef int (*RCTLIB_C2K_NSFT_ChangeCellPower_CALLBACK)(double d_cell_power);
typedef int (*RCTLIB_C2K_NSFT_TPC_CALLBACK)(const S_RCTLIB_C2K_NSFT_TPC_REQUEST_T *tpc_request);
typedef int (*RCTLIB_C2K_NSFT_FetchTXPerformanceMaxPower_CALLBACK)(S_RCTLIB_C2K_NSFT_TX_PERF_MAX_POWER_RESULT_T *tx_perf_result);
typedef int (*RCTLIB_C2K_NSFT_FetchTXPerformanceMinPower_CALLBACK)(S_RCTLIB_C2K_NSFT_TX_PERF_MIN_POWER_RESULT_T *tx_perf_result);
typedef int (*RCTLIB_C2K_NSFT_InitiateFER_CALLBACK)(void);
typedef int (*RCTLIB_C2K_NSFT_ReadFER_CALLBACK)(S_RCTLIB_C2K_NSFT_FER_RESULT_T *result);
typedef int (*RCTLIB_C2K_NSFT_APP_PreSetting_CALLBACK)(const S_RCTLIB_C2K_NSFT_APP_PRESETTING_T *pSettings);
typedef int (*RCTLIB_C2K_NSFT_APP_CasePreSetting_CALLBACK)(void);
typedef int (*RCTLIB_C2K_NSFT_APP_InitiateTestCase_CALLBACK)(const S_RCTLIB_C2K_NSFT_APP_CONFIG_T *pSettings);
typedef int (*RCTLIB_C2K_NSFT_APP_ChangeCellPower_CALLBACK)(double d_cell_power);
typedef int (*RCTLIB_C2K_NSFT_APP_FetchTestCase_CALLBACK)(S_RCTLIB_C2K_NSFT_APP_RESULT_T *result);
typedef int (*RCTLIB_C2K_GET_SpecificSettings_CALLBACK)(S_RCTLIB_C2K_SPECIFIC_SETTINGS_T *pSettings);

typedef struct
{
    RCTLIB_C2K_InstrumentInit_CALLBACK  InstrumentInit_CALLBACK;
    RCTLIB_C2K_ConfigCellPower_CALLBACK  ConfigCellPower_CALLBACK;
    RCTLIB_C2K_ConfigDefaultSettings_CALLBACK  ConfigDefaultSettings_CALLBACK;
    RCTLIB_C2K_AFC_PreSetting_CALLBACK  AFC_PreSetting_CALLBACK;
    RCTLIB_C2K_AFC_FetchResult_CALLBACK  AFC_FetchResult_CALLBACK;
    RCTLIB_C2K_AGC_PreSetting_CALLBACK  AGC_PreSetting_CALLBACK;
    RCTLIB_C2K_AGC_ChangeCellBand_CALLBACK  AGC_ChangeCellBand_CALLBACK;
    RCTLIB_C2K_AGC_ChangeChannel_CALLBACK  AGC_ChangeChannel_CALLBACK;
    RCTLIB_C2K_AGC_ChangeCellPower_CALLBACK  AGC_ChangeCellPower_CALLBACK;
    RCTLIB_C2K_AGC_PreSetting_CALLBACK  AGC_RXD_PreSetting_CALLBACK;
    RCTLIB_C2K_AGC_ChangeCellBand_CALLBACK  AGC_RXD_ChangeCellBand_CALLBACK;
    RCTLIB_C2K_AGC_ChangeChannel_CALLBACK  AGC_RXD_ChangeChannel_CALLBACK;
    RCTLIB_C2K_AGC_ChangeCellPower_CALLBACK  AGC_RXD_ChangeCellPower_CALLBACK;
    RCTLIB_C2K_APC_PreSetting_CALLBACK  APC_PreSetting_CALLBACK;
    RCTLIB_C2K_APC_ChangeCellBand_CALLBACK  APC_ChangeCellBand_CALLBACK;
    RCTLIB_C2K_APC_ChangeChannel_CALLBACK  APC_ChangeChannel_CALLBACK;
    RCTLIB_C2K_APC_ChangeExpectedPower_CALLBACK  APC_ChangeExpectedPower_CALLBACK;
    RCTLIB_C2K_APC_Initiate_CALLBACK  APC_Initiate_CALLBACK;
    RCTLIB_C2K_APC_FetchResult_CALLBACK  APC_FetchResult_CALLBACK;
    RCTLIB_C2K_FHC_PreSetting_CALLBACK  FHC_PreSetting_CALLBACK;
    RCTLIB_C2K_FHC_StartIteration_CALLBACK  FHC_StartIteration_CALLBACK;
    RCTLIB_C2K_FHC_FetchResult_CALLBACK  FHC_FetchResult_CALLBACK;
    RCTLIB_C2K_NSFT_PreSetting_CALLBACK  NSFT_PreSetting_CALLBACK;
    RCTLIB_C2K_NSFT_InitiateTestCase_CALLBACK  NSFT_InitiateTestCase_CALLBACK;
    RCTLIB_C2K_NSFT_ChangeCellPower_CALLBACK  NSFT_ChangeCellPower_CALLBACK;
    RCTLIB_C2K_NSFT_TPC_CALLBACK  NSFT_TPC_CALLBACK;
    RCTLIB_C2K_NSFT_FetchTXPerformanceMaxPower_CALLBACK  NSFT_FetchTXPerformanceMaxPower_CALLBACK;
    RCTLIB_C2K_NSFT_FetchTXPerformanceMinPower_CALLBACK  NSFT_FetchTXPerformanceMinPower_CALLBACK;
    RCTLIB_C2K_NSFT_InitiateFER_CALLBACK  NSFT_InitiateFER_CALLBACK;
    RCTLIB_C2K_NSFT_ReadFER_CALLBACK  NSFT_ReadFER_CALLBACK;
    RCTLIB_C2K_NSFT_APP_PreSetting_CALLBACK  NSFT_APP_PreSetting_CALLBACK;
    RCTLIB_C2K_NSFT_APP_CasePreSetting_CALLBACK  NSFT_APP_CasePreSetting_CALLBACK;
    RCTLIB_C2K_NSFT_APP_InitiateTestCase_CALLBACK  NSFT_APP_InitiateTestCase_CALLBACK;
    RCTLIB_C2K_NSFT_APP_ChangeCellPower_CALLBACK  NSFT_APP_ChangeCellPower_CALLBACK;
    RCTLIB_C2K_NSFT_APP_FetchTestCase_CALLBACK  NSFT_APP_FetchTestCase_CALLBACK;
    RCTLIB_C2K_GET_SpecificSettings_CALLBACK  GET_SpecificSettings_CALLBACK;
    RCTLIB_C2K_APC_PreSetting_Ex_CALLBACK  APC_PreSetting_Ex_CALLBACK;
    // please add in the last
} RCTLIB_C2K_CALLBACKS_CFG_T;
#endif //#ifdef __META_C2K__
/**
 * Register callbacks interface
 **/
NON_REENTRANT_FUNC int __stdcall RCTLIB_SetCommonInstrumentInstance(RCTLIB_COMMON_CALLBACKS_CFG_T *instCallbacks);
NON_REENTRANT_FUNC int __stdcall RCTLIB_SetGGEInstrumentInstance(RCTLIB_GGE_CALLBACKS_CFG_T *instCallbacks);
NON_REENTRANT_FUNC int __stdcall RCTLIB_SetWCDMAInstrumentInstance(RCTLIB_WCDMA_CALLBACKS_CFG_T *instCallbacks);
NON_REENTRANT_FUNC int __stdcall RCTLIB_SetTDSCDMAInstrumentInstance(RCTLIB_TDSCDMA_CALLBACKS_CFG_T *instCallbacks);
#ifdef __META_C2K__
NON_REENTRANT_FUNC int __stdcall RCTLIB_SetC2KInstrumentInstance(RCTLIB_C2K_CALLBACKS_CFG_T *instCallbacks);
#endif //#ifdef __META_C2K__

// reentrant functions
int __stdcall RCTLIB_SetCommonInstrumentInstance_r(const int meta_handle, RCTLIB_COMMON_CALLBACKS_CFG_T *instCallbacks);
int __stdcall RCTLIB_SetGGEInstrumentInstance_r(const int meta_handle, RCTLIB_GGE_CALLBACKS_CFG_T *instCallbacks);
int __stdcall RCTLIB_SetWCDMAInstrumentInstance_r(const int meta_handle, RCTLIB_WCDMA_CALLBACKS_CFG_T *instCallbacks);
int __stdcall RCTLIB_SetTDSCDMAInstrumentInstance_r(const int meta_handle, RCTLIB_TDSCDMA_CALLBACKS_CFG_T *instCallbacks);
#ifdef __META_C2K__
int __stdcall RCTLIB_SetC2KInstrumentInstance_r(const int meta_handle, RCTLIB_C2K_CALLBACKS_CFG_T *instCallbacks);
#endif //#ifdef __META_C2K__

///add by zishuo on 20150924 for Multi ATE
typedef enum tag_multiTestmode
{
    ENUM_MULTI_DUT_NORMAL = 0,
    ENUM_MULTI_DUT_INTERLEAVING = 1,
    ENUM_MULTI_DUT_BROADCAST = 2,
    ENUM_MULTI_DUT_PER_CMD_LOCK = 3
} EN_MULTI_DUT_TEST_MODE;

typedef enum
{
    ENUM_COMMON_SA = 0,
    ENUM_COMMON_SG = 1,
    ENUM_COMMON_WHOLE_INST = 2,
    ENUM_COMMON_RESOURCE_MAX,
} ENUM_COMMON_RESOURCE;
typedef enum
{
    ENUM_TEST_STAGE_INIT = 0,
    //For example, if we perform NR and LTE NSFT
    ENUM_TEST_STAGE_SINGLE_FLOW_DONE = 1,  //NR or LTE done.
    ENUM_TEST_STAGE_SINGLE_TASK_DONE = 2,  //NR or LTE done.
    ENUM_TEST_STAGE_FULL_TASK_DONE = 3,    //NR + LTE done.
    ENUM_TEST_STAGE_DEINIT = 4,            //Freq1(NR+LTE), ..., FreqN(NR+LTE) done.
    ENUM_TEST_STAGE_MAX = 5,
} EN_MULTI_DUT_TEST_STAGE;
static const char *MULTI_DUT_TEST_STAGE_STR[] =
{
    "ENUM_TEST_STAGE_INIT",
    "ENUM_TEST_STAGE_SINGLE_FLOW_DONE",
    "ENUM_TEST_STAGE_SINGLE_TASK_DONE",
    "ENUM_TEST_STAGE_FULL_TASK_DONE",
    "ENUM_TEST_STAGE_DEINIT",
    "ENUM_TEST_STAGE_MAX"
};

int __stdcall RCTLIB_Common_RegistTestGroupInfo_r(const int metaHandle, const unsigned int dutCount, const unsigned int waitTimeMs);
void __stdcall RCTLIB_Common_Get_MetaHandles_In_TestGroup_r(const int meta_handle, int *handleArray, unsigned int handleArraySize, unsigned int *handleCount);
void __stdcall RCTLIB_Common_StageBarrier_r(const int meta_handle, EN_MULTI_DUT_TEST_STAGE stage);
void __stdcall RCTLIB_Common_SetEnableThreadBarrier_r(const int meta_handle, bool isEnable);
int __stdcall RCTLIB_GetInstrumentInformation_r(const int meta_handle, char *verion, char *GPIBaddr);

int __stdcall RCTLIB_Instrument_LOCK_r(const int meta_handle);
int __stdcall RCTLIB_Instrument_UNLOCK_r(const int meta_handle);
int __stdcall RCTLIB_CreateJoinPoint_r(const int meta_handle, const char *prefixName, bool create);
int __stdcall RCTLIB_WaitJoinPoint_r(const int meta_handle, const char *prefixName);
int __stdcall RCTLIB_SetSlotStatus_r(const int meta_handle, bool isSkip, const char *prefixName);
int __stdcall RCTLIB_GetMultiDutTestType_r(const int meta_handle, int *Type);
#if !defined(__NO_WCN_TEST__)
typedef enum
{
    BT = 0,
    BLE,
    BT_Antenna1,
    BLE_Antenna1,
    BT_Antenna2,
    BLE_Antenna2,
    BT_ALL,
} BT_TYPE;

typedef enum
{
    WAVE_SINGLE = 0,
    WAVE_CONTINUE = 1,

} WAVE_PLAY_TYPE;

typedef struct
{
    BT_TYPE bt_type;
    int type;
    char *bt_addr;
    char *packet_type;
    unsigned int pattern;
    unsigned int rfport;
    unsigned int bt_test_phy;
    double vsa;
    int btTestChannel;
    double loss;
    unsigned int packet;
    double vsa_margin;
} RCTLIB_BT_TX_START_ITERATION_T;

typedef struct
{
    BT_TYPE bt_type;
    unsigned int bt_test_pattern;
    bool bt_pattern_PRBS9;
    unsigned int btPacketType;
    unsigned int ble_test_phy;
    double vsg;
    unsigned int rfport;
    int btTestChannel;
    double loss;
    int signal_off;
    int btPacketNumber;
} RCTLIB_BT_RX_START_ITERATION_T;

typedef struct
{
    //int case_index;
    //int channel_index;
    int type;
    double bt_freq_offset;
    double bt_freq_drift;
    double bt_output_power;
    double bt_peak_devm;
    double ble_freq_accuracy;
    double ble_freq_drift;
    double ble_freq_offset;
    double ble_max_drift_rate;
    double bt_rms_devm;
    double bt_99_devm;
    double bt_initial_freq_error;
    double bt_block_freq_error;
    double bt_total_freq_error;
    double bt_relative_power;
    double bt_GFSK_power;
    double bt_DPSK_power;

    double bt_DF1;
    double bt_DF2;
    double bt_DP2DF1;
    double ble_DF1_avg;
    double ble_DF1_max;

    bool bt_cal_pass;
    bool bthw_cal_pass;
} RCTLIB_BT_TX_RESULT_T;

int __stdcall RCTLIB_WCN_BT_TX_StartIteration(RCTLIB_BT_TX_START_ITERATION_T *btTxInstPara);
int __stdcall RCTLIB_WCN_BT_TX_StartIteration_r(const int meta_handle, RCTLIB_BT_TX_START_ITERATION_T *btTxInstPara);

int __stdcall RCTLIB_WCN_BT_TX_FetchResult(bool auto_range, RCTLIB_BT_TX_RESULT_T *bt_result);
int __stdcall RCTLIB_WCN_BT_TX_FetchResult_r(const int metaHandle, bool auto_range, RCTLIB_BT_TX_RESULT_T *bt_result);

int __stdcall RCTLIB_WCN_BT_RX_Presetting(RCTLIB_BT_RX_START_ITERATION_T *);
int __stdcall RCTLIB_WCN_BT_RX_Presetting_r(const int metaHandle, RCTLIB_BT_RX_START_ITERATION_T *);

int __stdcall RCTLIB_WCN_BT_RX_StartIteration();
int __stdcall RCTLIB_WCN_BT_RX_StartIteration_r(const int metaHandle);

int __stdcall RCTLIB_WCN_BT_Switch_RF_Port_Ex(const int meta_handle, BT_TYPE type, AntennaPortType eRfPortType);
int __stdcall RCTLIB_WCN_BT_Config_CableLoss_Ex(const int meta_handle, BT_TYPE type, AntennaPortType eAntennaPortType);
E_RCTLIB_RF_PORT_TYPE __stdcall RCTLIB_WCN_BT_Get_RF_Port_Mapping_Port_Number(const int meta_handle, BT_TYPE type, AntennaPortType eRfPortType);
int __stdcall RCTLIB_WCN_BT_Get_RF_Carkit(const int meta_handle, BT_TYPE type, AntennaPortType eRfPortType);
int __stdcall RCTLIB_WCN_BT_Presetting_r(const int metaHandle);

typedef enum
{
    _2DOT4,
    _4DOT9,
    //_5DOT0,
    _5DOT2,
    _5DOT4,
    _5DOT6,
    _5DOT8,
    _5DOT9,
    _6DOT0,
    _6DOT2,
    _6DOT3,
    _6DOT5,
    _6DOT7,
    _6DOT8,
    _7DOT0,
    WIFI_ALL,
} WIFI_FREQ_TYPE;
typedef enum
{
    ANT0,
    ANT1,
    ANT2,
    ANT3,
    ANT4,
    ANT_MAX,
} WIFI_MIMO;

typedef struct
{
    WIFI_FREQ_TYPE type;
    WIFI_MIMO ant;
    AntennaPortType eRfPortType;
    unsigned char DBDC_Band;
    unsigned short ANT_Swap;
} RCTLIB_RF_PORT_WIFI_SETTING_T;

int __stdcall RCTLIB_WCN_WIFI_Switch_RF_Port_Ex(const int meta_handle, WIFI_FREQ_TYPE type, WIFI_MIMO ant, AntennaPortType eRfPortType);
int __stdcall RCTLIB_WCN_WIFI_Switch_RF_Port_Ex_V2(const int meta_handle, RCTLIB_RF_PORT_WIFI_SETTING_T *pData);

int __stdcall RCTLIB_WCN_WIFICAL_Switch_RF_Port_Ex(const int meta_handle, WIFI_FREQ_TYPE type, WIFI_MIMO ant, AntennaPortType eRfPortType);

int __stdcall RCTLIB_WCN_WIFI_Config_CableLoss_Ex(const int meta_handle, WIFI_FREQ_TYPE type, WIFI_MIMO ant, AntennaPortType eRfPortType);

int __stdcall RCTLIB_WCN_WIFICAL_Config_CableLoss_Ex(const int meta_handle, WIFI_FREQ_TYPE type, WIFI_MIMO ant, AntennaPortType eRfPortType);

E_RCTLIB_RF_PORT_TYPE __stdcall RCTLIB_WCN_WIFI_Get_RF_Port_Mapping_Port_Number(const int meta_handle, WIFI_FREQ_TYPE type, WIFI_MIMO ant, AntennaPortType eRfPortType);
E_RCTLIB_RF_PORT_TYPE __stdcall RCTLIB_WCN_WIFI_Get_RF_Port_Mapping_Port_Number_V2(const int meta_handle, RCTLIB_RF_PORT_WIFI_SETTING_T *pData);
int __stdcall RCTLIB_WCN_WIFI_Get_RF_Carkit(const int meta_handle, RCTLIB_RF_PORT_WIFI_SETTING_T *pData);

int __stdcall RCTLIB_WCN_SingleTone_TX_FetchResult_r(const int meta_handle, double *dFreqError);
int __stdcall RCTLIB_WCN_SingleTone_TX_FetchResult(double *dFreqError);

int __stdcall RCTLIB_WCN_SingleTone_TX_PreSetting_r(const int meta_handle, const S_RCTLIB_SINGLETONE_AFC_PRESETTINGS_T *pSettings);
int __stdcall RCTLIB_WCN_SingleTone_TX_PreSetting(const S_RCTLIB_SINGLETONE_AFC_PRESETTINGS_T *pSettings);

typedef enum
{
    _11B  = 0, //CCK, 11b(DSSS)
    _11AG = 1, //OFDM
    _HT20 = 2, //11n, BW20
    _HT40 = 3, //11n, BW40
    _11AC = 4, //VHT
    _11AX = 5, //HE
    _11BE = 6, //EHT
    WIFI_STANDARD_END = 7,

} WIFI_TYPE; //WIFI TEST Condition

typedef struct
{
    int NoOfSegments;
    int MOEX_ON;
    double MeasTimeout;
    int Threshold_Voltage;
} RCTLIB_WIFI_TX_START_PRESET_T;

typedef struct
{
    int wifi_channel_type; //0: 2.4GHz; 1: 5GHz; 2: 6GHz
    int wifi_test_condition;
    double wifi_vsa;
    double wifi_vsa_margin;
    unsigned int wifi_rfport;
    int wifi_test_channel;
    unsigned int wifi_tx_preamble;
    unsigned int wifi_tx_cbw;
    double loss;
    int ax_mcs;
    int ltf;
    double gi;
    int channel_estimation;
    // List Mode
    unsigned int segment_index;
    int StatCountMod;
    int StatCountSem;
    int SegTime;            // Unit: ms
    int MeasureOffsetTime;  // Unit: ms
    int MeasurementTime;    // Unit: ms
    int packet_count;
    int pre_power;
    int pre_power_duration_enable;
    double pre_power_duration_start;
    double pre_power_duration_stop;
    int Retrigger_ON;
} RCTLIB_WIFI_TX_START_ITERATION_T;

typedef struct
{
    double vsg;
    unsigned int wifi_rfport;
    int TestChannel;
    double loss;
    int frame_cnt;
    int test_condition;
    int preamble;
    unsigned int wifi_rate;
    unsigned int wifi_bandwidth;
    int wifi_cbw;
    int waveform_play_type;
    int fec_coding_type; // 0: BCC, 1: LDPC
    int signal_off_only;
    char waveformbuffer[1025];
    double dArbDuration;
    double SampleRate;
    double TriggerVSA;
    double SegTime; // Unit: s
    double MeasureOffsetTime; // Unit: s
} RCTLIB_WIFI_RX_START_ITERATION_T;
typedef struct
{
    double wifi_per;
    long wifi_rssi;
    long wifi_another_rssi;
    long wifi_third_rssi;
    // List Mode
    int case_index;
    int test_channel_number;
} RCTLIB_WIFI_RX_RESULT_T;

typedef struct
{
    int mask_spec_num;
    double mask_spec[10];
    double mask_up[10];
    double mask_down[10];
    double mask_slope[10];
    double mask_const[10];//Mask
} S_WIFI_TEST_SEM_SPEC_T;

typedef struct
{
    double wifi_output_power;
    double wifi_evm;
    double wifi_freq_error;
    double wifi_loleakage;
    //std::map<double, double> wifi_sem;
    bool sem_result;
    int Fetch_SEM_Type;
    // List Mode
    unsigned int segment_index;
    int test_channel_number;
    int case_index;
    int test_condition;
    int freqMHz;
} RCTLIB_WIFI_TX_RESULT_T;

typedef struct
{
    int Reliability;

} RCTLIB_WIFI_TX_MEAS_STAT_T;

typedef struct
{
    double wifi_output_power;

} RCTLIB_WIFI_TX_CAL_RESULT_T;

int __stdcall RCTLIB_WCN_WIFI_TX_StartIteration(RCTLIB_WIFI_TX_START_ITERATION_T *btTxInstPara);
int __stdcall RCTLIB_WCN_WIFI_TX_StartIteration_r(const int meta_handle, RCTLIB_WIFI_TX_START_ITERATION_T *btTxInstPara);

int __stdcall RCTLIB_WCN_WIFI_TX_FetchResult(bool auto_range, RCTLIB_WIFI_TX_RESULT_T *bt_result, S_WIFI_TEST_SEM_SPEC_T *sem_spec);
int __stdcall RCTLIB_WCN_WIFI_TX_FetchResult_r(const int metaHandle, bool auto_range, RCTLIB_WIFI_TX_RESULT_T *bt_result, S_WIFI_TEST_SEM_SPEC_T *sem_spec);


int __stdcall RCTLIB_WCN_WIFICAL_TX_StartIteration(RCTLIB_WIFI_TX_START_ITERATION_T *btTxInstPara);
int __stdcall RCTLIB_WCN_WIFICAL_TX_StartIteration_r(const int meta_handle, RCTLIB_WIFI_TX_START_ITERATION_T *btTxInstPara);

int __stdcall RCTLIB_WCN_WIFICAL_TX_FetchResult(bool auto_range, RCTLIB_WIFI_TX_RESULT_T *bt_result, S_WIFI_TEST_SEM_SPEC_T *sem_spec);
int __stdcall RCTLIB_WCN_WIFICAL_TX_FetchResult_r(const int metaHandle, bool auto_range, RCTLIB_WIFI_TX_RESULT_T *bt_result, S_WIFI_TEST_SEM_SPEC_T *sem_spec);


int __stdcall RCTLIB_WCN_WIFI_RX_Presetting(RCTLIB_WIFI_RX_START_ITERATION_T *);
int __stdcall RCTLIB_WCN_WIFI_RX_Presetting_r(const int metaHandle, RCTLIB_WIFI_RX_START_ITERATION_T *);

int __stdcall RCTLIB_WCN_WIFI_RX_StartIteration();
int __stdcall RCTLIB_WCN_WIFI_RX_StartIteration_r(const int metaHandle);

int __stdcall RCTLIB_WCN_WIFI_Presetting_r(const int meta_handle);

int __stdcall RCTLIB_WCN_WIFI_List_TX_Presetting_r(const int meta_handle, RCTLIB_WIFI_TX_START_PRESET_T *wifiTxInstPresetPara);
int __stdcall RCTLIB_WCN_WIFI_List_TX_StartIteration_r(const int meta_handle, RCTLIB_WIFI_TX_START_ITERATION_T *btTxInstPara);
int __stdcall RCTLIB_WCN_WIFI_List_TX_Init_Measure_r(const int meta_handle);
int __stdcall RCTLIB_WCN_WIFI_List_TX_FetchMeasStat_r(const int metaHandle, RCTLIB_WIFI_TX_MEAS_STAT_T *WifiTxMeasStat);
int __stdcall RCTLIB_WCN_WIFI_List_TX_FetchResult_r(const int metaHandle, RCTLIB_WIFI_TX_RESULT_T *wifi_result);
int __stdcall RCTLIB_WCN_WIFI_List_TX_OFF_r(const int meta_handle);
int __stdcall RCTLIB_WCN_WIFI_List_TX_Capture_Analyzer_r(const int metaHandle);
int __stdcall RCTLIB_WCN_WIFI_SCPI_r(const int meta_handle, const char *cmd);
int __stdcall RCTLIB_WCN_WIFI_List_RX_Presetting_r(const int meta_handle, const int NoOfSegments, RCTLIB_WIFI_RX_START_ITERATION_T *wifiRxInstParaArray);
int __stdcall RCTLIB_WCN_WIFI_List_RX_StartIteration_r(const int meta_handle);

typedef enum
{
    GPS,
    GLONASS,
    BD1,
    BD2,
    GALILEO,
    ALL_GPS_SYS,
} NAVIGATION_SYS;

#define GENERATEGPSFREQINTERVAL(NUM) GENERATEGPSFREQINTERVAL_ADDPREFIX(FREQ_, NUM)
#define GENERATEGPSFREQINTERVAL_ADDPREFIX(FREQ, NUM) FREQ##NUM
typedef enum
{
    //GENERATEGPSFREQINTERVAL(0),
    //GENERATEGPSFREQINTERVAL(1),
    //GENERATEGPSFREQINTERVAL(2),
    GENERATEGPSFREQINTERVAL0 = 0,
    GENERATEGPSFREQINTERVAL1,
    GENERATEGPSFREQINTERVAL2,
    GENERATEGPSFREQINTERVAL3
} NAVIGATION_SYS_FREQ_LIST;

int __stdcall RCTLIB_WCN_GPS_Switch_RF_Port_Ex(const int meta_handle, NAVIGATION_SYS sys, NAVIGATION_SYS_FREQ_LIST freq, AntennaPortType eRfPortType);
E_RCTLIB_RF_PORT_TYPE __stdcall RCTLIB_WCN_GPS_Get_RF_Port_Mapping_Port_Number(const int meta_handle, NAVIGATION_SYS sys, NAVIGATION_SYS_FREQ_LIST freq, AntennaPortType eRfPortType);
int __stdcall RCTLIB_WCN_GPS_Get_RF_Carkit(const int meta_handle, NAVIGATION_SYS sys, NAVIGATION_SYS_FREQ_LIST freq, AntennaPortType eRfPortType);
int __stdcall RCTLIB_WCN_GPS_Config_CableLoss_Ex(const int meta_handle, NAVIGATION_SYS sys, NAVIGATION_SYS_FREQ_LIST freq, AntennaPortType eRfPortType);
typedef enum
{
    SATELLITE_GPS = 0,
    SATELLITE_BEIDOU,
    SATELLITE_GLONASS,
    SATELLITE_END = 0xffffffff

} SATELLITE_SYS;

typedef struct SVInfo
{
    int SVid;            // PRN
    int SNR;
    int elv;             // elevation angle : 0~90
    int azimuth;         // azimuth : 0~360
    unsigned char Fix;   // 0:None , 1:FixSV
} SVInfo;

#ifndef SATELLITE_COUNT
#define SATELLITE_COUNT 3
#endif

typedef struct
{
    unsigned int gps_rfport;
    double       gps_power;
    int          gps_test_mode;
    int          gps_test_type;
    int          gps_name;
    int          gps_svid;
    double       loss;
    int          gps_l1;
    double       Duty_test_time;
} RCTLIB_GPS_SETTING_T;
typedef struct
{
    int gps_test_mode;
    int gps_test_type;
    double phase;
    double tcxo_offset;
    double tcxo_drift;
    double CNR_Mean;
    double CNR_Sigma;
    double freq_update;
    int bit_sync;
    int Acquistion;
    int gps_test_idx[2];//result ok or not?
    double m_CNRClockDrift;//new clock drift
    double m_CNRClockDriftRate;//new clock drift rate
    double m_CNRClockDriftRate_max;//new clock drift rate
    double m_CWCNR;
    double m_CWCNR_L1;
    double m_CWCNR_L5;
    double m_CWClockDrift;
    double m_CWClockDrift_L1;
    double m_CWClockDrift_L5;
    int CW_status;

    int CNR_status[SATELLITE_COUNT];
    float m_CNRMode_CNR[SATELLITE_COUNT];
    //
    SVInfo m_svInfo[SATELLITE_COUNT][20];
    int m_GPSSV_cnt[SATELLITE_COUNT];
    int m_CNRMode_Svid[SATELLITE_COUNT];
    int m_bCNRModeCNR[SATELLITE_COUNT]; //not used

#ifdef _WIN32
    HANDLE gps_result_ok;
#endif //#ifdef _WIN32

    int meta_handle;
    int clock_drift;
    int Car_L5;// productType (1 for car 0 for others)
} RCTLIB_RESULT_T;
int __stdcall RCTLIB_WCN_GPS_Setting(RCTLIB_GPS_SETTING_T *);
int __stdcall RCTLIB_WCN_GPS_Setting_r(const int metaHandle, RCTLIB_GPS_SETTING_T *);
// *+> 20220128 GPS BugFix
int __stdcall RCTLIB_WCN_GPS_SGOff(const int metaHandle);
// <+* 20220128 GPS BugFix

#endif

typedef struct
{
    int dummy;
} S_IOT_NTN_FHC_PRESETTINGS_T;

typedef struct
{
    unsigned char     txRfPort;
    unsigned int      frequencyKhz;
    double            pwrLevel;
    unsigned char     pwrStepDuration;
} S_RCTLIB_IOT_NTN_FHC_TX_PWR_STEP_T;

typedef struct
{
    unsigned char     rxRfPortCount;
    unsigned char     rxRfPort[8];
    unsigned int      frequencyKhz;
    double            pwrLevel;
    unsigned char     pwrStepDuration;
    float             timingOffset;
} S_RCTLIB_IOT_NTN_FHC_RX_PWR_STEP_T;

typedef struct
{
    unsigned char     pwrStepDuration;
} S_RCTLIB_IOT_NTN_FHC_SWITCH_PWR_STEP_T;

typedef enum
{
    RCTLIB_IOT_NTN_FHC_TX_PWR_STEP = 0,
    RCTLIB_IOT_NTN_FHC_RX_PWR_STEP,
    RCTLIB_IOT_NTN_FHC_SWITCH_PWR_STEP,
    RCTLIB_IOT_NTN_FHC_PWR_STEP_TYPE_NUM
} IotNtnFhcPowerStepType;

typedef struct
{
    IotNtnFhcPowerStepType stepType;
    union
    {
        S_RCTLIB_IOT_NTN_FHC_TX_PWR_STEP_T txPwrStep;
        S_RCTLIB_IOT_NTN_FHC_RX_PWR_STEP_T rxPwrStep;
        S_RCTLIB_IOT_NTN_FHC_SWITCH_PWR_STEP_T switchPwrStep;
    };
} S_RCTLIB_IOT_NTN_FHC_PWR_STEP_T;

typedef struct
{
    bool isSwitchPortMode;
    int pwrStepCount;
    S_RCTLIB_IOT_NTN_FHC_PWR_STEP_T  *pwrSteps;
} S_IOT_NTN_FHC_MEASUREMENT_PARAM;

typedef struct
{
    double measuredPower;
} S_RCTLIB_IOT_NTN_FHC_TX_PWR_STEP_RESULT_T;

typedef struct
{
    IotNtnFhcPowerStepType stepType;
    S_RCTLIB_EN_FHC_TX_PWR_STEP_RESULT_T txPwrStepResult;
} S_RCTLIB_IOT_NTN_FHC_PWR_STEP_RESULT_T;

typedef struct
{
    int pwrStepCount;
    S_RCTLIB_IOT_NTN_FHC_PWR_STEP_RESULT_T  *pwrStepsResult;
} S_IOT_NTN_FHC_MEASUREMENT_RESULT;

typedef struct
{
    /// physical Cell Id
    unsigned short cellId;
    /// C-RNTI
    unsigned short rnti;
    /// cell power for UE NSFT sync
    double cellPower;
} S_RCTLIB_IOT_NTN_NSFT_LIST_MODE_PRESETTINGS_T;

/**
 * Description: IoT-NTN instrument's spcial settings
 */
typedef struct
{
    /// ========== FHC instrument parameters ==========
    /// FHC RX Max Step Count
    unsigned int uiFHC_RX_MaxStepCount;
    /// FHC TX Max Step Count
    unsigned int uiFHC_TX_MaxStepCount;
    /// FHC Multiple Band Support
    unsigned char ucFHC_MultiBand; // 0: single band, 1: multiple band support
    /// FHC switch time (ms)
    unsigned char ucFHC_SwitchTimeMS;
    /// FHC tx step width (ms)
    unsigned char ucFHC_TxStepWidthMS;
} S_RCTLIB_IOT_NTN_FHC_SPECIFIC_SETTINGS_T;

/**
 * The structure is IoT-NTN NSFT list mode specific setting
 */
typedef struct
{
    // NSFT TX+RX Max Step Count
    unsigned int uiNSFT_MaxStepCount;
    // NSFT RX Max Step Count
    unsigned int uiNSFT_RX_MaxStepCount;
    // NSFT TX Max Step Count
    unsigned int uiNSFT_TX_MaxStepCount;
    // NSFT step limit consider switch time number
    bool bNSFT_SwitchNumStepCost;
    // NSFT step limit consider switch time duration
    bool bNSFT_SwitchTimeStepCost;
    // NSFT measure time limit
    unsigned int uiNSFT_MaxTimeMs;
    /// NSFT list mode support
    bool bNSFT_ListModeSupported;
    // NSFT Test Case Combine
    bool bNSFT_TestCaseCombine;
    //  Support source generator broadcast
    bool bSgBroadcastSupport;
} S_RCTLIB_IOT_NTN_NSFT_LIST_MODE_SPECIFIC_SETTINGS_T;

typedef struct
{
    bool isRxSnr;

    unsigned short band;
    unsigned int   ulFrequencyKhz;
    unsigned int   dlFrequencyKhz;  // Rx Snr add 600kHz offset for CW tone
    float          rxPower;

    char txCarkit;
    char rxmCarkit;
    char rxdCarkit;
} S_RCTLIB_IOT_NTN_NSFT_LIST_RX_STEP_INFO;

typedef struct
{
    bool isTxPowerCheck;
    bool isEvmCheck;
    bool isAclrCheck;
    bool isSemCheck;
    bool isObwCheck;
    bool isGainErrCheck;
    bool isIbEmissionCheck;
    bool isFreqErrorCheck;

    unsigned short band;
    unsigned char  duplexMode;
    unsigned int   ulFrequencyKhz;
    unsigned int   dlFrequencyKhz;  // same channel, add 600kHz offset for CW tone
    float          txPower;
    unsigned char  scs;
    unsigned char  mcsMode;
    unsigned char  isc;

    float          dlSyncPower;     // Tx sync step: sync power / Tx update step: -100
    unsigned short offsetWidthMs;   // if need more offset time for route change, specified the offset time

    char txCarkit;
    char rxmCarkit;
    char rxdCarkit;
} S_RCTLIB_IOT_NTN_NSFT_LIST_TX_STEP_INFO;

typedef struct
{
    E_INST_LIST_MODE_STEP_TYPE stepType;
    unsigned short stepWidthMs;
    void *stepInfoPtr;      // TX_START, SFFT_TX, FREQ_CHANGE use TX_STEP_INFO / RX_RSSI, RX_SENS use RX_STEP_INFO / TX_STOP, STOP use NULL
} S_RCTLIB_IOT_NTN_NSFT_LIST_STEP_CMD;

typedef struct
{
    bool isSwitchTxPort;
    bool isSwitchRxmPort;
    bool isSwitchRxdPort;
    bool isS15K;
    unsigned int stepCount;
    S_RCTLIB_IOT_NTN_NSFT_LIST_STEP_CMD *stepCmd;
} S_RCTLIB_IOT_NTN_NSFT_LIST_CMD;

/**
 * The structure for storing NR NSFT EVM result
 */
typedef struct
{
    /// EVM (%rms)
    double dRMS_EVM;
    /// Frequency Error(ppm)
    double dFreq_Error_Ppm;
    /// Magnitude Error (%)
    double dMagnitude_Error;
    /// Phase Error (Degree)
    double dPhase_Error_Deg;
    /// IQ Offset (dB)
    double dIq_Offset_dB;
    /// Transmit time error (Ts)
    double dTiming_Error;
} S_RCTLIB_IOT_NTN_NSFT_EVM_RESULT_T;

/**
 * the structure for storing IoT-NTN NSFT ACP(ACLR) result
 */
typedef struct
{
    /// Total Carrier Power
    double dCarrierPower;
    /// Lower Offset GSM - relative power
    double dRel_Low_Power_Gsm;
    /// Upper Offset GSM - relative power
    double dRel_Upper_Power_Gsm;
    /// Lower Offset UTRA - relative power
    double dRel_Low_Power_Utra;
    /// Upper Offset UTRA - relative power
    double dRel_Upper_Power_Utra;
} S_RCTLIB_IOT_NTN_NSFT_ACP_RESULT_T;

/**
 * The structure for storing IoT-NTN NSFT SEM result
 * Description: the sections are defined in 3GPP spec Table *******.5-1
 *              General E-UTRA spectrum emission mask
 */
typedef struct
{
    /// pass flag (overall), pass(0)/fail(1)/not tested(-1)
    char cPass;
    /// Abs Peak Power of lowers
    double dAbsPeakPowerLower[5];
    /// Abs Peak Power of uppers
    double dAbsPeakPowerUpper[5];
    /// Lower delta Limit
    double dDeltaLimitLower[5];
    /// Upper delta Limit
    double dDeltaLimitUpper[5];
} S_RCTLIB_IOT_NTN_NSFT_SEM_RESULT_T;

typedef struct
{
    /// Occupied Bandwidth (MHz)
    double dOccupied_Bandwidth_KHz;
} S_RCTLIB_IOT_NTN_NSFT_OBW_RESULT_T;

/**
 * The structure for storing IoT-NTN NSFT SEM result
 * Description: the sections are defined in 3GPP spec Table *******.5-1
 *              General E-UTRA spectrum emission mask
 */
typedef struct
{
    /// pass flag (overall), pass(0)/fail(1)/not tested(-1)
    char cPass;
    /// worst in-band emission, relative value in db
    double worst_emission;
} S_RCTLIB_IOT_NTN_NSFT_IB_EMISSION_RESULT_T;

/**
 * The structure is for the result of IoT-NTN NSFT List Mode
 */
typedef struct
{
    double                            dPoutPower;
    S_RCTLIB_IOT_NTN_NSFT_EVM_RESULT_T    sEVMResult;
    S_RCTLIB_IOT_NTN_NSFT_ACP_RESULT_T    sACPResult;
    S_RCTLIB_IOT_NTN_NSFT_SEM_RESULT_T    sSEMResult;
    S_RCTLIB_IOT_NTN_NSFT_OBW_RESULT_T    sOBWResult;
    S_RCTLIB_IOT_NTN_NSFT_IB_EMISSION_RESULT_T sIEMResult;
} S_RCTLIB_IOT_NTN_NSFT_LIST_TX_STEP_RESULT;

typedef struct
{
    unsigned int stepCount;
    S_RCTLIB_IOT_NTN_NSFT_LIST_TX_STEP_RESULT  *stepResult;
} S_RCTLIB_IOT_NTN_NSFT_LIST_RESULT;

int __stdcall RCTLIB_IOT_NTN_GET_FHC_SpecificSettings_r(const int metaHandle, S_RCTLIB_IOT_NTN_FHC_SPECIFIC_SETTINGS_T *pSettings);
int __stdcall RCTLIB_IOT_NTN_FHC_PreSetting_r(const int metaHandle, const S_IOT_NTN_FHC_PRESETTINGS_T *pSettings);
int __stdcall RCTLIB_IOT_NTN_FHC_StartIteration_r(const int metaHandle, const S_IOT_NTN_FHC_MEASUREMENT_PARAM *pSettings);
int __stdcall RCTLIB_IOT_NTN_FHC_FetchResult_r(const int metaHandle, const S_IOT_NTN_FHC_MEASUREMENT_PARAM *pSettings, S_IOT_NTN_FHC_MEASUREMENT_RESULT *pResult);

int __stdcall RCTLIB_IOT_NTN_GET_NSFT_ListMode_SpecificSettings_r(const int meta_handle, S_RCTLIB_IOT_NTN_NSFT_LIST_MODE_SPECIFIC_SETTINGS_T *pSettings);
int __stdcall RCTLIB_IOT_NTN_NSFT_ListMode_PreSetting_r(const int metaHandle, const S_RCTLIB_IOT_NTN_NSFT_LIST_MODE_PRESETTINGS_T *pSettings);
int __stdcall RCTLIB_IOT_NTN_NSFT_List_Config_r(const int metaHandle, const S_RCTLIB_IOT_NTN_NSFT_LIST_CMD *pSettings);
int __stdcall RCTLIB_IOT_NTN_NSFT_List_Start_r(const int metaHandle);
int __stdcall RCTLIB_IOT_NTN_NSFT_List_FetchResult_r(const int metaHandle, const S_RCTLIB_IOT_NTN_NSFT_LIST_CMD *pSettings, S_RCTLIB_IOT_NTN_NSFT_LIST_RESULT *pResult);
int __stdcall RCTLIB_IOT_NTN_NSFT_ListMode_Stop_r(const int meta_handle);

int __stdcall RCTLIB_IOT_NTN_Get_RF_Port_Mapping_Port_Number(const int metaHandle, unsigned short band, AntennaPortType eAntennaPortType);
int __stdcall RCTLIB_IOT_NTN_Get_RF_Port_Mapping_By_RF_Carkit(const int metaHandle, RF_Carkit_Index_E eRFCarkitIndex);
int __stdcall RCTLIB_IOT_NTN_Switch_RF_Port_Ex(const int metaHandle, AntennaPortType eRfPortType, E_RCTLIB_RF_PORT_TYPE ePort);
int __stdcall RCTLIB_IOT_NTN_Config_CableLoss_Ex(const int metaHandle, unsigned short band, AntennaPortType eAntennaPortType);
int __stdcall RCTLIB_IOT_NTN_GetCableLossOffsetByCarkit(const int meta_handle, AntennaPortType eAntennaPortType, unsigned int freqKHz, RF_Carkit_Index_E carkit, double *lossOffset);
int __stdcall RCTLIB_IOT_NTN_Switch_RF_MultipleRxPort(const int metaHandle, const S_RCTLIB_ENABLE_MULTIPLE_RX_RF_PORT_T rxPortList);

#ifdef __cplusplus
}
#endif
#endif
