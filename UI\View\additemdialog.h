#ifndef ADDITEMDIALOG_H
#define ADDITEMDIALOG_H

#include <QDialog>

namespace Ui {
class AddItemDialog;
}

class MainWindow;

class AddItemDialog : public QDialog
{
    Q_OBJECT

public:
    explicit AddItemDialog(MainWindow *mainwindow, QWidget *parent = nullptr);
    ~AddItemDialog();

    QString getSelectedType();

private:
    Ui::AddItemDialog *ui;
	MainWindow *m_mainwindow;
};

#endif // ADDITEMDIALOG_H
