#ifndef HTTPCLIENT_H
#define HTTPCLIENT_H

#include <QString>
#include <QObject>
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QFile>
#include <QTimer>

class HttpClient:public QObject
{
    Q_OBJECT
public:
    HttpClient();
    QString HttpGet(const QString url);
    QString HttpPost( const QString url,const QString data );
    void SetContentType(QString strContentType);

private:
    QString m_strContentType;

};

#endif // HTTPCLIENT_H
