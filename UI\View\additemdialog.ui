<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>AddItemDialog</class>
 <widget class="QDialog" name="AddItemDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>400</width>
    <height>135</height>
   </rect>
  </property>
  <property name="font">
   <font>
    <family>Arial</family>
   </font>
  </property>
  <property name="windowTitle">
   <string>Add Item</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>30</number>
   </property>
   <item>
    <widget class="QLabel" name="label">
     <property name="font">
      <font>
       <family>Arial</family>
       <bold>true</bold>
      </font>
     </property>
     <property name="text">
      <string>Item Select: </string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QComboBox" name="comboBox"/>
   </item>
   <item>
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::Cancel|QDialogButtonBox::Ok</set>
     </property>
     <property name="centerButtons">
      <bool>true</bool>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>AddItemDialog</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>248</x>
     <y>254</y>
    </hint>
    <hint type="destinationlabel">
     <x>157</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>buttonBox</sender>
   <signal>rejected()</signal>
   <receiver>AddItemDialog</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>316</x>
     <y>260</y>
    </hint>
    <hint type="destinationlabel">
     <x>286</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
