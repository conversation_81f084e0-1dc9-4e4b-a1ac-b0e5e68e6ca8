#include "WorkerThread.h"

WorkerThread::WorkerThread(QObject *parent,
                           SmartPhoneSN *MetaInstance,
                           ConnectAgilent *VisaInstance,
                           MesProxyBase *MesProxy,
                           UINT nTreadID)
    : QThread(parent)
    , m_nTreadID(nTreadID)
    , m_pMetaInstance(MetaInstance)
    , m_pVisaInstance(VisaInstance)
    , m_pMesProxy(MesProxy)
{
    memset(&m_sScanData, 0, sizeof(ScanData_struct));
    UsbControl = Common::getInstance()->getUsbControlType();
    Common::getInstance()->setSleepFlag(false);
    m_bPowerSupplyConnect = false;
}

WorkerThread::~WorkerThread()
{
}

void WorkerThread::run()
{
    bool loopflag = true;
    getTestItemList();

    /*if(Common::getInstance()->getUseRelayEnable())
    {
        emit signal_checkstatus(m_nTreadID);
    }*/

    while(loopflag)
    {
        QString pItemName = "";
        TestResult_Status pItemValue = IDLE;
        META_RESULT iRet = META_SUCCESS;
        bool bPass = true;
        bool failflag = false;
        float AverageValue = 0.0f;
        float MaxValue = 0.0f;
        QElapsedTimer timer;
        TEST_STAGE_e spStage = START_STAGE;
        TEST_STAGE_e spNextStage = END_STAGE;

        //Codes
        char m_szBarcode[BARCODE_MAX_LENGTH] = {0};
        char m_szIMEI[4][IMEI_ARRAY_LEN] = {0};
        char m_szMeid[MEID_ARRAY_LEN] = { 0 };
        char m_szBtAddr[BT_ARRAY_LEN] = { 0 };
        char m_szWifiAddr[WIFI_ARRAY_LEN] = { 0 };

        Common::getInstance()->setPortRequest(m_nTreadID, "");
        Common::getInstance()->setSignalRet(m_nTreadID, false);

        while((spStage != END_STAGE) && g_TestStopFlag[m_nTreadID] != BOOT_STOP)
        {
            switch(spStage)
            {
                case START_STAGE:
                {
                    Common::getInstance()->DebugOnOff(m_nTreadID, true);
                    SetLogPath();
                    //spNextStage = CHECK_AUTO_TEST_STATUS;
                    spNextStage = INIT_UI_DISPALY;
                    break;
                }

                /*case CHECK_AUTO_TEST_STATUS:
                {
                    while(Common::getInstance()->getUseRelayEnable() && g_TestStopFlag[m_nTreadID] != BOOT_STOP)
                    {
                        if(CheckFixtureStatus())
                            break;

                        Sleep(1000);
                    }

                    if(g_TestStopFlag[m_nTreadID] == BOOT_STOP)
                    {
                        bPass = false;
                        spNextStage = END_STAGE;
                        break;
                    }

                    spNextStage = INIT_UI_DISPALY;
                    break;
                }*/

                case INIT_UI_DISPALY:
                {
                    //Init UI
                    ClearUIlogs();
                    //UpdateBarcodeToUI("");
                    InitItemListView();
                    UpdateResult(RUNNING);
                    UpdateUILog("Start Testting !");
                    timer.start();
                    emit signal_StartTime(m_nTreadID);

                    pItemName = "Production Flow Start";
                    pItemValue = RUNNING;
                    UpdateTaskListItem(pItemName, pItemValue);
                    pItemValue = PASS;
                    UpdateTaskListItem(pItemName, pItemValue);

                    m_sScanData = Common::getInstance()->getScanData(m_nTreadID);
                    memcpy(m_szBarcode, m_sScanData.strBarcode, BARCODE_MAX_LENGTH-1);//init barcode to mes
                    if(TestItemList.contains(InputCodeItem::staticType()) && Common::getInstance()->getItemEnable(InputCodeItem::staticType()))
                    {
                        QJsonObject testCaseObj = Common::getInstance()->getJsonParams(InputCodeItem::staticType());
                        if(testCaseObj.isEmpty() )
                        {
                            UpdateTaskListItem("InputCode", FAIL);
                            UpdateUILog("InputCode Params is Empty!");
                            bPass = false;
                            spNextStage = EXIT_META_MODE;
                            break;
                        }

                        for(UINT i=0; i<E_UNKNOWN_CODE; i++)
                        {
                            if(!testCaseObj[ECodeStr[i]].toBool())
                            {
                                continue;
                            }

                            pItemName = "Input "+ECodeStr[i];
                            switch(i)
                            {
                            case WRITE_BARCODE:
                                UpdateTaskListItem(pItemName, PASS, m_sScanData.strBarcode);
                                break;
                            case WRITE_IMEI:
                                UpdateTaskListItem(pItemName, PASS, m_sScanData.strIMEI[0]);
                                break;
                            case WRITE_IMEI2:
                                UpdateTaskListItem(pItemName, PASS, m_sScanData.strIMEI[1]);
                                break;
                            case WRITE_MEID:
                                UpdateTaskListItem(pItemName, PASS, m_sScanData.strMeid);
                                break;
                            case WRITE_BT:
                                UpdateTaskListItem(pItemName, PASS, m_sScanData.strBTAddr);
                                break;
                            case WRITE_WIFI:
                                UpdateTaskListItem(pItemName, PASS, m_sScanData.strWifiAddr);
                                break;
                            case WRITE_ETHERNET_MAC:
                                UpdateTaskListItem(pItemName, PASS, m_sScanData.strEthernetMac);
                                break;
                            case WRITE_SERIALNO:
                                UpdateTaskListItem(pItemName, PASS, m_sScanData.strSerialNo);
                                break;
                            case WRITE_NETCODE:
                                UpdateTaskListItem(pItemName, PASS, m_sScanData.strNetCode);
                                break;
                            }
                        }
                    }

                    if(TestItemList.contains(InputYDCodesItem::staticType()) && Common::getInstance()->getItemEnable(InputYDCodesItem::staticType()))
                    {
                        QJsonObject testCaseObj = Common::getInstance()->getJsonParams(InputYDCodesItem::staticType());
                        if(testCaseObj.isEmpty())
                        {
                            UpdateTaskListItem("InputYDCodes", FAIL);
                            UpdateUILog("InputYDCodes Params is Empty!");
                            bPass = false;
                            spNextStage = EXIT_META_MODE;
                            break;
                        }

                        for(UINT i=0; i<E_UNKNOWN_YDCODE; i++)
                        {
                            if(!testCaseObj[EYDCodeStr[i]].toBool())
                            {
                                continue;
                            }

                            pItemName = "Input "+EYDCodeStr[i];
                            switch(i)
                            {
                            case WRITE_OBSN:
                                UpdateTaskListItem(pItemName, PASS, m_sScanData.strOBSN);
                                break;
                            case WRITE_IWSN:
                                UpdateTaskListItem(pItemName, PASS, m_sScanData.strIWSN);
                                break;
                            case WRITE_BATLABEL:
                                UpdateTaskListItem(pItemName, PASS, m_sScanData.strBatteryLabel);
                                break;
                            case WRITE_SCRILSF:
                                UpdateTaskListItem(pItemName, PASS, m_sScanData.strScrilSf);
                                break;
                            }
                        }
                    }

                    spNextStage = CONNECT_PWR;
                    break;
                }

                case CONNECT_PWR:
                {
                    if(NeedPowerSupply())
                    {
                        if(Common::getInstance()->getPowerType() == PowerType::BlueBird)
                        {
                            pItemName = "Connect BlueBird Power";
                            pItemValue = RUNNING;
                            UpdateTaskListItem(pItemName, pItemValue);
                            OpenPowerSupply();
                            iRet = META_SUCCESS;
                            UpdateTaskListItem(pItemName, PASS);
                            ControlUsb(false);
                        }
                        else
                        {
                            pItemName = "Connect Power By GPIB";
                            pItemValue = RUNNING;
                            UpdateTaskListItem(pItemName, pItemValue);
                            if(m_pVisaInstance->ConnectByGPIB())
                            {
                                iRet = META_SUCCESS;
                                pItemValue = PASS;
                                UpdateTaskListItem(pItemName, pItemValue);
                                InitPowerStatus();

                                pItemName = "PowerSupply On";
                                pItemValue = RUNNING;
                                UpdateTaskListItem(pItemName, pItemValue);

                                m_pVisaInstance->EnableOutput(OUTPUT_CHANNEL_1, true);
                                ControlUsb(false);

                                pItemValue = PASS;
                                UpdateTaskListItem(pItemName, pItemValue);

                            }
                            else
                            {
                                iRet = META_FAILED;
                                pItemValue = FAIL;
                                UpdateTaskListItem(pItemName, pItemValue);
                                bPass = false;
                                spNextStage = DISCONNECT_PWR_STAGE;
                                break;
                            }
                        }
                    }

                    spNextStage = TEST_BOTTOM_CURRENT;
                    break;
                }

                case TEST_BOTTOM_CURRENT:
                {
                    if(TestItemList.contains(BottomCurrentItem::staticType()) && Common::getInstance()->getItemEnable(BottomCurrentItem::staticType()))
                    {
                        pItemName = "Bottom Current Test";
                        pItemValue = RUNNING;
                        UpdateTaskListItem(pItemName, pItemValue);

                        int sleeptime=0;
                        while(sleeptime < Common::getInstance()->getCurrentSleepTime(BOTTOM_ITEM) && g_TestStopFlag[m_nTreadID] != BOOT_STOP)
                        {
                            sleeptime = sleeptime + 100;
                            Sleep(100);
                        }

                        if(g_TestStopFlag[m_nTreadID] == BOOT_STOP)
                        {
                            pItemValue = FAIL;
                            UpdateTaskListItem(pItemName, pItemValue);
                            bPass = false;
                            failflag = true;
                            spNextStage = DISCONNECT_PWR_STAGE;
                            break;
                        }

                        AverageValue = 0.0f;
                        if(!BottomCurrentTest(&AverageValue))
                        {
                            pItemValue = FAIL;
                            UpdateTaskListItem(pItemName, pItemValue, QString("%1 mA").arg(QString::number(AverageValue, 'f', 2)));
                            bPass = false;
                            failflag = true;
                            spNextStage = DISCONNECT_PWR_STAGE;
                            break;
                        }

                        pItemValue = PASS;
                        UpdateTaskListItem(pItemName, pItemValue, QString("%1 mA").arg(QString::number(AverageValue, 'f', 2)));
                    }

                    if((UsbControl == ControlBox && Common::getInstance()->getUseRelayEnable()) || NeedPowerSupply())
                        ControlUsb(true);

                    spNextStage = ENTER_META_MODE;
                    break;
                }

                case ENTER_META_MODE:
                {
                    if(TestItemList.contains(EnterModeItem::staticType()) && Common::getInstance()->getItemEnable(EnterModeItem::staticType()))
                    {
                        pItemName = "Enter Meta Mode";
                        pItemValue = RUNNING;
                        UpdateTaskListItem(pItemName, pItemValue);
                        iRet = m_pMetaInstance->BootMetaMode();
                        pItemValue = ((iRet == 0) ? PASS : FAIL);
                        UpdateTaskListItem(pItemName, pItemValue);
                        if (iRet != META_SUCCESS)
                        {
                            bPass = false;
                            spNextStage = EXIT_META_MODE;
                            break;
                        }

                        //Update Port Info
                        UpdateUIPortInfo();

                        spNextStage = INIT_APDB_STAGE;
                        break;
                    }

                    spNextStage = READBACK_DUT_INFO;
                    break;
                }

                case INIT_APDB_STAGE:
                {
                    pItemName = "Init AP DB";
                    pItemValue = RUNNING;
                    UpdateTaskListItem(pItemName, pItemValue);
                    iRet = m_pMetaInstance->InitAPMeta();
                    pItemValue = ((iRet == 0) ? PASS : FAIL);
                    UpdateTaskListItem(pItemName, pItemValue);
                    if (iRet != META_SUCCESS)
                    {
                        bPass = false;
                        spNextStage = EXIT_META_MODE;
                        break;
                    }

                    spNextStage = INIT_MDDB_STAGE;
                    break;
                }

                case INIT_MDDB_STAGE:
                {
                    if(!Common::getInstance()->getWifionlyEnable())
                    {
                        pItemName = "Init MD DB";
                        pItemValue = RUNNING;
                        UpdateTaskListItem(pItemName, pItemValue);
                        iRet = m_pMetaInstance->InitMDMeta();
                        pItemValue = ((iRet == 0) ? PASS : FAIL);
                        UpdateTaskListItem(pItemName, pItemValue);
                        if (iRet != META_SUCCESS)
                        {
                            bPass = false;
                            spNextStage = EXIT_META_MODE;
                            break;
                        }
                    }

                    spNextStage = READBACK_DUT_INFO;
                    break;
                }

                case READBACK_DUT_INFO:
                {
                    if(TestItemList.contains(LoadSnItem::staticType()) && Common::getInstance()->getItemEnable(LoadSnItem::staticType()))
                    {
                        pItemName = "Load SN";
                        pItemValue = RUNNING;
                        UpdateTaskListItem(pItemName, pItemValue);

                        if(!Common::getInstance()->getWifionlyEnable())
                            iRet = m_pMetaInstance->ReadBarcodeFromMd(m_szBarcode);
                        else
                            iRet = m_pMetaInstance->ReadBarcodeFromAp(m_szBarcode);

                        pItemValue = ((iRet == 0) ? PASS : FAIL);
                        UpdateTaskListItem(pItemName, pItemValue, (iRet == 0) ? QString(m_szBarcode):"");
                        if (iRet != META_SUCCESS)
                        {
                            bPass = false;
                            spNextStage = EXIT_META_MODE;
                            break;
                        }
                        else
                        {
                            UpdateBarcodeToUI(m_szBarcode);
                            UpdateUILog(QString("barcode: %1").arg(m_szBarcode));
                        }
                    }

                    spNextStage = READ_SW_VER_STAGE;
                    break;
                }

                case READ_SW_VER_STAGE:
                {
                    if(TestItemList.contains(ReadSwVerItem::staticType()) && Common::getInstance()->getItemEnable(ReadSwVerItem::staticType()))
                    {
                        QJsonObject testCaseObj = Common::getInstance()->getJsonParams(ReadSwVerItem::staticType());
                        if(testCaseObj.isEmpty())
                        {
                            UpdateTaskListItem("Read SW Version", FAIL);
                            UpdateUILog("Read SW Version Params is Empty!");
                            bPass = false;
                            spNextStage = EXIT_META_MODE;
                            break;
                        }

                        char SwVerProp[256] = { 0 }, m_szReadVer[PROP_VALUE_MAX] = { 0 };
                        _snprintf_s(SwVerProp, sizeof(SwVerProp)-1, "%s", testCaseObj["System Property"].toString().toStdString().c_str());

                        pItemName = "Read SW Version";
                        pItemValue = RUNNING;
                        UpdateTaskListItem(pItemName, pItemValue);

                        iRet = m_pMetaInstance->ReadSwVer(SwVerProp, m_szReadVer);
                        if(iRet != META_SUCCESS)
                        {
                            UpdateTaskListItem(pItemName, FAIL);
                            UpdateUILog(QString("ERROR!! ReadSwVer : MetaResult = %1").arg(Common::getInstance()->ResultToString_SP(iRet)));
                            bPass = false;
                            spNextStage = EXIT_META_MODE;
                            break;
                        }

                        if(!QString(m_szReadVer).contains(testCaseObj["verify"].toString()))
                        {
                            UpdateTaskListItem(pItemName, FAIL, m_szReadVer);
                            UpdateUILog(QString("ERROR!! ReadSwVer don't contain %1").arg(testCaseObj["verify"].toString()));
                            bPass = false;
                            spNextStage = EXIT_META_MODE;
                            break;
                        }
                        else
                        {
                            UpdateTaskListItem(pItemName, PASS, m_szReadVer);
                        }
                    }

                    spNextStage = CHECK_BARCODE_FLAG_STAGE;
                    break;
                }

                case CHECK_BARCODE_FLAG_STAGE:
                {
                    if(TestItemList.contains(CheckBarcodeFlagItem::staticType()) && Common::getInstance()->getItemEnable(CheckBarcodeFlagItem::staticType()))
                    {
                        pItemName = "Check Barcode Flag";
                        pItemValue = RUNNING;
                        UpdateTaskListItem(pItemName, pItemValue);

                        bPass = m_pMetaInstance->CheckBarcodeFlag();

                        pItemValue = (bPass ? PASS : FAIL);
                        UpdateTaskListItem(pItemName, pItemValue);
                        if (!bPass)
                        {
                            UpdateUILog("ERROR!! Check Barcode Flag Fail !");
                            spNextStage = EXIT_META_MODE;
                            break;
                        }
                        else
                        {
                            UpdateUILog("Check Barcode Flag Success!");
                        }
                    }

                    spNextStage = WRITE_CODES_STAGE;
                    break;
                }

                case WRITE_CODES_STAGE:
                {
                    if(TestItemList.contains(WriteCodesItem::staticType()) && Common::getInstance()->getItemEnable(WriteCodesItem::staticType()))
                    {
                        QJsonObject testCaseObj = Common::getInstance()->getJsonParams(InputCodeItem::staticType());
                        if(testCaseObj.isEmpty() && Common::getInstance()->getItemEnable(InputCodeItem::staticType()))
                        {
                            UpdateTaskListItem("WriteCodes", FAIL);
                            UpdateUILog("WriteCodes Params is Empty!");
                            bPass = false;
                            spNextStage = EXIT_META_MODE;
                            break;
                        }

                        if(Common::getInstance()->getItemEnable(InputCodeItem::staticType()))
                        {
                            for(UINT i=0; i<WRITE_SERIALNO; i++)
                            {
                                if(!testCaseObj[ECodeStr[i]].toBool())
                                {
                                    continue;
                                }

                                switch(i)
                                {
                                case WRITE_BARCODE:
                                    if(Common::getInstance()->getWifionlyEnable())
                                    {
                                        break;
                                    }

                                    pItemName = "Write Barcode";
                                    pItemValue = RUNNING;
                                    UpdateTaskListItem(pItemName, pItemValue);

                                    iRet = m_pMetaInstance->REQ_WriteModem_NVRAM_Start(WRITE_BARCODE, m_sScanData.strBarcode, 1);

                                    pItemValue = ((iRet == 0) ? PASS : FAIL);
                                    UpdateTaskListItem(pItemName, pItemValue, QString(m_sScanData.strBarcode));
                                    if (iRet != META_SUCCESS)
                                    {
                                        UpdateUILog(QString("ERROR!! Barcode : MetaResult = %1").arg(Common::getInstance()->ResultToString_SP(iRet)));
                                        bPass = false;
                                        spNextStage = EXIT_META_MODE;
                                        break;
                                    }
                                    else
                                    {
                                        UpdateUILog(QString("Write barcode: %1").arg(m_sScanData.strBarcode));
                                    }

                                    break;

                                case WRITE_IMEI:
                                    if(Common::getInstance()->getWifionlyEnable())
                                    {
                                        break;
                                    }

                                    pItemName = "Write IMEI1";
                                    pItemValue = RUNNING;
                                    UpdateTaskListItem(pItemName, pItemValue);

                                    iRet = m_pMetaInstance->REQ_WriteModem_NVRAM_Start(WRITE_IMEI, m_sScanData.strIMEI[0], 1);

                                    pItemValue = ((iRet == 0) ? PASS : FAIL);
                                    UpdateTaskListItem(pItemName, pItemValue, QString(m_sScanData.strIMEI[0]));
                                    if (iRet != META_SUCCESS)
                                    {
                                        UpdateUILog(QString("ERROR!! IMEI1 : MetaResult = %1").arg(Common::getInstance()->ResultToString_SP(iRet)));
                                        bPass = false;
                                        spNextStage = EXIT_META_MODE;
                                        break;
                                    }
                                    else
                                    {
                                        UpdateUILog(QString("Write IMEI1: %1").arg(m_sScanData.strIMEI[0]));
                                    }

                                    break;

                                case WRITE_IMEI2:
                                    if(Common::getInstance()->getWifionlyEnable())
                                    {
                                        break;
                                    }

                                    pItemName = "Write IMEI2";
                                    pItemValue = RUNNING;
                                    UpdateTaskListItem(pItemName, pItemValue);

                                    iRet = m_pMetaInstance->REQ_WriteModem_NVRAM_Start(WRITE_IMEI, m_sScanData.strIMEI[1], 2);

                                    pItemValue = ((iRet == 0) ? PASS : FAIL);
                                    UpdateTaskListItem(pItemName, pItemValue, QString(m_sScanData.strIMEI[1]));
                                    if (iRet != META_SUCCESS)
                                    {
                                        UpdateUILog(QString("ERROR!! IMEI2 : MetaResult = %1").arg(Common::getInstance()->ResultToString_SP(iRet)));
                                        bPass = false;
                                        spNextStage = EXIT_META_MODE;
                                        break;
                                    }
                                    else
                                    {
                                        UpdateUILog(QString("Write IMEI2: %1").arg(m_sScanData.strIMEI[0]));
                                    }

                                    break;

                                case WRITE_MEID:
                                    if(Common::getInstance()->getWifionlyEnable())
                                    {
                                        break;
                                    }

                                    pItemName = "Write MEID";
                                    pItemValue = RUNNING;
                                    UpdateTaskListItem(pItemName, pItemValue);

                                    if (m_pMetaInstance->m_iC2kProject != 0)
                                        iRet = m_pMetaInstance->EnterC2KGen90();
                                    else
                                        iRet = (META_RESULT)m_pMetaInstance->EnterC2KGen93();

                                    pItemValue = ((iRet == 0) ? PASS : FAIL);
                                    UpdateTaskListItem(pItemName, pItemValue, QString(m_sScanData.strMeid));
                                    if (iRet != META_SUCCESS)
                                    {
                                        bPass = false;
                                        spNextStage = EXIT_META_MODE;
                                        break;
                                    }
                                    else
                                    {
                                        UpdateUILog(QString("Write MEID: %1").arg(m_sScanData.strMeid));
                                    }

                                    break;

                                case WRITE_BT:
                                    pItemName = "Write BT";
                                    pItemValue = RUNNING;
                                    UpdateTaskListItem(pItemName, pItemValue);

                                    iRet = m_pMetaInstance->REQ_WriteAP_NVRAM_Start(WRITE_BT, m_sScanData.strBTAddr, 1);

                                    pItemValue = ((iRet == 0) ? PASS : FAIL);
                                    UpdateTaskListItem(pItemName, pItemValue, QString(m_sScanData.strBTAddr));
                                    if (iRet != META_SUCCESS)
                                    {
                                        UpdateUILog(QString("ERROR!! BT : MetaResult = %1").arg(Common::getInstance()->ResultToString_SP(iRet)));
                                        bPass = false;
                                        spNextStage = EXIT_META_MODE;
                                        break;
                                    }
                                    else
                                    {
                                        UpdateUILog(QString("Write BT: %1").arg(m_sScanData.strBTAddr));
                                    }

                                    break;

                                case WRITE_WIFI:
                                    pItemName = "Write WIFI";
                                    pItemValue = RUNNING;
                                    UpdateTaskListItem(pItemName, pItemValue);

                                    iRet = m_pMetaInstance->REQ_WriteAP_NVRAM_Start(WRITE_WIFI, m_sScanData.strWifiAddr, 1);

                                    pItemValue = ((iRet == 0) ? PASS : FAIL);
                                    UpdateTaskListItem(pItemName, pItemValue, QString(m_sScanData.strWifiAddr));
                                    if (iRet != META_SUCCESS)
                                    {
                                        UpdateUILog(QString("ERROR!! Wifi : MetaResult = %1").arg(Common::getInstance()->ResultToString_SP(iRet)));
                                        bPass = false;
                                        spNextStage = EXIT_META_MODE;
                                        break;
                                    }
                                    else
                                    {
                                        UpdateUILog(QString("Write WIFI: %1").arg(m_sScanData.strWifiAddr));
                                    }

                                    break;

                                case WRITE_ETHERNET_MAC:
                                    pItemName = "Write Ethernet Mac";
                                    pItemValue = RUNNING;
                                    UpdateTaskListItem(pItemName, pItemValue);

                                    iRet = m_pMetaInstance->REQ_WriteAP_NVRAM_Start(WRITE_ETHERNET_MAC, m_sScanData.strEthernetMac, 1);

                                    pItemValue = ((iRet == 0) ? PASS : FAIL);
                                    UpdateTaskListItem(pItemName, pItemValue, QString(m_sScanData.strEthernetMac));
                                    if (iRet != META_SUCCESS)
                                    {
                                        UpdateUILog(QString("ERROR!! Ethernet Mac : MetaResult = %1").arg(Common::getInstance()->ResultToString_SP(iRet)));
                                        bPass = false;
                                        spNextStage = EXIT_META_MODE;
                                        break;
                                    }
                                    else
                                    {
                                        UpdateUILog(QString("Write Ethernet Mac: %1").arg(m_sScanData.strEthernetMac));
                                    }

                                    break;
                                }

                                if(!bPass)
                                    break;
                            }
                        }

                        if(!bPass)
                        {
                            spNextStage = EXIT_META_MODE;
                            break;
                        }
                    }

                    spNextStage = SP_PRODINFO_STAGE;
                    break;
                }

                case SP_PRODINFO_STAGE:
                {
                    if (TestItemList.contains(WriteCodesItem::staticType()) && Common::getInstance()->getItemEnable(WriteCodesItem::staticType()))
                    {
                        pItemName = "Write ProdInfo";
                        pItemValue = RUNNING;
                        UpdateTaskListItem(pItemName, pItemValue);

                        MTRACE(m_hDebugTrace[m_nTreadID], "Prod_Info = \"...\"");
                        iRet = m_pMetaInstance->REQ_WriteAP_PRODINFO_Start();

                        pItemValue = ((iRet == META_SUCCESS) ? PASS : FAIL);
                        UpdateTaskListItem(pItemName, pItemValue);
                        if (iRet != META_SUCCESS)
                        {
                           UpdateUILog(QString("ERROR!! Prod_Info : MetaResult = %1").arg(Common::getInstance()->ResultToString_SP(iRet)));
                            bPass = false;
                            spNextStage = EXIT_META_MODE;
                            break;
                        }
                        else
                        {
                            UpdateUILog("Write ProdInfo Success!");
                        }
                    }

                    spNextStage = TEE_SUPPORT_STAGE;
                    break;
                }

                case TEE_SUPPORT_STAGE:
                {
                    if (TestItemList.contains(TeeSupportItem::staticType()) && Common::getInstance()->getItemEnable(TeeSupportItem::staticType()))
                    {
                        pItemName = "Tee Support";
                        pItemValue = RUNNING;
                        UpdateTaskListItem(pItemName, pItemValue);

                        iRet = m_pMetaInstance->TEE_Authorize();

                        pItemValue = ((iRet == META_SUCCESS) ? PASS : FAIL);
                        UpdateTaskListItem(pItemName, pItemValue);
                        if (iRet != META_SUCCESS)
                        {
                            UpdateUILog(QString("ERROR!! TEE_Authorize : MetaResult = %1").arg(Common::getInstance()->ResultToString_SP(iRet)));
                            bPass = false;
                            spNextStage = EXIT_META_MODE;
                            break;
                        }
                        else
                        {
                            UpdateUILog("Tee Support Success!");
                        }
                    }

                    spNextStage = CHECK_CDOES_STAGE;
                    break;
                }

                case CHECK_CDOES_STAGE:
                {
                    if (TestItemList.contains(CheckCodesItem::staticType()) && Common::getInstance()->getItemEnable(CheckCodesItem::staticType()))
                    {
                        m_pMetaInstance->SNThread_Init();
                        QJsonObject testCaseObj = Common::getInstance()->getJsonParams(InputCodeItem::staticType());
                        if(testCaseObj.isEmpty() && Common::getInstance()->getItemEnable(InputCodeItem::staticType()))
                        {
                            UpdateTaskListItem("CheckCodes", FAIL);
                            UpdateUILog("InputCode Params is Empty!");
                            bPass = false;
                            spNextStage = EXIT_META_MODE;
                            break;
                        }

                        QJsonObject YDtestCaseObj = Common::getInstance()->getJsonParams(InputYDCodesItem::staticType());
                        if(YDtestCaseObj.isEmpty() && Common::getInstance()->getItemEnable(InputYDCodesItem::staticType()))
                        {
                            UpdateTaskListItem("CheckCodes", FAIL);
                            UpdateUILog("InputYDCodes Params is Empty!");
                            bPass = false;
                            spNextStage = EXIT_META_MODE;
                            break;
                        }

                        if(Common::getInstance()->getItemEnable(InputCodeItem::staticType()))
                        {
                            for(UINT i=0; i<E_UNKNOWN_CODE; i++)
                            {
                                if(!testCaseObj[ECodeStr[i]].toBool())
                                {
                                    continue;
                                }

                                switch(i)
                                {
                                case WRITE_BARCODE:
                                    pItemName = "Check Barcode";
                                    pItemValue = RUNNING;
                                    UpdateTaskListItem(pItemName, pItemValue);

                                    if(Common::getInstance()->getWifionlyEnable())
                                    {
                                        bPass = m_pMetaInstance->CheckAPBarcode(m_szBarcode, sizeof(m_szBarcode));
                                    }
                                    else
                                    {
                                        bPass = m_pMetaInstance->CheckMdBarcode(m_szBarcode, sizeof(m_szBarcode));
                                        if(bPass)
                                        {
                                            bPass = m_pMetaInstance->CheckAPBarcode(m_szBarcode, sizeof(m_szBarcode));
                                        }
                                    }

                                    pItemValue = (bPass ? PASS : FAIL);
                                    UpdateTaskListItem(pItemName, pItemValue, QString(m_sScanData.strBarcode));
                                    if (!bPass)
                                    {
                                        UpdateUILog("ERROR!! Check barcode Fail !");
                                        spNextStage = EXIT_META_MODE;
                                        break;
                                    }
                                    else
                                    {
                                        UpdateUILog(QString("Check barcode: %1").arg(m_sScanData.strBarcode));
                                    }

                                    break;

                                case WRITE_IMEI:
                                    if(Common::getInstance()->getWifionlyEnable())
                                    {
                                        break;
                                    }

                                    pItemName = "Check IMEI1";
                                    pItemValue = RUNNING;
                                    UpdateTaskListItem(pItemName, pItemValue);

                                    bPass = m_pMetaInstance->CheckIMEI1(m_szIMEI[0]);

                                    pItemValue = (bPass ? PASS : FAIL);
                                    UpdateTaskListItem(pItemName, pItemValue, QString(m_sScanData.strIMEI[0]));
                                    if (!bPass)
                                    {
                                        UpdateUILog("ERROR!! Check IMEI1 Fail !");
                                        spNextStage = EXIT_META_MODE;
                                        break;
                                    }
                                    else
                                    {
                                        UpdateUILog(QString("Check IMEI1: %1").arg(m_sScanData.strIMEI[0]));
                                    }

                                    break;

                                case WRITE_IMEI2:
                                    if(Common::getInstance()->getWifionlyEnable())
                                    {
                                        break;
                                    }

                                    pItemName = "Check IMEI2";
                                    pItemValue = RUNNING;
                                    UpdateTaskListItem(pItemName, pItemValue);

                                    bPass = m_pMetaInstance->CheckIMEI2(m_szIMEI[1]);

                                    pItemValue = (bPass ? PASS : FAIL);
                                    UpdateTaskListItem(pItemName, pItemValue, QString(m_sScanData.strIMEI[1]));
                                    if (!bPass)
                                    {
                                        UpdateUILog("ERROR!! Check IMEI2 Fail !");
                                        spNextStage = EXIT_META_MODE;
                                        break;
                                    }
                                    else
                                    {
                                        UpdateUILog(QString("Check IMEI2: %1").arg(m_sScanData.strIMEI[1]));
                                    }

                                    break;

                                case WRITE_MEID:
                                    if(Common::getInstance()->getWifionlyEnable())
                                    {
                                        break;
                                    }

                                    pItemName = "Check MEID";
                                    pItemValue = RUNNING;
                                    UpdateTaskListItem(pItemName, pItemValue);

                                    bPass = m_pMetaInstance->CheckMEID(m_szMeid);

                                    pItemValue = (bPass ? PASS : FAIL);
                                    UpdateTaskListItem(pItemName, pItemValue, QString(m_sScanData.strMeid));
                                    if (!bPass)
                                    {
                                        UpdateUILog("ERROR!! Check MEID Fail !");
                                        spNextStage = EXIT_META_MODE;
                                        break;
                                    }
                                    else
                                    {
                                        UpdateUILog(QString("Check MEID: %1").arg(m_sScanData.strMeid));
                                    }

                                    break;

                                case WRITE_BT:
                                    pItemName = "Check BtMac";
                                    pItemValue = RUNNING;
                                    UpdateTaskListItem(pItemName, pItemValue);

                                    Sleep(500);
                                    bPass = m_pMetaInstance->CheckBtAddr(m_szBtAddr);

                                    pItemValue = (bPass ? PASS : FAIL);
                                    UpdateTaskListItem(pItemName, pItemValue, QString(m_sScanData.strBTAddr));
                                    if (!bPass)
                                    {
                                        UpdateUILog("ERROR!! Check BtMac Fail !");
                                        spNextStage = EXIT_META_MODE;
                                        break;
                                    }
                                    else
                                    {
                                        UpdateUILog(QString("Check BtMac: %1").arg(m_sScanData.strBTAddr));
                                    }

                                    break;

                                case WRITE_WIFI:
                                    pItemName = "Check WifiMac";
                                    pItemValue = RUNNING;
                                    UpdateTaskListItem(pItemName, pItemValue);

                                    bPass = m_pMetaInstance->CheckWifiMac(m_szWifiAddr);

                                    pItemValue = (bPass ? PASS : FAIL);
                                    UpdateTaskListItem(pItemName, pItemValue, QString(m_sScanData.strWifiAddr));
                                    if (!bPass)
                                    {
                                        UpdateUILog("ERROR!! Check WifiMac Fail !");
                                        spNextStage = EXIT_META_MODE;
                                        break;
                                    }
                                    else
                                    {
                                        UpdateUILog(QString("Check WifiMac: %1").arg(m_sScanData.strWifiAddr));
                                    }

                                    break;

                                case WRITE_ETHERNET_MAC:
                                    pItemName = "Check EthernetMac";
                                    pItemValue = RUNNING;
                                    UpdateTaskListItem(pItemName, pItemValue);

                                    bPass = m_pMetaInstance->CheckEthernetMac();

                                    pItemValue = (bPass ? PASS : FAIL);
                                    UpdateTaskListItem(pItemName, pItemValue, QString(m_sScanData.strEthernetMac));
                                    if (!bPass)
                                    {
                                        UpdateUILog("ERROR!! Check EthernetMac Fail !");
                                        spNextStage = EXIT_META_MODE;
                                        break;
                                    }
                                    else
                                    {
                                        UpdateUILog(QString("Check EthernetMac: %1").arg(m_sScanData.strEthernetMac));
                                    }

                                    break;

                                case WRITE_SERIALNO:
                                    pItemName = "Check Serialno";
                                    pItemValue = RUNNING;
                                    UpdateTaskListItem(pItemName, pItemValue);

                                    bPass = m_pMetaInstance->CheckSerialNo();

                                    pItemValue = (bPass ? PASS : FAIL);
                                    UpdateTaskListItem(pItemName, pItemValue, QString(m_sScanData.strSerialNo));
                                    if (!bPass)
                                    {
                                        UpdateUILog("ERROR!! Check Serialno Fail !");
                                        spNextStage = EXIT_META_MODE;
                                        break;
                                    }
                                    else
                                    {
                                        UpdateUILog(QString("Check Serialno: %1").arg(m_sScanData.strSerialNo));
                                    }

                                    break;

                                case WRITE_NETCODE:
                                    pItemName = "Check NetCode";
                                    pItemValue = RUNNING;
                                    UpdateTaskListItem(pItemName, pItemValue);

                                    bPass = m_pMetaInstance->CheckNetCode();

                                    pItemValue = (bPass ? PASS : FAIL);
                                    UpdateTaskListItem(pItemName, pItemValue, QString(m_sScanData.strNetCode));
                                    if (!bPass)
                                    {
                                        UpdateUILog("ERROR!! Check NetCode Fail !");
                                        spNextStage = EXIT_META_MODE;
                                        break;
                                    }
                                    else
                                    {
                                        UpdateUILog(QString("Check NetCode: %1").arg(m_sScanData.strNetCode));
                                    }

                                    break;
                                }

                                if(!bPass)
                                    break;
                            }

                            if(!bPass)
                            {
                                spNextStage = EXIT_META_MODE;
                                break;
                            }
                        }

                        if(Common::getInstance()->getItemEnable(InputYDCodesItem::staticType()))
                        {
                            for(UINT i=0; i<E_UNKNOWN_YDCODE; i++)
                            {
                                if(!YDtestCaseObj[EYDCodeStr[i]].toBool() || i == WRITE_BATLABEL)//The battery label does not need to be checked
                                {
                                    continue;
                                }

                                switch(i)
                                {
                                case WRITE_OBSN:
                                    pItemName = "Check OBSN";
                                    pItemValue = RUNNING;
                                    UpdateTaskListItem(pItemName, pItemValue);

                                    if(Common::getInstance()->getWifionlyEnable())
                                    {
                                        bPass = m_pMetaInstance->CheckAPSN(m_szBarcode, sizeof(m_szBarcode), 0);
                                    }
                                    else
                                    {
                                        bPass = m_pMetaInstance->CheckMdSN(m_szBarcode, sizeof(m_szBarcode), 0);
                                    }

                                    pItemValue = (bPass ? PASS : FAIL);
                                    UpdateTaskListItem(pItemName, pItemValue, QString(m_sScanData.strOBSN));
                                    if (!bPass)
                                    {
                                        UpdateUILog("ERROR!! Check OBSN Fail !");
                                        spNextStage = EXIT_META_MODE;
                                        break;
                                    }
                                    else
                                    {
                                        UpdateUILog(QString("Check OBSN: %1").arg(m_sScanData.strOBSN));
                                    }

                                    break;

                                case WRITE_IWSN:
                                    pItemName = "Check IWSN";
                                    pItemValue = RUNNING;
                                    UpdateTaskListItem(pItemName, pItemValue);

                                    if(Common::getInstance()->getWifionlyEnable())
                                    {
                                        bPass = m_pMetaInstance->CheckAPSN(m_szBarcode, sizeof(m_szBarcode), 1);
                                    }
                                    else
                                    {
                                        bPass = m_pMetaInstance->CheckMdSN(m_szBarcode, sizeof(m_szBarcode), 1);
                                    }

                                    pItemValue = (bPass ? PASS : FAIL);
                                    UpdateTaskListItem(pItemName, pItemValue, QString(m_sScanData.strIWSN));
                                    if (!bPass)
                                    {
                                        UpdateUILog("ERROR!! Check IWSN Fail !");
                                        spNextStage = EXIT_META_MODE;
                                        break;
                                    }
                                    else
                                    {
                                        UpdateUILog(QString("Check IWSN: %1").arg(m_sScanData.strIWSN));
                                    }

                                    break;

                                case WRITE_SCRILSF:
                                    pItemName = "Check ScrilSf";
                                    pItemValue = RUNNING;
                                    UpdateTaskListItem(pItemName, pItemValue);

                                    bPass = m_pMetaInstance->CheckScrilSf(0);

                                    pItemValue = (bPass ? PASS : FAIL);
                                    UpdateTaskListItem(pItemName, pItemValue, QString(m_sScanData.strScrilSf));
                                    if (!bPass)
                                    {
                                        UpdateUILog("ERROR!! Check ScrilSf Fail !");
                                        spNextStage = EXIT_META_MODE;
                                        break;
                                    }
                                    else
                                    {
                                        UpdateUILog(QString("Check ScrilSf: %1").arg(m_sScanData.strScrilSf));
                                    }

                                    break;
                                }

                                if(!bPass)
                                    break;
                            }

                            if(!bPass)
                            {
                                spNextStage = EXIT_META_MODE;
                                break;
                            }
                        }
                    }

                    spNextStage = CHECK_CARD_STAGE;
                    break;
                }

                case CHECK_CARD_STAGE:
                {
                    if(TestItemList.contains(CheckCardItem::staticType()) && Common::getInstance()->getItemEnable(CheckCardItem::staticType()))
                    {
                        QJsonObject testCaseObj = Common::getInstance()->getJsonParams(CheckCardItem::staticType());
                        if(testCaseObj.isEmpty())
                        {
                            UpdateTaskListItem("CheckCodes", FAIL);
                            UpdateUILog("CheckCodes Params is Empty!");
                            bPass = false;
                            spNextStage = EXIT_META_MODE;
                            break;
                        }

                        if(testCaseObj["Check SIM1"].toBool())
                        {
                            pItemName = "Check SIM1";
                            pItemValue = RUNNING;
                            UpdateTaskListItem(pItemName, pItemValue);

                            bPass = m_pMetaInstance->CheckSIM(0);

                            pItemValue = (bPass ? PASS : FAIL);
                            UpdateTaskListItem(pItemName, pItemValue);
                            if (!bPass)
                            {
                                UpdateUILog("ERROR!! Check SIM1 Fail !");
                                spNextStage = EXIT_META_MODE;
                                break;
                            }
                            else
                            {
                                UpdateUILog("Check SIM1 Pass !");
                            }
                        }

                        if(testCaseObj["Check SIM2"].toBool())
                        {
                            pItemName = "Check SIM2";
                            pItemValue = RUNNING;
                            UpdateTaskListItem(pItemName, pItemValue);

                            bPass = m_pMetaInstance->CheckSIM(1);

                            pItemValue = (bPass ? PASS : FAIL);
                            UpdateTaskListItem(pItemName, pItemValue);
                            if (!bPass)
                            {
                                UpdateUILog("ERROR!! Check SIM2 Fail !");
                                spNextStage = EXIT_META_MODE;
                                break;
                            }
                            else
                            {
                                UpdateUILog("Check SIM2 Pass !");
                            }
                        }

                        if(testCaseObj["Check TCard"].toBool())
                        {
                            pItemName = "Check TCard";
                            pItemValue = RUNNING;
                            UpdateTaskListItem(pItemName, pItemValue);

                            bPass = m_pMetaInstance->CheckTCard();

                            pItemValue = (bPass ? PASS : FAIL);
                            UpdateTaskListItem(pItemName, pItemValue);
                            if (!bPass)
                            {
                                UpdateUILog("ERROR!! Check TCard Fail !");
                                spNextStage = EXIT_META_MODE;
                                break;
                            }
                            else
                            {
                                UpdateUILog("Check TCard Pass !");
                            }
                        }

                        if(testCaseObj["Check NoCard"].toBool())
                        {
                            pItemName = "Check NoCard";
                            pItemValue = RUNNING;
                            UpdateTaskListItem(pItemName, pItemValue);

                            bPass = (m_pMetaInstance->CheckTCard()==false &&
                                     m_pMetaInstance->CheckSIM(0)==false &&
                                     m_pMetaInstance->CheckSIM(1)==false);

                            pItemValue = (bPass ? PASS : FAIL);
                            UpdateTaskListItem(pItemName, pItemValue);
                            if (!bPass)
                            {
                                UpdateUILog("ERROR!! Check NoCard Fail !");
                                spNextStage = EXIT_META_MODE;
                                break;
                            }
                            else
                            {
                                UpdateUILog("Check NoCard Pass !");
                            }
                        }
                    }

                    spNextStage = CHECK_BATLEVEL_STAGE;
                    break;
                }

                case CHECK_BATLEVEL_STAGE:
                {
                    if(TestItemList.contains(CheckBatLevelItem::staticType()) && Common::getInstance()->getItemEnable(CheckBatLevelItem::staticType()))
                    {
                        QJsonObject testCaseObj = Common::getInstance()->getJsonParams(CheckBatLevelItem::staticType());
                        if(testCaseObj.isEmpty())
                        {
                            UpdateTaskListItem("Check BatLevel", FAIL);
                            UpdateUILog("Check BatLevel Params is Empty!");
                            bPass = false;
                            spNextStage = EXIT_META_MODE;
                            break;
                        }

                        pItemName = "Check BatLevel";
                        pItemValue = RUNNING;
                        UpdateTaskListItem(pItemName, pItemValue);

                        int Level = 0;
                        iRet = m_pMetaInstance->GetBatLevel(&Level);
                        if (iRet != META_SUCCESS)
                        {
                            UpdateUILog("ERROR!! Get BatLevel Fail !");
                            UpdateTaskListItem(pItemName, FAIL);
                            spNextStage = EXIT_META_MODE;
                            break;
                        }
                        else
                        {
                            if(testCaseObj["Min"].toString().toInt() > Level || testCaseObj["Max"].toString().toInt() < Level)
                            {
                                UpdateUILog(QString("ERROR!! The battery level(%1) is not within the range !").arg(Level));
                                UpdateTaskListItem(pItemName, FAIL);
                                spNextStage = EXIT_META_MODE;
                                break;
                            }

                            UpdateUILog(QString("Check BatLevel(%1) Pass !").arg(Level));
                            UpdateTaskListItem(pItemName, PASS);
                        }
                    }

                    spNextStage = TEE_CHECK_STAGE;
                    break;
                }

                case TEE_CHECK_STAGE:
                {
                    if (TestItemList.contains(TeeCheckItem::staticType()) && Common::getInstance()->getItemEnable(TeeCheckItem::staticType()))
                    {
                        pItemName = "Tee Check";
                        pItemValue = RUNNING;
                        UpdateTaskListItem(pItemName, pItemValue);

                        iRet = m_pMetaInstance->TEE_Check();

                        pItemValue = ((iRet == META_SUCCESS) ? PASS : FAIL);
                        UpdateTaskListItem(pItemName, pItemValue);
                        if (iRet != META_SUCCESS)
                        {
                            bPass = false;
                            spNextStage = EXIT_META_MODE;
                            break;
                        }
                        else
                        {
                            UpdateUILog("Tee Check Success!");
                        }
                    }

                    spNextStage = SP_BACKUP_NVRAM_STAGE;
                    break;
                }

                case SP_BACKUP_NVRAM_STAGE:
                {
                    //Backup in advance bacause need to Factoryreset
                    if(bPass)
                    {
                        pItemName = "Backup NvRam to BinRegion";
                        UpdateTaskListItem(pItemName, RUNNING);
                        iRet = m_pMetaInstance->REQ_BackupNvram2BinRegion_Start();
                        pItemValue = (iRet == META_SUCCESS ? PASS : FAIL);
                        UpdateTaskListItem(pItemName, pItemValue);
                        if (iRet != META_SUCCESS)
                        {
                            bPass = false;
                            spNextStage = EXIT_META_MODE;
                            break;
                        }
                    }

                    spNextStage = FACTORY_RESET_STAGE;
                    break;
                }

                case FACTORY_RESET_STAGE:
                {
                    if(TestItemList.contains(FactoryResetItem::staticType()) && Common::getInstance()->getItemEnable(FactoryResetItem::staticType()))
                    {
                        pItemName = "Factory Reset";
                        pItemValue = RUNNING;
                        UpdateTaskListItem(pItemName, pItemValue);

                        bPass = m_pMetaInstance->FactoryReset();

                        pItemValue = (bPass ? PASS : FAIL);
                        UpdateTaskListItem(pItemName, pItemValue);
                        if (!bPass)
                        {
                            spNextStage = EXIT_META_MODE;
                            break;
                        }

                        UpdateUILog("Factory Reset Success!");
                    }

                    spNextStage = REBOOT_STAGE;
                    break;
                }

                case REBOOT_STAGE:
                {
                    if(TestItemList.contains(RebootItem::staticType()) && Common::getInstance()->getItemEnable(RebootItem::staticType()) && bPass)
                    {
                        pItemName = "Reboot";
                        pItemValue = RUNNING;
                        UpdateTaskListItem(pItemName, pItemValue);

                        iRet = m_pMetaInstance->ExitMetaModeToReboot();

                        pItemValue = (iRet == META_SUCCESS ? PASS : FAIL);
                        UpdateTaskListItem(pItemName, pItemValue);
                        if (iRet != META_SUCCESS)
                        {
                            bPass = false;
                            spNextStage = DISCONNECT_PWR_STAGE;
                            break;
                        }

                        m_pMetaInstance->WaitKernelPortDisappear();
                        if((TestItemList.contains(PowerOnCurrentItem::staticType()) && Common::getInstance()->getItemEnable(PowerOnCurrentItem::staticType()) ||
                             TestItemList.contains(DeepSleepCurrentItem::staticType()) && Common::getInstance()->getItemEnable(DeepSleepCurrentItem::staticType()))
                            && !failflag && bPass)
                        {
                            if(UsbControl == Manual)
                                UpdateUILog("Please DisConnect Usb....!");
                            ControlUsb(false);
                        }
                        Sleep(3000);

                        UpdateUILog("Reboot Success!");
                        if(!failflag && bPass)
                            spNextStage = TEST_PWRON_CURRENT;
                        else
                            spNextStage = DISCONNECT_PWR_STAGE;

                        break;
                    }

                    spNextStage = EXIT_META_MODE;
                    break;
                }

                case EXIT_META_MODE:
                {
                    //Exit Text
                    if(TestItemList.contains(PowerOffItem::staticType()) && Common::getInstance()->getItemEnable(PowerOffItem::staticType()))
                    {
                        pItemName = "Exit Meta Mode";
                        pItemValue = RUNNING;
                        UpdateTaskListItem(pItemName, pItemValue);

                        iRet = m_pMetaInstance->ExitMetaMode();

                        pItemValue = ((iRet == 0) ? PASS : FAIL);
                        UpdateTaskListItem(pItemName, pItemValue);

                        m_pMetaInstance->WaitKernelPortDisappear();
                        Sleep(3000);
                    }

                    spNextStage = DISCONNECT_PWR_STAGE;
                    break;
                }

                case TEST_PWRON_CURRENT:
                {
                    if(TestItemList.contains(PowerOnCurrentItem::staticType()) && Common::getInstance()->getItemEnable(PowerOnCurrentItem::staticType()))
                    {
                        pItemName = "PowerOn Current Test";
                        pItemValue = RUNNING;
                        UpdateTaskListItem(pItemName, pItemValue);

                        int sleeptime=0;
                        while(sleeptime < Common::getInstance()->getCurrentSleepTime(POWERON_ITEM) && g_TestStopFlag[m_nTreadID] != BOOT_STOP)
                        {
                            sleeptime = sleeptime + 100;
                            Sleep(100);
                        }

                        if(g_TestStopFlag[m_nTreadID] == BOOT_STOP)
                        {
                            pItemValue = FAIL;
                            UpdateTaskListItem(pItemName, pItemValue);
                            bPass = false;
                            failflag = true;
                            spNextStage = DISCONNECT_PWR_STAGE;
                            break;
                        }

                        AverageValue = MaxValue = 0.0f;
                        if(!PowerOnCurrentTest(&AverageValue, &MaxValue))
                        {
                            pItemValue = FAIL;
                            UpdateTaskListItem("PowerOn Ave", pItemValue, QString("%1 mA").arg(QString::number(AverageValue,'f',2)));
                            UpdateTaskListItem("PowerOn Max", pItemValue, QString("%1 mA").arg(QString::number(MaxValue,'f',2)));
                            UpdateTaskListItem(pItemName, pItemValue);
                            bPass = false;
                            spNextStage = DISCONNECT_PWR_STAGE;
                            break;
                        }
                        else
                        {
                            pItemValue = PASS;
                            UpdateTaskListItem("PowerOn Ave", pItemValue, QString("%1 mA").arg(QString::number(AverageValue,'f',2)));
                            UpdateTaskListItem("PowerOn Max", pItemValue, QString("%1 mA").arg(QString::number(MaxValue,'f',2)));
                            UpdateTaskListItem(pItemName, pItemValue);
                        }
                    }

                    spNextStage = TEST_DEELSLEEP_CURRENT;
                    break;
                }

                case TEST_DEELSLEEP_CURRENT:
                {
                    if(TestItemList.contains(DeepSleepCurrentItem::staticType()) && Common::getInstance()->getItemEnable(DeepSleepCurrentItem::staticType()))
                    {
                        pItemName = "DeepSleep Current Test";
                        pItemValue = RUNNING;
                        UpdateTaskListItem(pItemName, pItemValue);

                        int sleeptime=0;
                        while(sleeptime < Common::getInstance()->getCurrentSleepTime(DEEPSLEEP_ITEM) && g_TestStopFlag[m_nTreadID] != BOOT_STOP)
                        {
                            sleeptime = sleeptime + 100;
                            Sleep(100);
                        }

                        if(g_TestStopFlag[m_nTreadID] == BOOT_STOP)
                        {
                            pItemValue = FAIL;
                            UpdateTaskListItem(pItemName, pItemValue);
                            bPass = false;
                            failflag = true;
                            spNextStage = DISCONNECT_PWR_STAGE;
                            break;
                        }

                        AverageValue = MaxValue = 0.0f;
                        if(!DeepSleepCurrentTest(&AverageValue))
                        {
                            pItemValue = FAIL;
                            UpdateTaskListItem(pItemName, pItemValue, QString("%1 mA").arg(QString::number(AverageValue, 'f', 2)));
                            bPass = false;
                            spNextStage = DISCONNECT_PWR_STAGE;
                            break;
                        }
                        else
                        {
                            pItemValue = PASS;
                            UpdateTaskListItem(pItemName, pItemValue, QString("%1 mA").arg(QString::number(AverageValue, 'f', 2)));
                        }
                    }

                    spNextStage = TEST_CHANGE_CURRENT;
                    break;
                }

                case TEST_CHANGE_CURRENT:
                {
                    if(TestItemList.contains(ChargeCurrentItem::staticType()) && Common::getInstance()->getItemEnable(ChargeCurrentItem::staticType())
                        && Common::getInstance()->getPowerType() != PowerType::BlueBird)
                    {
                        pItemName = "Charge Current Test";
                        pItemValue = RUNNING;
                        UpdateTaskListItem(pItemName, pItemValue);

                        int sleeptime=0;
                        while(sleeptime < Common::getInstance()->getCurrentSleepTime(CHARGE_ITEM) && g_TestStopFlag[m_nTreadID] != BOOT_STOP)
                        {
                            sleeptime = sleeptime + 100;
                            Sleep(100);
                        }

                        if(g_TestStopFlag[m_nTreadID] == BOOT_STOP)
                        {
                            pItemValue = FAIL;
                            UpdateTaskListItem(pItemName, pItemValue);
                            bPass = false;
                            failflag = true;
                            spNextStage = DISCONNECT_PWR_STAGE;
                            break;
                        }

                        ControlUsb(true);
                        Sleep(500);

                        AverageValue = MaxValue = 0.0f;
                        if(!ChargeCurrentTest(&AverageValue))
                        {
                            pItemValue = FAIL;
                            UpdateTaskListItem(pItemName, pItemValue, QString("%1 mA").arg(QString::number(AverageValue,'f',2)));
                            bPass = false;
                            spNextStage = DISCONNECT_PWR_STAGE;
                            break;
                        }
                        else
                        {
                            pItemValue = PASS;
                            UpdateTaskListItem(pItemName, pItemValue, QString("%1 mA").arg(AverageValue));
                        }

                        ControlUsb(false);
                        Sleep(500);
                    }

                    spNextStage = TEST_PWROFF_CURRENT;
                    break;
                }

                case TEST_PWROFF_CURRENT:
                {
                    if((UsbControl == ControlBox && Common::getInstance()->getUseRelayEnable()) || NeedPowerSupply())
                        ControlUsb(false);

                    if(TestItemList.contains(PowerOffCurrentItem::staticType()) && Common::getInstance()->getItemEnable(PowerOffCurrentItem::staticType()))
                    {
                        if(Common::getInstance()->getPowerType() == PowerType::BlueBird)
                        {
                            ClosePowerSupply();
                        }
                        else
                        {
                            m_pVisaInstance->EnableOutput(OUTPUT_CHANNEL_1, false);
                        }

                        pItemName = "PowerOff Current Test";
                        pItemValue = RUNNING;
                        UpdateTaskListItem(pItemName, pItemValue);

                        int sleeptime=0;
                        while(sleeptime < Common::getInstance()->getCurrentSleepTime(POWEROFF_ITEM) && g_TestStopFlag[m_nTreadID] != BOOT_STOP)
                        {
                            sleeptime = sleeptime + 100;
                            Sleep(100);
                        }

                        if(g_TestStopFlag[m_nTreadID] == BOOT_STOP)
                        {
                            pItemValue = FAIL;
                            UpdateTaskListItem(pItemName, pItemValue);
                            bPass = false;
                            failflag = true;
                            spNextStage = DISCONNECT_PWR_STAGE;
                            break;
                        }

                        if(Common::getInstance()->getPowerType() == PowerType::BlueBird)
                        {
                            OpenPowerSupply();
                        }
                        else
                        {
                            m_pVisaInstance->EnableOutput(OUTPUT_CHANNEL_1, true);
                        }

                        AverageValue = 0.0f;
                        if(!PowerOffCurrentTest(&AverageValue))
                        {
                            pItemValue = FAIL;
                            UpdateTaskListItem(pItemName, pItemValue, QString("%1 mA").arg(QString::number(AverageValue, 'f', 2)));
                            bPass = false;
                            failflag = true;
                            spNextStage = DISCONNECT_PWR_STAGE;
                            break;
                        }

                        pItemValue = PASS;
                        UpdateTaskListItem(pItemName, pItemValue, QString("%1 mA").arg(QString::number(AverageValue, 'f', 2)));
                    }

                    spNextStage = DISCONNECT_PWR_STAGE;
                    break;
                }

                case DISCONNECT_PWR_STAGE:
                {
                    if((UsbControl == ControlBox && Common::getInstance()->getUseRelayEnable()) || NeedPowerSupply())
                        ControlUsb(false);

                    if(NeedPowerSupply())
                    {
                        if(Common::getInstance()->getPowerType() == PowerType::BlueBird)
                        {
                            pItemName = "DisConnect BlueBird Power";
                            pItemValue = RUNNING;
                            UpdateTaskListItem(pItemName, pItemValue);
                            ClosePowerSupply();
                            UpdateTaskListItem(pItemName, PASS);
                        }
                        else
                        {
                            m_pVisaInstance->EnableOutput(OUTPUT_CHANNEL_1, false);
                            pItemName = "DisConnect Power By GPIB";
                            pItemValue = RUNNING;
                            UpdateTaskListItem(pItemName, pItemValue);
                            m_pVisaInstance->DisConnectByGPIB();
                            pItemValue = PASS;
                            UpdateTaskListItem(pItemName, pItemValue);
                        }
                    }

                    spNextStage = END_STAGE;
                    break;
                }

            }

            spStage = spNextStage;
        }

        if(g_TestStopFlag[m_nTreadID] == BOOT_STOP)
        {
            UpdateUILog("User Stop Test!", RED);
            if((UsbControl == ControlBox && Common::getInstance()->getUseRelayEnable()) || NeedPowerSupply())
                ControlUsb(false);

            if(NeedPowerSupply())
            {
                if(Common::getInstance()->getPowerType() == PowerType::BlueBird)
                {
                    ClosePowerSupply();
                }
                else
                {
                    m_pVisaInstance->EnableOutput(OUTPUT_CHANNEL_1, false);
                    m_pVisaInstance->DisConnectByGPIB();
                }
            }

            bPass = false;
        }

        //Upload Mac To YN System
        if(TestItemList.contains(WriteCodesItem::staticType()) && Common::getInstance()->getItemEnable(WriteCodesItem::staticType()))
        {
            QJsonObject testCaseObj = Common::getInstance()->getJsonParams(InputCodeItem::staticType());
            if((testCaseObj["BtMac"].toBool() && testCaseObj["BtMac From"].toString() == "YnSystem")
                || (testCaseObj["WifiMac"].toBool() && testCaseObj["WifiMac From"].toString() == "YnSystem"))
            {
                QString YnCode = testCaseObj["YNCode"].toString();
                QString SN = m_sScanData.strBarcode;
                QString BtMac = m_sScanData.strBTAddr;
                QString WifiMac = m_sScanData.strWifiAddr;

                pItemName = "Upload Mac To YnSystem";
                pItemValue = RUNNING;
                UpdateTaskListItem(pItemName, pItemValue);

                bPass = UploadMacToYnSystem(YnCode, SN, BtMac, WifiMac);

                pItemValue = (bPass ? PASS : FAIL);
                UpdateTaskListItem(pItemName, pItemValue);
            }
        }

        if(bPass)
            UpdateUILog("Test Pass.", GREEN);
        else
            UpdateUILog("Test Failed.", RED);

        if(!Common::getInstance()->getMesOfflineEnable() && !Common::getInstance()->getMesNotUpdateEnable())
            UpdateResultToMes(bPass ? PASS : FAIL, m_szBarcode, static_cast<int>(std::round((float)timer.elapsed()/1000)));

        UpdateResult(bPass ? PASS : FAIL);
        Common::getInstance()->DebugOnOff(m_nTreadID, false);
        UpdateUILog("End Testting !");

        if(g_TestStopFlag[m_nTreadID] == BOOT_STOP)
            loopflag = false;

        loopflag = false;
    }

    EnableUIItem();
}

void WorkerThread::ClearUIlogs()
{
    emit signal_ClearUIlogs(m_nTreadID);
}

void WorkerThread::UpdateResult(TestResult_Status Status)
{
    emit signal_UpdateResult(m_nTreadID, Status);
}

void WorkerThread::UpdateResultToMes(TestResult_Status Status, QString barcode, UINT TestTime)
{
    emit signal_UpdateResultToMes(m_nTreadID, Status, barcode, TestTime);
}

void WorkerThread::UpdateUILog(QString log, LOGCOLOR_TYPE textcolor)
{
    emit signal_UpdateUILog(m_nTreadID, QString("[Thread]%1").arg(log), textcolor);
}

void WorkerThread::EnableUIItem()
{
    emit signal_EnableUIItem(m_nTreadID);
}

void WorkerThread::UpdateTaskListItem(QString m_ItemName, TestResult_Status m_Status, QString value)
{
    emit signal_UpdateMainUIItem(m_nTreadID, m_ItemName, m_Status, value);
}

void WorkerThread::UpdateBarcodeToUI(QString m_Barcode)
{
    emit signal_UpdateBarcodeToUI(m_nTreadID, m_Barcode);
}

void WorkerThread::UpdateUIPortInfo()
{
    emit signal_UpdateUIPortInfo(m_nTreadID);
}

void WorkerThread::InitItemListView()
{
    emit signal_InitItemListView(m_nTreadID);
}

void WorkerThread::SetLogPath()
{
    emit signal_SetLogPath(m_nTreadID);
}

void WorkerThread::getTestItemList()
{
    QJsonArray testCasesArray = Common::getInstance()->getItemJsonDoc().array();

    for (int i = 0; i < testCasesArray.size(); ++i) {
        QJsonObject testCaseObj = testCasesArray[i].toObject();
        if(!testCaseObj["type"].toString().isEmpty())
            TestItemList.append(testCaseObj["type"].toString());
    }

    qDebug() << "TestItemList count:" << TestItemList.size();
    for(int i=0; i<TestItemList.size(); i++) {
        qDebug() << TestItemList[i];
    }
}

bool WorkerThread::CheckFixtureStatus()
{
    while(g_TestStopFlag[m_nTreadID] != BOOT_STOP && !Common::getInstance()->getSignalRet(m_nTreadID))
    {
        Sleep(100);
    }

    QString BoxRes = Common::getInstance()->getPortRequest(m_nTreadID);
    QString statusbit = stringOfBits(BoxRes.mid(7,1));

    if(statusbit == "Invalid decimal number")
    {
        return false;
    }

    BoxStatus = (statusbit.mid(3-m_nTreadID,1) == "1");

    if(BoxStatus && !Common::getInstance()->getTestBoxLastStatus(m_nTreadID))
    {
        Common::getInstance()->setTestBoxLastStatus(m_nTreadID, BoxStatus);
        return true;
    }
    else
    {
        Common::getInstance()->setTestBoxLastStatus(m_nTreadID, BoxStatus);
        return false;
    }

    return false;
}

QString WorkerThread::stringOfBits(const QString &decimalString)
{
    bool ok;
    int decimalNumber = decimalString.toInt(&ok, 16);
    if (!ok) {
        return "Invalid decimal number";
    }

    QString binary = QString::number(decimalNumber, 2);
    int bitLength = sizeof(int) * decimalString.length();
    while (binary.length() < bitLength) {
        binary = "0" + binary;
    }
    return binary;
}

void WorkerThread::InitPowerStatus()
{
    m_pVisaInstance->ClearStatus();
    m_pVisaInstance->ResetDevice();
    m_pVisaInstance->SetOutputCoupled(false); // 必须关闭耦合以分开控制Output1和Output2

    m_pVisaInstance->SetOutputVoltageAndCurrentLevels(OUTPUT_CHANNEL_1, Common::getInstance()->getDutOutput1Vol(m_nTreadID), Common::getInstance()->getDutOutput1Current(m_nTreadID));
    m_pVisaInstance->SetCurrentMeasurementRange(HIGH);
    m_pVisaInstance->EnableOutput(OUTPUT_CHANNEL_1, false);

    if(Common::getInstance()->getPowerType() == PowerType::Dual)
    {
        m_pVisaInstance->SetOutputVoltageAndCurrentLevels(OUTPUT_CHANNEL_2, Common::getInstance()->getDutOutput2Vol(m_nTreadID), Common::getInstance()->getDutOutput2Current(m_nTreadID));
        m_pVisaInstance->EnableOutput(OUTPUT_CHANNEL_2, false);
    }
}

bool WorkerThread::NeedPowerSupply()
{
    return Common::getInstance()->getPowerType() != Battery;
}

bool WorkerThread::PowerOffCurrentTest(float* value)
{
    char szTemp[MAX_PATH] = {0};

    double totalTime = Common::getInstance()->getCurrentTestTime(POWEROFF_ITEM) * 1000; // s = > ms
    double interval = 1000.0f / 5 ; // Feq
    double timeout = Common::getInstance()->getCurrentTimeout(POWEROFF_ITEM) * 1000; // s = > ms

    long waitTime = 0;
    long waitdelay = 200; // 200ms
    double current = 0.0f;
    double measAvgCurrent = 0.0f;
    double time = 0.0f;
    double measMaxCurrent = 0.0f;
    double totalMeasCurrent = 0.0f;
    int measCount = 0;
    bool isStartMeasure = false;

    int cout = 0;
    QElapsedTimer timer;
    qint64 slepsedTime, start_time, end_time;

    MTRACE(m_hDebugTrace[m_nTreadID], "WorkerThread::PowerOffCurrentTest()  PowerOff Current Test Start! ");
    UpdateUILog("PowerOff Current Test Start!");
    if(Common::getInstance()->getPowerType() != PowerType::BlueBird)
        m_pVisaInstance->SetCurrentMeasurementRange(LOW);
    timer.start();

    while(time < totalTime && g_TestStopFlag[m_nTreadID] != BOOT_STOP) {
        start_time = timer.elapsed();
        if(start_time < 2000)
            continue;

        if(Common::getInstance()->getPowerType() == PowerType::BlueBird)
            current = GetCurrentFromPowerSupply() / 1000.0f ; // uA -> mA
        else
            current = m_pVisaInstance->ReadMeasureCurrent(OUTPUT_CHANNEL_1);// mA

        MTRACE(m_hDebugTrace[m_nTreadID], "WorkerThread::PowerOffCurrentTest() The Current is %f mA", current);

        if (current <= 0)
        {
            goto GoOn;
        }

        if (current > Common::getInstance()->getCurrentTriggerValue(POWEROFF_ITEM) && !isStartMeasure) // 判断是否进入开机状态
        {
            Sleep(waitdelay);
            waitTime += waitdelay;

            if (waitTime >= timeout)
            {
                sprintf_s(szTemp, "PowerOff Current Test Timeout! Current = %f, The Current Does not exceed %f mA within %d seconds",
                          current, Common::getInstance()->getCurrentTriggerValue(POWEROFF_ITEM), waitTime/1000);
                MTRACE(m_hDebugTrace[m_nTreadID], "WorkerThread::PowerOffCurrentTest()  %s", szTemp);
                UpdateUILog(szTemp, RED);
                goto End;
            }
            continue;
        }

        if (current <= Common::getInstance()->getCurrentTriggerValue(POWEROFF_ITEM)) // 只要有一次触发就开始采样记录
        {
            isStartMeasure = true;
        }

        if (isStartMeasure)
        {
            if (current > measMaxCurrent)
            {
                measMaxCurrent = current;
            }

            totalMeasCurrent += current;
            measCount++;

            time += interval;

            cout++;
        }
GoOn:
        end_time = timer.elapsed();
        int temp = (DWORD)interval - (DWORD)(end_time - start_time);
        if(temp>0)
            Sleep(temp);
    }

    if(Common::getInstance()->getPowerType() != PowerType::BlueBird)
        m_pVisaInstance->SetCurrentMeasurementRange(HIGH);

    if(measCount != 0)
        measAvgCurrent = QString::number(totalMeasCurrent / measCount, 'f', 2).toFloat();
    sprintf_s(szTemp, "PowerOff Current Test Current is %0.2fmA !", measAvgCurrent);
    MTRACE(m_hDebugTrace[m_nTreadID], "WorkerThread::PowerOffCurrentTest()  %s", szTemp);
    UpdateUILog(szTemp);

    slepsedTime = timer.elapsed();
    sprintf_s(szTemp, "PowerOff Current Test Time is %llds !", slepsedTime/1000);
    MTRACE(m_hDebugTrace[m_nTreadID], "WorkerThread::PowerOffCurrentTest()  %s", szTemp);
    UpdateUILog(szTemp);

    *value = measAvgCurrent;
    if (measAvgCurrent >= Common::getInstance()->getCurrentMinValue(POWEROFF_ITEM) && measAvgCurrent <= Common::getInstance()->getCurrentMaxValue(POWEROFF_ITEM))
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "WorkerThread::PowerOffCurrentTest()  PowerOff Current Test Pass !");
        UpdateUILog("PowerOff Current Test Pass !");  
        return true;
    }
    else
    {
        UpdateUILog(QString("PowerOff Current Test Fail! Test Value is %1").arg(measAvgCurrent), RED);
    }

End:
    MTRACE(m_hDebugTrace[m_nTreadID], "WorkerThread::PowerOffCurrentTest()  PowerOff Current Test Fail !");
    return false;
}

bool WorkerThread::PowerOnCurrentTest(float* value, float* maxValue)
{
    char szTemp[MAX_PATH] = {0};
    double totalTime = Common::getInstance()->getCurrentTestTime(POWERON_ITEM) * 1000; // s = > ms
    double interval = 1000.0f / 5 ; // Feq
    double timeout = Common::getInstance()->getCurrentTimeout(POWERON_ITEM) * 1000; // s = > ms

    long waitTime = 0;
    long waitdelay = 200; // 200ms
    double time = 0.0f;
    double current = 0.0f;
    double measAvgCurrent = 0.0f;
    double measMaxCurrent = 0.0f;
    double totalMeasCurrent = 0.0f;
    int measCount = 0;
    bool isStartMeasure = false;

    int cout = 0;
    QElapsedTimer timer;
    qint64 slepsedTime, start_time, end_time;

    MTRACE(m_hDebugTrace[m_nTreadID], "WorkerThread::PowerOnCurrentTest()  PowerOn Current Test Start! ");
    UpdateUILog("PowerOn Current Test Start!");
    if(Common::getInstance()->getPowerType() != PowerType::BlueBird)
        m_pVisaInstance->SetCurrentMeasurementRange(HIGH);
    timer.start();

    while(time <= totalTime && g_TestStopFlag[m_nTreadID] != BOOT_STOP)
    {
        start_time = timer.elapsed();
        if(start_time < 2000)
            continue;

        if(Common::getInstance()->getPowerType() == PowerType::BlueBird)
            current = GetCurrentFromPowerSupply() / 1000.0f ; // uA -> mA
        else
            current = m_pVisaInstance->ReadMeasureCurrent(OUTPUT_CHANNEL_1);// mA

        MTRACE(m_hDebugTrace[m_nTreadID], "WorkerThread::PowerOnCurrentTest() The Current is %f mA", current);

        if (current < Common::getInstance()->getCurrentTriggerValue(POWERON_ITEM) && !isStartMeasure) // 判断是否进入开机状态
        {
            Sleep(waitdelay);
            waitTime += waitdelay;

            if (waitTime >= timeout)
            {
                sprintf_s(szTemp, "PowerOn Current Test Timeout! Current = %f, The Current Does not exceed %f mA within %d seconds",
                          current, Common::getInstance()->getCurrentTriggerValue(POWERON_ITEM), waitTime/1000);
                MTRACE(m_hDebugTrace[m_nTreadID], "WorkerThread::PowerOnCurrentTest()  %s", szTemp);
                UpdateUILog(szTemp, RED);
                goto End;
            }
            continue;
        }

        if (current >= Common::getInstance()->getCurrentTriggerValue(POWERON_ITEM)) // 只要有一次触发就开始采样记录
        {
            isStartMeasure = true;
        }

        if (isStartMeasure)
        {
            if (current > measMaxCurrent)
            {
                measMaxCurrent = current;
            }

            totalMeasCurrent += current;
            measCount++;

            time += interval;

            cout++;
        }
GoOn:
        end_time = timer.elapsed();
        int temp = (DWORD)interval - (DWORD)(end_time - start_time);
        if(temp>0)
            Sleep(temp);
    }

    if(measCount != 0)
        measAvgCurrent = QString::number(totalMeasCurrent / measCount, 'f', 2).toFloat();
    sprintf_s(szTemp, "PowerOn Current Test Current is %0.2fmA !", measAvgCurrent);
    MTRACE(m_hDebugTrace[m_nTreadID], "WorkerThread::PowerOnCurrentTest()  %s", szTemp);
    UpdateUILog(szTemp);

    sprintf_s(szTemp, "PowerOn Current Test Max Current is %0.2fmA !", measMaxCurrent);
    MTRACE(m_hDebugTrace[m_nTreadID], "WorkerThread::PowerOnCurrentTest()  %s", szTemp);
    UpdateUILog(szTemp);

    slepsedTime = timer.elapsed();
    if(Common::getInstance()->getPowerType() != PowerType::BlueBird)
        m_pVisaInstance->SetCurrentMeasurementRange(HIGH);
    sprintf_s(szTemp, "PowerOn Current Test Time is %llds !", slepsedTime/1000);
    MTRACE(m_hDebugTrace[m_nTreadID], "WorkerThread::PowerOnCurrentTest()  %s", szTemp);
    UpdateUILog(szTemp);

    *value = measAvgCurrent;
    *maxValue = measMaxCurrent;
    if (measAvgCurrent >= Common::getInstance()->getCurrentMinValue(POWERON_ITEM) && measAvgCurrent <= Common::getInstance()->getCurrentMaxValue(POWERON_ITEM))
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "WorkerThread::PowerOnCurrentTest()  PowerOn Current Test Pass !");
        UpdateUILog("PowerOn Current Test Pass !");
        return true;
    }
    else
    {
        UpdateUILog(QString("PowerOn Current Test Fail! Test Value is %1").arg(measAvgCurrent), RED);
    }

End:
    MTRACE(m_hDebugTrace[m_nTreadID], "WorkerThread::PowerOnCurrentTest()  PowerOn Current Test Fail !");
    return false;
}

bool WorkerThread::DeepSleepCurrentTest(float* value)
{
    char szTemp[MAX_PATH] = {0};

    double totalTime = Common::getInstance()->getCurrentTestTime(DEEPSLEEP_ITEM) * 1000; // s = > ms
    double interval = 1000.0f / 5 ; // Feq
    double timeout = Common::getInstance()->getCurrentTimeout(DEEPSLEEP_ITEM) * 1000; // s = > ms

    long waitTime = 0; // 等待进入待机状态的时间
    long waitdelay = 200; // 200ms
    double time = 0.0f;   // 进入待机状态之后的测试采样时间
    double current = 0.0f;
    double measAvgCurrent = 0.0f;
    double totalMeasCurrent = 0.0f;
    int measCount = 0;
    bool isStartMeasure = false;
    bool isTriggered = false;
    int hitNum = 10;

    int cout = 0;
    QElapsedTimer timer;
    qint64 slepsedTime, start_time, end_time;

    MTRACE(m_hDebugTrace[m_nTreadID], "WorkerThread::DeepSleepCurrentTest()  DeepSleep Current Test Start! ");
    UpdateUILog("DeepSleep Current Test Start!");

    timer.start();

    while(time <= totalTime && g_TestStopFlag[m_nTreadID] != BOOT_STOP)
    {
        start_time = timer.elapsed();
        if(start_time < 2000)
            continue;

        if(Common::getInstance()->getPowerType() == PowerType::BlueBird)
            current = GetCurrentFromPowerSupply() / 1000.0f ; // uA -> mA
        else
            current = m_pVisaInstance->ReadMeasureCurrent(OUTPUT_CHANNEL_1); // uA -> mA

        MTRACE(m_hDebugTrace[m_nTreadID], "WorkerThread::DeepSleepCurrentTest() The Current is %f mA", current);

        if (current > Common::getInstance()->getCurrentTriggerValue(DEEPSLEEP_ITEM) && !isStartMeasure && !isTriggered) // 判断是否进入待机状态
        {
            Sleep(waitdelay);
            waitTime += waitdelay;

            if (waitTime >= timeout)
            {
                sprintf_s(szTemp, "DeepSleep Current Test Timeout! Current = %f, The Current Does not exceed %f mA within %d seconds",
                          current, Common::getInstance()->getCurrentTriggerValue(DEEPSLEEP_ITEM), waitTime/1000);
                MTRACE(m_hDebugTrace[m_nTreadID], "WorkerThread::DeepSleepCurrentTest()  %s", szTemp);
                UpdateUILog(szTemp, RED);
                goto End;
            }
            continue;
        }

        if (current > Common::getInstance()->getCurrentTriggerValue(DEEPSLEEP_ITEM) && !isStartMeasure && isTriggered)
        {
            hitNum = 10;
        }

        if (current <= Common::getInstance()->getCurrentTriggerValue(DEEPSLEEP_ITEM))
        {
            isTriggered = TRUE;

            if (hitNum <= 0) // 只有采集到10次小于触发电流才开始采样记录
            {
                isStartMeasure = true;
            }
            else
            {
                hitNum--;
            }
        }

        if (isStartMeasure)
        {
            if (current >= Common::getInstance()->getCurrentTriggerValue(DEEPSLEEP_ITEM)) // 飞行模式待机时 也会有快速的唤醒动作，此时电流会大于100mA影响底电流测试，过滤掉这些电流
            {
                // 过滤掉大于触发电流的记录
                goto GoOn;
            }

            totalMeasCurrent += current;
            measCount++;
            time += interval;

            cout++;
        }
GoOn:
        end_time = timer.elapsed();
        int temp = (DWORD)interval - (DWORD)(end_time - start_time);
        if(temp>0)
            Sleep(temp);
    }

    if(Common::getInstance()->getPowerType() != PowerType::BlueBird)
        m_pVisaInstance->SetCurrentMeasurementRange(HIGH);

    if(measCount != 0)
        measAvgCurrent = QString::number(totalMeasCurrent / measCount, 'f', 2).toFloat();
    sprintf_s(szTemp, "DeepSleep Current Test Current is %0.2fmA !", measAvgCurrent);
    MTRACE(m_hDebugTrace[m_nTreadID], "WorkerThread::DeepSleepCurrentTest()  %s", szTemp);
    UpdateUILog(szTemp);

    slepsedTime = timer.elapsed();
    sprintf_s(szTemp, "DeepSleep Current Test Time is %llds !", slepsedTime/1000);
    MTRACE(m_hDebugTrace[m_nTreadID], "WorkerThread::DeepSleepCurrentTest()  %s", szTemp);
    UpdateUILog(szTemp);

    *value = measAvgCurrent;
    if (measAvgCurrent >= Common::getInstance()->getCurrentMinValue(DEEPSLEEP_ITEM) && measAvgCurrent <= Common::getInstance()->getCurrentMaxValue(DEEPSLEEP_ITEM))
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "WorkerThread::DeepSleepCurrentTest()  DeepSleep Current Test Pass !");
        UpdateUILog("DeepSleep Current Test Pass !");
        return true;
    }
    else
    {
        UpdateUILog(QString("DeepSleep Current Test Fail! Test Value is %1").arg(measAvgCurrent), RED);
    }

End:
    MTRACE(m_hDebugTrace[m_nTreadID], "WorkerThread::DeepSleepCurrentTest()  DeepSleep Current Test Fail !");
    return false;
}

bool WorkerThread::ChargeCurrentTest(float* value)
{
    char szTemp[MAX_PATH] = {0};
    double totalTime = Common::getInstance()->getCurrentTestTime(CHARGE_ITEM) * 1000; // s = > ms
    double interval = 1000.0f / 5 ; // Feq
    double timeout = Common::getInstance()->getCurrentTimeout(CHARGE_ITEM) * 1000; // s = > ms

    long waitTime = 0;
    long waitdelay = 200; // 200ms
    double time = 0.0f;
    double current = 0.0f;
    double measAvgCurrent = 0.0f;
    double measMaxCurrent = 0.0f;
    double totalMeasCurrent = 0.0f;
    int measCount = 0;
    bool isStartMeasure = false;

    int cout = 0;
    QElapsedTimer timer;
    qint64 slepsedTime, start_time, end_time;

    MTRACE(m_hDebugTrace[m_nTreadID], "WorkerThread::ChargeCurrentTest()  Charge Current Test Start! ");
    UpdateUILog("Charge Current Test Start!");
    m_pVisaInstance->SetCurrentMeasurementRange(HIGH);
    timer.start();

    while(time <= totalTime && g_TestStopFlag[m_nTreadID] != BOOT_STOP)
    {
        start_time = timer.elapsed();
        if(start_time < 2000)
            continue;

        current = m_pVisaInstance->ReadMeasureCurrent(OUTPUT_CHANNEL_2);// mA
        MTRACE(m_hDebugTrace[m_nTreadID], "WorkerThread::ChargeCurrentTest() The Current is %f mA", current);

        if (current < Common::getInstance()->getCurrentTriggerValue(CHARGE_ITEM) && !isStartMeasure) // 判断是否进入开机状态
        {
            Sleep(waitdelay);
            waitTime += waitdelay;

            if (waitTime >= timeout)
            {
                sprintf_s(szTemp, "Charge Current Test Timeout! Current = %f, The Current Does not exceed %f mA within %d seconds",
                          current, Common::getInstance()->getCurrentTriggerValue(CHARGE_ITEM), waitTime/1000);
                MTRACE(m_hDebugTrace[m_nTreadID], "WorkerThread::ChargeCurrentTest()  %s", szTemp);
                UpdateUILog(szTemp, RED);
                goto End;
            }
            continue;
        }

        if (current >= Common::getInstance()->getCurrentTriggerValue(CHARGE_ITEM)) // 只要有一次触发就开始采样记录
        {
            isStartMeasure = true;
        }

        if (isStartMeasure)
        {
            if (current > measMaxCurrent)
            {
                measMaxCurrent = current;
            }

            totalMeasCurrent += current;
            measCount++;

            time += interval;

            cout++;
        }
GoOn:
        end_time = timer.elapsed();
        int temp = (DWORD)interval - (DWORD)(end_time - start_time);
        if(temp>0)
            Sleep(temp);
    }

    if(measCount != 0)
        measAvgCurrent = QString::number(totalMeasCurrent / measCount, 'f', 2).toFloat();
    sprintf_s(szTemp, "Charge Current Test Current is %0.2fmA !", measAvgCurrent);
    MTRACE(m_hDebugTrace[m_nTreadID], "WorkerThread::ChargeCurrentTest()  %s", szTemp);
    UpdateUILog(szTemp);

    sprintf_s(szTemp, "Charge Current Test Max Current is %0.2fmA !", measMaxCurrent);
    MTRACE(m_hDebugTrace[m_nTreadID], "WorkerThread::ChargeCurrentTest()  %s", szTemp);
    UpdateUILog(szTemp);

    slepsedTime = timer.elapsed();
    m_pVisaInstance->SetCurrentMeasurementRange(HIGH);
    sprintf_s(szTemp, "Charge Current Test Time is %llds !", slepsedTime/1000);
    MTRACE(m_hDebugTrace[m_nTreadID], "WorkerThread::ChargeCurrentTest()  %s", szTemp);
    UpdateUILog(szTemp);

    *value = measAvgCurrent;
    if (measAvgCurrent >= Common::getInstance()->getCurrentMinValue(CHARGE_ITEM) && measAvgCurrent <= Common::getInstance()->getCurrentMaxValue(CHARGE_ITEM))
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "WorkerThread::ChargeCurrentTest()  Charge Current Test Pass !");
        UpdateUILog("Charge Current Test Pass !");
        return true;
    }
    else
    {
        UpdateUILog(QString("Charge Current Test Fail! Test Value is %1").arg(measAvgCurrent), RED);
    }

End:
    MTRACE(m_hDebugTrace[m_nTreadID], "WorkerThread::ChargeCurrentTest()  Charge Current Test Fail !");
    return false;
}

bool WorkerThread::BottomCurrentTest(float* value)
{
    char szTemp[MAX_PATH] = {0};

    double totalTime = Common::getInstance()->getCurrentTestTime(BOTTOM_ITEM) * 1000; // s = > ms
    double interval = 1000.0f / 5 ; // Feq
    double timeout = Common::getInstance()->getCurrentTimeout(BOTTOM_ITEM) * 1000; // s = > ms

    long waitTime = 0;
    long waitdelay = 200; // 200ms
    double current = 0.0f;
    double measAvgCurrent = 0.0f;
    double time = 0.0f;
    double measMaxCurrent = 0.0f;
    double totalMeasCurrent = 0.0f;
    int measCount = 0;
    bool isStartMeasure = false;

    int cout = 0;
    QElapsedTimer timer;
    qint64 slepsedTime, start_time, end_time;

    MTRACE(m_hDebugTrace[m_nTreadID], "WorkerThread::BottomCurrentTest()  Bottom Current Test Start! ");
    UpdateUILog("Bottom Current Test Start!");
    if(Common::getInstance()->getPowerType() != PowerType::BlueBird)
        m_pVisaInstance->SetCurrentMeasurementRange(LOW);
    timer.start();

    while(time < totalTime && g_TestStopFlag[m_nTreadID] != BOOT_STOP) {
        start_time = timer.elapsed();
        if(start_time < 2000)
            continue;

        if(Common::getInstance()->getPowerType() == PowerType::BlueBird)
            current = GetCurrentFromPowerSupply() / 1000.0f ; // uA -> mA
        else
            current = m_pVisaInstance->ReadMeasureCurrent(OUTPUT_CHANNEL_1);// mA

        MTRACE(m_hDebugTrace[m_nTreadID], "WorkerThread::BottomCurrentTest() The Current is %f mA", current);

        if (current <= 0)
        {
            goto GoOn;
        }

        if (current > Common::getInstance()->getCurrentTriggerValue(BOTTOM_ITEM) && !isStartMeasure) // 判断是否进入开机状态
        {
            Sleep(waitdelay);
            waitTime += waitdelay;

            if (waitTime >= timeout)
            {
                sprintf_s(szTemp, "Bottom Current Test Timeout! Current = %f, The Current Does not exceed %f mA within %d seconds",
                          current, Common::getInstance()->getCurrentTriggerValue(BOTTOM_ITEM), waitTime/1000);
                MTRACE(m_hDebugTrace[m_nTreadID], "WorkerThread::BottomCurrentTest()  %s", szTemp);
                UpdateUILog(szTemp, RED);
                goto End;
            }
            continue;
        }

        if (current <= Common::getInstance()->getCurrentTriggerValue(BOTTOM_ITEM)) // 只要有一次触发就开始采样记录
        {
            isStartMeasure = true;
        }

        if (isStartMeasure)
        {
            if (current > measMaxCurrent)
            {
                measMaxCurrent = current;
            }

            totalMeasCurrent += current;
            measCount++;

            time += interval;

            cout++;     
        }

GoOn:
        end_time = timer.elapsed();
        int temp = (DWORD)interval - (DWORD)(end_time - start_time);
        if(temp>0)
            Sleep(temp);
    }

    if(Common::getInstance()->getPowerType() != PowerType::BlueBird)
        m_pVisaInstance->SetCurrentMeasurementRange(HIGH);

    if(measCount != 0)
        measAvgCurrent = QString::number(totalMeasCurrent / measCount, 'f', 2).toFloat();
    sprintf_s(szTemp, "Bottom Current Test Current is %0.2fmA !", measAvgCurrent);
    MTRACE(m_hDebugTrace[m_nTreadID], "WorkerThread::BottomCurrentTest()  %s", szTemp);
    UpdateUILog(szTemp);

    slepsedTime = timer.elapsed();
    sprintf_s(szTemp, "Bottom Current Test Time is %llds !", slepsedTime/1000);
    MTRACE(m_hDebugTrace[m_nTreadID], "WorkerThread::BottomCurrentTest()  %s", szTemp);
    UpdateUILog(szTemp);

    *value = measAvgCurrent;
    if (measAvgCurrent >= Common::getInstance()->getCurrentMinValue(BOTTOM_ITEM) && measAvgCurrent <= Common::getInstance()->getCurrentMaxValue(BOTTOM_ITEM))
    {
        MTRACE(m_hDebugTrace[m_nTreadID], "WorkerThread::BottomCurrentTest()  Bottom Current Test Pass !");
        UpdateUILog("Bottom Current Test Pass !");
        return true;
    }
    else
    {
        UpdateUILog(QString("Bottom Current Test Fail! Test Value is %1").arg(measAvgCurrent), RED);
    }

End:
    MTRACE(m_hDebugTrace[m_nTreadID], "WorkerThread::BottomCurrentTest()  Bottom Current Test Fail !");
    return false;
}

void WorkerThread::ControlUsb(bool enable)
{
    double current = 0.0f;
    QString Command;

    switch(UsbControl)
    {
        case PowerSupply:
        {
            m_pVisaInstance->EnableOutput(OUTPUT_CHANNEL_2, enable);
            break;
        }

        case Manual:
        {
            if(Common::getInstance()->getPowerType() != PowerType::BlueBird)
                m_pVisaInstance->SetCurrentMeasurementRange(HIGH);

            if(enable)
            {
                UpdateUILog("Please Connect Usb....");

                while(g_TestStopFlag[m_nTreadID] != BOOT_STOP)
                {
                    Sleep(200);
                    if(Common::getInstance()->getPowerType() == PowerType::BlueBird)
                        current = GetCurrentFromPowerSupply() / 1000.0f ; // uA -> mA
                    else
                        current = m_pVisaInstance->ReadMeasureCurrent(OUTPUT_CHANNEL_1);// mA

                    MTRACE(m_hDebugTrace[m_nTreadID], "WorkerThread::ControlUsb() The OUTPUT_CHANNEL_1 Current is %f mA", current);
                    if(current < -20 || (Common::getInstance()->getPowerType() == PowerType::BlueBird && current > 10))
                        break;
                }
            }
            else
            {
                bool titleflag = true;
                while(g_TestStopFlag[m_nTreadID] != BOOT_STOP)
                {
                    Sleep(200);
                    if(Common::getInstance()->getPowerType() == PowerType::BlueBird)
                        current = GetCurrentFromPowerSupply() / 1000.0f ; // uA -> mA
                    else
                        current = m_pVisaInstance->ReadMeasureCurrent(OUTPUT_CHANNEL_1);// mA

                    MTRACE(m_hDebugTrace[m_nTreadID], "WorkerThread::ControlUsb() The OUTPUT_CHANNEL_1 Current is %f mA", current);
                    if(current < -1 || (Common::getInstance()->getPowerType() == PowerType::BlueBird && current < 0))
                    {
                        if(titleflag)
                        {
                            UpdateUILog("Please DisConnect Usb....!");
                            titleflag = false;
                        }
                    }
                    else
                        break;
                }
            }

            if(g_TestStopFlag[m_nTreadID] == BOOT_STOP)
                return;

            break;
        }

        case ControlBox:
        {
            switch(m_nTreadID)
            {
            case 0:
                if(enable)
                    Command = "FE050000FF009835";
                else
                    Command = "FE0500000000D9C5";
                break;
            case 1:
                if(enable)
                    Command = "FE050001FF00C9F5";
                else
                    Command = "FE05000100008805";
                break;
            case 2:
                if(enable)
                    Command = "FE050002FF0039F5";
                else
                    Command = "FE05000200007805";
                break;
            case 3:
                if(enable)
                    Command = "FE050003FF006835";
                else
                    Command = "FE050003000029C5";
                break;
            default:
                break;
            }

            if(!Command.isEmpty())
            {
                while(g_TestStopFlag[m_nTreadID] != BOOT_STOP && Common::getInstance()->getSleepFlag())
                {
                    Sleep(200);
                }

                emit signal_send_data(m_nTreadID, Command);
            }
            break;
        }

        default:
            break;
    }
}

void WorkerThread::OpenPowerSupply()
{
    PowerSupplyInfo Info;
    memset(&Info, 0x00, sizeof(Info));
    Info.port = Common::getInstance()->getDutBluPort(m_nTreadID);
    Info.h_Handle = m_nTreadID;
    Info.voltage = Common::getInstance()->getDutBluOutputVol(m_nTreadID)*1000; // 转换为mV
    fnBLUAPI(Connect, &Info);
    fnBLUAPI(PowerOff, &Info);
    Sleep(500);
    fnBLUAPI(Connect, &Info);
    Sleep(500);
    fnBLUAPI(PowerOn, &Info);
    m_bPowerSupplyConnect = true;
}

void WorkerThread::ClosePowerSupply()
{
    PowerSupplyInfo Info;
    memset(&Info, 0x00, sizeof(Info));
    Info.port = Common::getInstance()->getDutBluPort(m_nTreadID);
    Info.h_Handle = m_nTreadID;
    if(m_bPowerSupplyConnect)
        fnBLUAPI(PowerOff, &Info);
    m_bPowerSupplyConnect = false;
}

double WorkerThread::GetCurrentFromPowerSupply()
{
    QDateTime currentDateTime = QDateTime::currentDateTime();
    PowerSupplyInfo Info;
    memset(&Info, 0x00, sizeof(Info));
    Info.port = Common::getInstance()->getDutBluPort(m_nTreadID);
    Info.h_Handle = m_nTreadID;
    fnBLUAPI(GetCurrent, &Info);
    qDebug() << currentDateTime.toString("yyyy-MM-dd hh:mm:ss ") << "GetCurrentFromPowerSupply Response: " <<  QString::number(Info.AvgCurrent/1000.0f, 'f', 3) << " mA";
    return Info.AvgCurrent;
}

bool WorkerThread::UploadMacToYnSystem(QString Order, QString SN, QString BtMac, QString WifiMac)
{
    QString request;
    Common::getInstance()->setPortRequest(m_nTreadID, "");
    Common::getInstance()->setSignalRet(m_nTreadID, false);

    emit signal_UploadMacToYnSystem(m_nTreadID, Order, SN, BtMac, WifiMac);

    while(!g_TestStopFlag[m_nTreadID] && !Common::getInstance()->getSignalRet(m_nTreadID))
        Sleep(200);

    if(g_TestStopFlag[m_nTreadID])
        return false;

    Sleep(500);

    request = Common::getInstance()->getPortRequest(m_nTreadID);
    if(!request.contains("Error"))
        return true;
    else
        UpdateUILog(request, RED);

    return false;
}
