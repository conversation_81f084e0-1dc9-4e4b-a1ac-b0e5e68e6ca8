#ifndef MAINWINDOWCALL<PERSON><PERSON>K_H
#define MAINWINDOWCALL<PERSON>CK_H

#include <QObject>
#include <QTimerEvent>
#include "Common/Common.h"
#include "workerthread.h"
#include "Instance/SerialPort/serialport.h"

class MainWindow;

class MainWindowCallback : public QObject
{
    Q_OBJECT

public:
    MainWindowCallback(MainWindow *mainwindow);
    ~MainWindowCallback(void);

    void Thread_Start(UINT nTreadID);
    void Thread_Stop(UINT nTreadID);
    void CheckFixtureStatus(UINT nTreadID);
    void ControlScanGun(UINT nTreadID);
    bool GetMacFromYnSystem(UINT nTreadID, QString &Order, QString &SN, QString &BtMac, QString &WifiMac);
    void UploadMacToYnSystem(UINT nTreadID, QString Order, QString SN, QString BtMac, QString WifiMac);
    bool CheckMacFromYnSystem(UINT nTreadID, QString &Order, QString &BtMac, QString &WifiMac);

    MesProxyBase    *g_pMesProxy[MAX_SUPPORT_COMPORT_NUM];
    QSerialPort     *globalBox_port[MAX_SUPPORT_COMPORT_NUM];
    QSerialPort     *globalScan_port[MAX_SUPPORT_COMPORT_NUM];
    SerialPort      *m_pTestBoxInstance[MAX_SUPPORT_COMPORT_NUM];
    SerialPort      *m_pScanGunInstance[MAX_SUPPORT_COMPORT_NUM];
    int timer_id[4] = {-1, -1, -1, -1};

private slots:
    void slot_EnableUIItem(UINT nTreadID);
    void slot_ClearUILogs(UINT nTreadID);
    void slot_UpdateResult(UINT nTreadID, TestResult_Status Status);
    void slot_UpdateResultToMes(UINT nTreadID, TestResult_Status Status, QString barcode, UINT TestTime);
    void slot_UpdateUILog(UINT nTreadID, QString log, LOGCOLOR_TYPE textcolor);
    void slot_UpdateMainUIItem(UINT nTreadID, QString m_ItemName, TestResult_Status m_Status, QString value);
    void slot_UpdateBarcodeToUI(UINT nTreadID, QString m_Barcode);
    void slot_UpdateUIPortInfo(UINT nTreadID);
    void slot_InitItemListView(UINT nTreadID);
    void slot_SetLogPath(UINT nTreadID);
    void slot_checkstatus(UINT nTreadID);
    void slot_StartTime(UINT nTreadID);
    void slot_send_data(UINT nTreadID, QString data);
    void slot_GetScanGunData1();
    void slot_GetScanGunData2();
    void slot_GetScanGunData3();
    void slot_GetScanGunData4();
    void slot_UploadMacToYnSystem(UINT nTreadID, QString Order, QString SN, QString BtMac, QString WifiMac);

private:
    void connectSignals(UINT nTreadID, WorkerThread *m_workerThread);

    // 重写定时器事件
    void timerEvent(QTimerEvent *event);
    void ResetSerialPort(UINT nTreadID);

private:
    MainWindow      *m_mainwindow;
    SmartPhoneSN    *m_pMetaInstance[MAX_SUPPORT_COMPORT_NUM];
    ConnectAgilent  *m_pVisaInstance[MAX_SUPPORT_COMPORT_NUM];
    HttpClient      *m_httpclient[MAX_SUPPORT_COMPORT_NUM];
    ServerApiProxy  *m_serviceproxy[MAX_SUPPORT_COMPORT_NUM];

    int ControlTime[MAX_SUPPORT_COMPORT_NUM];
    int ScanGunTime[MAX_SUPPORT_COMPORT_NUM];
    QTimer timeoutTimer[MAX_SUPPORT_COMPORT_NUM];
};

#endif // MAINWINDOWCALLBACK_H
