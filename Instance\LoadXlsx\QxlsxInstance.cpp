#include "QxlsxInstance.h"

QSharedPointer<QxlsxInstance> QxlsxInstance::m_instance = QSharedPointer<QxlsxInstance>();

QxlsxInstance::QxlsxInstance()
{
    memset(&_column, 0, sizeof(DataColumn_struct));
}

QxlsxInstance::~QxlsxInstance()
{
    if(xlsx != NULL)
    {
        delete xlsx;
        xlsx = NULL;
    }
}

QSharedPointer<QxlsxInstance> QxlsxInstance::getInstance()
{
    if (!m_instance) {
        m_instance = QSharedPointer<QxlsxInstance>(new QxlsxInstance());
    }
    return m_instance;
}

bool QxlsxInstance::Init(QString datacache_path_, QString& ErrInfo)
{
    bool ret = true;
    xlsx = new QXlsx::Document(datacache_path_);
    if (xlsx->load())
    {
        rowCount = xlsx->dimension().rowCount();
        columnCount = xlsx->dimension().columnCount();
        qDebug() << "rowCount: " << rowCount << " ,columnCount: " << columnCount;

        if(columnCount > 0 )
        {
            if(!readTitle(xlsx, ErrInfo))
                ret = false;
            else
            {
                InitData();
            }
        }
        else
        {
            ErrInfo = QString("ColumnCount is less than 1 From %1.").arg(datacache_path_);
            ret = false;
        }
    }
    else
    {
        ErrInfo = QString("Failed to load %1.").arg(datacache_path_);
        ret = false;
    }

    if(!ret)
        qDebug() << ErrInfo;

    return ret;
}

bool QxlsxInstance::readTitle(QXlsx::Document *xlsx, QString &ErrInfo)
{
    bool ret = true;
    //init
    _column.i_barcode = -1;
    _column.i_imei1 = -1;
    _column.i_imei2 = -1;
    _column.i_meid = -1;
    _column.i_btmac = -1;
    _column.i_wifimac = -1;
    _column.i_ethernetmac = -1;
    _column.i_serialno = -1;
    _column.i_netcode = -1;

    for (int column = 1; column <= columnCount; ++column)
    {
        std::shared_ptr<QXlsx::Cell> cell = xlsx->cellAt(1, column);
        if (cell)
        {
            QVariant value = cell->value();
            if(value.toString().compare("Barcode", Qt::CaseInsensitive) == 0)
            {
                _column.i_barcode = column;
            }
            if(value.toString().compare("IMEI1", Qt::CaseInsensitive) == 0 || value.toString().compare("IMEI", Qt::CaseInsensitive) == 0)
            {
                _column.i_imei1 = column;
            }
            if(value.toString().compare("IMEI2", Qt::CaseInsensitive) == 0)
            {
                _column.i_imei2 = column;
            }
            if(value.toString().compare("MEID", Qt::CaseInsensitive) == 0)
            {
                _column.i_meid = column;
            }
            if(value.toString().compare("BtMac", Qt::CaseInsensitive) == 0 || value.toString().compare("Bt", Qt::CaseInsensitive) == 0)
            {
                _column.i_btmac = column;
            }
            if(value.toString().compare("WifiMac", Qt::CaseInsensitive) == 0 || value.toString().compare("Wifi", Qt::CaseInsensitive) == 0)
            {
                _column.i_wifimac = column;
            }
            if(value.toString().compare("EthernetMac", Qt::CaseInsensitive) == 0)
            {
                _column.i_ethernetmac = column;
            }
            if(value.toString().compare("SerialNo", Qt::CaseInsensitive) == 0)
            {
                _column.i_serialno = column;
            }
            if(value.toString().compare("NetCode", Qt::CaseInsensitive) == 0)
            {
                _column.i_netcode = column;
            }
        }
    }

    if(_column.i_barcode == 0)
    {
        ErrInfo = QString("%1Missing Barcode column;").arg(ErrInfo);
        ret = false;
    }

    return ret;
}

void QxlsxInstance::InitData()
{
    if(!Codedata.isEmpty())
        Codedata.clear();
    std::shared_ptr<QXlsx::Cell> cell;

    for(int i = 2; i <= rowCount; ++i)
    {
        Codes codes;
        cell = xlsx->cellAt(i, _column.i_barcode);
        if(cell == NULL || cell->readValue().isNull() || cell->readValue().toString().isEmpty())
            continue;

        codes.szBarcode.size = cell->value().toString().size();
        codes.szBarcode.value = cell->value().toString();

        if(_column.i_imei1 != -1)
        {
            cell = xlsx->cellAt(i, _column.i_imei1);
            codes.szIMEI1.size = cell->value().toString().size();
            codes.szIMEI1.value = cell->value().toString();
        }
        if(_column.i_imei2 != -1)
        {
            cell = xlsx->cellAt(i, _column.i_imei2);
            codes.szIMEI2.size = cell->value().toString().size();
            codes.szIMEI2.value = cell->value().toString();
        }
        if(_column.i_meid != -1)
        {
            cell = xlsx->cellAt(i, _column.i_meid);
            codes.szMEID.size = cell->value().toString().size();
            codes.szMEID.value = cell->value().toString();
        }
        if(_column.i_btmac != -1)
        {
            cell = xlsx->cellAt(i, _column.i_btmac);
            codes.szBtMac.size = cell->value().toString().size();
            codes.szBtMac.value = cell->value().toString();
        }
        if(_column.i_wifimac != -1)
        {
            cell = xlsx->cellAt(i, _column.i_wifimac);
            codes.szWifiMac.size = cell->value().toString().size();
            codes.szWifiMac.value = cell->value().toString();
        }
        if(_column.i_ethernetmac != -1)
        {
            cell = xlsx->cellAt(i, _column.i_ethernetmac);
            codes.szEthernetMac.size = cell->value().toString().size();
            codes.szEthernetMac.value = cell->value().toString();
        }
        if(_column.i_serialno != -1)
        {
            cell = xlsx->cellAt(i, _column.i_serialno);
            codes.szSerialNo.size = cell->value().toString().size();
            codes.szSerialNo.value = cell->value().toString();
        }
        if(_column.i_netcode != -1)
        {
            cell = xlsx->cellAt(i, _column.i_netcode);
            codes.szNetCode.size = cell->value().toString().size();
            codes.szNetCode.value = cell->value().toString();
        }

        Codedata.insert(codes.szBarcode.value, codes);
    }
}

Codes QxlsxInstance::GetDataByBarcode(QString Barcode)
{
    return Codedata.value(Barcode);
}
