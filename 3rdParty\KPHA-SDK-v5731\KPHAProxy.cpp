#include "KPHAProxy.h"

typedef int (__stdcall* KPHA_InitEnv)(char *base_path);
typedef int (__stdcall* KPHA_ClearEnv)(void);
typedef int (__stdcall* KPHA_GetLastError)(char *err_msg, unsigned int capacity);
typedef int (__stdcall* KPHA_SetLogPath)(const char *log_path);
typedef int (__stdcall* KPHA_ShutdownDevice)(int handle);
typedef int (__stdcall* KPHA_GetLicenseCount)(void);
typedef int (__stdcall* KPHA_Init)(int handle, char *log_path, char *infile_dir, char *outfile_dir);
typedef int (__stdcall* KPHA_SetupDevice)(int handle);
typedef int (__stdcall* KPHA_Init2)(int handle, const char *base_path);
typedef int (__stdcall* KPHA_Is_TEE_Supported)(int handle);
typedef int (__stdcall* KPHA_Check_TEE_State)(int handle);
typedef unsigned long (__stdcall* Get_KPHA_Version)(void);
typedef int (__stdcall* KPHA_Check_RPMB)(int handle, char *buf, int len);
typedef int (__stdcall* KPHA_Write_Daccess_Lock_File)(int handle, const char *infile_dir);
typedef int (__stdcall* KPHA_CSRCheck)(int handle);

KPHA_InitEnv					m_KPHA_InitEnv;
KPHA_ClearEnv					m_KPHA_ClearEnv;
KPHA_GetLastError				m_KPHA_GetLastError;
KPHA_SetLogPath					m_KPHA_SetLogPath;
KPHA_ShutdownDevice				m_KPHA_ShutdownDevice;
KPHA_GetLicenseCount			m_KPHA_GetLicenseCount;
KPHA_Init						m_KPHA_Init;
KPHA_SetupDevice				m_KPHA_SetupDevice;
KPHA_Init2						m_KPHA_Init2;
KPHA_Is_TEE_Supported			m_KPHA_Is_TEE_Supported;
KPHA_Check_TEE_State			m_KPHA_Check_TEE_State;
Get_KPHA_Version				m_Get_KPHA_Version;
KPHA_Check_RPMB					m_KPHA_Check_RPMB;
KPHA_Write_Daccess_Lock_File	m_KPHA_Write_Daccess_Lock_File;
KPHA_CSRCheck					m_KPHA_CSRCheck;

KPHAProxy::KPHAProxy(void)
{
}

KPHAProxy::~KPHAProxy(void)
{
}

KPHAProxy * KPHAProxy::inst()
{
    static KPHAProxy s_me;
    return &s_me;
}

bool KPHAProxy::init(HWND parent, bool flag)
{
	oldversion = flag;
    return DBHelper_Library_DynamicLoad(parent);
}

bool KPHAProxy::DBHelper_Library_DynamicLoad(HWND parent)
{
	HINSTANCE hinstLib = LoadLibrary(_T("LibKPHA.dll"));
	if (hinstLib == NULL)
    {
        MessageBox(parent, "���� LibKPHA.dll ʧ��!", "ERROR");
        return false;
    }
	else
	{
		m_KPHA_InitEnv = (KPHA_InitEnv)GetProcAddress(hinstLib, "_KPHA_InitEnv@4");  
		m_KPHA_ClearEnv = (KPHA_ClearEnv)GetProcAddress(hinstLib, "_KPHA_ClearEnv@0");  
		m_KPHA_GetLastError = (KPHA_GetLastError)GetProcAddress(hinstLib, "_KPHA_GetLastError@8");  
		m_KPHA_SetLogPath = (KPHA_SetLogPath)GetProcAddress(hinstLib, "_KPHA_SetLogPath@4");  
		m_KPHA_ShutdownDevice = (KPHA_ShutdownDevice)GetProcAddress(hinstLib, "_KPHA_ShutdownDevice@4");  
		m_KPHA_GetLicenseCount = (KPHA_GetLicenseCount)GetProcAddress(hinstLib, "_KPHA_GetLicenseCount@0");  
		m_KPHA_Init = (KPHA_Init)GetProcAddress(hinstLib, "_KPHA_Init@16");  
		m_KPHA_SetupDevice = (KPHA_SetupDevice)GetProcAddress(hinstLib, "_KPHA_SetupDevice@4");  
		m_KPHA_Init2 = (KPHA_Init2)GetProcAddress(hinstLib, "_KPHA_Init2@8");  
		m_KPHA_Is_TEE_Supported = (KPHA_Is_TEE_Supported)GetProcAddress(hinstLib, "_KPHA_Is_TEE_Supported@4");  
		m_KPHA_Check_TEE_State = (KPHA_Check_TEE_State)GetProcAddress(hinstLib, "_KPHA_Check_TEE_State@4");  
		m_Get_KPHA_Version = (Get_KPHA_Version)GetProcAddress(hinstLib, "_Get_KPHA_Version@0");  
		m_KPHA_Check_RPMB = (KPHA_Check_RPMB)GetProcAddress(hinstLib, "_KPHA_Check_RPMB@12");  
		m_KPHA_Write_Daccess_Lock_File = (KPHA_Write_Daccess_Lock_File)GetProcAddress(hinstLib, "_KPHA_Write_Daccess_Lock_File@8");  
		m_KPHA_CSRCheck = (KPHA_CSRCheck)GetProcAddress(hinstLib, "_KPHA_CSRCheck@4");  
	}

    return true;
}

int KPHAProxy::Dll_KPHA_InitEnv(char *base_path)
{
	return m_KPHA_InitEnv(base_path);
}

int KPHAProxy::Dll_KPHA_ClearEnv(void)
{
	return m_KPHA_ClearEnv();
}

int KPHAProxy::Dll_KPHA_GetLastError(char *err_msg, unsigned int capacity)
{
	return m_KPHA_GetLastError(err_msg, capacity);
}

int KPHAProxy::Dll_KPHA_SetLogPath(const char *log_path)
{
	return m_KPHA_SetLogPath(log_path);
}

int KPHAProxy::Dll_KPHA_ShutdownDevice(int handle)
{
	return m_KPHA_ShutdownDevice(handle);
}

int KPHAProxy::Dll_KPHA_GetLicenseCount(void)
{
	return m_KPHA_GetLicenseCount();
}

int KPHAProxy::Dll_KPHA_Init(int handle, char *log_path, char *infile_dir, char *outfile_dir)
{
	return m_KPHA_Init(handle, log_path, infile_dir, outfile_dir);
}

int KPHAProxy::Dll_KPHA_SetupDevice(int handle)
{
	return m_KPHA_SetupDevice(handle);
}

int KPHAProxy::Dll_KPHA_Init2(int handle, const char *base_path)
{
	return m_KPHA_Init2(handle, base_path);
}

int KPHAProxy::Dll_KPHA_Is_TEE_Supported(int handle)
{
	return m_KPHA_Is_TEE_Supported(handle);
}

int KPHAProxy::Dll_KPHA_Check_TEE_State(int handle)
{
	return m_KPHA_Check_TEE_State(handle);
}

unsigned long KPHAProxy::Dll_Get_KPHA_Version(void)
{
	return m_Get_KPHA_Version();
}

int KPHAProxy::Dll_KPHA_Check_RPMB(int handle, char *buf, int len)
{
	return m_KPHA_Check_RPMB(handle, buf, len);
}

int KPHAProxy::Dll_KPHA_Write_Daccess_Lock_File(int handle, const char *infile_dir)
{
	return m_KPHA_Write_Daccess_Lock_File(handle, infile_dir);
}

int KPHAProxy::Dll_KPHA_CSRCheck(int handle)
{
	return m_KPHA_CSRCheck(handle);
}
