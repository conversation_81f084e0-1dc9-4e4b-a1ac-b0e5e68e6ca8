#include "aboutdialog.h"
#include "ui_aboutdialog.h"
#include <QDate>

CAboutDialog::CAboutDialog(QWidget *parent)
    : QDialog(parent)
    , ui(new Ui::CAboutDialog)
{
    // 去掉标题栏上的问号图标
    Qt::WindowFlags flags = this->windowFlags();
    flags &= ~Qt::WindowContextHelpButtonHint;
    this->setWindowFlags(flags);

    ui->setupUi(this);
    QDate date = QDate::currentDate();
    QString copyRight = QString("(C) Copyright 2024 - %1 Agenew Inc. All rights reserved.").arg(date.year());
    ui->m_lbCopyRight->setText(copyRight);
}

CAboutDialog::~CAboutDialog()
{
    delete ui;
}
