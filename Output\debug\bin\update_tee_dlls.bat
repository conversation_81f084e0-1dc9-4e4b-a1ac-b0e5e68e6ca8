@echo off
setlocal enabledelayedexpansion
echo Waiting for application to close...
timeout /t 5 /nobreak >nul 2>nul

echo Checking for running processes...
taskkill /f /im Agenew_MultiCodes_Tool.exe >nul 2>nul
timeout /t 3 /nobreak >nul 2>nul

echo Attempting to unlock DLL files...
for %%f in (LibKpaUtil.dll LibKPHA.dll LibTurkey.dll LibUploader.dll) do (
    if exist "%%f" (
        attrib -r "%%f" >nul 2>nul
        del /f /q "%%f.bak" >nul 2>nul
        ren "%%f" "%%f.bak" >nul 2>nul
    )
)

echo Updating TEE DLL files to version v4.6.1.0...

if exist "E:/tool_develop/mydevelop/Agenew_MultiCodes_Tool/Output/debug/bin/TEE_Dll/v4.6.1.0\LibKpaUtil.dll" (
    echo Copying LibKpaUtil.dll...
    copy /Y "E:/tool_develop/mydevelop/Agenew_MultiCodes_Tool/Output/debug/bin/TEE_Dll/v4.6.1.0\LibKpaUtil.dll" "E:/tool_develop/mydevelop/Agenew_MultiCodes_Tool/Output/debug/bin\LibKpaUtil.dll" >nul 2>nul
    if errorlevel 1 (
        echo Failed to copy LibKpaUtil.dll, trying alternative method...
        timeout /t 1 /nobreak >nul 2>nul
        move "E:/tool_develop/mydevelop/Agenew_MultiCodes_Tool/Output/debug/bin/TEE_Dll/v4.6.1.0\LibKpaUtil.dll" "E:/tool_develop/mydevelop/Agenew_MultiCodes_Tool/Output/debug/bin\LibKpaUtil.dll.tmp" >nul 2>nul
        if exist "E:/tool_develop/mydevelop/Agenew_MultiCodes_Tool/Output/debug/bin\LibKpaUtil.dll.tmp" (
            ren "E:/tool_develop/mydevelop/Agenew_MultiCodes_Tool/Output/debug/bin\LibKpaUtil.dll.tmp" "LibKpaUtil.dll" >nul 2>nul
            copy /Y "E:/tool_develop/mydevelop/Agenew_MultiCodes_Tool/Output/debug/bin\LibKpaUtil.dll" "E:/tool_develop/mydevelop/Agenew_MultiCodes_Tool/Output/debug/bin/TEE_Dll/v4.6.1.0\LibKpaUtil.dll" >nul 2>nul
            echo Successfully copied LibKpaUtil.dll using alternative method
        ) else (
            echo Failed to copy LibKpaUtil.dll
        )
    ) else (
        echo Successfully copied LibKpaUtil.dll
    )
) else (
    echo Source file not found: LibKpaUtil.dll
)

if exist "E:/tool_develop/mydevelop/Agenew_MultiCodes_Tool/Output/debug/bin/TEE_Dll/v4.6.1.0\LibKPHA.dll" (
    echo Copying LibKPHA.dll...
    copy /Y "E:/tool_develop/mydevelop/Agenew_MultiCodes_Tool/Output/debug/bin/TEE_Dll/v4.6.1.0\LibKPHA.dll" "E:/tool_develop/mydevelop/Agenew_MultiCodes_Tool/Output/debug/bin\LibKPHA.dll" >nul 2>nul
    if errorlevel 1 (
        echo Failed to copy LibKPHA.dll, trying alternative method...
        timeout /t 1 /nobreak >nul 2>nul
        move "E:/tool_develop/mydevelop/Agenew_MultiCodes_Tool/Output/debug/bin/TEE_Dll/v4.6.1.0\LibKPHA.dll" "E:/tool_develop/mydevelop/Agenew_MultiCodes_Tool/Output/debug/bin\LibKPHA.dll.tmp" >nul 2>nul
        if exist "E:/tool_develop/mydevelop/Agenew_MultiCodes_Tool/Output/debug/bin\LibKPHA.dll.tmp" (
            ren "E:/tool_develop/mydevelop/Agenew_MultiCodes_Tool/Output/debug/bin\LibKPHA.dll.tmp" "LibKPHA.dll" >nul 2>nul
            copy /Y "E:/tool_develop/mydevelop/Agenew_MultiCodes_Tool/Output/debug/bin\LibKPHA.dll" "E:/tool_develop/mydevelop/Agenew_MultiCodes_Tool/Output/debug/bin/TEE_Dll/v4.6.1.0\LibKPHA.dll" >nul 2>nul
            echo Successfully copied LibKPHA.dll using alternative method
        ) else (
            echo Failed to copy LibKPHA.dll
        )
    ) else (
        echo Successfully copied LibKPHA.dll
    )
) else (
    echo Source file not found: LibKPHA.dll
)

if exist "E:/tool_develop/mydevelop/Agenew_MultiCodes_Tool/Output/debug/bin/TEE_Dll/v4.6.1.0\LibTurkey.dll" (
    echo Copying LibTurkey.dll...
    copy /Y "E:/tool_develop/mydevelop/Agenew_MultiCodes_Tool/Output/debug/bin/TEE_Dll/v4.6.1.0\LibTurkey.dll" "E:/tool_develop/mydevelop/Agenew_MultiCodes_Tool/Output/debug/bin\LibTurkey.dll" >nul 2>nul
    if errorlevel 1 (
        echo Failed to copy LibTurkey.dll, trying alternative method...
        timeout /t 1 /nobreak >nul 2>nul
        move "E:/tool_develop/mydevelop/Agenew_MultiCodes_Tool/Output/debug/bin/TEE_Dll/v4.6.1.0\LibTurkey.dll" "E:/tool_develop/mydevelop/Agenew_MultiCodes_Tool/Output/debug/bin\LibTurkey.dll.tmp" >nul 2>nul
        if exist "E:/tool_develop/mydevelop/Agenew_MultiCodes_Tool/Output/debug/bin\LibTurkey.dll.tmp" (
            ren "E:/tool_develop/mydevelop/Agenew_MultiCodes_Tool/Output/debug/bin\LibTurkey.dll.tmp" "LibTurkey.dll" >nul 2>nul
            copy /Y "E:/tool_develop/mydevelop/Agenew_MultiCodes_Tool/Output/debug/bin\LibTurkey.dll" "E:/tool_develop/mydevelop/Agenew_MultiCodes_Tool/Output/debug/bin/TEE_Dll/v4.6.1.0\LibTurkey.dll" >nul 2>nul
            echo Successfully copied LibTurkey.dll using alternative method
        ) else (
            echo Failed to copy LibTurkey.dll
        )
    ) else (
        echo Successfully copied LibTurkey.dll
    )
) else (
    echo Source file not found: LibTurkey.dll
)

if exist "E:/tool_develop/mydevelop/Agenew_MultiCodes_Tool/Output/debug/bin/TEE_Dll/v4.6.1.0\LibUploader.dll" (
    echo Copying LibUploader.dll...
    copy /Y "E:/tool_develop/mydevelop/Agenew_MultiCodes_Tool/Output/debug/bin/TEE_Dll/v4.6.1.0\LibUploader.dll" "E:/tool_develop/mydevelop/Agenew_MultiCodes_Tool/Output/debug/bin\LibUploader.dll" >nul 2>nul
    if errorlevel 1 (
        echo Failed to copy LibUploader.dll, trying alternative method...
        timeout /t 1 /nobreak >nul 2>nul
        move "E:/tool_develop/mydevelop/Agenew_MultiCodes_Tool/Output/debug/bin/TEE_Dll/v4.6.1.0\LibUploader.dll" "E:/tool_develop/mydevelop/Agenew_MultiCodes_Tool/Output/debug/bin\LibUploader.dll.tmp" >nul 2>nul
        if exist "E:/tool_develop/mydevelop/Agenew_MultiCodes_Tool/Output/debug/bin\LibUploader.dll.tmp" (
            ren "E:/tool_develop/mydevelop/Agenew_MultiCodes_Tool/Output/debug/bin\LibUploader.dll.tmp" "LibUploader.dll" >nul 2>nul
            copy /Y "E:/tool_develop/mydevelop/Agenew_MultiCodes_Tool/Output/debug/bin\LibUploader.dll" "E:/tool_develop/mydevelop/Agenew_MultiCodes_Tool/Output/debug/bin/TEE_Dll/v4.6.1.0\LibUploader.dll" >nul 2>nul
            echo Successfully copied LibUploader.dll using alternative method
        ) else (
            echo Failed to copy LibUploader.dll
        )
    ) else (
        echo Successfully copied LibUploader.dll
    )
) else (
    echo Source file not found: LibUploader.dll
)

echo Cleaning up backup files...
del /f /q "E:/tool_develop/mydevelop/Agenew_MultiCodes_Tool/Output/debug/bin\*.dll.bak" >nul 2>nul

echo DLL update completed.
echo Restarting application...

timeout /t 1 /nobreak >nul 2>nul
start "Agenew MultiCodes Tool" "E:/tool_develop/mydevelop/Agenew_MultiCodes_Tool/Output/debug/bin/Agenew_MultiCodes_Tool.exe"

echo Cleaning up...
timeout /t 2 /nobreak >nul 2>nul
(goto) 2>nul & del "%~f0"
