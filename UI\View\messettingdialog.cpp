#include "messettingdialog.h"
#include "ui_messettingdialog.h"
#include "MainWindow.h"
#include "Common/Common.h"

MesSettingDialog::MesSettingDialog(MainWindow *mainwindow, QWidget *parent)
    : QDialog(parent)
    , ui(new Ui::MesSettingDialog)
    , m_mainwindow(mainwindow)
{
    ui->setupUi(this);
    this->setWindowFlags(Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint);

    InitSettings();
}

MesSettingDialog::~MesSettingDialog()
{
    delete ui;
}

void MesSettingDialog::InitSettings()
{
    m_mainwindow->showAllWidgetsInGroupBox(ui->MesInfo_groupBox);

    ui->Mes_Offline_checkBox->setChecked(Common::getInstance()->getMesOfflineEnable());
    ui->NotCheckStation_checkBox->setChecked(Common::getInstance()->getMesNotCheckStationEnable());
    ui->NotUpdateStation_checkBox->setChecked(Common::getInstance()->getMesNotUpdateEnable());
    QString Type = Common::getInstance()->getMesType();

    if(Type == "SD_MES")
    {
        ui->IP_label->hide();
        ui->IP_Edit->hide();
        ui->Line_label->hide();
        ui->line_Edit->hide();
        ui->ResName_lineEdit->hide();
        ui->Res_label->hide();
        ui->Order_lineEdit->setText(Common::getInstance()->getSDMesOrder());
        ui->Station_lineEdit->setText(Common::getInstance()->getSDMesStation());
        ui->operator_lineEdit->setText(Common::getInstance()->getSDMesUserCode());
    }
    else if(Type == "AGN_MES")
    {
        ui->ResName_lineEdit->hide();
        ui->Res_label->hide();
        ui->Station_lineEdit->setText(Common::getInstance()->getAGNMesStation());
        ui->line_Edit->setText(Common::getInstance()->getAGNMesLine());
        ui->IP_Edit->setText(Common::getInstance()->getAGNMesServer());
        ui->Order_lineEdit->setText(Common::getInstance()->getAGNMesOrder());
        ui->operator_lineEdit->setText(Common::getInstance()->getAGNMesUserCode());
    }
    else if(Type == "YD_MES")
    {
        ui->operator_label->hide();
        ui->operator_lineEdit->hide();
        ui->Line_label->hide();
        ui->line_Edit->hide();
        ui->IP_Edit->setText(Common::getInstance()->getYDMesIP());
        ui->Order_lineEdit->setText(Common::getInstance()->getYDMesOrder());
        ui->Station_lineEdit->setText(Common::getInstance()->getYDMesStation());
        ui->ResName_lineEdit->setText(Common::getInstance()->getYDMesResName());
    }

    ui->MesSelect_comboBox->setCurrentText(Type);
}

void MesSettingDialog::on_MesSelect_comboBox_currentTextChanged(const QString &arg1)
{
    m_mainwindow->showAllWidgetsInGroupBox(ui->MesInfo_groupBox);

    if(arg1 == "SD_MES")
    {
        ui->IP_label->hide();
        ui->IP_Edit->hide();
        ui->Line_label->hide();
        ui->line_Edit->hide();
        ui->Res_label->hide();
        ui->ResName_lineEdit->hide();
        ui->Order_lineEdit->setText(Common::getInstance()->getSDMesOrder());
        ui->Station_lineEdit->setText(Common::getInstance()->getSDMesStation());
        ui->operator_lineEdit->setText(Common::getInstance()->getSDMesUserCode());
    }
    else if(arg1 == "AGN_MES")
    {
        ui->Res_label->hide();
        ui->ResName_lineEdit->hide();
        ui->Station_lineEdit->setText(Common::getInstance()->getAGNMesStation());
        ui->line_Edit->setText(Common::getInstance()->getAGNMesLine());
        ui->IP_Edit->setText(Common::getInstance()->getAGNMesServer());
        ui->Order_lineEdit->setText(Common::getInstance()->getAGNMesOrder());
        ui->operator_lineEdit->setText(Common::getInstance()->getAGNMesUserCode());
    }
    else if(arg1 == "YD_MES")
    {
        ui->operator_label->hide();
        ui->operator_lineEdit->hide();
        ui->Line_label->hide();
        ui->line_Edit->hide();
        ui->IP_Edit->setText(Common::getInstance()->getYDMesIP());
        ui->Order_lineEdit->setText(Common::getInstance()->getYDMesOrder());
        ui->Station_lineEdit->setText(Common::getInstance()->getYDMesStation());
        ui->ResName_lineEdit->setText(Common::getInstance()->getYDMesResName());
    }
}

void MesSettingDialog::on_buttonBox_accepted()
{
    SaveSettings();
    Common::getInstance()->saveOptionFile();
    m_mainwindow->UpdateUIDisplay();
}

void MesSettingDialog::SaveSettings()
{
    Common::getInstance()->setMesOfflineEnable(ui->Mes_Offline_checkBox->isChecked());
    Common::getInstance()->setMesNotCheckStationEnable(ui->NotCheckStation_checkBox->isChecked());
    Common::getInstance()->setMesNotUpdateEnable(ui->NotUpdateStation_checkBox->isChecked());

    QString Type = ui->MesSelect_comboBox->currentText();
    Common::getInstance()->setMesType(Type);
    if(Type == "SD_MES")
    {
        Common::getInstance()->setSDMesOrder(ui->Order_lineEdit->text());
        Common::getInstance()->setSDMesStation(ui->Station_lineEdit->text());
        Common::getInstance()->setSDMesUserCode(ui->operator_lineEdit->text());
    }
    else if(Type == "AGN_MES")
    {
        Common::getInstance()->setAGNMesLine(ui->line_Edit->text());
        Common::getInstance()->setAGNMesOrder(ui->Order_lineEdit->text());
        Common::getInstance()->setAGNMesServer(ui->IP_Edit->text());
        Common::getInstance()->setAGNMesStation(ui->Station_lineEdit->text());
        Common::getInstance()->setAGNMesUserCode(ui->operator_lineEdit->text());
    }
    else if(Type == "YD_MES")
    {
        Common::getInstance()->setYDMesIP(ui->IP_Edit->text());
        Common::getInstance()->setYDMesOrder(ui->Order_lineEdit->text());
        Common::getInstance()->setYDMesStation(ui->Station_lineEdit->text());
        Common::getInstance()->setYDMesResName(ui->ResName_lineEdit->text());
    }
}
