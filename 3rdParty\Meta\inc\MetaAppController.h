/*
 * Copyright (c) 2020, MediaTek Inc. All rights reserved.
 *
 * This software/firmware and related documentation ("MediaTek Software") are
 * protected under relevant copyright laws.
 * The information contained herein is confidential and proprietary to
 * MediaTek Inc. and/or its licensors.
 * Except as otherwise provided in the applicable licensing terms with
 * MediaTek Inc. and/or its licensors, any reproduction, modification, use or
 * disclosure of MediaTek Software, and information contained herein, in whole
 * or in part, shall be strictly prohibited.
*/
// The following ifdef block is the standard way of creating macros which make exporting
// from a DLL simpler. All files within this DLL are compiled with the METACONN_EXPORTS
// symbol defined on the command line. this symbol should not be defined on any project
// that uses this DLL. This way any other project whose source files include this file see
// METAAPPAPI functions as being imported from a DLL, whereas this DLL sees symbols
// defined with this macro as being exported.
#ifndef __META_APP_CONTROLLER_H__
#define __META_APP_CONTROLLER_H__

#include "export.h"

typedef enum _METAAPP_RESULT
{
    METAAPP_SUCCESS = 0,                           // Success
    METAAPP_FAIL,
    METAAPP_TIMEOUT,
    METAAPP_STOP,
    METAAPP_CANCEL,
    METAAPP_SECTION_OK,
    METAAPP_FUNC_NOT_IMPLEMENT_YET,
} METAAPP_RESULT;

METAAPP_RESULT METAAPPAPI METAAPP_CompressFolder(const char *outputArchiveName, const char *intputFolderName);
METAAPP_RESULT METAAPPAPI METAAPP_CompressFolder_W(const wchar_t *outputArchiveName, const wchar_t *intputFolderName);
#endif // #ifndef __META_APP_CONTROLLER_H__
