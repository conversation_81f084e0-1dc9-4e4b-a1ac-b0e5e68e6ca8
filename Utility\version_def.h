#ifndef VERION_DEF_H
#define VERION_DEF_H

//************** PLEASE DO NOT MODIFY THE NAMES MANUALLY IN THIS FILE *******************
#define  CUSTOMER_VER      true
#define  TEMP_VER          false

#define  MAJOR_VER         1
#define  MINOR_VER         0
#define  BUILD_NUM         00
#define  BUILD_SN          000
//Note: REVISION_NUM is commit hash of the tool, should be HEX(0x...)
#define  REVISION_NUM      0x615438
#define  BUILD_DATE        "2025/05/06 15:10"
#define  COMMENTS          "N/A"

#define  VER_FILEVERSION      		MAJOR_VER,MINOR_VER,BUILD_NUM,BUILD_SN
#define  VER_FILEVERSION_STR        "**********"
//************** THE NAMES ABOVE WILL BE MODIFIED BY DAILY BUILD SCRIPTS *****************

#define  VER_PRODUCTVERSION          VER_FILEVERSION
#define  VER_PRODUCTVERSION_STR 	 VER_FILEVERSION_STR

#define  VER_COMPANYNAME_STR         "Agenew Inc"

#define  VER_FILEDESCRIPTION_STR     "Agenew_MultiCodes_Tool.exe"
#define  VER_ORIGINALFILENAME_STR    "Agenew_MultiCodes_Tool.exe"
#define  VER_PRODUCTNAME_STR         "Agenew MultiCodes Tool"
#define  APP_NAME                    QStringLiteral("Agenew MultiCodes Tool")

#define  VER_LEGALCOPYRIGHT_STR      "Copyright(C) 2025 Agenew Inc. All Rights Reserved"

#define  VER_COMPANYDOMAIN_STR       "www.agenewtech.com"
//*****************************************************************

#endif // VERION_DEF_H
