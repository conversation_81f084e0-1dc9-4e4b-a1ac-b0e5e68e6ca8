#ifndef LIBKPHA_H_
#define LIBKPHA_H_

#define LIBKPHA_API __declspec(dllexport)
#define WINDOWS

#ifdef __cplusplus
extern "C" {
#endif

LIBKPHA_API int __stdcall KPHA_InitEnv(char *base_path);

typedef void (* KPHA_LogCallback) (int loglevel,
        const char *filename, int lineno, const char *str);

LIBKPHA_API int __stdcall KPHA_InitLog(KPHA_LogCallback logCallback);

LIBKPHA_API int __stdcall KPHA_ClearEnv(void);

LIBKPHA_API int __stdcall KPHA_GetLastError(char *err_msg, unsigned int capacity);

LIBKPHA_API int  __stdcall KPHA_SetLogPath(const char *log_path);

LIBKPHA_API int  __stdcall KPHA_ShutdownDevice(int handle);

LIBKPHA_API int  __stdcall KPHA_GetLicenseCount(void);

LIBKPHA_API int  __stdcall KPHA_Init(int handle,
    char *log_path, char *infile_dir, char *outfile_dir);

LIBKPHA_API int  __stdcall KPHA_SetupDevice(int handle);

LIBKPHA_API int __stdcall KPHA_Init2(int handle, const char *base_path);

LIBKPHA_API int __stdcall KPHA_Is_TEE_Supported(int handle);

LIBKPHA_API int __stdcall KPHA_Check_TEE_State(int handle);

LIBKPHA_API unsigned long  __stdcall Get_KPHA_Version(void);

LIBKPHA_API int  __stdcall KPHA_Check_RPMB(int handle, char *buf, int len);

LIBKPHA_API int  __stdcall KPHA_Write_Daccess_Lock_File(int handle, const char *infile_dir);

LIBKPHA_API int __stdcall KPHA_CSRCheck(int handle);

#ifdef __cplusplus
}
#endif
#endif
